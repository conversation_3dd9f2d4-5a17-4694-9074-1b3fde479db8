package com.whfc.fse.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/19
 */
@Data
@Schema(description = "4S天秤吊设备DTO")
public class FseTowerCraneDTO implements Serializable {

    @JsonIgnore
    @Schema(description = "主键ID")
    private Integer id;
    /**
     * GUID
     */
    @Schema(description = "GUID")
    private String guid;

    /**
     * 组织机构ID
     */
    @Schema(description = "组织机构ID")
    private Integer deptId;

    /**
     * 设备编号
     */
    @Schema(description = "设备编号")
    private String code;

    /**
     * 设备型号
     */
    @Schema(description = "设备型号")
    private String model;

    /**
     * 制造单位
     */
    @Schema(description = "制造单位")
    private String manufacturer;

    /**
     * 出厂编号
     */
    @Schema(description = "出厂编号")
    private String deliveryCode;

    /**
     * 出厂日期
     */
    @Schema(description = "出厂日期")
    private Date deliveryDate;

    @Schema(description = "位置信息")
    private String address;

    @Schema(description = "报警次数")
    private Integer warnNum = 0;

    @Schema(description = "AI服务器")
    private List<FseTowerCraneDeviceDTO> aiServerList;

    @Schema(description = "UWB基站")
    private List<FseTowerCraneDeviceDTO> uwbServerList;

    @Schema(description = "视频监控")
    private List<FseTowerCraneDeviceDTO> fvsList;


}
