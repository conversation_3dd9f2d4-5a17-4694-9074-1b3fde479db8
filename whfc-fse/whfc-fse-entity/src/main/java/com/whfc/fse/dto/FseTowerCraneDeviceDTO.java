package com.whfc.fse.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 4S天秤吊关联设备
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/19
 */
@Schema(description = "4S天秤吊关联设备")
@Data
public class FseTowerCraneDeviceDTO implements Serializable {

    @JsonIgnore
    @Schema(description = "天秤吊ID")
    private Integer towerId;
    /**
     * 监控方案 1-AI监控 2-UWB测距 3-视频监控
     */
    @Schema(description = "监控方案 1-AI监控 2-UWB测距 3-视频监控")
    private Integer monitorType;

    /**
     * 部位名称
     */
    @Schema(description = "部位名称")
    private String partName;

    /**
     * 监控设备ID
     */
    @Schema(description = "监控设备ID")
    private Integer deviceId;

    /**
     * 监控设备名称
     */
    @Schema(description = "监控设备名称")
    private String deviceName;

    @Schema(description = "在线状态 0-离线 1-在线")
    private Integer netState = 0;

}