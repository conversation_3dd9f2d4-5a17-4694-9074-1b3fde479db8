package com.whfc.fse.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 4S天秤吊设备绑定参数
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/19
 */
@Data
@Schema(description = "4S天秤吊设备绑定参数")
public class FseTowerCraneBindParam implements Serializable {
    /**
     * GUID
     */
    @Schema(description = "GUID")
    private String guid;

    /**
     * 监控方案 1-AI监控 2-UWB测距
     */
    @Schema(description = "监控方案 1-AI监控 2-UWB测距 3-视频监控")
    private Integer monitorType;


    @Schema(description = "设备列表")
    private List<FseTowerCraneDeviceParam> deviceList;

}
