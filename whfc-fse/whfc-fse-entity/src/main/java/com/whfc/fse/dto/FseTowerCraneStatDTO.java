package com.whfc.fse.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "4S天秤吊设备统计")
public class FseTowerCraneStatDTO implements Serializable {

    @Schema(description = "总数")
    private Integer total = 0;

    @Schema(description = "AI服务器总数")
    private Integer aiServerTotal = 0;

    @Schema(description = "UWB基站总数")
    private Integer uwbTotal = 0;

    @Schema(description = "视频监控总数")
    private Integer fvsTotal = 0;

}
