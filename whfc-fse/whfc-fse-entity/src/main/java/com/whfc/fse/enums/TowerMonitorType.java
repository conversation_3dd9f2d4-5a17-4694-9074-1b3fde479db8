package com.whfc.fse.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/12
 */
public enum TowerMonitorType {

    AI(1, "AI监控"),

    UWB(2, "UWB测距"),

    FVS(3, "视频监控"),

    ;

    private final Integer value;

    private final String desc;

    TowerMonitorType(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
