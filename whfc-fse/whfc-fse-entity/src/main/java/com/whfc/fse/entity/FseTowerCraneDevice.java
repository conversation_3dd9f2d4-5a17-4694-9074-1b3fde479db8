package com.whfc.fse.entity;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

import lombok.Data;

/**
 * 4S天秤吊关联设备
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/19
 */
@Schema(description = "4S天秤吊关联设备")
@Data
public class FseTowerCraneDevice {
    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Integer id;

    /**
     * 组织机构ID
     */
    @Schema(description = "组织机构ID")
    private Integer deptId;

    /**
     * 天秤吊ID
     */
    @Schema(description = "天秤吊ID")
    private Integer towerId;

    /**
     * 监控方案 1-AI监控 2-UWB测距
     */
    @Schema(description = "监控方案 1-AI监控 2-UWB测距")
    private Integer monitorType;

    /**
     * 部位名称
     */
    @Schema(description = "部位名称")
    private String partName;

    /**
     * 监控设备ID
     */
    @Schema(description = "监控设备ID")
    private Integer deviceId;

    /**
     * 监控设备名称
     */
    @Schema(description = "监控设备名称")
    private String deviceName;

    /**
     * 删除标记 0-未删除 1-已删除
     */
    @Schema(description = "删除标记 0-未删除 1-已删除")
    private Integer delFlag;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;
}