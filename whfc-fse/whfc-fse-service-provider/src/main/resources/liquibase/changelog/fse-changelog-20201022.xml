<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd">
    <property name="autoIncrement" value="true" dbms="mysql"/>

    <changeSet id="1" author="sunguodong">
        <comment>初始化表</comment>
        <sql>
            CREATE TABLE `fse_crane` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `dept_id` int(11) NOT NULL COMMENT '组织机构ID',
            `dept_name` varchar(25) NOT NULL COMMENT '组织机构名称',
            `code` varchar(25) NOT NULL COMMENT '起重机编号',
            `model` int(11) NOT NULL COMMENT '型号(字典)',
            `model_name` varchar(25) NOT NULL,
            `manufacturer` varchar(25) NOT NULL COMMENT '制造单位',
            `delivery_code` varchar(25) NOT NULL COMMENT '出厂编号',
            `delivery_date` date NOT NULL COMMENT '出厂日期',
            `property_unit` varchar(25) NOT NULL COMMENT '产权单位',
            `monitor_no` varchar(25) NOT NULL COMMENT '检测证号',
            `file_no` varchar(25) NOT NULL COMMENT '备案编号',
            `filing_date` date NOT NULL COMMENT '备案日期',
            `min_weight` double NOT NULL COMMENT '最小起重重量',
            `max_weight` double NOT NULL COMMENT '最大起重重量',
            `bind_flag` int(11) NOT NULL DEFAULT 0 COMMENT '绑定标记(0-未绑定 1-已绑定)',
            `sn` varchar(25) NULL DEFAULT NULL COMMENT '绑定的硬件sn码',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT
            '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '起重机';


            CREATE TABLE `fse_crane_data` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `crane_id` int(11) NOT NULL COMMENT '起重机id',
            `fore_arm_length` double NULL DEFAULT NULL COMMENT '前臂长（米）',
            `rear_arm_length` double NULL DEFAULT NULL COMMENT '后臂长（米）',
            `tower_arm_height` double NULL DEFAULT NULL COMMENT '塔臂高（米）',
            `range` double NOT NULL COMMENT '幅度（米）',
            `moment_ratio` double NOT NULL COMMENT '力矩比',
            `max_weight` double NULL DEFAULT NULL COMMENT '最大载重',
            `arm_length` double NULL DEFAULT NULL COMMENT '臂长',
            `height` double NOT NULL COMMENT '高度（米）',
            `weight` double NOT NULL COMMENT '重量（顿）',
            `wind_speed` double NOT NULL COMMENT '风速（m/s）',
            `lift_speed` double NULL DEFAULT NULL COMMENT '起重速度（m/s）',
            `derrick_speed` double NULL DEFAULT NULL COMMENT '变幅速度（m/s）',
            `dip_angle` double NOT NULL COMMENT '倾角（度）',
            `turn_angle` double NOT NULL COMMENT '转角（度）',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT
            '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '起重机数据';


            CREATE TABLE `fse_crane_device` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `crane_id` int(11) NOT NULL COMMENT '起重机ID',
            `device_id` int(11) NOT NULL COMMENT '硬件id',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT
            '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '起重机绑定信息表';


            CREATE TABLE `fse_crane_device_log` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `crane_id` int(11) NOT NULL,
            `device_id` int(11) NOT NULL,
            `time` datetime(0) NOT NULL COMMENT '操作时间',
            `type` int(11) NOT NULL COMMENT '类型(1-绑定 2-解绑)',
            `user_id` int(11) NULL DEFAULT NULL COMMENT '操作人',
            `user_name` varchar(20) NULL DEFAULT NULL COMMENT '操作人姓名',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT
            '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '起重机和硬件绑定日志表';


            CREATE TABLE `fse_crane_operator` (
            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
            `crane_id` int(11) NOT NULL COMMENT '起重机id',
            `operator_name` varchar(50) NOT NULL COMMENT '操作手姓名',
            `operator_cert` varchar(50) NOT NULL COMMENT '操作手证书编号',
            `operator_id_card_no` varchar(20) NULL DEFAULT NULL COMMENT '操作手身份证号码',
            `phone` varchar(11) NOT NULL COMMENT '操作手电话',
            `del_flag` int(4) NOT NULL DEFAULT 0,
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '起重机操作手信息';


            CREATE TABLE `fse_crane_record` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `crane_id` int(11) NOT NULL COMMENT '起重机id',
            `start_time` datetime(0) NOT NULL COMMENT '起吊时间',
            `end_time` datetime(0) NULL DEFAULT NULL COMMENT '卸吊时间',
            `start_height` double NOT NULL COMMENT '开始高度（米）',
            `end_height` double NULL DEFAULT NULL COMMENT '结束高度（米）',
            `start_angle` double NULL DEFAULT NULL COMMENT '开始角度（度）',
            `end_angle` double NULL DEFAULT NULL COMMENT '结束角度（度）',
            `start_range` double NOT NULL COMMENT '开始幅度（米）',
            `end_range` double NULL DEFAULT NULL COMMENT '结束幅度（米）',
            `max_weight` double NULL DEFAULT NULL COMMENT '最大吊重（顿）',
            `max_wind_speed` double NULL DEFAULT NULL COMMENT '最大风速（m/s）',
            `max_dip_angle` double NULL DEFAULT NULL COMMENT '最大倾角（度）',
            `max_turn_angle` double NULL DEFAULT NULL COMMENT '最大转角（度）',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT
            '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '起重机吊装记录';


            CREATE TABLE `fse_device_crane` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `dept_id` int(11) NOT NULL COMMENT '组织机构ID',
            `sn` varchar(20) NOT NULL COMMENT '硬件唯一SN',
            `active_flag` int(11) NOT NULL DEFAULT 0 COMMENT '激活标记:0-未激活 1-已激活',
            `active_time` datetime(0) NULL DEFAULT NULL COMMENT '激活时间',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
            `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '起重机硬件信息';


            CREATE TABLE `fse_device_crane_data` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `device_id` int(11) NOT NULL COMMENT '起重机硬件id',
            `time` datetime(0) NOT NULL COMMENT '时间',
            `fore_arm_length` double NULL DEFAULT NULL COMMENT '前臂长（米）',
            `rear_arm_length` double NULL DEFAULT NULL COMMENT '后臂长（米）',
            `tower_arm_height` double NULL DEFAULT NULL COMMENT '塔臂高（米）',
            `range` double NOT NULL COMMENT '幅度（米）',
            `moment_ratio` double NOT NULL COMMENT '力矩比',
            `max_weight` double NULL DEFAULT NULL COMMENT '最大载重',
            `arm_length` double NULL DEFAULT NULL COMMENT '臂长',
            `height` double NOT NULL COMMENT '高度（米）',
            `weight` double NOT NULL COMMENT '重量（顿）',
            `wind_speed` double NOT NULL COMMENT '风速（m/s）',
            `lift_speed` double NULL DEFAULT NULL COMMENT '起重速度（m/s）',
            `derrick_speed` double NULL DEFAULT NULL COMMENT '变幅速度（m/s）',
            `dip_angle` double NOT NULL COMMENT '倾角（度）',
            `turn_angle` double NOT NULL COMMENT '转角（度）',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT
            '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`),
            UNIQUE INDEX `uk_deviceId`(`device_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '起重机硬件数据';


            CREATE TABLE `fse_device_crane_data_log` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `crane_id` int(11) NOT NULL COMMENT '起重机id',
            `device_id` int(11) NOT NULL COMMENT '起重机硬件id',
            `time` datetime(0) NOT NULL COMMENT '时间',
            `fore_arm_length` double NULL DEFAULT NULL COMMENT '前臂长（米）',
            `rear_arm_length` double NULL DEFAULT NULL COMMENT '后臂长（米）',
            `tower_arm_height` double NULL DEFAULT NULL COMMENT '塔臂高（米）',
            `range` double NOT NULL COMMENT '幅度（米）',
            `moment_ratio` double NOT NULL COMMENT '力矩比',
            `max_weight` double NULL DEFAULT NULL COMMENT '最大载重',
            `arm_length` double NULL DEFAULT NULL COMMENT '臂长',
            `height` double NOT NULL COMMENT '高度（米）',
            `weight` double NOT NULL COMMENT '重量（顿）',
            `wind_speed` double NOT NULL COMMENT '风速（m/s）',
            `lift_speed` double NULL DEFAULT NULL COMMENT '起重速度（m/s）',
            `derrick_speed` double NULL DEFAULT NULL COMMENT '变幅速度（m/s）',
            `dip_angle` double NOT NULL COMMENT '倾角（度）',
            `turn_angle` double NOT NULL COMMENT '转角（度）',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT
            '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '起重机硬件历史数据';


            CREATE TABLE `fse_device_lift` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `dept_id` int(11) NOT NULL COMMENT '组织机构ID',
            `sn` varchar(20) NOT NULL COMMENT '硬件唯一SN',
            `active_flag` int(11) NOT NULL DEFAULT 0 COMMENT '激活标记:0-未激活 1-已激活',
            `active_time` datetime(0) NULL DEFAULT NULL COMMENT '激活时间',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
            `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '升降机硬件信息';


            CREATE TABLE `fse_device_lift_data` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `device_id` int(11) NOT NULL COMMENT '升降机硬件id',
            `time` datetime(0) NOT NULL COMMENT '时间',
            `height` double NOT NULL COMMENT '高度（米）',
            `wind_speed` double NOT NULL COMMENT '风速（m/s）',
            `speed` double NOT NULL COMMENT '速度（m/s）',
            `floor` int(11) NOT NULL COMMENT '楼层',
            `weight` double NOT NULL COMMENT '载重（顿）',
            `person_no` int(11) NOT NULL COMMENT '人数',
            `dip_angle` double NOT NULL COMMENT '倾角（度）',
            `front_door_state` int(11) NOT NULL COMMENT '前门状态 （1-关闭 2-开启）',
            `back_door_state` int(11) NOT NULL COMMENT '后门状态 （1-关闭 2-开启）',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT
            '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`),
            UNIQUE INDEX `uk_deviceId`(`device_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '升降机硬件数据';


            CREATE TABLE `fse_device_lift_data_log` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `lift_id` int(11) NOT NULL COMMENT '升降机id',
            `device_id` int(11) NOT NULL COMMENT '升降机硬件id',
            `time` datetime(0) NOT NULL COMMENT '时间',
            `height` double NOT NULL COMMENT '高度（米）',
            `wind_speed` double NOT NULL COMMENT '风速（m/s）',
            `speed` double NOT NULL COMMENT '速度（m/s）',
            `floor` int(11) NOT NULL COMMENT '楼层',
            `weight` double NOT NULL COMMENT '载重（顿）',
            `person_no` int(11) NOT NULL COMMENT '人数',
            `dip_angle` double NOT NULL COMMENT '倾角（度）',
            `front_door_state` int(11) NOT NULL COMMENT '前门状态 （1-关闭 2-开启）',
            `back_door_state` int(11) NOT NULL COMMENT '后门状态 （1-关闭 2-开启）',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT
            '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`)
            )ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '升降机硬件历史数据';


            CREATE TABLE `fse_lift` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `dept_id` int(11) NOT NULL COMMENT '组织机构ID',
            `dept_name` varchar(25) NOT NULL COMMENT '组织机构名称',
            `code` varchar(25) NOT NULL COMMENT '升降机编号',
            `model` int(11) NOT NULL COMMENT '型号(字典)',
            `model_name` varchar(25) NOT NULL,
            `manufacturer` varchar(25) NOT NULL COMMENT '制造单位',
            `delivery_code` varchar(25) NOT NULL COMMENT '出厂编号',
            `delivery_date` date NOT NULL COMMENT '出厂日期',
            `property_unit` varchar(25) NOT NULL COMMENT '产权单位',
            `monitor_no` varchar(25) NOT NULL COMMENT '检测证号',
            `file_no` varchar(25) NOT NULL COMMENT '备案编号',
            `filing_date` date NOT NULL COMMENT '备案日期',
            `min_weight` double NOT NULL COMMENT '最小起重重量',
            `max_weight` double NOT NULL COMMENT '最大起重重量',
            `bind_flag` int(11) NOT NULL DEFAULT 0 COMMENT '绑定标记(0-未绑定 1-已绑定)',
            `sn` varchar(25) NULL DEFAULT NULL COMMENT '绑定的硬件sn码',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT
            '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '升降机';


            CREATE TABLE `fse_lift_data` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `lift_id` int(11) NOT NULL COMMENT '升降机id',
            `height` double NOT NULL COMMENT '高度（米）',
            `wind_speed` double NOT NULL COMMENT '风速（m/s）',
            `speed` double NOT NULL COMMENT '速度（m/s）',
            `floor` int(11) NOT NULL COMMENT '楼层',
            `weight` double NOT NULL COMMENT '载重（顿）',
            `person_no` int(11) NOT NULL COMMENT '人数',
            `dip_angle` double NOT NULL COMMENT '倾角（度）',
            `front_door_state` int(11) NOT NULL COMMENT '前门状态 （1-关闭 2-开启）',
            `back_door_state` int(11) NOT NULL COMMENT '后门状态 （1-关闭 2-开启）',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT
            '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`)
            )ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '升降机数据';


            CREATE TABLE `fse_lift_device` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `lift_id` int(11) NOT NULL COMMENT '升降机ID',
            `device_id` int(11) NOT NULL COMMENT '硬件id',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT
            '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '升降机绑定信息表';


            CREATE TABLE `fse_lift_device_log` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `lift_id` int(11) NOT NULL,
            `device_id` int(11) NOT NULL,
            `time` datetime(0) NOT NULL COMMENT '操作时间',
            `type` int(11) NOT NULL COMMENT '类型(1-绑定 2-解绑)',
            `user_id` int(11) NULL DEFAULT NULL COMMENT '操作人',
            `user_name` varchar(20) NULL DEFAULT NULL COMMENT '操作人姓名',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT
            '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '升降机和硬件绑定日志表';


            CREATE TABLE `fse_lift_operator` (
            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
            `lift_id` int(11) NOT NULL COMMENT '升降机id',
            `operator_name` varchar(50) NOT NULL COMMENT '操作手姓名',
            `operator_cert` varchar(50) NOT NULL COMMENT '操作手证书编号',
            `operator_id_card_no` varchar(20) NULL DEFAULT NULL COMMENT '操作手身份证号码',
            `phone` varchar(11) NOT NULL COMMENT '操作手电话',
            `del_flag` int(4) NOT NULL DEFAULT 0,
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
            PRIMARY KEY (`id`)
            )ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '升降机操作手信息';


            CREATE TABLE `fse_lift_record` (
            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
            `lift_id` int(11) NOT NULL COMMENT '升降机ID',
            `start_time` datetime(0) NOT NULL COMMENT '开始时间',
            `end_time` datetime(0) NULL DEFAULT NULL COMMENT '结束时间',
            `weight` double NOT NULL COMMENT '载重（吨）',
            `person_no` int(11) NOT NULL COMMENT '人数',
            `start_height` double NOT NULL COMMENT '开始高度 (米)',
            `end_height` double NULL DEFAULT NULL COMMENT '结束高度 (米)',
            `stroke_height` double NULL DEFAULT NULL COMMENT '行程高度 (米)',
            `avg_speed` double NOT NULL COMMENT '平均速度 (米/秒)',
            `direction` int(11) NOT NULL COMMENT '方向 1-下 2-上',
            `dip_angle_x` double NULL DEFAULT NULL COMMENT 'X倾向角度 (度)',
            `dip_angle_y` double NULL DEFAULT NULL COMMENT 'Y倾向角度 (度)',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '升降机运行记录';


            CREATE TABLE `fse_sync_device` (
            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
            `type` int(11) NOT NULL COMMENT '1-塔机 2-升降机',
            `device_id` int(11) NOT NULL COMMENT '需要同步的设备ID',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '特种设备同步表';


            CREATE TABLE `fse_warn` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `dept_id` int(11) NOT NULL COMMENT '项目ID',
            `warn_rule_id` int(11) NOT NULL COMMENT '规则ID',
            `rule_type` int(11) NULL DEFAULT NULL COMMENT '规则类型',
            `rule_max_value` varchar(32) NULL DEFAULT NULL COMMENT '报警阈值(最大)',
            `rule_min_value` varchar(32) NULL DEFAULT NULL COMMENT '报警阈值(最小)',
            `trigger_time` datetime(0) NOT NULL COMMENT '触发时间',
            `trigger_value` varchar(255) NULL DEFAULT NULL COMMENT '触发值',
            `trigger_object_id` varchar(32) NOT NULL COMMENT '触发业务对象ID(设备ID)',
            `state` int(11) NOT NULL DEFAULT 0 COMMENT '报警记录状态(0-未处理 1-已处理)',
            `handle_time` datetime(0) NULL DEFAULT NULL COMMENT '处理时间',
            `handle_result` varchar(100) NULL DEFAULT NULL COMMENT '处理结果',
            `handle_remark` varchar(100) NULL DEFAULT NULL COMMENT '处理结果备注',
            `handle_user_id` int(11) NULL DEFAULT NULL COMMENT '处理人',
            `handle_user_name` varchar(32) NULL DEFAULT NULL COMMENT '处理人姓名',
            `handle_user_phone` varchar(20) NULL DEFAULT NULL COMMENT '处理人手机号',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除,1-已删除)',
            `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
            `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '特种设备报警记录';


            CREATE TABLE `fse_warn_rule` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `dept_id` int(11) NOT NULL COMMENT '项目ID',
            `type` int(11) NOT NULL COMMENT '特种设备类型(1-塔吊 2-升降机)',
            `name` varchar(32) NOT NULL DEFAULT '' COMMENT '名称',
            `rule_type` int(11) NULL DEFAULT NULL COMMENT '报警规则类型',
            `enable_flag` int(11) NOT NULL DEFAULT 1 COMMENT '启用标记(0-未启用 1-已启用)',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
            `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '特种设备报警规则';


            CREATE TABLE `fse_warn_rule_channel` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `warn_rule_id` int(11) NOT NULL COMMENT '特种设备报警规则id（表fse_warn_rule中id）',
            `msg_channel` int(11) NOT NULL COMMENT '接收方式 1-小程序 2-公众号 3-后台 4-短信',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记 0-未删除 1-已删除',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '特种设备报警接收方式';


            CREATE TABLE `fse_warn_rule_object` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `warn_rule_id` int(11) NOT NULL COMMENT '报警规则id',
            `object_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '报警对象id',
            `type` int(11) NOT NULL COMMENT '特种设备类型(1-塔吊 2-升降机)',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记（0-否 1-是）',
            `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
            `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '特种设备报警规则和报警对象关联表';


            CREATE TABLE `fse_warn_rule_user` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `warn_rule_id` int(11) NOT NULL COMMENT '特种设备报警规则id（表fse_warn_rule中id）',
            `to_user_id` int(11) NULL DEFAULT NULL COMMENT '接收人(后台用户ID)',
            `to_user_name` varchar(32) NULL DEFAULT NULL COMMENT '接收人姓名',
            `to_user_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT
            '接收人手机号',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
            `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '特种设备报警-接收人';
        </sql>
    </changeSet>

    <changeSet id="2" author="xuguocheng">
        <comment>增加索引</comment>
        <sql>
            ALTER TABLE `fse_device_crane_data_log`
            ADD INDEX `idx_craneId_time` (`crane_id`, `time`) USING BTREE ;

            ALTER TABLE `fse_device_lift_data_log`
            ADD INDEX `idx_liftId_time` (`lift_id`, `time`) USING BTREE;
        </sql>
    </changeSet>

    <changeSet id="3" author="qinzexing">
        <comment>修改塔机表</comment>
        <sql>
            ALTER TABLE `fse_crane`
            DROP COLUMN `min_weight`,
            DROP COLUMN `max_weight`,
            MODIFY COLUMN `model` int(11) NULL COMMENT '型号(字典)' AFTER `code`,
            MODIFY COLUMN `model_name` varchar(25) NULL COMMENT '设备型号' AFTER `model`,
            MODIFY COLUMN `manufacturer` varchar(25) NULL COMMENT '制造单位' AFTER `model_name`,
            MODIFY COLUMN `delivery_code` varchar(25) NULL COMMENT '出厂编号' AFTER `manufacturer`,
            MODIFY COLUMN `delivery_date` date NULL COMMENT '出厂日期' AFTER `delivery_code`,
            MODIFY COLUMN `property_unit` varchar(25) NULL COMMENT '产权单位' AFTER `delivery_date`,
            MODIFY COLUMN `monitor_no` varchar(25) NULL COMMENT '检测证号' AFTER `property_unit`,
            CHANGE COLUMN `file_no` `filing_no` varchar(25) NULL COMMENT '备案编号' AFTER `monitor_no`,
            MODIFY COLUMN `filing_date` date NULL COMMENT '备案日期' AFTER `filing_no`,
            ADD COLUMN `net_state` int(11) NOT NULL DEFAULT 0 COMMENT '网络状态(0-离线 1-在线)' AFTER `bind_flag`;
        </sql>
    </changeSet>

    <changeSet id="4" author="qinzexing">
        <comment>修改塔机数据表</comment>
        <sql>
            ALTER TABLE `fse_crane_data`
            DROP COLUMN `arm_length`,
            DROP COLUMN `lift_speed`,
            DROP COLUMN `derrick_speed`,
            MODIFY COLUMN `fore_arm_length` double NULL COMMENT '起重臂长（米）' AFTER `crane_id`,
            MODIFY COLUMN `rear_arm_length` double NULL COMMENT '平衡臂长（米）' AFTER `fore_arm_length`,
            MODIFY COLUMN `max_weight` double NULL COMMENT '最大载重（吨）' AFTER `tower_arm_height`,
            ADD COLUMN `max_height` double NULL COMMENT '最大高度（米）' AFTER `max_weight`,
            ADD COLUMN `max_range` double NULL COMMENT '最大幅度（米）' AFTER `max_height`,
            ADD COLUMN `max_wind_speed` double NULL COMMENT '最大风速（m/s）' AFTER `max_range`,
            ADD COLUMN `max_dip_angle` double NULL COMMENT '最大倾角（度）' AFTER `max_wind_speed`,
            ADD COLUMN `max_turn_angle` double NULL COMMENT '最大转角（度）' AFTER `max_dip_angle`,
            ADD COLUMN `max_moment_ratio` double NULL COMMENT '最大力矩比（%）' AFTER `max_turn_angle`,
            MODIFY COLUMN `height` double NOT NULL DEFAULT 0 COMMENT '高度（米）' AFTER `max_moment_ratio`,
            MODIFY COLUMN `weight` double NOT NULL DEFAULT 0 COMMENT '重量（吨）' AFTER `height`,
            MODIFY COLUMN `range` double NOT NULL DEFAULT 0 COMMENT '幅度（米）' AFTER `weight`,
            MODIFY COLUMN `wind_speed` double NOT NULL DEFAULT 0 COMMENT '风速（m/s）' AFTER `range`,
            MODIFY COLUMN `dip_angle` double NOT NULL DEFAULT 0 COMMENT '倾角（度）' AFTER `wind_speed`,
            MODIFY COLUMN `turn_angle` double NOT NULL DEFAULT 0 COMMENT '转角（度）' AFTER `dip_angle`,
            MODIFY COLUMN `moment_ratio` double NOT NULL DEFAULT 0 COMMENT '力矩比（%）' AFTER `turn_angle`;
        </sql>
    </changeSet>

    <changeSet id="5" author="qinzexing">
        <comment>新建塔机视频监控绑定表</comment>
        <sql>
            CREATE TABLE `fse_crane_fvs_device` (
            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
            `crane_id` int(11) NOT NULL COMMENT '塔机ID',
            `crane_part` int(11) NOT NULL COMMENT '塔机部位',
            `fvs_device_id` int(11) NULL COMMENT '视频监控设备ID',
            `fvs_device_name` varchar(32) NULL COMMENT '视频监控设备名称',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除,1-已删除)',
            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '塔机视频监控绑定表';
        </sql>
    </changeSet>

    <changeSet id="6" author="qinzexing">
        <comment>修改升降机表</comment>
        <sql>
            ALTER TABLE `fse_lift`
            DROP COLUMN `min_weight`,
            DROP COLUMN `max_weight`,
            CHANGE COLUMN `file_no` `filing_no` varchar(25) NULL COMMENT '备案编号' AFTER `monitor_no`,
            CHANGE COLUMN `bind_flag` `left_bind_flag` int(11) NOT NULL DEFAULT 0 COMMENT '左轿厢绑定标记（0-未绑定 1-已绑定）' AFTER
            `filing_date`,
            CHANGE COLUMN `sn` `left_sn` varchar(32) NULL COMMENT '左轿厢绑定的硬件sn码' AFTER `left_bind_flag`,
            MODIFY COLUMN `dept_name` varchar(25) NULL COMMENT '组织机构名称' AFTER `dept_id`,
            MODIFY COLUMN `model` int(11) NULL COMMENT '型号(字典)' AFTER `code`,
            MODIFY COLUMN `model_name` varchar(25) NULL AFTER `model`,
            MODIFY COLUMN `manufacturer` varchar(25) NULL COMMENT '制造单位' AFTER `model_name`,
            MODIFY COLUMN `delivery_code` varchar(25) NULL COMMENT '出厂编号' AFTER `manufacturer`,
            MODIFY COLUMN `delivery_date` date NULL COMMENT '出厂日期' AFTER `delivery_code`,
            MODIFY COLUMN `property_unit` varchar(25) NULL COMMENT '产权单位' AFTER `delivery_date`,
            MODIFY COLUMN `monitor_no` varchar(25) NULL COMMENT '检测证号' AFTER `property_unit`,
            MODIFY COLUMN `filing_date` date NULL COMMENT '备案日期' AFTER `filing_no`,
            ADD COLUMN `left_net_state` int(11) NOT NULL DEFAULT 0 COMMENT '左轿厢网络状态（0-离线 1-在线）' AFTER `left_sn`,
            ADD COLUMN `right_bind_flag` int(11) NOT NULL DEFAULT 0 COMMENT '右轿厢绑定标记（0-未绑定 1-已绑定）' AFTER
            `left_net_state`,
            ADD COLUMN `right_sn` varchar(32) NULL COMMENT '左轿厢绑定的硬件sn码' AFTER `right_bind_flag`,
            ADD COLUMN `right_net_state` int(11) NOT NULL DEFAULT 0 COMMENT '右轿厢网络状态（0-离线 1-在线）' AFTER `right_sn`,
            MODIFY COLUMN `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记（0-未删除 1-已删除）' AFTER `right_net_state`;
        </sql>
    </changeSet>

    <changeSet id="7" author="qinzexing">
        <comment>升降机数据表修改</comment>
        <sql>
            ALTER TABLE `fse_lift_data`
            ADD COLUMN `lift_side` int(11) NOT NULL DEFAULT 1 COMMENT '升降机轿厢方位（1-左 2-右）' AFTER `lift_id`,
            ADD COLUMN `max_height` double NULL COMMENT '最大高度（米）' AFTER `lift_side`,
            ADD COLUMN `max_weight` double NULL COMMENT '最大载重（吨）' AFTER `max_height`,
            ADD COLUMN `max_person_no` int(11) NULL COMMENT '最大人数（人）' AFTER `max_weight`,
            ADD COLUMN `max_dip_angle` double NULL COMMENT '最大倾角（度）' AFTER `max_person_no`,
            ADD COLUMN `max_speed` double NULL COMMENT '最大速度（m/s）' AFTER `max_dip_angle`,
            MODIFY COLUMN `weight` double NOT NULL COMMENT '载重（吨）' AFTER `height`,
            MODIFY COLUMN `person_no` int(11) NOT NULL COMMENT '人数' AFTER `weight`,
            MODIFY COLUMN `floor` int(11) NOT NULL COMMENT '楼层' AFTER `dip_angle`;
        </sql>
    </changeSet>

    <changeSet id="8" author="qinzexing">
        <comment>升降机表增加左右轿厢字段</comment>
        <sql>
            ALTER TABLE `fse_lift_record`
            ADD COLUMN `lift_side` int(11) NOT NULL DEFAULT 1 COMMENT '升降机轿厢方位（1-左 2-右）' AFTER `lift_id`;

            ALTER TABLE `fse_device_lift_data`
            ADD COLUMN `lift_side` int(11) NOT NULL DEFAULT 1 COMMENT '升降机轿厢方位（1-左 2-右）' AFTER `id`;

            ALTER TABLE `fse_device_lift_data_log`
            ADD COLUMN `lift_side` int(11) NOT NULL DEFAULT 1 COMMENT '升降机轿厢方位（1-左 2-右）' AFTER `lift_id`;

            ALTER TABLE `fse_lift_device`
            ADD COLUMN `lift_side` int(11) NOT NULL DEFAULT 1 COMMENT '升降机轿厢方位（1-左 2-右）' AFTER `lift_id`;
        </sql>
    </changeSet>

    <changeSet id="9" author="qinzexing">
        <comment>新建升降机视频监控绑定表</comment>
        <sql>
            CREATE TABLE `fse_lift_fvs_device` (
            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
            `lift_id` int(11) NOT NULL COMMENT '升降机ID',
            `lift_side` int(11) NOT NULL COMMENT '升降机轿厢方位（1-左 2-右）',
            `fvs_device_id` int(11) NULL COMMENT '视频监控设备ID',
            `fvs_device_name` varchar(32) NULL COMMENT '视频监控设备名称',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除,1-已删除)',
            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '升降机视频监控绑定表';
        </sql>
    </changeSet>

    <changeSet id="10" author="sunguodong">
        <comment>新增表</comment>
        <sql>
            CREATE TABLE `fse_gantry` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `dept_id` int(11) NOT NULL COMMENT '组织机构ID',
            `dept_name` varchar(25) NOT NULL COMMENT '组织机构名称',
            `code` varchar(25) NOT NULL COMMENT '龙门吊编号',
            `model_name` varchar(25) NULL DEFAULT NULL COMMENT '品牌型号',
            `manufacturer` varchar(25) NULL DEFAULT NULL COMMENT '制造单位',
            `delivery_code` varchar(25) NULL DEFAULT NULL COMMENT '出厂编号',
            `delivery_date` date NULL DEFAULT NULL COMMENT '出厂日期',
            `property_unit` varchar(25) NULL DEFAULT NULL COMMENT '产权单位',
            `monitor_no` varchar(25) NULL DEFAULT NULL COMMENT '监测证号',
            `filing_no` varchar(25) NULL DEFAULT NULL COMMENT '备案编号',
            `filing_date` date NULL DEFAULT NULL COMMENT '备案日期',
            `bind_flag` int(11) NOT NULL DEFAULT 0 COMMENT '绑定标记(0-未绑定 1-已绑定)',
            `device_id` int(11) NULL DEFAULT NULL COMMENT '绑定的硬件id',
            `sn` varchar(25) NULL DEFAULT NULL COMMENT '绑定的硬件sn码',
            `net_state` int(11) NOT NULL DEFAULT 0 COMMENT '网络状态(0-离线 1-在线)',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '龙门吊';


            CREATE TABLE `fse_gantry_device` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `gantry_id` int(11) NOT NULL COMMENT '龙门吊ID',
            `device_id` int(11) NOT NULL COMMENT '硬件id',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '龙门吊绑定信息表';


            CREATE TABLE `fse_gantry_device_log` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `gantry_id` int(11) NOT NULL,
            `device_id` int(11) NOT NULL,
            `time` datetime NOT NULL COMMENT '操作时间',
            `type` int(11) NOT NULL COMMENT '类型(1-绑定 2-解绑)',
            `user_id` int(11) NULL DEFAULT NULL COMMENT '操作人',
            `user_name` varchar(20) NULL DEFAULT NULL COMMENT '操作人姓名',
            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`)
            )ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '龙门吊和硬件绑定日志表';


            CREATE TABLE `fse_gantry_fvs_device` (
            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
            `gantry_id` int(11) NOT NULL COMMENT '龙门吊ID',
            `gantry_part` int(11) NOT NULL COMMENT '龙门吊部位',
            `fvs_device_id` int(11) NULL DEFAULT NULL COMMENT '视频监控设备ID',
            `fvs_device_name` varchar(32) NULL DEFAULT NULL COMMENT '视频监控设备名称',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除,1-已删除)',
            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '龙门吊视频监控绑定表';


            CREATE TABLE `fse_gantry_operator` (
            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
            `gantry_id` int(11) NOT NULL COMMENT '龙门吊id',
            `operator_name` varchar(50) NOT NULL COMMENT '操作手姓名',
            `operator_cert` varchar(50) NOT NULL COMMENT '操作手证书编号',
            `phone` varchar(11) NOT NULL COMMENT '操作手电话',
            `del_flag` int(4) NOT NULL DEFAULT 0,
            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '龙门吊操作手信息';


            CREATE TABLE `fse_device_gantry` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `dept_id` int(11) NOT NULL COMMENT '组织机构ID',
            `sn` varchar(20) NOT NULL COMMENT '硬件唯一SN',
            `active_flag` int(11) NOT NULL DEFAULT 0 COMMENT '激活标记:0-未激活 1-已激活',
            `active_time` datetime NULL DEFAULT NULL COMMENT '激活时间',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
            `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '龙门吊硬件信息';


            CREATE TABLE `fse_device_gantry_data` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `device_id` int(11) NOT NULL COMMENT '龙门吊硬件id',
            `gantry_id` int(11) NULL DEFAULT NULL COMMENT '龙门吊id',
            `time` datetime NOT NULL COMMENT '时间',
            `rigid_leg_shift` double NULL DEFAULT NULL COMMENT '刚腿位移（米）',
            `soft_leg_shift` double NULL DEFAULT NULL COMMENT '柔腿位移（米）',
            `shift` double NULL DEFAULT NULL COMMENT '位移（米）',
            `wind_speed` double NULL DEFAULT NULL COMMENT '风速（m/s）',
            `running_turn` double NULL DEFAULT NULL COMMENT '运行偏斜（米）',
            `main_hook_lift_height` double NULL DEFAULT NULL COMMENT '主钩起升高度（米）',
            `sub_hook_lift_height` double NULL DEFAULT NULL COMMENT '副钩起升高度（米）',
            `main_hook_lift_weight` double NULL DEFAULT NULL COMMENT '主钩起升重量（吨）',
            `sub_hook_lift_weight` double NULL DEFAULT NULL COMMENT '副钩起升重量（吨）',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) ,
            UNIQUE INDEX `uk_deviceId`(`device_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '龙门吊硬件数据';


            CREATE TABLE `fse_bridge` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `dept_id` int(11) NOT NULL COMMENT '组织机构ID',
            `dept_name` varchar(25) NOT NULL COMMENT '组织机构名称',
            `code` varchar(25) NOT NULL COMMENT '架桥机编号',
            `model_name` varchar(25) NULL DEFAULT NULL COMMENT '品牌型号',
            `manufacturer` varchar(25) NULL DEFAULT NULL COMMENT '制造单位',
            `delivery_code` varchar(25) NULL DEFAULT NULL COMMENT '出厂编号',
            `delivery_date` date NULL DEFAULT NULL COMMENT '出厂日期',
            `property_unit` varchar(25) NULL DEFAULT NULL COMMENT '产权单位',
            `monitor_no` varchar(25) NULL DEFAULT NULL COMMENT '监测证号',
            `filing_no` varchar(25) NULL DEFAULT NULL COMMENT '备案编号',
            `filing_date` date NULL DEFAULT NULL COMMENT '备案日期',
            `bind_flag` int(11) NOT NULL DEFAULT 0 COMMENT '绑定标记(0-未绑定 1-已绑定)',
            `sn` varchar(25) NULL DEFAULT NULL COMMENT '绑定的硬件sn码',
            `device_id` int(11) NULL DEFAULT NULL COMMENT '绑定的硬件id',
            `net_state` int(11) NOT NULL DEFAULT 0 COMMENT '网络状态(0-离线 1-在线)',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '架桥机';


            CREATE TABLE `fse_bridge_device` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `bridge_id` int(11) NOT NULL COMMENT '架桥机ID',
            `device_id` int(11) NOT NULL COMMENT '硬件id',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '架桥机绑定信息表';


            CREATE TABLE `fse_bridge_device_log` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `bridge_id` int(11) NOT NULL,
            `device_id` int(11) NOT NULL,
            `time` datetime NOT NULL COMMENT '操作时间',
            `type` int(11) NOT NULL COMMENT '类型(1-绑定 2-解绑)',
            `user_id` int(11) NULL DEFAULT NULL COMMENT '操作人',
            `user_name` varchar(20) NULL DEFAULT NULL COMMENT '操作人姓名',
            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`)
            )ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '架桥机和硬件绑定日志表';


            CREATE TABLE `fse_bridge_fvs_device` (
            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
            `bridge_id` int(11) NOT NULL COMMENT '架桥机ID',
            `bridge_part` int(11) NOT NULL COMMENT '架桥机部位',
            `fvs_device_id` int(11) NULL DEFAULT NULL COMMENT '视频监控设备ID',
            `fvs_device_name` varchar(32) NULL DEFAULT NULL COMMENT '视频监控设备名称',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除,1-已删除)',
            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '架桥机视频监控绑定表';


            CREATE TABLE `fse_bridge_operator` (
            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
            `bridge_id` int(11) NOT NULL COMMENT '架桥机id',
            `operator_name` varchar(50) NOT NULL COMMENT '操作手姓名',
            `operator_cert` varchar(50) NOT NULL COMMENT '操作手证书编号',
            `phone` varchar(11) NOT NULL COMMENT '操作手电话',
            `del_flag` int(4) NOT NULL DEFAULT 0,
            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '架桥机操作手信息';


            CREATE TABLE `fse_device_bridge` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `dept_id` int(11) NOT NULL COMMENT '组织机构ID',
            `sn` varchar(20) NOT NULL COMMENT '硬件唯一SN',
            `active_flag` int(11) NOT NULL DEFAULT 0 COMMENT '激活标记:0-未激活 1-已激活',
            `active_time` datetime NULL DEFAULT NULL COMMENT '激活时间',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
            `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '架桥机硬件信息';


            CREATE TABLE `fse_device_bridge_data` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `device_id` int(11) NOT NULL COMMENT '架桥机硬件id',
            `bridge_id` int(11) NULL DEFAULT NULL COMMENT '架桥机id',
            `time` datetime NOT NULL COMMENT '时间',
            `front_lift_height` double NULL DEFAULT NULL COMMENT '前天车起升高度（米）',
            `front_lift_weight` double NULL DEFAULT NULL COMMENT '前天车起升重量（吨）',
            `front_lng_position` double NULL DEFAULT NULL COMMENT '前天车纵移位置（米）',
            `front_lat_position` double NULL DEFAULT NULL COMMENT '前天车横移位置（米）',
            `back_lift_height` double NULL DEFAULT NULL COMMENT '后天车起升高度（米）',
            `back_lift_weight` double NULL DEFAULT NULL COMMENT '后天车起升重量（吨）',
            `back_lng_position` double NULL DEFAULT NULL COMMENT '后天车纵移位置（米）',
            `back_lat_position` double NULL DEFAULT NULL COMMENT '后天车横移位置（米）',
            `lng_position` double NULL DEFAULT NULL COMMENT '纵移位置（米）',
            `lat_position` double NULL DEFAULT NULL COMMENT '横移位置（米）',
            `wind_speed` double NULL DEFAULT NULL COMMENT '风速（m/s）',
            `level_angle` double NULL DEFAULT NULL COMMENT '主梁水平度（度）',
            `vertical_angle` double NULL DEFAULT NULL COMMENT '前支垂直度（度）',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) ,
            UNIQUE INDEX `uk_deviceId`(`device_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '架桥机硬件数据';


        </sql>
    </changeSet>

    <changeSet id="11" author="sunguodong">
        <comment>修改表</comment>
        <sql>
            ALTER TABLE `fse_bridge_operator`
            CHANGE COLUMN `operator_name` `name` varchar(16) NOT NULL COMMENT '姓名',
            CHANGE COLUMN `operator_cert` `cert` varchar(16) NOT NULL COMMENT '证书编号';

            ALTER TABLE `fse_gantry_operator`
            CHANGE COLUMN `operator_name` `name` varchar(16) NOT NULL COMMENT '姓名',
            CHANGE COLUMN `operator_cert` `cert` varchar(16) NOT NULL COMMENT '证书编号';

            ALTER TABLE `fse_gantry`
            ADD COLUMN `platform` int(11)  NULL DEFAULT NULL COMMENT '设备所在的厂家平台' AFTER `id`;

            ALTER TABLE `fse_bridge`
            ADD COLUMN `platform` int(11)  NULL DEFAULT NULL COMMENT '设备所在的厂家平台' AFTER `id`;
        </sql>
    </changeSet>

    <changeSet id="12" author="hw">
        <comment>修改表</comment>
        <sql>
            ALTER TABLE `fse_device_gantry_data`
            ADD COLUMN `span` double NULL COMMENT '跨度' AFTER `time`,
            ADD COLUMN `height` double NULL COMMENT '高度' AFTER `span`,
            ADD COLUMN `main_hook_warn` int NULL COMMENT '主起升超载状态 0-正常，1-预警' AFTER `sub_hook_lift_weight`,
            ADD COLUMN `sub_hook_warn` int NULL COMMENT '副起升超载状态 0-正常，1-预警' AFTER `main_hook_warn`,
            ADD COLUMN `main_hook_warn_num` int NULL COMMENT '主起升超载次数' AFTER `sub_hook_warn`,
            ADD COLUMN `sub_hook_warn_num` int NULL COMMENT '副起升超载次数 ' AFTER `main_hook_warn_num`,
            ADD COLUMN `wind_warn_num` int NULL COMMENT '风速报警次数 ' AFTER `sub_hook_warn_num`;

            ALTER TABLE `fse_device_bridge_data`
            ADD COLUMN `span` double NULL COMMENT '跨度' AFTER `time`,
            ADD COLUMN `height` double NULL COMMENT '高度' AFTER `span`,
            ADD COLUMN `front_lift_warn` int NULL COMMENT '前天车超载状态 0-正常；1-预警' AFTER `vertical_angle`,
            ADD COLUMN `back_lift_warn` int NULL COMMENT '后天车超载状态 0-正常；1-预警' AFTER `front_lift_warn`,
            ADD COLUMN `front_lift_warn_num` int NULL COMMENT '前天车超载次数' AFTER `back_lift_warn`,
            ADD COLUMN `back_lift_warn_num` int NULL COMMENT '后天车超载次数' AFTER `front_lift_warn_num`,
            ADD COLUMN `wind_warn_num` int NULL COMMENT '风速报警次数 ' AFTER `back_lift_warn_num`
        </sql>
    </changeSet>

    <changeSet id="13" author="hw">
        <comment>修改表长度</comment>
        <sql>
            ALTER TABLE `fse_bridge_operator`
            MODIFY COLUMN `cert` varchar(32) NOT NULL COMMENT '证书编号' AFTER `name`;

            ALTER TABLE `fse_gantry_operator`
            MODIFY COLUMN `cert` varchar(32) NOT NULL COMMENT '证书编号' AFTER `name`
        </sql>
    </changeSet>

    <changeSet id="14" author="xuguocheng">
        <comment>修改默认值</comment>
        <sql>
            ALTER TABLE `fse_device_crane_data`
            MODIFY COLUMN `range`  double NULL COMMENT '幅度（米）' AFTER `tower_arm_height`,
            MODIFY COLUMN `moment_ratio`  double NULL COMMENT '力矩比' AFTER `range`,
            MODIFY COLUMN `height`  double NULL COMMENT '高度（米）' AFTER `arm_length`,
            MODIFY COLUMN `weight`  double NULL COMMENT '重量（顿）' AFTER `height`,
            MODIFY COLUMN `wind_speed`  double NULL COMMENT '风速（m/s）' AFTER `weight`;
        </sql>
    </changeSet>

    <changeSet id="15" author="hw">
        <comment>修改默认值</comment>
        <sql>
            ALTER TABLE `fse_device_gantry_data`
            add COLUMN `main_hook_time_warn_num`  int NULL COMMENT '主起当前升超载次数' AFTER `main_hook_warn_num`,
            add COLUMN `sub_hook_time_warn_num`  int NULL COMMENT '副起当前升超载次数 ' AFTER `sub_hook_warn_num`,
            add COLUMN `wind_time_warn_num`  int NULL COMMENT '风速当前报警次数 ' AFTER `wind_warn_num`;

            ALTER TABLE `fse_device_bridge_data`
            add COLUMN `front_lift_time_warn_num`  int NULL COMMENT '前天车当前超载次数' AFTER `front_lift_warn_num`,
            add COLUMN `back_lift_time_warn_num`  int NULL COMMENT '后天车当前超载次数 ' AFTER `back_lift_warn_num`,
            add COLUMN `wind_time_warn_num`  int NULL COMMENT '风速当前报警次数 ' AFTER `wind_warn_num`;
        </sql>
    </changeSet>

    <changeSet id="16" author="hw">
        <comment>新增表</comment>
        <sql>
            CREATE TABLE fse_resume  (
            `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
            `dept_id` int NULL DEFAULT NULL COMMENT '特种设备id',
            `fse_id` int NULL DEFAULT NULL COMMENT '特种设备id',
            `fse_type` int NULL DEFAULT NULL COMMENT '特种设备类型',
            `resume_type` int NULL DEFAULT NULL COMMENT '履历种类（1-安装信息，2-使用信息，3-维护信息，4-拆卸信息）',
            `name` varchar(32)  NULL DEFAULT NULL COMMENT '名称',
            `part` varchar(32)  NULL DEFAULT NULL COMMENT '部位',
            `start_time` datetime NULL DEFAULT NULL COMMENT '开始时间',
            `end_time` datetime NULL DEFAULT NULL COMMENT '结束时间',
            `duration` int NULL DEFAULT NULL COMMENT '时长',
            `state` int NULL DEFAULT NULL COMMENT '状态',
            `op_user` varchar(32) NULL DEFAULT NULL COMMENT '操作人',
            `remark` varchar(300) NULL DEFAULT NULL COMMENT '说明',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE
            ) COMMENT = '特种设备履历';
            CREATE TABLE fse_resume_attach  (
            `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
            `resume_id` int NULL DEFAULT NULL COMMENT '履历id',
            `url` varchar(256) NULL DEFAULT NULL COMMENT '文件地址',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE
            ) COMMENT = '特种设备履历附件';
            ALTER TABLE `fse_lift`
            add COLUMN `install_unit` varchar(64) NULL COMMENT '安装单位' AFTER `delivery_date`;
            ALTER TABLE `fse_bridge`
            add COLUMN `install_unit` varchar(64) NULL COMMENT '安装单位' AFTER `delivery_date`;
            ALTER TABLE `fse_crane`
            add COLUMN `install_unit` varchar(64) NULL COMMENT '安装单位' AFTER `delivery_date`;
            ALTER TABLE `fse_gantry`
            add COLUMN `install_unit` varchar(64) NULL COMMENT '安装单位' AFTER `delivery_date`;

        </sql>
    </changeSet>

    <changeSet id="17" author="qinzexing">
        <comment>新建表</comment>
        <sql>
            CREATE TABLE `fse_crawler`  (
            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
            `dept_id` int(11) NOT NULL COMMENT '组织机构ID',
            `dept_name` varchar(25) NOT NULL COMMENT '组织机构名称',
            `platform` int(11) NULL COMMENT '设备厂家平台',
            `code` varchar(25) NOT NULL COMMENT '履带吊编号',
            `model_name` varchar(25) NULL COMMENT '品牌型号',
            `manufacturer` varchar(25) NULL COMMENT '制造单位',
            `delivery_code` varchar(25) NULL COMMENT '出厂编号',
            `delivery_date` date NULL COMMENT '出厂日期',
            `install_unit` varchar(64) NULL COMMENT '安装单位',
            `property_unit` varchar(25) NULL COMMENT '产权单位',
            `monitor_no` varchar(25) NULL COMMENT '监测证号',
            `filing_no` varchar(25) NULL COMMENT '备案编号',
            `filing_date` date NULL COMMENT '备案日期',
            `operators` text NULL COMMENT '操作手信息（json数据）',
            `fvs_devices` text NULL COMMENT '设备监控信息（json数据）',
            `net_state` int(11) NOT NULL DEFAULT 0 COMMENT '网络状态(0-离线 1-在线)',
            `bind_flag` int(11) NOT NULL DEFAULT 0 COMMENT '绑定标记(0-未绑定 1-已绑定)',
            `device_id` int(11) NULL COMMENT '绑定的硬件id',
            `sn` varchar(25) NULL COMMENT '绑定的硬件sn码',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '履带吊设备信息';


            CREATE TABLE `fse_device_crawler`  (
            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
            `sn` varchar(20) NOT NULL COMMENT '硬件唯一SN',
            `active_flag` int(11) NOT NULL DEFAULT 0 COMMENT '激活标记:0-未激活 1-已激活',
            `active_time` datetime NULL DEFAULT NULL COMMENT '激活时间',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '履带吊硬件信息';


            CREATE TABLE `fse_device_crawler_data`  (
            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
            `device_id` int(11) NOT NULL COMMENT '履带吊硬件ID',
            `crawler_id` int(11) NULL COMMENT '履带吊ID',
            `time` datetime NULL COMMENT '时间',
            `main_hook_weight` double NULL COMMENT '主钩吊重',
            `main_hook_height` double NULL COMMENT '主钩高度',
            `main_hook_moment` double NULL COMMENT '主钩力矩',
            `vice_hook_weight` double NULL COMMENT '副钩吊重',
            `vice_hook_height` double NULL COMMENT '副钩高度',
            `vice_hook_moment` double NULL COMMENT '副钩力矩',
            `range` double NULL COMMENT '幅度',
            `wind_speed` double NULL COMMENT '风速',
            `turn_speed` double NULL COMMENT '回转速度',
            `turn_angle` double NULL COMMENT '回转角度',
            `dip_angle` double NULL COMMENT '俯仰角',
            `levelness` double NULL COMMENT '水平度',
            `conditions` int(11) NULL COMMENT '工况设置',
            `total_overload_cnt` int(11) NULL COMMENT '累计超载次数',
            `total_wind_work_cnt` int(11) NULL COMMENT '累计抗风作业次数',
            `total_work_times` int(11) NULL COMMENT '累计工作时长',
            `total_work_cnt` int(11) NULL COMMENT '累计吊装次数',
            `work_start_time` datetime NULL COMMENT '工作循环开始',
            `work_end_time` datetime NULL COMMENT '工作循环结束',
            `total_main_cnt` int(11) NULL COMMENT '主升动作累计次数',
            `total_vice_cnt` int(11) NULL COMMENT '副升动作累计次数',
            `total_turn_cnt` int(11) NULL COMMENT '累计回转次数',
            `state` int(11) NULL COMMENT '状态',
            `action` int(11) NULL COMMENT '动作',
            `trip` int(11) NULL COMMENT '行程',
            `turn_warn` int(11) NULL COMMENT '回转报警',
            `main_hook_height_warn` int(11) NULL COMMENT '主钩高度报警',
            `main_hook_weight_warn` int(11) NULL COMMENT '主钩重量报警',
            `vice_hook_height_warn` int(11) NULL COMMENT '副钩高度报警',
            `vice_hook_weight_warn` int(11) NULL COMMENT '副钩重量报警',
            `dip_angle_warn` int(11) NULL COMMENT '防后倾报警',
            `wind_speed_warn` int(11) NULL COMMENT '风速报警',
            `abnormal_op_warn` int(11) NULL COMMENT '不正常操作报警',
            `turn_left`  int(11) NULL COMMENT '回转左转',
            `turn_right`  int(11) NULL COMMENT '回转左转',
            `main_hook_up`  int(11) NULL COMMENT '主钩上升',
            `main_ook_own`  int(11) NULL COMMENT '主钩下降',
            `vice_hook_up`  int(11) NULL COMMENT '副钩上升',
            `vice_hook_down`  int(11) NULL COMMENT '副钩下降',
            `main_hook_brake`  int(11) NULL COMMENT '主钩抱闸状态',
            `vice_hook_brake`  int(11) NULL COMMENT '副钩抱闸状态',
            `dip_angle_limit`  int(11) NULL COMMENT '防后倾限位',
            `door_limit`  int(11) NULL COMMENT '门限位',
            `main_hook_height_upper_limit`  int(11) NULL COMMENT '主钩高度上限位',
            `main_hook_height_lower_limit`  int(11) NULL COMMENT '主钩高度下限位',
            `vice_hook_height_upper_limit`  int(11) NULL COMMENT '副钩高度上限位',
            `vice_hook_height_lower_limit`  int(11) NULL COMMENT '副钩高度下限位',
            `min_height` double NULL COMMENT '高度下限',
            `max_height` double NULL COMMENT '高度上限',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '履带吊硬件数据';

        </sql>
    </changeSet>

    <changeSet id="18" author="qinzexing">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `fse_device_crawler_data`
            ADD COLUMN `probe_distances` text NULL COMMENT '防撞探头感应距离列表（json）' AFTER `max_height`,
            ADD COLUMN `probe_distance_warns` varchar(255) NULL COMMENT '防撞探测报警（json）' AFTER `probe_distances`;

            ALTER TABLE `fse_device_crawler`
            ADD COLUMN `collision_flag` int(11) NOT NULL DEFAULT 0 COMMENT '是否防碰撞（0-非防碰撞硬件 1-防碰撞硬件）' AFTER `active_time`;
        </sql>
    </changeSet>

    <changeSet id="19" author="qinzexing">
        <comment>修改表</comment>
        <sql>
            ALTER TABLE `fse_gantry`
            MODIFY COLUMN `platform` varchar(32) NULL DEFAULT NULL COMMENT '设备所在的厂家平台' AFTER `id`,
            ADD COLUMN `operators` text NULL COMMENT '操作手信息（json数据）' AFTER `net_state`,
            ADD COLUMN `fvs_devices` text NULL COMMENT '设备监控信息（json数据）' AFTER `operators`;
        </sql>
    </changeSet>

    <changeSet id="20" author="qinzexing">
        <comment>修改表</comment>
        <sql>
            ALTER TABLE `fse_crawler`
                MODIFY COLUMN `platform` varchar(32) NULL DEFAULT NULL COMMENT '设备厂家平台' AFTER `dept_name`,
                ADD COLUMN `collision_flag` int(11) NOT NULL DEFAULT 0 COMMENT '是否防碰撞(0-否  1-是)' AFTER `sn`;

            ALTER TABLE `fse_bridge`
                MODIFY COLUMN `platform` varchar(32) NULL DEFAULT NULL COMMENT '设备所在的厂家平台' AFTER `id`,
                ADD COLUMN `operators` text NULL COMMENT '操作手信息（json数据）' AFTER `net_state`,
                ADD COLUMN `fvs_devices` text NULL COMMENT '设备监控信息（json数据）' AFTER `operators`;
        </sql>
    </changeSet>

    <changeSet id="21" author="xuguocheng">
        <comment>新增表</comment>
        <sql>
            CREATE TABLE `fse_crane_tool` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `dept_id` int(11) NOT NULL COMMENT '项目ID',
            `hook` varchar(64) DEFAULT NULL COMMENT '吊钩',
            `wire` varchar(64) DEFAULT NULL COMMENT '钢丝绳',
            `bolt` varchar(64) DEFAULT NULL COMMENT '插销保险',
            `other` varchar(64) DEFAULT NULL COMMENT '其他',
            `del_flag` varchar(255) NOT NULL DEFAULT '0' COMMENT '删除标记:0-未删除 1-已删除',
            `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='吊索具';
        </sql>
    </changeSet>

    <changeSet id="22" author="xuguocheng">
        <comment>新增索引</comment>
        <sql>
            ALTER TABLE `fse_warn`
            ADD COLUMN `type`  int NULL COMMENT '特种设备类型' AFTER `warn_rule_id`,
            ADD INDEX `idx_deptId_time` (`dept_id`, `trigger_time`) USING BTREE ;
        </sql>
    </changeSet>

    <changeSet id="23" author="qinzexing">
        <comment>删除无用表</comment>
        <sql>
            DROP TABLE fse_bridge_device;
            DROP TABLE fse_bridge_device_log;
            DROP TABLE fse_bridge_fvs_device;
            DROP TABLE fse_bridge_operator;
            DROP TABLE fse_device_bridge;
            DROP TABLE fse_device_crawler;
            DROP TABLE fse_device_gantry;
            DROP TABLE fse_gantry_device;
            DROP TABLE fse_gantry_device_log;
            DROP TABLE fse_gantry_fvs_device;
            DROP TABLE fse_gantry_operator;
        </sql>
    </changeSet>

    <changeSet id="24" author="qinzexing">
        <comment>修改表</comment>
        <sql>
            ALTER TABLE `fse_crane`
                ADD COLUMN `platform` varchar(32) NULL COMMENT '设备所在的厂家平台' AFTER `id`,
                ADD COLUMN `operators` text NULL COMMENT '操作手信息（json数据）' AFTER `sn`,
                ADD COLUMN `fvs_devices` text NULL COMMENT '设备监控信息（json数据）' AFTER `operators`;

            ALTER TABLE `fse_crane_data`
                ADD COLUMN `time` datetime NULL COMMENT '数据时间' AFTER `crane_id`;
        </sql>
    </changeSet>

    <changeSet id="25" author="qinzexing">
        <comment>修改表</comment>
        <sql>
            ALTER TABLE `fse_lift`
                ADD COLUMN `platform` varchar(32) NULL COMMENT '设备所在的厂家平台' AFTER `id`,
                ADD COLUMN `operators` text NULL COMMENT '操作手信息（json数据）' AFTER `right_net_state`,
                ADD COLUMN `fvs_devices` text NULL COMMENT '设备监控信息（json数据）' AFTER `operators`;

            ALTER TABLE `fse_lift_data`
                ADD COLUMN `time` datetime NULL COMMENT '数据时间' AFTER `lift_side`;
        </sql>
    </changeSet>

    <changeSet id="26" author="qinzexing">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `fse_crane_record`
                ADD COLUMN `time` datetime NULL COMMENT '数据上报时间' AFTER `crane_id`;
        </sql>
    </changeSet>

    <changeSet id="27" author="qinzexing">
        <comment>清理表</comment>
        <sql>
            DROP TABLE fse_crane_device;
            DROP TABLE fse_crane_device_log;
            DROP TABLE fse_crane_fvs_device;
            DROP TABLE fse_crane_operator;
            DROP TABLE fse_device_crane;
            DROP TABLE fse_device_crane_data;
            DROP TABLE fse_device_crane_data_log;
            DROP TABLE fse_device_lift;
            DROP TABLE fse_device_lift_data;
            DROP TABLE fse_device_lift_data_log;
            DROP TABLE fse_lift_device;
            DROP TABLE fse_lift_device_log;
            DROP TABLE fse_lift_fvs_device;
            DROP TABLE fse_lift_operator;
            DROP TABLE fse_sync_device;


            ALTER TABLE `fse_bridge`
            DROP COLUMN `device_id`;

            ALTER TABLE `fse_device_bridge_data`
            MODIFY COLUMN `bridge_id` int(11) NOT NULL COMMENT '架桥机id' AFTER `id`,
            MODIFY COLUMN `time` datetime NULL COMMENT '时间' AFTER `bridge_id`,
            DROP COLUMN `device_id`;

            ALTER TABLE `fse_crawler`
            DROP COLUMN `device_id`;

            ALTER TABLE `fse_device_crawler_data`
            MODIFY COLUMN `crawler_id` int(11) NOT NULL COMMENT '履带吊ID' AFTER `id`,
            DROP COLUMN `device_id`;

            ALTER TABLE `fse_gantry`
            DROP COLUMN `device_id`;

            ALTER TABLE `fse_device_gantry_data`
            MODIFY COLUMN `gantry_id` int(11) NOT NULL COMMENT '龙门吊id' AFTER `id`,
            MODIFY COLUMN `time` datetime NULL COMMENT '时间' AFTER `gantry_id`,
            DROP COLUMN `device_id`;

            ALTER TABLE fse_device_bridge_data RENAME TO fse_bridge_data;
            ALTER TABLE fse_device_crawler_data RENAME TO fse_crawler_data;
            ALTER TABLE fse_device_gantry_data RENAME TO fse_gantry_data;
        </sql>
    </changeSet>

    <changeSet id="28" author="qinzexing">
        <comment>调整表</comment>
        <sql>
            ALTER TABLE `fse_crane_record`
                MODIFY COLUMN `start_time` datetime NULL COMMENT '起吊时间' AFTER `time`,
                MODIFY COLUMN `start_height` double NULL COMMENT '开始高度（米）' AFTER `end_time`,
                MODIFY COLUMN `start_range` double NULL COMMENT '开始幅度（米）' AFTER `end_angle`;

            ALTER TABLE `fse_lift_record`
                MODIFY COLUMN `start_time` datetime NULL COMMENT '开始时间' AFTER `lift_side`,
                MODIFY COLUMN `weight` double NULL COMMENT '载重（吨）' AFTER `end_time`,
                MODIFY COLUMN `person_no` int(11) NULL COMMENT '人数' AFTER `weight`,
                MODIFY COLUMN `start_height` double NULL COMMENT '开始高度 (米)' AFTER `person_no`,
                MODIFY COLUMN `avg_speed` double NULL COMMENT '平均速度 (米/秒)' AFTER `stroke_height`,
                MODIFY COLUMN `direction` int(11) NULL COMMENT '方向 1-下 2-上' AFTER `avg_speed`;
        </sql>
    </changeSet>

    <changeSet id="29" author="qinzexing">
        <comment>新建索引</comment>
        <sql>
            ALTER TABLE `fse_crane`
                ADD INDEX `idx_dept_id`(`dept_id`) USING BTREE;

            ALTER TABLE `fse_lift`
                ADD INDEX `idx_dept_id`(`dept_id`) USING BTREE;

            ALTER TABLE `fse_crane_record`
            ADD INDEX `idx_start_time`(`start_time`) USING BTREE;

            ALTER TABLE `fse_lift_record`
                ADD INDEX `idx_start_time`(`start_time`) USING BTREE;

            ALTER TABLE `fse_warn`
                DROP INDEX `idx_deptId_time`,
                ADD INDEX `idx_deptId_type_time`(`dept_id`, `type`, `trigger_time`) USING BTREE;
        </sql>
    </changeSet>

    <changeSet id="30" author="qinzexing">
        <comment>调整索引</comment>
        <sql>
            ALTER TABLE `fse_warn`
                DROP INDEX `idx_deptId_type_time`,
                ADD INDEX `idx_deptId_time`(`dept_id`, `trigger_time`) USING BTREE;

            ALTER TABLE `fse_warn_rule`
                ADD INDEX `idx_type`(`type`) USING BTREE;
        </sql>
    </changeSet>

    <changeSet id="31" author="xuguocheng">
        <comment>调整字段</comment>
        <sql>
            ALTER TABLE `fse_crawler`
                MODIFY COLUMN `dept_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '组织机构名称' AFTER `dept_id`;

            ALTER TABLE `fse_bridge`
                MODIFY COLUMN `dept_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '组织机构名称' AFTER `dept_id`;

            ALTER TABLE `fse_crane`
                MODIFY COLUMN `dept_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '组织机构名称' AFTER `dept_id`;

            ALTER TABLE `fse_gantry`
                MODIFY COLUMN `dept_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '组织机构名称' AFTER `dept_id`;

            ALTER TABLE `fse_lift`
                MODIFY COLUMN `dept_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '组织机构名称' AFTER `dept_id`;
        </sql>
    </changeSet>

    <changeSet id="32" author="xuguocheng">
        <comment>新建表</comment>
        <sql>
            CREATE TABLE `fse_hoisting`  (
             `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
             `dept_id` int(11) NOT NULL COMMENT '项目ID',
             `fse_type` int(11) NOT NULL COMMENT '特种设备类型',
             `fse_id` int(11) NULL DEFAULT NULL COMMENT '特种设备ID',
             `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目名称',
             `state` int(11) NOT NULL COMMENT '状态',
             `detail` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '详细信息',
             `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
             `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
             `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
             PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '特种设备-吊装项目' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="33" author="xuguocheng">
        <comment>修改字段</comment>
        <sql>
            ALTER TABLE `fse_hoisting`
                MODIFY COLUMN `fse_type` int(11) NULL COMMENT '特种设备类型' AFTER `dept_id`;
        </sql>
    </changeSet>

    <changeSet id="34" author="xuguocheng">
        <comment>新增索引</comment>
        <sql>
            ALTER TABLE `fse_warn`
                ADD INDEX `idx_objId_time`(`trigger_time`, `trigger_object_id`, `type`) USING BTREE;
        </sql>
    </changeSet>

    <changeSet id="35" author="xuguocheng">
        <comment>新建表</comment>
        <sql>
            CREATE TABLE `fse_crawler_day`  (
                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                `dept_id` int(11) NOT NULL COMMENT '项目ID',
                `crawler_id` int(11) NOT NULL COMMENT '履带吊ID',
                `date` date NOT NULL COMMENT '日期',
                `start_time` datetime(0) NULL DEFAULT NULL COMMENT '开始时间',
                `end_time` datetime(0) NULL DEFAULT NULL COMMENT '结束时间',
                `num` int(11) NULL DEFAULT 0 COMMENT '通信次数',
                `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
                `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE,
                INDEX `idx_dept_date`(`dept_id`, `date`) USING BTREE,
                INDEX `idx_crawler_date`(`crawler_id`, `date`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '履带吊-数据每日统计' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="36" author="qzexing">
        <comment>新增天秤吊相关表</comment>
        <sql>
            CREATE TABLE `fse_tower_crane` (
               `id` INT ( 11 ) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
               `guid` VARCHAR ( 32 ) NOT NULL COMMENT 'GUID',
               `dept_id` INT ( 11 ) NOT NULL COMMENT '组织机构ID',
               `code` VARCHAR ( 30 ) NOT NULL COMMENT '设备编号',
               `model` VARCHAR ( 30 ) NULL COMMENT '设备型号',
               `manufacturer` VARCHAR ( 50 ) NULL COMMENT '制造单位',
               `delivery_code` VARCHAR ( 30 ) NULL COMMENT '出厂编号',
               `delivery_date` DATE NULL COMMENT '出厂日期',
               `del_flag` INT ( 11 ) NOT NULL DEFAULT 0 COMMENT '删除标记 0-未删除 1-已删除',
               `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
               `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
               PRIMARY KEY ( `id` ) USING BTREE
            ) ENGINE = InnoDB COMMENT = '4S天秤吊表' ROW_FORMAT = DYNAMIC;

            CREATE TABLE `fse_tower_crane_device` (
              `id` INT ( 11 ) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
              `dept_id` INT ( 11 ) NOT NULL COMMENT '组织机构ID',
              `tower_id` INT ( 11 ) NOT NULL COMMENT '天秤吊ID',
              `monitor_type` INT ( 11 ) NOT NULL COMMENT '监控方案 1-AI监控 2-UWB测距',
              `device_id` INT ( 11 ) NOT NULL COMMENT '监控设备ID',
              `device_name` VARCHAR ( 30 ) NOT NULL COMMENT '监控设备名称',
              `del_flag` INT ( 11 ) NOT NULL DEFAULT 0 COMMENT '删除标记 0-未删除 1-已删除',
              `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
              `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
              PRIMARY KEY ( `id` ) USING BTREE
            ) ENGINE = InnoDB COMMENT = '4S天秤吊关联设备' ROW_FORMAT = DYNAMIC;
        </sql>
    </changeSet>

    <changeSet id="37" author="qzexing">
        <comment>新增天秤吊报警相关表</comment>
        <sql>
            CREATE TABLE `fse_tower_warn` (
              `id` INT ( 11 ) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
              `dept_id` INT ( 11 ) NOT NULL COMMENT '项目ID',
              `warn_rule_id` INT ( 11 ) NOT NULL COMMENT '规则ID',
              `rule_type` INT ( 11 ) NULL DEFAULT NULL COMMENT '规则类型',
              `trigger_param` text COMMENT '触发参数',
              `trigger_time` DATETIME NOT NULL COMMENT '触发时间',
              `trigger_value` VARCHAR ( 255 ) NULL COMMENT '触发值',
              `trigger_object_id` VARCHAR ( 32 ) NOT NULL COMMENT '触发业务对象ID(设备ID)',
              `state` INT ( 11 ) NOT NULL DEFAULT 0 COMMENT '报警记录状态(0-未处理 1-已处理)',
              `handle_time` DATETIME NULL DEFAULT NULL COMMENT '处理时间',
              `handle_result` VARCHAR ( 100 ) NULL COMMENT '处理结果',
              `handle_remark` VARCHAR ( 100 ) NULL COMMENT '处理结果备注',
              `handle_user_id` INT ( 11 ) NULL DEFAULT NULL COMMENT '处理人',
              `handle_user_name` VARCHAR ( 32 ) NULL COMMENT '处理人姓名',
              `handle_user_phone` VARCHAR ( 20 ) NULL COMMENT '处理人手机号',
              `del_flag` INT ( 11 ) NOT NULL DEFAULT 0 COMMENT '删除标记',
              `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
              `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
              PRIMARY KEY ( `id` ) USING BTREE
            ) ENGINE = InnoDB COMMENT = '天秤吊报警记录' ROW_FORMAT = Dynamic;

            CREATE TABLE `fse_tower_warn_rule` (
               `id` INT ( 11 ) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
               `dept_id` INT ( 11 ) NOT NULL COMMENT '项目ID',
               `name` VARCHAR ( 32 ) NOT NULL DEFAULT '' COMMENT '名称',
               `rule_type` INT ( 11 ) NULL DEFAULT NULL COMMENT '报警规则类型',
               `enable_flag` INT ( 11 ) NOT NULL DEFAULT 1 COMMENT '启用标记(0-未启用 1-已启用)',
               `del_flag` INT ( 11 ) NOT NULL DEFAULT 0 COMMENT '删除标记',
               `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
               `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
               PRIMARY KEY ( `id` ) USING BTREE
            ) ENGINE = InnoDB COMMENT = '天秤吊报警规则' ROW_FORMAT = Dynamic;

            CREATE TABLE `fse_tower_warn_rule_channel` (
               `id` INT ( 11 ) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
               `warn_rule_id` INT ( 11 ) NOT NULL COMMENT '特种设备报警规则id',
               `msg_channel` INT ( 11 ) NOT NULL COMMENT '接收方式 1-小程序 2-公众号 3-后台 4-短信 5-邮件',
               `del_flag` INT ( 11 ) NOT NULL DEFAULT 0 COMMENT '删除标记',
               `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
               `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
               PRIMARY KEY ( `id` ) USING BTREE
            ) ENGINE = InnoDB COMMENT = '天秤吊报警接收方式' ROW_FORMAT = Dynamic;

            CREATE TABLE `fse_tower_warn_rule_object` (
              `id` INT ( 11 ) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
              `warn_rule_id` INT ( 11 ) NOT NULL COMMENT '报警规则id',
              `object_id` VARCHAR ( 32 ) NOT NULL COMMENT '报警对象id',
              `del_flag` INT ( 11 ) NOT NULL DEFAULT 0 COMMENT '删除标记',
              `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
              `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
              PRIMARY KEY ( `id` ) USING BTREE
            ) ENGINE = InnoDB COMMENT = '天秤吊报警规则和报警对象关联表' ROW_FORMAT = Dynamic;

            CREATE TABLE `fse_tower_warn_rule_user` (
                `id` INT ( 11 ) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                `warn_rule_id` INT ( 11 ) NOT NULL COMMENT '天秤吊报警规则id',
                `to_user_id` INT ( 11 ) NULL DEFAULT NULL COMMENT '接收人',
                `to_user_name` VARCHAR ( 32 ) NULL COMMENT '接收人姓名',
                `to_user_phone` VARCHAR ( 20 ) NULL COMMENT '接收人手机号',
                `del_flag` INT ( 11 ) NOT NULL DEFAULT 0 COMMENT '删除标记',
                `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                PRIMARY KEY ( `id` ) USING BTREE
            ) ENGINE = InnoDB COMMENT = '天秤吊报警-接收人' ROW_FORMAT = Dynamic;

            CREATE TABLE `fse_tower_warn_rule_time`  (
             `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
             `rule_id` int(11) NOT NULL COMMENT '报警规则ID',
             `start_time` time NOT NULL COMMENT '开始时间',
             `end_time` time NOT NULL COMMENT '结束时间',
             `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
             `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
             `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
             PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB COMMENT = '天秤吊报警-报警时间' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="38" author="qzexing">
        <comment>天秤吊新增字段</comment>
        <sql>
            ALTER TABLE `fse_tower_crane`
            ADD COLUMN `address` VARCHAR ( 255 ) NULL COMMENT '位置信息' AFTER `delivery_date`;
        </sql>
    </changeSet>

    <changeSet id="39" author="qzexing">
        <comment>天秤吊增加报警频率设置表</comment>
        <sql>
            CREATE TABLE `fse_tower_warn_freq_config`  (
             `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
             `dept_id` int(11) NOT NULL COMMENT '项目id',
             `frequency_limit_day` int(11) NULL DEFAULT NULL COMMENT '每天最大次数',
             `frequency_limit_hour` int(11) NULL DEFAULT NULL COMMENT '每小时最大次数',
             `frequency_limit_interval` int(11) NOT NULL COMMENT '报警时间间隔（分钟）',
             `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记 0-未删除 1-已删除',
             `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
             `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
             PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB COMMENT = '报警频率配置' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="40" author="qzexing">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `fse_tower_warn_rule`
                ADD COLUMN `warn_type` int(11) NOT NULL DEFAULT 1 COMMENT '报警类型 1-硬件报警 2-系统报警' AFTER `name`,
                ADD COLUMN `rule_param` text NULL COMMENT '报警报警参数' AFTER `rule_type`;

            UPDATE `fse_tower_warn_rule` SET warn_type = 2, rule_param = '{"ruleMinValue":7}' WHERE rule_type = 201;
        </sql>
    </changeSet>

    <changeSet id="41" author="qzexing">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `fse_tower_crane_device`
                ADD COLUMN `part_name` varchar(32)  NULL COMMENT '部位名称' AFTER `monitor_type`;
        </sql>
    </changeSet>

</databaseChangeLog>