<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.fse.dao.FseTowerCraneDeviceMapper">
    <resultMap id="BaseResultMap" type="com.whfc.fse.entity.FseTowerCraneDevice">
        <!--@mbg.generated-->
        <!--@Table fse_tower_crane_device-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="dept_id" jdbcType="INTEGER" property="deptId"/>
        <result column="tower_id" jdbcType="INTEGER" property="towerId"/>
        <result column="monitor_type" jdbcType="INTEGER" property="monitorType"/>
        <result column="part_name" jdbcType="VARCHAR" property="partName"/>
        <result column="device_id" jdbcType="INTEGER" property="deviceId"/>
        <result column="device_name" jdbcType="VARCHAR" property="deviceName"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        dept_id,
        tower_id,
        monitor_type,
        part_name,
        device_id,
        device_name,
        del_flag,
        update_time,
        create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from fse_tower_crane_device
        where id = #{id,jdbcType=INTEGER}
    </select>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.fse.entity.FseTowerCraneDevice"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into fse_tower_crane_device
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="towerId != null">
                tower_id,
            </if>
            <if test="monitorType != null">
                monitor_type,
            </if>
            <if test="partName != null">
                part_name,
            </if>
            <if test="deviceId != null">
                device_id,
            </if>
            <if test="deviceName != null">
                device_name,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                #{deptId,jdbcType=INTEGER},
            </if>
            <if test="towerId != null">
                #{towerId,jdbcType=VARCHAR},
            </if>
            <if test="monitorType != null">
                #{monitorType,jdbcType=INTEGER},
            </if>
            <if test="partName != null">
                #{partName,jdbcType=VARCHAR},
            </if>
            <if test="deviceId != null">
                #{deviceId,jdbcType=INTEGER},
            </if>
            <if test="deviceName != null">
                #{deviceName,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.fse.entity.FseTowerCraneDevice">
        <!--@mbg.generated-->
        update fse_tower_crane_device
        <set>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=INTEGER},
            </if>
            <if test="towerId != null">
                tower_id = #{towerId,jdbcType=VARCHAR},
            </if>
            <if test="monitorType != null">
                monitor_type = #{monitorType,jdbcType=INTEGER},
            </if>
            <if test="partName != null">
                part_name = #{partName,jdbcType=VARCHAR},
            </if>
            <if test="deviceId != null">
                device_id = #{deviceId,jdbcType=INTEGER},
            </if>
            <if test="deviceName != null">
                device_name = #{deviceName,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectByTowerIds" resultType="com.whfc.fse.dto.FseTowerCraneDeviceDTO">
        select
        <include refid="Base_Column_List"/>
        from fse_tower_crane_device
        where del_flag = 0
          and tower_id in
        <foreach item="item" index="index" collection="towerIdList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <update id="logicDeleteByTowerId">
        update fse_tower_crane_device
        set del_flag = 1
        where tower_id = #{towerId}
        <if test="monitorTypes != null and monitorTypes.size() != 0">
            and monitor_type in
            <foreach item="item" index="index" collection="monitorTypes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>

    <select id="countByMonitorType" resultType="com.whfc.fse.dto.FseTowerCraneStatDTO">
        select
               count(case when monitor_type = 1 then 1 end) as aiServerTotal,
               count(case when monitor_type = 2 then 1 end) as uwbTotal,
               count(case when monitor_type = 3 then 1 end) as fvsTotal
        from fse_tower_crane_device
        where del_flag = 0
          and dept_id = #{deptId}
    </select>

    <select id="selectDeviceIdAndType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fse_tower_crane_device
        where del_flag = 0
          and dept_id = #{deptId}
          and device_id = #{deviceId}
          and monitor_type = #{monitorType}
        limit 1
    </select>
</mapper>