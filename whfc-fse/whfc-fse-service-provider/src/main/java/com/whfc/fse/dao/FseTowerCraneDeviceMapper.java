package com.whfc.fse.dao;

import com.whfc.fse.dto.FseTowerCraneDeviceDTO;
import com.whfc.fse.dto.FseTowerCraneStatDTO;
import com.whfc.fse.entity.FseTowerCraneDevice;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/19
 */
@Mapper
public interface FseTowerCraneDeviceMapper {

    int insertSelective(FseTowerCraneDevice record);

    FseTowerCraneDevice selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(FseTowerCraneDevice record);

    /**
     * 根据天秤吊ID查询设备
     *
     * @param towerIdList
     * @return
     */
    List<FseTowerCraneDeviceDTO> selectByTowerIds(@Param("towerIdList") List<Integer> towerIdList);

    /**
     * 逻辑删除天秤吊设备
     *
     * @param towerId
     */
    void logicDeleteByTowerId(@Param("towerId") Integer towerId,
                              @Param("monitorTypes") List<Integer> monitorTypes);

    /**
     * 统计监控方案数量
     *
     * @param deptId
     * @return
     */
    FseTowerCraneStatDTO countByMonitorType(@Param("deptId") Integer deptId);

    /**
     * 根据组织机构ID、设备ID和监控类型查询设备
     *
     * @param deptId
     * @param deviceId
     * @param monitorType
     * @return
     */
    FseTowerCraneDevice selectDeviceIdAndType(@Param("deptId") Integer deptId,
                                              @Param("deviceId") Integer deviceId,
                                              @Param("monitorType") Integer monitorType);
}