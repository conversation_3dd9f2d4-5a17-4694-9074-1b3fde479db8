package com.whfc.fse.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.whfc.common.enums.I18nErrorCode;
import com.whfc.common.enums.NetState;
import com.whfc.common.exception.BizException;
import com.whfc.common.redis.RedisConst;
import com.whfc.common.redis.RedisService;
import com.whfc.common.result.PageData;
import com.whfc.common.result.ResultEnum;
import com.whfc.common.util.PageUtil;
import com.whfc.entity.dto.warn.AppWarnRuleType;
import com.whfc.fse.dao.FseTowerCraneDeviceMapper;
import com.whfc.fse.dao.FseTowerCraneMapper;
import com.whfc.fse.dao.FseTowerWarnMapper;
import com.whfc.fse.dto.FseTowerCraneDTO;
import com.whfc.fse.dto.FseTowerCraneDeviceDTO;
import com.whfc.fse.dto.FseTowerCraneStatDTO;
import com.whfc.fse.entity.FseTowerCrane;
import com.whfc.fse.entity.FseTowerCraneDevice;
import com.whfc.fse.enums.TowerMonitorType;
import com.whfc.fse.param.FseTowerCraneBindParam;
import com.whfc.fse.param.FseTowerCraneDeviceParam;
import com.whfc.fse.param.FseTowerCraneParam;
import com.whfc.fse.service.FseTowerCraneService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/19
 */
@DubboService(interfaceClass = FseTowerCraneService.class, version = "1.0.0")
public class FseTowerCraneServiceImpl implements FseTowerCraneService {


    @Autowired
    private FseTowerCraneMapper fseTowerCraneMapper;

    @Autowired
    private FseTowerCraneDeviceMapper fseTowerCraneDeviceMapper;

    @Autowired
    private FseTowerWarnMapper fseTowerWarnMapper;

    @Autowired
    private RedisService redisService;

    @Override
    public PageData<FseTowerCraneDTO> page(Integer deptId, String keyword, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<FseTowerCraneDTO> list = fseTowerCraneMapper.selectList(deptId, keyword);
        PageHelper.clearPage();
        setTowerDetail(deptId, list);
        return PageUtil.pageData(PageInfo.of(list));
    }

    @Override
    public FseTowerCraneStatDTO stat(Integer deptId) throws BizException {
        // 统计设备数量
        FseTowerCraneStatDTO fseTowerCraneStatDTO = fseTowerCraneDeviceMapper.countByMonitorType(deptId);
        // 天秤吊数量
        Integer towerNum = fseTowerCraneMapper.countByDeptId(deptId);
        fseTowerCraneStatDTO.setTotal(towerNum);

        return fseTowerCraneStatDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(Integer deptId, FseTowerCraneParam param) {
        FseTowerCrane fseTowerCrane = fseTowerCraneMapper.selectByCode(deptId, param.getCode());
        if (fseTowerCrane != null) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.SYS_BE_055.getCode());
        }
        fseTowerCrane = new FseTowerCrane();
        BeanUtils.copyProperties(param, fseTowerCrane);
        fseTowerCrane.setDeptId(deptId);
        fseTowerCrane.setGuid(IdUtil.fastSimpleUUID());
        fseTowerCraneMapper.insertSelective(fseTowerCrane);
        List<FseTowerCraneDeviceParam> deviceList = param.getDeviceList();
        saveTowerDevice(deptId, fseTowerCrane.getId(), deviceList);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(FseTowerCraneParam param) {
        FseTowerCrane fseTowerCrane = fseTowerCraneMapper.selectByGuid(param.getGuid());
        if (ObjectUtil.isEmpty(fseTowerCrane)) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.MACH_BE_001.getCode());
        }
        if (!param.getCode().equals(fseTowerCrane.getCode())) {
            FseTowerCrane towerCrane = fseTowerCraneMapper.selectByCode(fseTowerCrane.getDeptId(), param.getCode());
            if (towerCrane != null) {
                throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.MACH_BE_025.getCode());
            }
        }
        BeanUtils.copyProperties(param, fseTowerCrane);
        fseTowerCraneMapper.updateByPrimaryKeySelective(fseTowerCrane);
        // 删除旧设备
        Integer towerId = fseTowerCrane.getId();
        // 编辑默认删除 AI 及 UWB 设备类型
        List<Integer> monitorTypes = Arrays.asList(TowerMonitorType.AI.getValue(), TowerMonitorType.UWB.getValue());
        fseTowerCraneDeviceMapper.logicDeleteByTowerId(towerId, monitorTypes);
        // 保存新设备
        saveTowerDevice(fseTowerCrane.getDeptId(), towerId, param.getDeviceList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String guid) {
        FseTowerCrane fseTowerCrane = fseTowerCraneMapper.selectByGuid(guid);
        if (ObjectUtil.isEmpty(fseTowerCrane)) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.MACH_BE_001.getCode());
        }
        //  删除设备
        fseTowerCraneDeviceMapper.logicDeleteByTowerId(fseTowerCrane.getId(), null);
        // 删除天秤吊
        fseTowerCraneMapper.logicDelete(fseTowerCrane.getId());
    }

    @Override
    public void bind(FseTowerCraneBindParam param) throws BizException {
        FseTowerCrane fseTowerCrane = fseTowerCraneMapper.selectByGuid(param.getGuid());
        if (ObjectUtil.isEmpty(fseTowerCrane)) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.MACH_BE_001.getCode());
        }

        // 删除旧的绑定
        fseTowerCraneDeviceMapper.logicDeleteByTowerId(fseTowerCrane.getId(), Collections.singletonList(param.getMonitorType()));

        List<FseTowerCraneDeviceParam> deviceList = param.getDeviceList();
        if (CollectionUtils.isEmpty(deviceList)) {
            return;
        }
        // 设置监控类型
        deviceList.forEach(device -> device.setMonitorType(param.getMonitorType()));
        saveTowerDevice(fseTowerCrane.getDeptId(), fseTowerCrane.getId(), deviceList);
    }

    /**
     * 保存天秤吊设备
     *
     * @param deptId     组织机构ID
     * @param towerId    塔吊ID
     * @param deviceList 设备列表
     */
    private void saveTowerDevice(Integer deptId, Integer towerId, List<FseTowerCraneDeviceParam> deviceList) {
        if (CollectionUtils.isEmpty(deviceList)) {
            return;
        }
        for (FseTowerCraneDeviceParam deviceParam : deviceList) {
            FseTowerCraneDevice device = new FseTowerCraneDevice();
            device.setDeptId(deptId);
            device.setTowerId(towerId);
            device.setMonitorType(deviceParam.getMonitorType());
            device.setPartName(deviceParam.getPartName());
            device.setDeviceId(deviceParam.getDeviceId());
            device.setDeviceName(deviceParam.getDeviceName());
            fseTowerCraneDeviceMapper.insertSelective(device);
        }
    }

    /**
     * 设置天秤吊详情
     *
     * @param deptId
     * @param list
     */
    private void setTowerDetail(Integer deptId, List<FseTowerCraneDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        // 查询设备信息
        List<Integer> idList = list.stream().map(FseTowerCraneDTO::getId).collect(Collectors.toList());
        // 查询设备信息
        List<FseTowerCraneDeviceDTO> deviceList = fseTowerCraneDeviceMapper.selectByTowerIds(idList);
        // 处理设备信息
        Map<Integer, List<FseTowerCraneDeviceDTO>> deviceMap = deviceList.stream()
                .collect(Collectors.groupingBy(FseTowerCraneDeviceDTO::getTowerId));

        // 查询当天报警数量
        Date startDate = DateUtil.beginOfDay(new Date());
        Date endDate = DateUtil.endOfDay(startDate);
        List<AppWarnRuleType> warnList = fseTowerWarnMapper.countWarnByObjId(deptId, startDate, endDate);
        Map<Integer, Integer> warnMap = warnList.stream().collect(Collectors.toMap(AppWarnRuleType::getWarnObjId, AppWarnRuleType::getWarnNum));
        for (FseTowerCraneDTO fseTowerCraneDTO : list) {
            // 设置设备信息
            List<FseTowerCraneDeviceDTO> towerDeviceList = deviceMap.getOrDefault(fseTowerCraneDTO.getId(), new ArrayList<>());
            if (CollectionUtils.isEmpty(towerDeviceList)) {
                fseTowerCraneDTO.setAiServerList(Collections.emptyList());
                fseTowerCraneDTO.setUwbServerList(Collections.emptyList());
                fseTowerCraneDTO.setFvsList(Collections.emptyList());
                continue;
            }
            List<FseTowerCraneDeviceDTO> aiServerList = new ArrayList<>();
            List<FseTowerCraneDeviceDTO> uwbServerList = new ArrayList<>();
            List<FseTowerCraneDeviceDTO> fvsList = new ArrayList<>();
            for (FseTowerCraneDeviceDTO fseTowerCraneDeviceDTO : towerDeviceList) {
                if (TowerMonitorType.AI.getValue().equals(fseTowerCraneDeviceDTO.getMonitorType())) {
                    // 设置在线状态
                    fseTowerCraneDeviceDTO.setNetState(NetState.OFFLINE.getValue());
                    String key = String.format(RedisConst.FIM_AI_SERVER_ONLINE, fseTowerCraneDeviceDTO.getDeviceId());
                    if (redisService.hasKey(key)) {
                        fseTowerCraneDeviceDTO.setNetState(NetState.ONLINE.getValue());
                    }
                    aiServerList.add(fseTowerCraneDeviceDTO);
                } else if (TowerMonitorType.UWB.getValue().equals(fseTowerCraneDeviceDTO.getMonitorType())) {
                    // 设置在线状态
                    fseTowerCraneDeviceDTO.setNetState(NetState.OFFLINE.getValue());
                    String key = String.format(RedisConst.UWB_DEVICE_ONLINE, fseTowerCraneDeviceDTO.getDeviceId());
                    if (redisService.hasKey(key)) {
                        fseTowerCraneDeviceDTO.setNetState(NetState.ONLINE.getValue());
                    }
                    uwbServerList.add(fseTowerCraneDeviceDTO);
                } else if (TowerMonitorType.FVS.getValue().equals(fseTowerCraneDeviceDTO.getMonitorType())) {
                    // 设置在线状态
                    fseTowerCraneDeviceDTO.setNetState(NetState.OFFLINE.getValue());
                    String key = String.format(RedisConst.FVS_DEVICE_ONLINE, fseTowerCraneDeviceDTO.getDeviceId());
                    if (redisService.hasKey(key)) {
                        fseTowerCraneDeviceDTO.setNetState(NetState.ONLINE.getValue());
                    }
                    fvsList.add(fseTowerCraneDeviceDTO);
                }
            }
            fseTowerCraneDTO.setAiServerList(aiServerList);
            fseTowerCraneDTO.setUwbServerList(uwbServerList);
            fseTowerCraneDTO.setFvsList(fvsList);

            // 统计报警数量
            fseTowerCraneDTO.setWarnNum(warnMap.getOrDefault(fseTowerCraneDTO.getId(), 0));
        }
    }

}
