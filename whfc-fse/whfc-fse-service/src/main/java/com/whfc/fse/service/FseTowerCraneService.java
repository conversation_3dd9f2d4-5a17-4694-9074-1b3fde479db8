package com.whfc.fse.service;

import com.whfc.common.exception.BizException;
import com.whfc.common.result.PageData;
import com.whfc.fse.dto.FseTowerCraneDTO;
import com.whfc.fse.dto.FseTowerCraneStatDTO;
import com.whfc.fse.param.FseTowerCraneBindParam;
import com.whfc.fse.param.FseTowerCraneParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/19
 */
public interface FseTowerCraneService {
    /**
     * 获取4S天秤吊设备列表(分页)
     *
     * @param deptId
     * @param keyword
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageData<FseTowerCraneDTO> page(Integer deptId, String keyword, Integer pageNum, Integer pageSize) throws BizException;

    /**
     * 4S天秤吊设备统计
     * @param deptId
     * @return
     * @throws BizException
     */
    FseTowerCraneStatDTO stat(Integer deptId) throws BizException;
    /**
     * 添加4S天秤吊设备
     *
     * @param deptId
     * @param param
     */
    void add(Integer deptId, FseTowerCraneParam param) throws BizException;

    /**
     * 修改4S天秤吊设备
     *
     * @param param
     */
    void edit(FseTowerCraneParam param) throws BizException;

    /**
     * 删除4S天秤吊设备
     *
     * @param guid
     */
    void delete(String guid) throws BizException;

    /**
     * 绑定设备
     * @param param
     */
    void bind(FseTowerCraneBindParam param) throws BizException;
}
