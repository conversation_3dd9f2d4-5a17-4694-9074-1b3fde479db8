package com.whfc.uni.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.whfc.common.exception.BizException;
import com.whfc.uni.dao.app.AppDictMapper;
import com.whfc.uni.dto.app.AppDictDTO;
import com.whfc.uni.service.AppDictService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.Map;

@Slf4j
@DubboService(interfaceClass = AppDictService.class, version = "1.0.0", timeout = 30 * 1000)
public class AppDictServiceImpl implements AppDictService {

    @Autowired
    private AppDictMapper appDictMapper;

    @Override
    public Map<String, Object> getDictParamData(Integer deptId, String code) throws BizException {

        AppDictDTO dictDTO = appDictMapper.selectByDeptIdAndCode(deptId, code);
        if (dictDTO != null) {
            String param = dictDTO.getParam();
            return JSONObject.parseObject(param);
        }
        return Collections.EMPTY_MAP;
    }
}
