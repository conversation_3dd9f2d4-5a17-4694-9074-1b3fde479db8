package com.whfc.uni.service.impl.track;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.whfc.common.enums.I18nErrorCode;
import com.whfc.common.exception.BizException;
import com.whfc.common.result.PageData;
import com.whfc.common.result.ResultEnum;
import com.whfc.common.util.PageUtil;
import com.whfc.entity.dto.OssPathDTO;
import com.whfc.uni.dao.track.TrackMachAttachMapper;
import com.whfc.uni.dao.track.TrackMachCertMapper;
import com.whfc.uni.dao.track.TrackMachInfoMapper;
import com.whfc.uni.dto.track.TrackMachCertDTO;
import com.whfc.uni.entity.track.TrackMachAttach;
import com.whfc.uni.entity.track.TrackMachCert;
import com.whfc.uni.entity.track.TrackMachInfo;
import com.whfc.uni.enums.track.TrackAttachType;
import com.whfc.uni.manager.track.TrackMachMgr;
import com.whfc.uni.param.track.TrackMachCertParam;
import com.whfc.uni.service.track.TrackMachCertService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/10
 */
@DubboService(interfaceClass = TrackMachCertService.class, version = "1.0.0")
public class TrackMachCertServiceImpl implements TrackMachCertService {

    @Autowired
    private TrackMachMgr trackMachMgr;

    @Autowired
    private TrackMachInfoMapper trackMachInfoMapper;

    @Autowired
    private TrackMachCertMapper trackMachCertMapper;

    @Autowired
    private TrackMachAttachMapper trackMachAttachMapper;

    @Override
    public PageData<TrackMachCertDTO> page(Integer deptId, Integer pageNum, Integer pageSize, String machGuid,
                                           Integer certType, Integer certState, String keyword) {
        PageHelper.startPage(pageNum, pageSize);
        List<TrackMachCertDTO> list = trackMachCertMapper.selectList(deptId, machGuid, certType, certState, keyword);
        PageHelper.clearPage();
        // 获取附件
        setMachCartAttach(list);
        return PageUtil.pageData(PageInfo.of(list));
    }


    @Override
    public List<TrackMachCertDTO> list(String machGuid) {
        TrackMachInfo machInfo = trackMachInfoMapper.selectByGuid(machGuid);
        if (machInfo == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.MACH_BE_001.getCode());
        }
        List<TrackMachCertDTO> list = trackMachCertMapper.selectListByMachId(machInfo.getId());
        // 获取附件
        setMachCartAttach(list);
        return list;
    }

    @Override
    public TrackMachCertDTO detail(Integer certId) {
        TrackMachCert cert = trackMachCertMapper.selectByPrimaryKey(certId);
        if (cert == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "该证书不存在");
        }
        TrackMachCertDTO dto = new TrackMachCertDTO();
        BeanUtils.copyProperties(cert, dto);
        // 获取附件
        setMachCartAttach(Collections.singletonList(dto));
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(Integer deptId, TrackMachCertParam param) {
        String machGuid = param.getMachGuid();
        TrackMachInfo trackMachInfo = trackMachInfoMapper.selectByGuid(machGuid);
        if (trackMachInfo == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.MACH_BE_001.getCode());
        }
        // 验证证书编号
        TrackMachCert machCert = trackMachCertMapper.selectByCertCode(deptId, param.getCertCode());
        if (machCert != null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "该证书编号已存在.");
        }

        TrackMachCert cert = new TrackMachCert();
        BeanUtils.copyProperties(param, cert);
        cert.setDeptId(deptId);
        cert.setMachId(trackMachInfo.getId());
        cert.setState(checkCertExpired(param.getCertEndDate()));
        trackMachCertMapper.insertSelective(cert);
        List<OssPathDTO> certAttachList = param.getCertAttachList();
        if (CollectionUtils.isEmpty(certAttachList)) {
            return;
        }
        // 批量保存附件
        saveAttach(TrackAttachType.MACH_CERT_IMG.getCode(), deptId, cert.getId(), certAttachList);
        // 更新设备证书状态
        updateMachCertState(cert.getMachId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(Integer deptId, TrackMachCertParam param) {
        TrackMachCert cert = trackMachCertMapper.selectByPrimaryKey(param.getCertId());
        if (cert == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "该证书不存在");
        }

        // 验证证书编号
        if (!param.getCertCode().equals(cert.getCertCode())) {
            TrackMachCert machCert = trackMachCertMapper.selectByCertCode(deptId, param.getCertCode());
            if (machCert != null) {
                throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "该证书编号已存在.");
            }
        }

        BeanUtils.copyProperties(param, cert);
        cert.setState(checkCertExpired(param.getCertEndDate()));
        trackMachCertMapper.updateByPrimaryKeySelective(cert);

        // 删除旧附件
        trackMachAttachMapper.logicDelete(param.getCertId(), TrackAttachType.MACH_CERT_IMG.getCode());
        List<OssPathDTO> certAttachList = param.getCertAttachList();
        if (CollectionUtils.isEmpty(certAttachList)) {
            return;
        }
        // 批量保存附件
        saveAttach(TrackAttachType.MACH_CERT_IMG.getCode(), deptId, cert.getId(), certAttachList);
        // 更新设备证书状态
        updateMachCertState(cert.getMachId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void del(Integer certId) {
        TrackMachCert machCert = trackMachCertMapper.selectByPrimaryKey(certId);
        // 删除证书
        trackMachCertMapper.logicDel(certId);
        // 删除证书附件
        trackMachAttachMapper.logicDelete(certId, TrackAttachType.MACH_CERT_IMG.getCode());
        // 更新设备证书状态
        updateMachCertState(machCert.getMachId());
    }

    /**
     * 更新设备证书状态
     *
     * @param machId
     */
    private void updateMachCertState(Integer machId) {
        CompletableFuture.runAsync(() -> {
            Integer certState = trackMachCertMapper.selectExpiredCert(machId);
            if (certState == null) {
                certState = -1;
            }
            trackMachInfoMapper.updateCertState(machId, certState);
            // 刷新设备证书缓存数据
            trackMachMgr.refreshTrackMachCertCacheState();
        });
    }

    /**
     * 检查证书是否过期
     *
     * @param certEndDate 证书的结束日期
     * @return 如果证书已过期返回 0，否则返回 1
     */
    private int checkCertExpired(Date certEndDate) {
        if (certEndDate == null) {
            return 1;
        }
        // 比较证书结束日期与当前时间
        return certEndDate.getTime() < System.currentTimeMillis() ? 0 : 1;
    }

    /**
     * 设置设备证书附件
     *
     * @param list
     */
    private void setMachCartAttach(List<TrackMachCertDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<Integer> objIdList = list.stream().map(TrackMachCertDTO::getId).collect(Collectors.toList());
        List<TrackMachAttach> attachList = trackMachAttachMapper.selectByObjIdList(objIdList, TrackAttachType.MACH_CERT_IMG.getCode());
        Map<Integer, List<TrackMachAttach>> attachMap = attachList.stream().collect(Collectors.groupingBy(TrackMachAttach::getObjId));
        for (TrackMachCertDTO trackMachCertDTO : list) {
            List<TrackMachAttach> machAttaches = attachMap.get(trackMachCertDTO.getId());
            if (CollectionUtils.isEmpty(machAttaches)) {
                continue;
            }
            List<OssPathDTO> ossPathList = machAttaches.stream().map(attach -> OssPathDTO.builder()
                    .name(attach.getName())
                    .path(attach.getUrl())
                    .build()).collect(Collectors.toList());
            trackMachCertDTO.setCertAttachList(ossPathList);
        }
    }

    /**
     * 保存图片
     *
     * @param attachType
     * @param deptId
     * @param objId
     * @param imgList
     */
    private void saveAttach(Integer attachType, Integer deptId, Integer objId, List<OssPathDTO> imgList) {
        if (CollectionUtils.isEmpty(imgList)) {
            return;
        }
        for (OssPathDTO ossPathDTO : imgList) {
            TrackMachAttach trackMachAttach = new TrackMachAttach();
            trackMachAttach.setDeptId(deptId);
            trackMachAttach.setAttachType(attachType);
            trackMachAttach.setObjId(objId);
            trackMachAttach.setUrl(ossPathDTO.getPath());
            trackMachAttach.setName(ossPathDTO.getName());
            trackMachAttachMapper.insertSelective(trackMachAttach);
        }
    }

}
