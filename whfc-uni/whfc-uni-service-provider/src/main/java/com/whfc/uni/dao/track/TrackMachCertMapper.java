package com.whfc.uni.dao.track;

import com.whfc.entity.dto.warn.AppWarnRuleType;
import com.whfc.uni.dto.track.TrackMachCertDTO;
import com.whfc.uni.entity.track.TrackMachCert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/10
 */
@Mapper
public interface TrackMachCertMapper {
    int insertSelective(TrackMachCert record);

    TrackMachCert selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(TrackMachCert record);

    /**
     * 查询列表
     *
     * @param deptId
     * @param machGuid
     * @param certType
     * @param certState
     * @param keyword
     * @return
     */
    List<TrackMachCertDTO> selectList(@Param("deptId") Integer deptId,
                                      @Param("machGuid") String machGuid,
                                      @Param("certType") Integer certType,
                                      @Param("certState") Integer certState,
                                      @Param("keyword") String keyword);

    /**
     * 查询设备证书
     *
     * @param machId
     * @return
     */
    List<TrackMachCertDTO> selectListByMachId(@Param("machId") Integer machId);

    /**
     * 更新设备证书状态
     *
     * @param id
     * @param state
     */
    void updateState(@Param("id") Integer id, @Param("state") Integer state);


    /**
     * @param deptId
     * @param certCode
     * @return
     */
    TrackMachCert selectByCertCode(@Param("deptId") Integer deptId,
                                   @Param("certCode") String certCode);

    /**
     * 逻辑删除
     *
     * @param certId
     */
    void logicDel(@Param("certId") Integer certId);

    /**
     *
     * @param machId
     * @return
     */
    Integer selectExpiredCert(@Param("machId") Integer machId);

    /**
     *
     * @param machId
     * @return
     */
    TrackMachCertDTO selectLastByMachId(@Param("machId") Integer machId);

    /**
     * 刷新设备证书状态
     * @param date
     */
    void refreshCertState(@Param("date") Date date);

    /**
     * 统计设备证书过期
     * @return
     */
    List<AppWarnRuleType> countCertExpired();

    /**
     * 根据设备ID统计证书过期
     * @param deptId
     * @return
     */
    List<AppWarnRuleType> countCertExpiredByMachId(@Param("deptId") Integer deptId);
}