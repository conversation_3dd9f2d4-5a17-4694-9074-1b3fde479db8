<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.app.AppDictMapper">
  <resultMap id="BaseResultMap" type="com.whfc.uni.entity.app.AppDict">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="pid" jdbcType="INTEGER" property="pid" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="ext1" jdbcType="VARCHAR" property="ext1" />
    <result column="ext2" jdbcType="VARCHAR" property="ext2" />
    <result column="param" jdbcType="LONGVARCHAR" property="param" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id,
    dept_id,
    pid,
    code,
    name,
    ext1,
    ext2,
    param,
    del_flag,
    update_time,
    create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from app_dict
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from app_dict
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.whfc.uni.entity.app.AppDict">
    insert into app_dict
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="pid != null">
        pid,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="ext1 != null">
        ext1,
      </if>
      <if test="ext2 != null">
        ext2,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="param != null">
        param,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="pid != null">
        #{pid,jdbcType=INTEGER},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="ext1 != null">
        #{ext1,jdbcType=VARCHAR},
      </if>
      <if test="ext2 != null">
        #{ext2,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="param != null">
        #{param,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.app.AppDict">
    update app_dict
    <set>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="pid != null">
        pid = #{pid,jdbcType=INTEGER},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="ext1 != null">
        ext1 = #{ext1,jdbcType=VARCHAR},
      </if>
      <if test="ext2 != null">
        ext2 = #{ext2,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="param != null">
        param = #{param,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByDeptIdAndCode" resultType="com.whfc.uni.dto.app.AppDictDTO">
    select d.id,
           d.pid,
           d.name,
           d.code,
           d.ext1,
           d.ext2,
           d.param
    from app_dict d
    where (d.dept_id = #{deptId} or dept_id = 0)
      and d.code = #{code}
      and del_flag = 0
    order by d.dept_id desc
    limit 1
  </select>

  <select id="selectPid" resultType="com.whfc.uni.dto.app.AppDictDTO">
    select d.id,
           d.name,
           d.ext1,
           d.ext2
    from app_dict d
    where d.pid = #{pid}
      and d.del_flag = 0
  </select>

  <select id="selectByPCode" resultType="com.whfc.uni.dto.app.AppDictDTO">
    select d.id,
           d.pid,
           d.name,
           d.code,
           d.ext1,
           d.ext2
    from app_dict d
    where d.pid = (select id from app_dict where code = #{code} AND del_flag = 0 AND (dept_id = #{deptId} or dept_id = 0))
      and del_flag = 0
  </select>

  <select id="selectByCode" resultType="com.whfc.uni.dto.app.AppDictDTO">
    select id,
           dept_id,
           pid,
           name,
           code,
           ext1,
           ext2,
           param
    from app_dict
    where code = #{code}
      and del_flag = 0
  </select>
</mapper>