<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.uni.dao.track.TrackMachCertMapper">
    <resultMap id="BaseResultMap" type="com.whfc.uni.entity.track.TrackMachCert">
        <!--@mbg.generated-->
        <!--@Table track_mach_cert-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="dept_id" jdbcType="INTEGER" property="deptId"/>
        <result column="mach_id" jdbcType="INTEGER" property="machId"/>
        <result column="cert_type" jdbcType="INTEGER" property="certType"/>
        <result column="cert_type_name" jdbcType="VARCHAR" property="certTypeName"/>
        <result column="cert_name" jdbcType="VARCHAR" property="certName"/>
        <result column="cert_code" jdbcType="VARCHAR" property="certCode"/>
        <result column="level" jdbcType="VARCHAR" property="level"/>
        <result column="cert_start_date" jdbcType="DATE" property="certStartDate"/>
        <result column="cert_end_date" jdbcType="DATE" property="certEndDate"/>
        <result column="cert_grant_org" jdbcType="VARCHAR" property="certGrantOrg"/>
        <result column="state" jdbcType="INTEGER" property="state"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        dept_id,
        mach_id,
        cert_type,
        cert_type_name,
        cert_name,
        cert_code,
        `level`,
        cert_start_date,
        cert_end_date,
        cert_grant_org,
        `state`,
        del_flag,
        update_time,
        create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from track_mach_cert
        where id = #{id,jdbcType=INTEGER}
    </select>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.uni.entity.track.TrackMachCert"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into track_mach_cert
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="machId != null">
                mach_id,
            </if>
            <if test="certType != null">
                cert_type,
            </if>
            <if test="certTypeName != null">
                cert_type_name,
            </if>
            <if test="certName != null">
                cert_name,
            </if>
            <if test="certCode != null">
                cert_code,
            </if>
            <if test="level != null">
                `level`,
            </if>
            <if test="certStartDate != null">
                cert_start_date,
            </if>
            <if test="certEndDate != null">
                cert_end_date,
            </if>
            <if test="certGrantOrg != null">
                cert_grant_org,
            </if>
            <if test="state != null">
                `state`,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                #{deptId,jdbcType=INTEGER},
            </if>
            <if test="machId != null">
                #{machId,jdbcType=INTEGER},
            </if>
            <if test="certType != null">
                #{certType,jdbcType=INTEGER},
            </if>
            <if test="certTypeName != null">
                #{certTypeName,jdbcType=VARCHAR},
            </if>
            <if test="certName != null">
                #{certName,jdbcType=VARCHAR},
            </if>
            <if test="certCode != null">
                #{certCode,jdbcType=VARCHAR},
            </if>
            <if test="level != null">
                #{level,jdbcType=VARCHAR},
            </if>
            <if test="certStartDate != null">
                #{certStartDate,jdbcType=DATE},
            </if>
            <if test="certEndDate != null">
                #{certEndDate,jdbcType=DATE},
            </if>
            <if test="certGrantOrg != null">
                #{certGrantOrg,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                #{state,jdbcType=INTEGER},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.uni.entity.track.TrackMachCert">
        <!--@mbg.generated-->
        update track_mach_cert
        <set>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=INTEGER},
            </if>
            <if test="machId != null">
                mach_id = #{machId,jdbcType=INTEGER},
            </if>
            <if test="certType != null">
                cert_type = #{certType,jdbcType=INTEGER},
            </if>
            <if test="certTypeName != null">
                cert_type_name = #{certTypeName,jdbcType=VARCHAR},
            </if>
            <if test="certName != null">
                cert_name = #{certName,jdbcType=VARCHAR},
            </if>
            <if test="certCode != null">
                cert_code = #{certCode,jdbcType=VARCHAR},
            </if>
            <if test="level != null">
                `level` = #{level,jdbcType=VARCHAR},
            </if>
            <if test="certStartDate != null">
                cert_start_date = #{certStartDate,jdbcType=DATE},
            </if>
            <if test="certEndDate != null">
                cert_end_date = #{certEndDate,jdbcType=DATE},
            </if>
            <if test="certGrantOrg != null">
                cert_grant_org = #{certGrantOrg,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                `state` = #{state,jdbcType=INTEGER},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>


    <select id="selectList" resultType="com.whfc.uni.dto.track.TrackMachCertDTO">
        select tmi.mach_code,
               tmi.mach_type,
               tmi.guid as machGuid,
               tmi.mach_type_name,
               tmc.*
        from track_mach_cert tmc
                 left join track_mach_info tmi on tmc.mach_id = tmi.id
        where tmc.dept_id = #{deptId,jdbcType=INTEGER}
          and tmc.del_flag = 0
          and tmi.del_flag = 0
        <if test="machGuid != null">
            and tmi.guid = #{machGuid,jdbcType=VARCHAR}
        </if>
        <if test="certState != null">
            and tmc.`state` = #{certState,jdbcType=INTEGER}
        </if>
        <if test="certType != null">
            and tmc.cert_type = #{certType,jdbcType=INTEGER}
        </if>
        <if test="keyword != null">
            and tmi.mach_code like concat('%', #{keyword,jdbcType=VARCHAR}, '%')
        </if>
        order by tmc.create_time DESC
    </select>

    <select id="selectListByMachId" resultType="com.whfc.uni.dto.track.TrackMachCertDTO">
        select
        <include refid="Base_Column_List"/>
        from track_mach_cert
        where mach_id = #{machId,jdbcType=INTEGER}
          and del_flag = 0
        order by create_time DESC
    </select>

    <update id="updateState">
        update track_mach_cert
        set `state` = #{state,jdbcType=INTEGER}
        where id = #{id}
    </update>

    <select id="selectByCertCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from track_mach_cert
        where cert_code = #{certCode,jdbcType=VARCHAR}
          and dept_id = #{deptId,jdbcType=INTEGER}
          and del_flag = 0
    </select>

    <update id="logicDel">
        update track_mach_cert
        set del_flag = 1
        where id = #{certId}
    </update>

    <select id="selectExpiredCert" resultType="java.lang.Integer">
        SELECT MIN(state)
        FROM track_mach_cert
        WHERE del_flag = 0
          AND mach_id = #{machId}
    </select>

    <select id="selectLastByMachId" resultType="com.whfc.uni.dto.track.TrackMachCertDTO">
        SELECT *
        FROM track_mach_cert
        WHERE del_flag = 0
          AND mach_id = #{machId}
        ORDER BY id DESC
        LIMIT 1
    </select>

    <update id="refreshCertState">
        UPDATE track_mach_cert
        SET `state` = 0
        WHERE del_flag = 0
          AND cert_end_date &lt; #{date}
    </update>


    <select id="countCertExpired" resultType="com.whfc.entity.dto.warn.AppWarnRuleType">
        SELECT tmc.dept_id,
               SUM(CASE WHEN tmc.state = 0 THEN 1 ELSE 0 END) AS warn_num
        FROM track_mach_cert tmc
            LEFT JOIN track_mach_info tmi ON tmc.mach_id = tmi.id
        WHERE tmc.del_flag = 0
            AND tmi.del_flag = 0
        GROUP BY tmc.dept_id
    </select>

    <select id="countCertExpiredByMachId" resultType="com.whfc.entity.dto.warn.AppWarnRuleType">
        SELECT mach_id warnObjId,
               SUM(CASE WHEN state = 0 THEN 1 ELSE 0 END) AS warn_num
        FROM track_mach_cert
        WHERE del_flag = 0
          AND dept_id = #{deptId}
        GROUP BY mach_id
    </select>
</mapper>