package com.whfc.uni.service.track;

import com.whfc.common.exception.BizException;
import com.whfc.common.result.PageData;
import com.whfc.uni.dto.track.TrackMachCertDTO;
import com.whfc.uni.param.track.TrackMachCertParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/10
 */
public interface TrackMachCertService {

    /**
     * 设备证书列表-分页
     *
     * @param deptId
     * @param pageNum
     * @param pageSize
     * @param machGuid
     * @param certType
     * @param certState
     * @param keyword
     * @return
     */
    PageData<TrackMachCertDTO> page(Integer deptId, Integer pageNum, Integer pageSize, String machGuid, Integer certType, Integer certState, String keyword) throws BizException;

    /**
     * 设备证书列表
     *
     * @param machGuid
     * @return
     */
    List<TrackMachCertDTO> list(String machGuid) throws BizException;

    /**
     * 设备证书详情
     * @param certId
     * @return
     */
    TrackMachCertDTO detail(Integer certId);
    /**
     * 添加设备证书
     *
     * @param deptId
     * @param param
     */
    void add(Integer deptId, TrackMachCertParam param) throws BizException;

    /**
     * 修改设备证书
     *
     * @param deptId
     * @param param
     */
    void edit(Integer deptId, TrackMachCertParam param) throws BizException;

    /**
     * 删除设备证书
     *
     * @param certId
     */
    void del(Integer certId) throws BizException;
}
