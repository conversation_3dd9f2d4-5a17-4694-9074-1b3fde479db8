package com.whfc.fvs.service;

import com.whfc.common.exception.BizException;
import com.whfc.common.result.ListData;
import com.whfc.common.result.PageData;
import com.whfc.fvs.dto.*;
import com.whfc.fvs.enums.FvsDevicePtzOption;
import com.whfc.fvs.param.FvsDeviceAddParam;
import com.whfc.fvs.param.FvsDeviceEditParam;
import com.whfc.fvs.param.FvsDeviceListParam;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 视频监控
 *
 * <AUTHOR>
 * @Description:
 * @date 2019年10月24日
 */
public interface FvsDeviceService {

    /**
     * 获取视频监控列表(分页)
     *
     * @param param 查询监控列表参数
     * @return 视频监控列表
     * @throws BizException 业务异常
     */
    PageData<FvsDeviceDTO> list(FvsDeviceListParam param, Integer pageNum, Integer pageSize) throws BizException;

    /**
     * 获取视频监控列表(不分页)
     *
     * @param param
     * @return
     * @throws BizException
     */
    List<FvsDeviceDTO> list(FvsDeviceListParam param) throws BizException;

    /**
     * 获取视频设备列表
     *
     * @param deptId 组织机构ID
     * @return
     * @throws BizException 业务异常
     */
    List<FvsDeviceDTO> list(Integer deptId) throws BizException;

    /**
     * 根据设备ID查找监控设备信息
     *
     * @param deviceIdList 设备ID列表
     * @return List<FvsDeviceDTO>
     * @throws BizException 业务异常
     */
    List<FvsDeviceDTO> list(Collection<Integer> deviceIdList) throws BizException;

    /**
     * 获取视频设备列表
     *
     * @param param 查询设备列表参数
     * @return 视频设备列表
     * @throws BizException 业务异常
     */
    PageData<FvsDeviceDTO> deviceList(FvsDeviceListParam param, Integer pageNum, Integer pageSize) throws BizException;

    /**
     * 获取视频设备列表
     *
     * @param param
     * @return
     * @throws BizException
     */
    List<FvsDeviceDTO> deviceList(FvsDeviceListParam param) throws BizException;

    /**
     * 获取监控设备详情
     *
     * @param id 设备ID
     * @return 设备信息
     * @throws BizException 业务异常
     */
    FvsDeviceDTO detail(Integer id) throws BizException;

    /**
     * 获取监控设备详情
     * @param guid
     * @return
     * @throws BizException
     */
    FvsDeviceDTO detail(String guid) throws BizException;

    /**
     * 添加监控设备
     *
     * @param deviceAddParam 添加设备param
     * @throws BizException 业务异常
     */
    void deviceAdd(FvsDeviceAddParam deviceAddParam) throws BizException;

    /**
     * 编辑监控设备
     *
     * @param deviceEditParam 修改设备param
     * @throws BizException 业务异常
     */
    void deviceEdit(FvsDeviceEditParam deviceEditParam) throws BizException;

    /**
     * 删除监控设备
     *
     * @param guid 设备GUID
     * @throws BizException 业务异常
     */
    void delDevice(String guid) throws BizException;

    /**
     * 设备云台操作
     *
     * @param deviceId        设备ID
     * @param devicePtzOption 云台操作
     * @throws BizException 业务异常
     */
    void ptz(Integer deviceId, FvsDevicePtzOption devicePtzOption) throws BizException;


    /**
     * 设备云台操作
     *
     * @param guid        设备ID
     * @param devicePtzOption 云台操作
     * @throws BizException 业务异常
     */
    void ptz(String guid, FvsDevicePtzOption devicePtzOption) throws BizException;

    /**
     * 给设备发送语音
     *
     * @param deviceId
     * @param fileUrl
     * @throws BizException
     */
    void sendVoice(Integer deviceId, String fileUrl) throws BizException;

    /**
     * 给设备发送语音
     *
     * @param guid
     * @param fileUrl
     * @throws BizException
     */
    void sendVoice(String guid, String fileUrl) throws BizException;

    /**
     * 获取视频监控直播地址
     *
     * @param deviceId 设备ID
     * @return 视频监控流对象
     * @throws BizException 业务异常
     */
    FvsStreamDTO getStreamUrl(Integer deviceId) throws BizException;

    /**
     * 获取视频监控直播地址
     *
     * @param guid 设备GUID
     * @return 视频监控流对象
     * @throws BizException 业务异常
     */
    FvsStreamDTO getStreamUrl(String guid) throws BizException;

    /**
     * 获取视频监控录像地址
     *
     * @param deviceId  设备ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 视频监控录像地址
     * @throws BizException 业务异常
     */
    ListData<FvsRecordDTO> getRecordList(Integer deviceId, Date startTime, Date endTime) throws BizException;

    /**
     * 获取视频监控录像地址
     *
     * @param guid  设备GUID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 视频监控录像地址
     * @throws BizException 业务异常
     */
    ListData<FvsRecordDTO> getRecordList(String guid, Date startTime, Date endTime) throws BizException;

    /**
     * 获取视频设备数
     *
     * @param deptIds
     * @return
     * @throws BizException 业务异常
     */
    FvsDeviceNumDTO getFvsDeviceNum(List<Integer> deptIds) throws BizException;

    /**
     * 获取视频监控token
     *
     * @param deptId   组织机构ID
     * @param platform 监控平台
     * @return token
     * @throws BizException 业务异常
     */
    FvsTokenDTO getToken(Integer deptId, Integer platform) throws BizException;

    /**
     * 获取项目视频列表(澳门)
     *
     * @param deptId
     * @return
     * @throws BizException 业务异常
     */
    List<FvsVideoDTO> getVideoList(Integer deptId) throws BizException;

    /**
     * 宣传视频
     *
     * @param deptId
     * @return
     * @throws BizException
     */
    List<FvsVideoDTO> getVideoPromotion(Integer deptId) throws BizException;

    /**
     * 获取项目视频列表(通用)
     *
     * @param deptId
     * @return
     * @throws BizException
     */
    List<FvsStreamDTO> getVideoList1(Integer deptId) throws BizException;

    /**
     * 摄像头大屏统计
     *
     * @param deptId
     * @return
     * @throws BizException
     */
    FvsAnalysisDTO analysis(Integer deptId) throws BizException;


    /***********芜湖***********/

    /**
     * 视频设备列表
     *
     * @param bdbm
     * @param pageNum
     * @param pageSize
     * @return
     * @throws BizException
     */
    PageData<FvsDeviceDTO> spsb(String bdbm, Integer pageNum, Integer pageSize) throws BizException;

    /**
     * 视频播放地址
     *
     * @param bdbm
     * @param sbbm
     * @param protocol
     * @param csxy
     * @param spType
     * @return
     * @throws BizException
     */
    FvsStreamDTO spUrl(String bdbm, String sbbm, String protocol, String csxy, String spType) throws BizException;

    /**
     * 获取视频回放 URL
     *
     * @param bdbm
     * @param sbbm
     * @param protocol
     * @param csxy
     * @param kssj
     * @param jssj
     * @return
     * @throws BizException
     */
    FvsStreamDTO spBackUrl(String bdbm, String sbbm, String protocol, String csxy, String kssj, String jssj) throws BizException;

    /**
     * 获取远程视频回放 URL
     *
     * @param bdbm
     * @param sbbm
     * @param protocol
     * @param csxy
     * @param kssj
     * @param jssj
     * @return
     * @throws BizException
     */
    FvsStreamDTO spRmtBackUrl(String bdbm, String sbbm, String protocol, String csxy, String kssj, String jssj) throws BizException;

    /**
     * 获取抓拍图片
     *
     * @param bdbm
     * @param sbbm
     * @param zpsj
     * @param zpCount
     * @return
     * @throws BizException
     */
    List<String> spCapturePic(String bdbm, String sbbm, String zpsj, Integer zpCount) throws BizException;

    /**
     * 获取抓拍视频录像
     *
     * @param bdbm
     * @param sbbm
     * @param lxkssj
     * @param lxjssj
     * @return
     * @throws BizException
     */
    List<String> spCaptureSplx(String bdbm, String sbbm, String lxkssj, String lxjssj) throws BizException;

    /**
     * 云台操作
     *
     * @param bdbm
     * @param sbbm
     * @throws BizException
     */
    void spYtStart(String bdbm, String sbbm, FvsDevicePtzOption devicePtzOption) throws BizException;

    /**
     * 云台操作
     *
     * @param bdbm
     * @param sbbm
     * @param devicePtzOption
     * @throws BizException
     */
    void spYtStop(String bdbm, String sbbm, FvsDevicePtzOption devicePtzOption) throws BizException;

    /***********收藏夹***********/

    /**
     * 收藏数量
     *
     * @param deptId
     * @param userId
     * @return
     * @throws BizException
     */
    FvsFavoriteDTO favoriteNum(Integer deptId, Integer userId) throws BizException;

    /**
     * 收藏列表
     *
     * @param deptId
     * @param userId
     * @return
     * @throws BizException
     */
    List<FvsDeviceDTO> favoriteList(Integer deptId, Integer userId) throws BizException;

    /**
     * 收藏列表
     *
     * @param param
     * @return
     * @throws BizException
     */
    PageData<FvsDeviceDTO> favoriteList(FvsDeviceListParam param, Integer pageNum, Integer pageSize) throws BizException;

    /**
     * 添加收藏
     *
     * @param deptId
     * @param userId
     * @param fvsDeviceId
     * @throws BizException
     */
    void addFavorite(Integer deptId, Integer userId, Integer fvsDeviceId) throws BizException;

    /**
     * 删除收藏
     *
     * @param deptId
     * @param userId
     * @param fvsDeviceId
     * @throws BizException
     */
    void delFavorite(Integer deptId, Integer userId, Integer fvsDeviceId) throws BizException;

    /***********视频权限***********/

    /**
     * 视频权限编辑
     *
     * @param deptId
     * @param userId
     * @param fvsDeviceIdList
     * @throws BizException
     */
    void fvsRuleEdit(Integer deptId, Integer userId, List<Integer> fvsDeviceIdList) throws BizException;

    /**
     * 视频权限查询
     *
     * @param deptId
     * @param userId
     * @return
     * @throws BizException
     */
    List<Integer> fvsRuleList(Integer deptId, Integer userId) throws BizException;
}
