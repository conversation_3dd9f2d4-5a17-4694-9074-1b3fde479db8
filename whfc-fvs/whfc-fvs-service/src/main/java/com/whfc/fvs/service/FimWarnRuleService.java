package com.whfc.fvs.service;

import com.whfc.common.exception.BizException;
import com.whfc.common.result.ListData;
import com.whfc.common.result.PageData;
import com.whfc.fim.dto.FimAlgCheckTypeDTO;
import com.whfc.fim.dto.FimAlgTypeDTO;
import com.whfc.fim.dto.FimWarnRuleDTO;
import com.whfc.fim.entity.FimWarnRule;
import com.whfc.fim.param.FimSetUserParam;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @DESCRIPTION 智能监控规则设置
 * @AUTHOR GuoDong_Sun
 * @DATE 2020/3/26
 */

public interface FimWarnRuleService {

    /**
     * 智能监控规则列表
     *
     * @param deptId   组织机构ID
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @return 智能监控规则列表
     * @throws BizException
     */
    PageData<FimWarnRuleDTO> list(Integer deptId, Integer pageNum, Integer pageSize) throws BizException;

    /**
     * 获取所有的智能监控规则
     *
     * @param deptId 组织机构ID
     * @return 智能监控规则列表
     * @throws BizException
     */
    ListData<FimWarnRuleDTO> list(Integer deptId) throws BizException;

    /**
     * 获取报警类型列表
     * @param deptId
     * @return
     */
    ListData<FimWarnRuleDTO> warnTypeList(Integer deptId);

    /**
     * 设置智能监控结果的消息通知人
     *
     * @param request 请求参数
     * @throws BizException
     */
    void setUser(FimSetUserParam request) throws BizException;


    /**
     * 获取报警类型列表
     *
     * @param deptId 组织机构ID
     * @return 报警类型列表
     * @throws BizException
     */
    List<FimAlgCheckTypeDTO> listAlgCheckType(Integer deptId) throws BizException;

    /**
     * 添加组织机构配置算法类型
     *
     * @param deptId 组织机构ID
     * @param types
     */
    void batchInsertAlgorithmType(Integer deptId, List<Integer> types) throws BizException;

    /**
     * 配置智能监控报警类型
     *
     * @param deptId 配置信息
     * @param list   配置信息
     * @throws BizException 业务异常
     */
    void algTypeConfigure(Integer deptId, List<FimAlgTypeDTO> list) throws BizException;

    /**
     * 移除删除算法类型
     *
     * @param deptId 算法类型
     * @param types  算法类型
     * @throws BizException
     */
    void removeAlgorithmByType(Integer deptId, List<Integer> types) throws BizException;

    /**
     * 下行同步项目算法配置类型
     *
     * @param deptId
     * @throws BizException
     */
    List<FimAlgTypeDTO> algTypeList(Integer deptId) throws BizException;

    /**
     * 更新项目算法配置
     *
     * @param deptId
     * @param types
     */
    void updateAlgorithmConf(Integer deptId, List<Integer> types) throws BizException;

}
