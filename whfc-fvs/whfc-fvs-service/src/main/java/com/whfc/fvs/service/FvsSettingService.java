package com.whfc.fvs.service;

import com.whfc.common.exception.BizException;
import com.whfc.fvs.dto.FvsSettingDTO;
import com.whfc.fvs.enums.VsPlatform;

import java.util.List;

/**
 * 视频监控配置
 *
 * <AUTHOR> qzexing
 * @version : 1.0
 * @date : 2020-07-25 17:26
 */
public interface FvsSettingService {

    /**
     * 获取 appKey appSecret
     *
     * @param deptId   组织机构ID
     * @param platform 云平台
     * @return appKey appSecret
     * @throws BizException 业务异常
     */
    FvsSettingDTO getAppKeySecret(Integer deptId, VsPlatform platform) throws BizException;

    /**
     * 获取配置列表
     *
     * @param deptId 组织机构ID
     * @return 配置列表
     * @throws BizException 业务异常
     */
    List<FvsSettingDTO> getConfigList(Integer deptId) throws BizException;

    /**
     * 添加配置
     *
     * @param deptId 组织机构ID
     * @param list   配置信息
     * @throws BizException 业务异常
     */
    void platformConfigure(Integer deptId, List<FvsSettingDTO> list) throws BizException;

    /**
     * 获取分屏配置
     *
     * @param deptId 组织机构ID
     * @return 默认平台
     * @throws BizException 业务异常
     */
    FvsSettingDTO getSplitview(Integer deptId) throws BizException;
}
