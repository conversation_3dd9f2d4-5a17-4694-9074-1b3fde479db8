package com.whfc.fvs.service;

import com.whfc.common.exception.BizException;
import com.whfc.common.result.ListData;
import com.whfc.common.result.PageData;
import com.whfc.fvs.dto.FimAiServerDTO;
import com.whfc.fvs.param.FimAiServerParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/18
 */
public interface FimAiServerService {

    /**
     * 获取AI服务器列表-分页
     *
     * @param deptId
     * @param keyword
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageData<FimAiServerDTO> page(Integer deptId, String keyword, Integer pageNum, Integer pageSize) throws BizException;

    /**
     * 获取AI服务器列表
     *
     * @param deptId
     * @param keyword
     * @return
     */
    ListData<FimAiServerDTO> list(Integer deptId, String keyword) throws BizException;

    /**
     * 添加AI服务器
     *
     * @param deptId
     * @param request
     */
    void add(Integer deptId, FimAiServerParam request) throws BizException;

    /**
     * 修改AI服务器
     *
     * @param request
     */
    void update(FimAiServerParam request) throws BizException;

    /**
     * 删除AI服务器
     *
     * @param guid
     */
    void del(String guid) throws BizException;

    /**
     * 根据token获取AI服务器
     *
     * @param deptId
     * @param platform
     * @param sn
     * @return
     */
    FimAiServerDTO getBySn(Integer deptId, String platform, String sn);
}
