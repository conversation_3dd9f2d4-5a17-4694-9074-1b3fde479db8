package com.whfc.fvs.service;

import com.whfc.common.exception.BizException;
import com.whfc.common.result.PageData;
import com.whfc.fvs.dto.FvsLogDTO;
import com.whfc.fvs.dto.FvsSnapshotConfigDTO;
import com.whfc.fvs.dto.FvsSnapshotDTO;
import com.whfc.fvs.param.FvsSnapshotConfigParam;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/9
 */
public interface FvsSnapshotService {

    /**
     * 获取抓拍列表
     *
     * @param deptId    组织机构ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param keyword   搜索关键字
     * @param pageNum   页码
     * @param pageSize  每页数量
     * @return 抓拍列表
     * @throws BizException 业务异常
     */
    PageData<FvsSnapshotDTO> snapshotList(Integer deptId, Date startDate, Date endDate, String keyword, Integer pageNum, Integer pageSize) throws BizException;

    /**
     * 获取抓拍列表
     *
     * @param deptId
     * @param startDate
     * @param endDate
     * @param deviceGuid
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageData<FvsSnapshotDTO> snapshotLog(Integer deptId, Date startDate, Date endDate, Integer groupId, String deviceGuid, Integer pageNum, Integer pageSize) throws BizException;

    /**
     * 视频日志
     *
     * @param deptId
     * @param date
     * @return
     */
    FvsLogDTO fvsLog(Integer deptId, Date date) throws BizException;

    /**
     * 抓拍时间配置信息
     *
     * @param deptId
     * @return
     */
    FvsSnapshotConfigDTO configInfo(Integer deptId) throws BizException;

    /**
     * 保存抓拍时间配置
     *
     * @param deptId
     * @param param
     */
    void saveConfig(Integer deptId, FvsSnapshotConfigParam param) throws BizException;

}
