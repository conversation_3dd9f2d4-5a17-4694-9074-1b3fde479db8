package com.whfc.fvs.service;

import com.whfc.common.exception.BizException;
import com.whfc.common.result.PageData;
import com.whfc.entity.dto.warn.AppWarnRuleType;
import com.whfc.fim.dto.FimResultDTO;
import com.whfc.fim.dto.FimResultRateContDTO;
import com.whfc.fim.dto.FimWarnAnalysisDTO;
import com.whfc.fim.dto.FimWarnNumDTO;
import com.whfc.fim.param.FimAlgResultDelParam;
import com.whfc.fim.param.FimResultBatchHandleParam;
import com.whfc.fim.param.FimResultEditParam;

import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @author: qzexing
 * @version: 1.0
 * @date: 2020/3/17 21:26
 */
public interface FimResultService {

    /**
     * 记录列表
     *
     * @param pageNum      页码
     * @param pageSize     每页数量
     * @param deptId       组织机构ID
     * @param handleStatus 操作状态
     * @param algCheckType 报警类型
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @return PageData<FimResultDTO>
     * @throws BizException
     */
    PageData<FimResultDTO> list(Integer deptId, Integer handleStatus, Integer algCheckType, Date startTime, Date endTime, Integer pageNum, Integer pageSize) throws BizException;

    /**
     * 获取报警记录详情
     *
     * @param resultId 报警记录ID
     * @return 报警记录
     * @throws BizException
     */
    FimResultDTO detail(Integer resultId) throws BizException;

    /**
     * 批量删除报警记录
     *
     * @param param
     * @throws BizException
     */
    void del(FimAlgResultDelParam param) throws BizException;

    /**
     * 处理报警记录
     *
     * @param editParam 处理报警记录参数
     * @throws BizException
     */
    void handle(FimResultEditParam editParam) throws BizException;

    /**
     * 批量处理报警记录
     *
     * @param param
     * @throws BizException
     */
    void batchHandle(FimResultBatchHandleParam param) throws BizException;

    /**
     * 时间内报警数
     *
     * @param deptIds
     * @param startTime
     * @param endTime
     * @return
     * @throws BizException
     */
    Integer getResultTotal(List<Integer> deptIds, Date startTime, Date endTime) throws BizException;

    /**
     * 智能监控报警统计
     *
     * @param deptId
     * @param handleStatus
     * @param startTime
     * @param endTime
     * @return
     * @throws BizException
     */
    List<FimResultRateContDTO> warnNumstatic(Integer deptId, Integer handleStatus, Date startTime, Date endTime) throws BizException;

    /**
     * 按小时分析智能监控报警
     *
     * @param deptId
     * @param startTime
     * @param endTime
     * @return
     * @throws BizException
     */
    FimWarnAnalysisDTO analysisByHour(Integer deptId, Date startTime, Date endTime) throws BizException;

    /**
     * 按天分析智能监控报警
     *
     * @param deptId
     * @param startDate
     * @param endDate
     * @return
     * @throws BizException
     */
    FimWarnAnalysisDTO analysisByDate(Integer deptId, Date startDate, Date endDate) throws BizException;

    /**
     * 获取报警数量
     *
     * @param deptId
     * @param algCheckType
     * @param startTime
     * @param endTime
     * @return
     * @throws BizException
     */
    FimWarnNumDTO getWarnNum(Integer deptId, Integer algCheckType, Date startTime, Date endTime) throws BizException;

    /**
     * 报警处理状态统计
     * @param deptId
     * @param algCheckType
     * @param startTime
     * @param endTime
     * @return
     */
    AppWarnRuleType warnHandleStateStat(Integer deptId, Integer algCheckType, Date startTime, Date endTime);
}
