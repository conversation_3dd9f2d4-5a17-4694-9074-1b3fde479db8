package com.whfc.fvs.service;

import com.whfc.common.exception.BizException;
import com.whfc.fvs.dto.FvsGroupDTO;
import com.whfc.fvs.param.FvsGroupAddParam;
import com.whfc.fvs.param.FvsGroupEditParam;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2020-12-08
 */
public interface FvsGroupService {

    /**
     * 分组列表
     *
     * @param deptId
     * @return
     * @throws BizException
     */
    List<FvsGroupDTO> list(Integer deptId) throws BizException;

    /**
     * 公司下全部分组列表
     *
     * @param deptIds
     * @return
     * @throws BizException
     */
    List<FvsGroupDTO> list(List<Integer> deptIds) throws BizException;

    /**
     * 新增分组
     *
     * @param param
     * @throws BizException
     */
    void add(FvsGroupAddParam param) throws BizException;

    /**
     * 编辑分组
     *
     * @param param
     * @throws BizException
     */
    void edit(FvsGroupEditParam param) throws BizException;

    /**
     * 删除分组
     *
     * @param groupId
     * @throws BizException
     */
    void del(Integer groupId) throws BizException;

    /**
     * 查找视频设备的分组树
     *
     * @param deptId
     * @param keyword
     * @param showHidden
     * @return
     * @throws BizException
     */
    List<FvsGroupDTO> tree(Integer deptId, Integer userId, Boolean showHidden, String keyword) throws BizException;

}
