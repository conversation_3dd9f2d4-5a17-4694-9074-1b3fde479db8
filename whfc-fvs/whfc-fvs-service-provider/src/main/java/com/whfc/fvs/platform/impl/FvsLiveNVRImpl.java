package com.whfc.fvs.platform.impl;

import com.whfc.common.enums.I18nErrorCode;
import com.whfc.common.exception.BizException;
import com.whfc.common.result.ListData;
import com.whfc.common.result.ResultEnum;
import com.whfc.common.vs.livenvr.LiveNVRApi;
import com.whfc.common.vs.livenvr.entity.LiveQingChannelInfo;
import com.whfc.common.vs.livenvr.entity.LiveQingRecord;
import com.whfc.common.vs.livenvr.entity.LiveQingStream;
import com.whfc.fvs.dao.FvsDeviceMapper;
import com.whfc.fvs.dao.FvsSettingMapper;
import com.whfc.fvs.dto.FvsDeviceDTO;
import com.whfc.fvs.dto.FvsRecordDTO;
import com.whfc.fvs.dto.FvsSettingDTO;
import com.whfc.fvs.dto.FvsStreamDTO;
import com.whfc.fvs.entity.FvsDevice;
import com.whfc.fvs.enums.VsPlatform;
import com.whfc.fvs.param.FvsDeviceAddParam;
import com.whfc.fvs.platform.FvsPlatformConstant;
import com.whfc.fvs.platform.FvsPlatformService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * liveNVR 播放
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/6
 */
@Service(FvsPlatformConstant.LIVE_NVR_SERVICE)
public class FvsLiveNVRImpl extends FvsPlatformBaseService implements FvsPlatformService {

    @Autowired
    private FvsDeviceMapper fvsDeviceMapper;

    @Autowired
    private FvsSettingMapper fvsSettingMapper;


    @Override
    public void addDevice(FvsDeviceDTO fvsDeviceDTO, FvsDeviceAddParam deviceAddParam) {
        // 参数验证
        String deviceSerial = deviceAddParam.getUsername();
        if (StringUtils.isEmpty(deviceSerial)) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.FVS_BE_001.getCode());
        }
        // 更新设备信息
        FvsDevice fvsDevice = new FvsDevice();
        fvsDevice.setId(fvsDeviceDTO.getId());
        fvsDevice.setUsername(deviceSerial);
        fvsDeviceMapper.updateByPrimaryKeySelective(fvsDevice);
    }

    @Override
    public String getSnapshotUrl(FvsDeviceDTO fvsDeviceDTO) {
        LiveNVRApi liveNVRApi = getLiveNVRApi(fvsDeviceDTO.getDeptId());
        return liveNVRApi.getSnapshot(fvsDeviceDTO.getUsername());
    }

    @Override
    public FvsStreamDTO getStreamUrl(FvsDeviceDTO fvsDeviceDTO) {
        FvsSettingDTO fvsSettingDTO = fvsSettingMapper.selectAppKeySecret(fvsDeviceDTO.getDeptId(), VsPlatform.LIVE_NVR.getValue());
        if (fvsSettingDTO == null) {
            throw new BizException(ResultEnum.FAILURE.getCode(), "未配置LiveNVR相关配置");
        }
        LiveNVRApi liveNVRApi = new LiveNVRApi(fvsSettingDTO.getHost(),fvsSettingDTO.getHost(),fvsSettingDTO.getAppSecret());
        FvsStreamDTO fvsStream = new FvsStreamDTO();
        LiveQingStream streamInfo = liveNVRApi.getStreamUrl(fvsDeviceDTO.getUsername(), "HLS");
        if (streamInfo != null) {
            if (StringUtils.isNotBlank(fvsSettingDTO.getExt2())){
                fvsStream.setHls(streamInfo.getURL().replace(fvsSettingDTO.getExt1(), fvsSettingDTO.getExt2()));
            }else {
                fvsStream.setHls(streamInfo.getURL());
            }
        }
        return fvsStream;
    }

    public LiveQingChannelInfo getDeviceInfo(Integer deptId, String username) {
        LiveNVRApi liveNVRApi = getLiveNVRApi(deptId);
        return liveNVRApi.getChannelInfo(username);
    }

    @Override
    public ListData<FvsRecordDTO> getRecordList(FvsDeviceDTO fvsDeviceDTO, Date startTime, Date endTime) {
        List<FvsRecordDTO> list = new ArrayList<>();

        LiveNVRApi liveNVRApi = getLiveNVRApi(fvsDeviceDTO.getDeptId());

        List<LiveQingRecord> dayRecordList = liveNVRApi.getDayRecordList(fvsDeviceDTO.getUsername(), startTime);
        if (CollectionUtils.isEmpty(dayRecordList)) {
            return new ListData<>(list);
        }
        for (LiveQingRecord liveQingRecord : dayRecordList) {
            FvsRecordDTO recordDTO = new FvsRecordDTO();
            recordDTO.setRecordUrl(liveQingRecord.getPlayUrl());
            recordDTO.setStartTime(liveQingRecord.getStartTime());
            recordDTO.setEndTime(liveQingRecord.getEndTime());
            list.add(recordDTO);
        }
        return new ListData<>(list);
    }

    /**
     * 获取当前项目 LiveNVRApi
     *
     * @param deptId
     * @return
     */
    public LiveNVRApi getLiveNVRApi(Integer deptId) {
        FvsSettingDTO fvsSettingDTO = fvsSettingMapper.selectAppKeySecret(deptId, VsPlatform.LIVE_NVR.getValue());
        if (fvsSettingDTO == null) {
            throw new BizException(ResultEnum.FAILURE.getCode(), "未配置LiveNVR相关配置");
        }
        return new LiveNVRApi(fvsSettingDTO.getHost(), fvsSettingDTO.getAppKey(), fvsSettingDTO.getAppSecret());
    }
}
