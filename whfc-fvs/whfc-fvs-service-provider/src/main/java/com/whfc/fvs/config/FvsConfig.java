package com.whfc.fvs.config;

import com.whfc.common.third.xxljob.XxlJobApi;
import com.whfc.common.third.xxljob.XxlJobProperties;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> qzexing
 * @version : 1.0
 * @date : 2020-09-22 15:26
 */
@Configuration
public class FvsConfig {


    @Bean(name = "xxlJobProperties")
    @ConfigurationProperties(prefix = "xxl.job.admin")
    public XxlJobProperties getXxlJobProps() {
        return new XxlJobProperties();
    }

    @Bean(name = "xxlJobApi")
    public XxlJobApi getXxlJobApi(@Qualifier("xxlJobProperties") XxlJobProperties xxlJobProperties) {
        return new XxlJobApi(xxlJobProperties.getAddresses(), xxlJobProperties.getUsername(), xxlJobProperties.getPassword());
    }

}
