package com.whfc.fvs.manager;

import com.whfc.common.exception.BizException;
import com.whfc.common.redis.RedisConst;
import com.whfc.common.redis.RedisService;
import com.whfc.common.vs.imou.entity.ImouDeviceInfo;
import com.whfc.common.vs.livenvr.entity.LiveQingChannelInfo;
import com.whfc.fvs.dao.FvsDeviceMapper;
import com.whfc.fvs.dto.FvsDeviceDTO;
import com.whfc.fvs.enums.FvsDeviceStatus;
import com.whfc.fvs.enums.FvsDeviceType;
import com.whfc.fvs.enums.VsPlatform;
import com.whfc.fvs.platform.FvsPlatformService;
import com.whfc.fvs.platform.FvsPlatformServiceFactory;
import com.whfc.fvs.platform.impl.FvsEzvizVsServiceImpl;
import com.whfc.fvs.platform.impl.FvsImouVsServiceImpl;
import com.whfc.fvs.platform.impl.FvsLiveNVRImpl;
import com.xxl.job.core.context.XxlJobHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/4/15 14:46
 */
@Component
public class FvsStatusMgr {

    @Autowired
    private FvsPlatformServiceFactory platformServiceFactory;

    @Autowired
    private FvsDeviceMapper fvsDeviceMapper;

    @Autowired
    private RedisService redisService;

    public void updateDeviceState(VsPlatform platform) throws BizException {
        XxlJobHelper.log("更新设备状态, platform:{}", platform);
        List<FvsDeviceDTO> deviceList = fvsDeviceMapper.selectByPlatform(platform.getValue());
        FvsPlatformService platFromService = platformServiceFactory.getFvsPlatFromService(platform);
        // 存储在线设备ID
        List<Integer> onLineDeviceIdList = new ArrayList<>();
        // 存储离线线设备ID
        List<Integer> offLineDeviceIdList = new ArrayList<>();
        for (FvsDeviceDTO fvsDeviceDTO : deviceList) {
            try {
                if (FvsDeviceType.DVR.getValue().equals(fvsDeviceDTO.getDeviceType())) {
                    // 跳过硬盘录像机
                    continue;
                }
                FvsDeviceStatus deviceStatus = FvsDeviceStatus.OFFLINE;
                if (VsPlatform.HIK_EZVIZ.equals(platform)) {
                    FvsEzvizVsServiceImpl fvsEzvizVsService = (FvsEzvizVsServiceImpl) platFromService;
                    deviceStatus = fvsEzvizVsService.getDeviceStatus(fvsDeviceDTO.getDeptId(), fvsDeviceDTO.getUsername());
                } else if (VsPlatform.DAHUA_IMOU.equals(platform)) {
                    FvsImouVsServiceImpl fvsImouVsService = (FvsImouVsServiceImpl) platFromService;
                    ImouDeviceInfo deviceInfo = fvsImouVsService.getDeviceInfo(fvsDeviceDTO.getDeptId(),
                            fvsDeviceDTO.getUsername(), fvsDeviceDTO.getChannelNo());
                    if (deviceInfo != null && 1 == deviceInfo.getStatus()) {
                        deviceStatus = FvsDeviceStatus.ONLINE;
                    }
                } else if (VsPlatform.LIVE_NVR.equals(platform)) {
                    FvsLiveNVRImpl fvsLiveNVRService = (FvsLiveNVRImpl) platFromService;
                    LiveQingChannelInfo deviceInfo = fvsLiveNVRService.getDeviceInfo(fvsDeviceDTO.getDeptId(), fvsDeviceDTO.getUsername());
                    if (deviceInfo != null && "1".equals(deviceInfo.getOnLine())) {
                        deviceStatus = FvsDeviceStatus.ONLINE;
                    }
                }
                if (FvsDeviceStatus.ONLINE.equals(deviceStatus)) {
                    onLineDeviceIdList.add(fvsDeviceDTO.getId());
                } else {
                    offLineDeviceIdList.add(fvsDeviceDTO.getId());
                }
            } catch (Exception ex) {
                XxlJobHelper.handleFail("萤石云刷新视频设备在线状态任务失败 error:{}" + ex.getMessage());
            }
        }
        batchUpdateDeviceStatus(onLineDeviceIdList, FvsDeviceStatus.ONLINE);
        batchUpdateDeviceStatus(offLineDeviceIdList, FvsDeviceStatus.OFFLINE);
    }

    /**
     * 批量更新设备状态
     *
     * @param deviceIdList    设备ID列表
     * @param fvsDeviceStatus 设备状态
     */
    private void batchUpdateDeviceStatus(List<Integer> deviceIdList, FvsDeviceStatus fvsDeviceStatus) throws BizException {
        XxlJobHelper.log("批量更新设备状态, deviceIdList:{}, fvsDeviceStatus:{}", deviceIdList, fvsDeviceStatus);
        if (deviceIdList == null || deviceIdList.isEmpty()) {
            return;
        }
        fvsDeviceMapper.batchUpdateDeviceStatus(deviceIdList, fvsDeviceStatus.getValue());

        // 更新缓存
        for (Integer deviceId : deviceIdList) {
            if (fvsDeviceStatus.equals(FvsDeviceStatus.OFFLINE)) {
                redisService.delete(String.format(RedisConst.FVS_DEVICE_ONLINE, deviceId));
            } else {
                redisService.set(String.format(RedisConst.FVS_DEVICE_ONLINE, deviceId), "1", 30, TimeUnit.MINUTES);
            }
        }
    }
}
