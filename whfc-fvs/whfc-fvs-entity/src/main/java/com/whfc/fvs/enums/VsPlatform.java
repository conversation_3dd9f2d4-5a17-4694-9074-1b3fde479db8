package com.whfc.fvs.enums;

/**
 * @Description: 视频监控云厂商
 * @author: qzexing
 * @version: 1.0
 * @date: 2020/3/5 14:08
 */
public enum VsPlatform {

    /**
     * 海康萤石
     */
    HIK_EZVIZ(0, "海康萤石"),

    /**
     * 大华乐橙
     */
    DAHUA_IMOU(1, "大华乐橙"),

    /**
     * 阿里云RTMP
     */
    @Deprecated
    ALIYUN_RTMP(2, "阿里云RTMP"),

    /**
     * 阿里云GB28181
     */
    @Deprecated
    ALIYUN_GB28181(3, "阿里云GB28181"),

    /**
     * 直播地址播放
     */
    LIVE_URL_PLAY(4, "直播地址播放"),

    /**
     * 自定义流媒体服务器
     */
    @Deprecated
    STREAM_SERVER(5, "自定义流媒体服务器"),

    /**
     * 海康萤石GB28181
     */
    EZVIZ_GB28181(6, "海康萤石GB28181"),

    /**
     * 本地监控播放
     */
    LOCAL_VS(7, "本地监控播放"),

    /**
     * 海康综合安防管理平台
     */
    HK_ISC(8, "海康综合安防管理平台"),

    /**
     * LiveNVR
     */
    LIVE_NVR(9, "LiveNVR"),

    ;

    private final Integer value;

    private final String desc;

    VsPlatform(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static VsPlatform parseValue(Integer value) {
        VsPlatform[] platforms = VsPlatform.values();
        for (VsPlatform platform : platforms) {
            if (platform.getValue().equals(value)) {
                return platform;
            }
        }
        return null;
    }

}
