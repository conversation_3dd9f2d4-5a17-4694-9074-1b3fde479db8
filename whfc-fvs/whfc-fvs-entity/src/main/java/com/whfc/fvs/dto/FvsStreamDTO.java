package com.whfc.fvs.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@Schema(description = "视频监控-直播流")
public class FvsStreamDTO implements Serializable {

    @JsonIgnore
    private Integer id;

    /**
     * 设备ID
     */
    @Schema(description = "设备ID")
    private Integer deviceId;

    /**
     * 设备名称
     */
    @Schema(description = "设备名称")
    private String name;

    /**
     * 视频直播地址 hls
     */
    @Schema(description = "视频直播地址 hls")
    private String hls;

    /**
     * 视频直播地址 hls 高清
     */
    @Schema(description = "视频直播地址 hls 高清")
    private String hlsHd;

    /**
     * 视频直播地址 rtmp
     */
    @Schema(description = "视频直播地址 rtmp")
    private String rtmp;

    /**
     * 视频直播地址 rtmp 高清
     */
    @Schema(description = "视频直播地址 rtmp 高清")
    private String rtmpHd;

    /**
     * 视频直播地址  rtsp
     */
    @Schema(description = "视频直播地址 rtsp")
    private String rtsp;

    /**
     * 视频直播地址  rtsp 高清
     */
    @Schema(description = "视频直播地址 rtsp 高清")
    private String rtspHd;

    /**
     * 播放地址
     */
    @Schema(description = "播放地址")
    private String playUrl;

    /**
     * 视频直播地址 ezopen
     */
    @Schema(description = "视频直播地址 ezopen")
    private String ezopen;

    /**
     * 视频直播地址 ezopen 高清
     */
    @Schema(description = "视频直播地址 ezopen 高清")
    private String ezopenHd;

    /**
     * 萤石云token
     */
    @Schema(description = "萤石云token")
    private String ezvizToken;

    /**
     * 萤石云appkey
     */
    @Schema(description = "萤石云appkey")
    private String ezvizAppKey;

    /**
     * 过期时间
     */
    @Schema(description = "过期时间")
    private Date expireTime;

    /**
     * 云台支持  0-不支持  1-支持
     */
    @Schema(description = "云台支持  0-不支持  1-支持")
    private Integer ptz;

    /**
     * 经度
     */
    @Schema(description = "经度")
    private Double lng;

    /**
     * 纬度
     */
    @Schema(description = "纬度")
    private Double lat;

    /**
     * 地址
     */
    @Schema(description = "地址")
    private String location;
}
