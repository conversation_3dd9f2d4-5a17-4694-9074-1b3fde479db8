package com.whfc.service;

import com.whfc.emp.param.AppEmpListExportParam;
import com.whfc.emp.service.AppEmpService;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @ClasssName AppEmpServiceTest
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/1/18 14:55
 * @Version 1.0
 */
public class AppEmpServiceTest {

    @Autowired
    private AppEmpService appEmpService;

    @Before
    public void setUp() {
        ReferenceConfig<AppEmpService> referenceConfig = new ReferenceConfig<>();
        referenceConfig.setApplication(new ApplicationConfig("consumer-test"));
        referenceConfig.setRegistry(new RegistryConfig("nacos://127.0.0.1:8848"));
        referenceConfig.setInterface(AppEmpService.class);
        referenceConfig.setVersion("1.0.0");
        referenceConfig.setRetries(0);
        appEmpService = referenceConfig.get();
    }

    @Test
    public void export(){
        AppEmpListExportParam appEmpListExportParam = new AppEmpListExportParam();
        appEmpListExportParam.setDeptId(2);
        System.out.println(appEmpService.export(appEmpListExportParam));
    }

    @Test
    public void getEmpQr(){
        System.out.println(appEmpService.getEmpQr(1));
    }

    @Test
    public void detail(){
        System.out.println(appEmpService.detail(937));
    }
}
