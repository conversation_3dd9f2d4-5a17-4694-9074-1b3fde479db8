package com.whfc.service;

import com.alibaba.fastjson.JSONObject;
import com.whfc.emp.param.AppFaceGateAddParam;
import com.whfc.emp.param.AppFaceGateGrantEmpParam;
import com.whfc.emp.service.AppFaceGateService;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;

/**
 * @ClasssName AppFaceGateServiceTest
 * <AUTHOR>
 * @Date 2021/1/20 14:47
 * @Version 1.0
 */
public class AppFaceGateServiceTest {

    @Autowired
    private AppFaceGateService appFaceGateService;

    @Before
    public void setUp() {
        ReferenceConfig<AppFaceGateService> referenceConfig = new ReferenceConfig<>();
        referenceConfig.setApplication(new ApplicationConfig("consumer-test"));
        referenceConfig.setRegistry(new RegistryConfig("nacos://127.0.0.1:8848"));
        referenceConfig.setInterface(AppFaceGateService.class);
        referenceConfig.setVersion("1.0.0");
        referenceConfig.setRetries(0);
        appFaceGateService = referenceConfig.get();
    }

    @Test
    public void faceGateRevokeEmp() {
        AppFaceGateGrantEmpParam appFaceGateGrantEmpParam = new AppFaceGateGrantEmpParam();
        appFaceGateGrantEmpParam.setFaceGateId(371);
        appFaceGateGrantEmpParam.setEmpIdList(Arrays.asList(new Integer[]{9080}));
        appFaceGateService.faceGateRevokeEmp(appFaceGateGrantEmpParam);
    }

    @Test
    public void del() {
        appFaceGateService.del(355);
    }

    @Test
    public void xiaomiaoAdd() {
        AppFaceGateAddParam appFaceGateAddParam = new AppFaceGateAddParam();
        appFaceGateAddParam.setDeptId(2);
        appFaceGateAddParam.setDeviceKey("dd32fcebf1d68b1f");
        appFaceGateAddParam.setName("公司大门闸机（进）(别删)");
//        appFaceGateAddParam.setPlatform("wheatsunshine");
        appFaceGateAddParam.setDirection(1);
        appFaceGateService.add(appFaceGateAddParam);
    }

    @Test
    public void wotuAdd() {
        AppFaceGateAddParam appFaceGateAddParam = new AppFaceGateAddParam();
        appFaceGateAddParam.setDeptId(2);
        appFaceGateAddParam.setDeviceKey("84E0F422348306B2");
        appFaceGateAddParam.setName("公司大门闸机（出）(别删)");
//        appFaceGateAddParam.setPlatform("wotu");
        appFaceGateAddParam.setDirection(1);
        appFaceGateService.add(appFaceGateAddParam);
    }

    @Test
    public void woAdd() {
        AppFaceGateAddParam appFaceGateAddParam = new AppFaceGateAddParam();
        appFaceGateAddParam.setDeptId(2);
        appFaceGateAddParam.setDeviceKey("84E0F42668581502");
        appFaceGateAddParam.setName("公司大门闸机_出_别删");
        appFaceGateAddParam.setPlatform("wo");
        appFaceGateAddParam.setDirection(0);
        appFaceGateService.add(appFaceGateAddParam);
    }

    @Test
    public void offlineAdd() {
        AppFaceGateAddParam appFaceGateAddParam = new AppFaceGateAddParam();
        appFaceGateAddParam.setDeptId(2);
        appFaceGateAddParam.setDeviceKey("84E0F42668581502");
        appFaceGateAddParam.setName("公司大门闸机(离线版)");
        appFaceGateAddParam.setPlatform("yfoffline");
        appFaceGateAddParam.setDirection(0);
        appFaceGateService.add(appFaceGateAddParam);
    }


    @Test
    public void faceGateGrantEmp() {
        AppFaceGateGrantEmpParam appFaceGateGrantEmpParam = new AppFaceGateGrantEmpParam();
        appFaceGateGrantEmpParam.setFaceGateId(374);
        appFaceGateGrantEmpParam.setEmpIdList(Arrays.asList(new Integer[]{9095, 926}));
        appFaceGateService.faceGateGrantEmp(appFaceGateGrantEmpParam);
    }

    @Test
    public void failEmpList() {
        System.out.println(JSONObject.toJSONString(appFaceGateService.failEmpList(371, null, null, 1, 10)));
    }
}
