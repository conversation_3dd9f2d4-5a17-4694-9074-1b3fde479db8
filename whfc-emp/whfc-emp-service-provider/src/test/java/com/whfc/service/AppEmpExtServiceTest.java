package com.whfc.service;

import com.alibaba.fastjson.JSONObject;
import com.whfc.emp.param.AppContractAddParam;
import com.whfc.emp.param.AppContractEditParam;
import com.whfc.emp.service.AppEmpExtService;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.junit.Before;
import org.junit.Test;

/**
 * @Description 人员扩展信息测试
 * <AUTHOR>
 * @Date 2021/1/15 15:17
 * @Version 1.0
 */
public class AppEmpExtServiceTest {

    private AppEmpExtService service;

    @Before
    public void setUp() {
        ReferenceConfig<AppEmpExtService> referenceConfig = new ReferenceConfig<>();
        referenceConfig.setApplication(new ApplicationConfig("consumer-test"));
        referenceConfig.setRegistry(new RegistryConfig("nacos://127.0.0.1:8848"));
        referenceConfig.setInterface(AppEmpExtService.class);
        referenceConfig.setVersion("1.0.0");
        referenceConfig.setRetries(0);
        service = referenceConfig.get();
    }

    @Test
    public void list() {
        String str = JSONObject.toJSONString(service.contractList(9084));
        System.out.println(str);
    }

    @Test
    public void add() {
        String json = "{\"empId\":9084,\"state\":0,\"contractNo\":\"1231233\",\"contactType\":0,\"startDate\":\"2021-01-14\",\"endDate\":\"2021-01-21\",\"payType\":1,\"salary\":\"100\",\"fileAttachList\":[]}";
        AppContractAddParam appContractAddParam = JSONObject.parseObject(json, AppContractAddParam.class);
        service.addContract(appContractAddParam);
    }

    @Test
    public void edit() {
        String json = "{\"id\":14,\"state\":1,\"contractNo\":\"123123123123\",\"contactType\":1,\"startDate\":\"2021-01-06\",\"endDate\":\"2021-02-28\",\"payType\":2,\"salary\":25000,\"fileAttachList\":[]}";
        AppContractEditParam appContractEditParam = JSONObject.parseObject(json, AppContractEditParam.class);
        service.editContract(appContractEditParam);
    }

    @Test
    public void del() {
        service.delContract(14);
    }

}
