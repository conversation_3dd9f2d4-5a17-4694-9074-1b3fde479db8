package com.whfc.service;

import com.alibaba.fastjson.JSONObject;
import com.whfc.common.util.DateUtil;
import com.whfc.emp.service.AppEmpWarnService;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.Date;

/**
 * @ClasssName AppEmpWarnServiceTest
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/1/18 9:18
 * @Version 1.0
 */
public class AppEmpWarnServiceTest {

    @Autowired
    private AppEmpWarnService appEmpWarnService;

    @Before
    public void setUp() {
        ReferenceConfig<AppEmpWarnService> referenceConfig = new ReferenceConfig<>();
        referenceConfig.setApplication(new ApplicationConfig("consumer-test"));
        referenceConfig.setRegistry(new RegistryConfig("nacos://127.0.0.1:8848"));
        referenceConfig.setInterface(AppEmpWarnService.class);
        referenceConfig.setVersion("1.0.0");
        referenceConfig.setRetries(0);
        appEmpWarnService = referenceConfig.get();
    }

    @Test
    public void getWarnNum() {
        Date endTime = new Date();
        Date startTime = DateUtil.addHours(endTime, -24);
        System.out.println(appEmpWarnService.getWarnNum(Arrays.asList(2), startTime, endTime));
    }
    @Test
    public void empWarnNum(){
        Date endTime = new Date();
        Date startTime = DateUtil.addHours(endTime, -24);
        System.out.println(JSONObject.toJSONString(appEmpWarnService.empWarnNum(2, 0,startTime, endTime)));
    }
}
