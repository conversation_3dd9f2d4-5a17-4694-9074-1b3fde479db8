package com.whfc.service;

import com.alibaba.fastjson.JSONObject;
import com.whfc.common.util.DateUtil;
import com.whfc.emp.service.AppEmpBoardService;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.Date;

/**
 * @ClasssName AppEmpBoardServiceTest
 * @Description 大屏服务测试
 * <AUTHOR>
 * @Date 2021/1/15 15:31
 * @Version 1.0
 */
public class AppEmpBoardServiceTest {

    @Autowired
    private AppEmpBoardService appEmpBoardService;

    @Before
    public void setUp() {
        ReferenceConfig<AppEmpBoardService> referenceConfig = new ReferenceConfig<>();
        referenceConfig.setApplication(new ApplicationConfig("consumer-test"));
        referenceConfig.setRegistry(new RegistryConfig("nacos://127.0.0.1:8848"));
        referenceConfig.setInterface(AppEmpBoardService.class);
        referenceConfig.setVersion("1.0.0");
        referenceConfig.setRetries(0);
        appEmpBoardService = referenceConfig.get();
    }

    @Test
    public void getEmpData() {
        String str = JSONObject.toJSONString(appEmpBoardService.getEmpData(2));
        System.out.println(str);
    }

    @Test
    public void getEmpNum() {
        System.out.println(appEmpBoardService.getEmpNum(Arrays.asList(2)));
    }

    @Test
    public void getGisEmpData() {
        String str = JSONObject.toJSONString(appEmpBoardService.getGisEmpData(2));
        System.out.println(str);
    }

    @Test
    public void getAttendData() {
        Date date = new Date();
        Date date2 = DateUtil.addDays(date, -6);
        String str = JSONObject.toJSONString(appEmpBoardService.getAttendData(2, date, date2));
        System.out.println(str);
    }
}
