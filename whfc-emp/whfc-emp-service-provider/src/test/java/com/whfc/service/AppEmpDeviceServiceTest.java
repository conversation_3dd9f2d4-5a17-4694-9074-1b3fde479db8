package com.whfc.service;

import com.alibaba.fastjson.JSONObject;
import com.whfc.emp.dto.AppDeviceCardLogDTO;
import com.whfc.emp.service.AppEmpDeviceService;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.junit.Before;
import org.junit.Test;

import java.util.Date;
import java.util.List;

/**
 * @ClasssName AppHelmetServiceTest
 * <AUTHOR>
 * @Date 2021/1/21 11:49
 * @Version 1.0
 */
public class AppEmpDeviceServiceTest {

    private AppEmpDeviceService service;

    @Before
    public void setUp() {
        ReferenceConfig<AppEmpDeviceService> referenceConfig = new ReferenceConfig<>();
        referenceConfig.setApplication(new ApplicationConfig("consumer-test"));
        referenceConfig.setRegistry(new RegistryConfig("nacos://127.0.0.1:8848"));
        referenceConfig.setInterface(AppEmpDeviceService.class);
        referenceConfig.setVersion("1.0.0");
        referenceConfig.setRetries(0);
        referenceConfig.setTimeout(60 * 1000);
        service = referenceConfig.get();
    }

    @Test
    public void test1() {
        Integer empId = 69546;
        Date date = new Date();
        AppDeviceCardLogDTO data = service.healthData(empId, date);
        System.out.println(JSONObject.toJSONString(data));
    }

    @Test
    public void test2() {
        Integer empId = 69546;
        Date date = new Date();
        List<AppDeviceCardLogDTO> data = service.healthLog(empId, date, null);
        System.out.println(JSONObject.toJSONString(data));
    }

}
