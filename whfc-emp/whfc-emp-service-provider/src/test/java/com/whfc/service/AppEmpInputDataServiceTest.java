package com.whfc.service;

import com.alibaba.fastjson.JSONObject;
import com.whfc.emp.param.AppEmpInputDataAddParam;
import com.whfc.emp.service.AppEmpInputDataService;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @ClasssName AppEmpInputDataServiceTest
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/1/18 11:29
 * @Version 1.0
 */
public class AppEmpInputDataServiceTest {


    @Autowired
    private AppEmpInputDataService appEmpInputDataService;

    @Before
    public void setUp() {
        ReferenceConfig<AppEmpInputDataService> referenceConfig = new ReferenceConfig<>();
        referenceConfig.setApplication(new ApplicationConfig("consumer-test"));
        referenceConfig.setRegistry(new RegistryConfig("nacos://127.0.0.1:8848"));
        referenceConfig.setInterface(AppEmpInputDataService.class);
        referenceConfig.setVersion("1.0.0");
        referenceConfig.setRetries(0);
        appEmpInputDataService = referenceConfig.get();
    }

    @Test
    public void add(){
        String str = "{\"empName\":\"管理2\",\"phone\":\"17702771992\",\"direction\":1,\"time\":\"2021-01-21 00:29:00\",\"deptId\":108}";
        AppEmpInputDataAddParam appEmpInputDataAddParam = JSONObject.parseObject(str,AppEmpInputDataAddParam.class);
        appEmpInputDataAddParam.setType(3);
        appEmpInputDataService.add(appEmpInputDataAddParam);
    }

}
