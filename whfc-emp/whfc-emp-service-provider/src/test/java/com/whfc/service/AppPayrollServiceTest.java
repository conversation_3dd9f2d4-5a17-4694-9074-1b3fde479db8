package com.whfc.service;

import com.alibaba.fastjson.JSONObject;
import com.whfc.emp.service.AppPayrollService;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @ClasssName AppPayrollServiceTest
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/1/18 16:07
 * @Version 1.0
 */
public class AppPayrollServiceTest {

    @Autowired
    private AppPayrollService appPayrollService;

    @Before
    public void setUp() {
        ReferenceConfig<AppPayrollService> referenceConfig = new ReferenceConfig<>();
        referenceConfig.setApplication(new ApplicationConfig("consumer-test"));
        referenceConfig.setRegistry(new RegistryConfig("nacos://127.0.0.1:8848"));
        referenceConfig.setInterface(AppPayrollService.class);
        referenceConfig.setVersion("1.0.0");
        referenceConfig.setRetries(0);
        appPayrollService = referenceConfig.get();
    }

    @Test
    public void export() {
        System.out.println(appPayrollService.export(146));
    }

    @Test
    public void empPayrollList() {
        System.out.println(JSONObject.toJSONString(appPayrollService.empPayrollList(133, 1, 10)));
    }
}
