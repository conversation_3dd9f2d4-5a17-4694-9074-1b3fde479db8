package com.whfc.service;

import com.whfc.emp.param.EmpMonthReportExportParam;
import com.whfc.emp.service.AppEmpAttendService;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

/**
 * @ClasssName AppEmpAttendServiceTest
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/1/18 15:17
 * @Version 1.0
 */
public class AppEmpAttendServiceTest {

    @Autowired
    private AppEmpAttendService AppEmpAttendService;

    @Before
    public void setUp() {
        ReferenceConfig<AppEmpAttendService> referenceConfig = new ReferenceConfig<>();
        referenceConfig.setApplication(new ApplicationConfig("consumer-test"));
        referenceConfig.setRegistry(new RegistryConfig("nacos://127.0.0.1:8848"));
        referenceConfig.setInterface(AppEmpAttendService.class);
        referenceConfig.setVersion("1.0.0");
        referenceConfig.setRetries(0);
        AppEmpAttendService = referenceConfig.get();
    }

    @Test
    public void exportMonthReport() {
        EmpMonthReportExportParam request = new EmpMonthReportExportParam();
        request.setDeptId(2);
        request.setMonth(new Date());
        System.out.println(AppEmpAttendService.exportMonthReport(request));
    }
}
