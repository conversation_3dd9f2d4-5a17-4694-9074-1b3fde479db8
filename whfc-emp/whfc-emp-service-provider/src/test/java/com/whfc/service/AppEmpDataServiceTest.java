package com.whfc.service;

import com.alibaba.fastjson.JSONObject;
import com.whfc.emp.service.AppEmpDataService;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.junit.Before;
import org.junit.Test;

/**
 * @ClasssName AppEmpIndexServiceTest
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/3/15 11:13
 * @Version 1.0
 */
public class AppEmpDataServiceTest {

    private AppEmpDataService appEmpDataService;

    @Before
    public void setUp() {
        ReferenceConfig<AppEmpDataService> referenceConfig = new ReferenceConfig<>();
        referenceConfig.setApplication(new ApplicationConfig("consumer-test"));
        referenceConfig.setRegistry(new RegistryConfig("nacos://127.0.0.1:8848"));
        referenceConfig.setInterface(AppEmpDataService.class);
        referenceConfig.setVersion("1.0.0");
        referenceConfig.setRetries(0);
        appEmpDataService = referenceConfig.get();
    }

    @Test
    public void getMapEmpList(){
        Long st = System.currentTimeMillis();
        System.out.println(JSONObject.toJSONString(appEmpDataService.getMapEmpList(130,null,null)));
        Long en = System.currentTimeMillis();
        System.out.println(en-st);
    }
}
