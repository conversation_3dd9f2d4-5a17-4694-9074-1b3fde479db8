package com.whfc.service;

import com.alibaba.fastjson.JSONObject;
import com.whfc.emp.param.AppEmpBlackAddParam;
import com.whfc.emp.service.AppEmpExtService;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.junit.Before;
import org.junit.Test;

/**
 * @ClasssName AppBlackServiceTest
 * @Description 黑名单管理单元测试
 * <AUTHOR>
 * @Date 2021/1/15 9:28
 * @Version 1.0
 */
public class AppBlackServiceTest {

    private AppEmpExtService service;

    @Before
    public void setUp() {
        ReferenceConfig<AppEmpExtService> referenceConfig = new ReferenceConfig<>();
        referenceConfig.setApplication(new ApplicationConfig("consumer-test"));
        referenceConfig.setRegistry(new RegistryConfig("nacos://127.0.0.1:8848"));
        referenceConfig.setInterface(AppEmpExtService.class);
        referenceConfig.setVersion("1.0.0");
        referenceConfig.setRetries(0);
        service = referenceConfig.get();
    }

    @Test
    public void blackList() {
        String str = JSONObject.toJSONString(service.blackList(2, 1, 10, null));
        System.out.println(str);
    }

    @Test
    public void blackAdd() {
        String json = "{\"empList\":[{\"deptId\":160,\"empId\":162,\"empName\":\"张红香\",\"idCardNo\":\"******************\"}],\"reason\":\"aa\"}";
        AppEmpBlackAddParam appEmpBlackAddParam = JSONObject.parseObject(json, AppEmpBlackAddParam.class);
        service.blackAdd(appEmpBlackAddParam);
    }

    @Test
    public void blackDel() {
        service.blackDel(1269);
    }
}
