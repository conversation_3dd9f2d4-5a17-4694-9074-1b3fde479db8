package com.whfc.service;

import com.alibaba.fastjson.JSONObject;
import com.whfc.emp.service.AppHelmetService;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @ClasssName AppHelmetServiceTest
 * <AUTHOR>
 * @Date 2021/1/21 11:49
 * @Version 1.0
 */
public class AppHelmetServiceTest {

    @Autowired
    private AppHelmetService appHelmetService;

    @Before
    public void setUp() {
        ReferenceConfig<AppHelmetService> referenceConfig = new ReferenceConfig<>();
        referenceConfig.setApplication(new ApplicationConfig("consumer-test"));
        referenceConfig.setRegistry(new RegistryConfig("nacos://127.0.0.1:8848"));
        referenceConfig.setInterface(AppHelmetService.class);
        referenceConfig.setVersion("1.0.0");
        referenceConfig.setRetries(0);
        referenceConfig.setTimeout(60 * 1000);
        appHelmetService = referenceConfig.get();
    }

    @Test
    public void helmetEmpList() {
        System.out.println(JSONObject.toJSONString(appHelmetService.helmetEmpList(2, 1, null)));
    }

}
