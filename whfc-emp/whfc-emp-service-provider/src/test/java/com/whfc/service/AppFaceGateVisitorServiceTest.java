package com.whfc.service;

import com.alibaba.fastjson.JSONObject;
import com.whfc.common.util.DateUtil;
import com.whfc.emp.param.AppFaceGateVisitorAddParam;
import com.whfc.emp.param.AppFaceGateVisitorCheckParam;
import com.whfc.emp.service.AppFaceGateVisitorService;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

/**
 * @ClasssName AppFaceGateVisitorServiceTest
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/2/26 15:33
 * @Version 1.0
 */
public class AppFaceGateVisitorServiceTest {

    @Autowired
    private AppFaceGateVisitorService appFaceGateVisitorService;

    @Before
    public void setUp() {
        ReferenceConfig<AppFaceGateVisitorService> referenceConfig = new ReferenceConfig<>();
        referenceConfig.setApplication(new ApplicationConfig("consumer-test"));
        referenceConfig.setRegistry(new RegistryConfig("nacos://127.0.0.1:8848"));
        referenceConfig.setInterface(AppFaceGateVisitorService.class);
        referenceConfig.setVersion("1.0.0");
        referenceConfig.setRetries(0);
        appFaceGateVisitorService = referenceConfig.get();
    }

    @Test
    public void add() {
        AppFaceGateVisitorAddParam param = new AppFaceGateVisitorAddParam();
        param.setFaceGateId(374);
        param.setName("何伟");
        param.setPhone("17702771994");
        param.setRemark("就想进去");
        appFaceGateVisitorService.add(param);
    }

    @Test
    public void list() {
        System.out.println(JSONObject.toJSONString(appFaceGateVisitorService.list(2, 10, 1, null, null, null)));
    }

    @Test
    public void check() {
        AppFaceGateVisitorCheckParam param = new AppFaceGateVisitorCheckParam();
        param.setCheckName("何伟");
        param.setCheckResult(2);
        param.setVisitorId(3);
        appFaceGateVisitorService.check(param);
    }

    @Test
    public void testExport() {
        Integer deptId = 2;
        Integer state = null;
        Date startTime = DateUtil.parseDate("2021-05-17", "yyyy-MM-dd");
        Date endTime = DateUtil.parseDate("2021-06-16", "yyyy-MM-dd");
        appFaceGateVisitorService.export(deptId, state, startTime, endTime);
    }
}
