package com.whfc.manager;

import com.whfc.common.util.DateUtil;
import com.whfc.common.util.JSONUtil;
import com.whfc.emp.dto.HelmetIconDTO;
import com.whfc.emp.manager.AppEmpDataManager;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2021-01-21 12:53
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class AppEmpDataManagerTest {

    @Autowired
    private AppEmpDataManager empDataManager;

    @Test
    public void testGetHelmetIcon() {
        HelmetIconDTO dto = empDataManager.getHelmetIcon("orange");
        System.out.println(JSONUtil.toPrettyString(dto));
    }

    @Test
    public void testGetHelmetDataLogByEmpId() {
        Integer empId = 9096;
        Date date = DateUtil.parseDate("2021-01-21", "yyyy-MM-dd");
        Date startTime = DateUtil.parseDate("2021-01-21 09:39:06", "yyyy-MM-dd HH:mm:ss");
        Date endTime = DateUtil.parseDate("2021-01-21 12:39:18", "yyyy-MM-dd HH:mm:ss");
        List<?> list = empDataManager.getHelmetDataLogByEmpId(empId, date, startTime, endTime);
        System.out.println(JSONUtil.toPrettyString(list));
    }




}
