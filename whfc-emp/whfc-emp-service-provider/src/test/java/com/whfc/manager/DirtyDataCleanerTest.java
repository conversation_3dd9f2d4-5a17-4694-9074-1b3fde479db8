package com.whfc.manager;

import com.whfc.common.util.DateUtil;
import com.whfc.emp.clean.DirtyDataCleaner;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;

/**
 * @Description: 脏数据清理
 * @author: xugcheng
 * @version: 1.0
 * @date: 2021-08-06 11:31
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class DirtyDataCleanerTest {

    @Autowired
    private DirtyDataCleaner cleaner;

    /**
     * 清理闸机识别记录脏数据
     */
    @Test
    public void testClearFacegateRecord() {
        Date startTime = DateUtil.parseDateTime("2021-07-07 00:00:00");
        Date endTime = DateUtil.parseDateTime("2021-08-07 23:59:59");
        cleaner.clearFacegateRecord(startTime, endTime);
    }

    /**
     * 清理人员考勤记录脏数据
     */
    @Test
    public void testCleanEmpAttendRecord() {
        Date startTime = DateUtil.parseDateTime("2021-08-07 00:00:00");
        Date endTime = DateUtil.parseDateTime("2021-08-07 23:59:59");
        cleaner.cleanEmpAttendRecord(startTime, endTime);
    }
}
