package com.whfc.manager;

import com.whfc.emp.dto.train.TrainPaperDTO;
import com.whfc.emp.dto.train.TrainRecordDTO;
import com.whfc.emp.dto.train.TrainRecordEmpDTO;
import com.whfc.emp.dto.train.TrainRecordFileDTO;
import com.whfc.emp.manager.TrainBoxManager;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-08-28 17:44
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class TrainBoxManagerTest {


    @Autowired
    private TrainBoxManager trainBoxManager;

    @Test
    public void addTrainRecordTest() {
        List<TrainRecordDTO> list = new ArrayList<>();
        TrainRecordDTO trainRecord = new TrainRecordDTO();
        trainRecord.setRecordName("重庆市宏怡劳务有限公司入场安全教育_20181007124656");
        trainRecord.setTrainType("入场(厂)培训");
        trainRecord.setTrainStartDate(new Date());
        trainRecord.setTrainContent("培训内容主要包括：|年龄要求|施工特点|权利和义务|施工现场基本要求|安全三宝|安全用电常识|现场文明施工一般要求|项目应急处置|现场防火基本要求|施工现场易发火灾场所|消防器材的使用|火灾逃生|施工现场的出血急救|施工现场的触电急救|施工现场的骨折急救|施工现场的中暑急救|高处作业基本要求|高处作业安全防护|起重吊装作业风险分析|起重吊装安全基本要求|吊索吊具检查|起重作业十不吊|高处坠落事故案例|坍塌事故案例|火灾事故案例|起重伤害事故案例|触电事故案例|");
        trainRecord.setTrainDescript("1.入场基本要求 - 年龄要求\r\n2.入场基本要求 - 施工特点\r\n3.入场基本要求 - 权利和义务\r\n4.入场基本要求 - 施工现场基本要求\r\n5.入场基本要求 - 安全三宝\r\n6.入场基本要求 - 安全用电常识\r\n7.入场基本要求 - 现场文明施工一般要求\r\n8.入场基本要求 - 项目应急处置\r\n9.施工现场消防常识 - 现场防火基本要求\r\n10.施工现场消防常识 - 施工现场易发火灾场所\r\n11.施工现场消防常识 - 消防器材的使用\r\n12.施工现场消防常识 - 火灾逃生\r\n13.应急急救常识 - 施工现场的出血急救\r\n14.应急急救常识 - 施工现场的触电急救\r\n15.应急急救常识 - 施工现场的骨折急救\r\n16.应急急救常识 - 施工现场的中暑急救\r\n17.高处作业 - 高处作业基本要求\r\n18.高处作业 - 高处作业安全防护\r\n19.起重吊装 - 起重吊装作业风险分析\r\n20.起重吊装 - 起重吊装安全基本要求\r\n21.起重吊装 - 吊索吊具检查\r\n22.起重吊装 - 起重作业十不吊\r\n23.建筑工程典型事故案例 - 高处坠落事故案例\r\n24.建筑工程典型事故案例 - 坍塌事故案例\r\n25.建筑工程典型事故案例 - 火灾事故案例\r\n26.建筑工程典型事故案例 - 起重伤害事故案例\r\n27.建筑工程典型事故案例 - 触电事故案例\r\n");
        trainRecord.setTrainPeriod(5);
        trainRecord.setPersonCount(28);
        trainRecord.setPassedCount(28);
        trainRecord.setDemandId("2064406912");
        trainRecord.setCourseCount(0);
        trainRecord.setCourseDuration(4430);
        trainRecord.setDeviceNo("ZGZT0407");
        trainRecord.setId("1691349460");
        list.add(trainRecord);
        trainBoxManager.addTrainRecord(list);
    }

    @Test
    public void addTrainEmpTest() {
        List<TrainRecordEmpDTO> list = new ArrayList<>();
        TrainRecordEmpDTO recordEmpDTO = new TrainRecordEmpDTO();
        recordEmpDTO.setEmpName("熊永启");
        recordEmpDTO.setIdentifyId("512324196701014877");
        recordEmpDTO.setPhoto("熊永启_512324196706154877.bmp");
        recordEmpDTO.setCategoryName("木工");
        recordEmpDTO.setRecordId("1691349460");
        recordEmpDTO.setScore(90);
        recordEmpDTO.setIsPass(1);
        list.add(recordEmpDTO);
        trainBoxManager.addTrainEmp(list);
    }


    @Test
    public void addTrainPaperTest() {
        List<TrainPaperDTO> list = new ArrayList<>();
        TrainPaperDTO trainPaperDTO = new TrainPaperDTO();
        trainPaperDTO.setId("106387790");
        trainPaperDTO.setRecordId("1691349460");
        trainPaperDTO.setExamNo("-63007291");
        trainPaperDTO.setGroupNo(1);
        trainPaperDTO.setCOrder(9);
        trainPaperDTO.setQsnAnswer("D");
        trainPaperDTO.setQsnCategory(0);
        trainPaperDTO.setQsnKind(1);
        trainPaperDTO.setQsnImportant(1);
        trainPaperDTO.setSource(0);
        trainPaperDTO.setVersion(0);
        trainPaperDTO.setContentId("E263A4EF180FE92209C1C9C159C0BB25");
        trainPaperDTO.setQsnContent("碗扣式模板支架立杆顶部应设可调支托，立杆上端（包括可调支托螺杆）伸出顶层水平杆的长度不得大于（     ）mm。|A，400|B，500|C，600|D，700");
        list.add(trainPaperDTO);
        trainBoxManager.addTrainPaper(list);
    }

    @Test
    public void addTrainFileTest() {
        List<TrainRecordFileDTO> list = new ArrayList<>();
        TrainRecordFileDTO recordFileDTO = new TrainRecordFileDTO();
        recordFileDTO.setRecordId("1691349460");
        recordFileDTO.setFileContent("http://10.36.1.139/STBMS.Web.2016/Upfile/TrainPhoto/6b3d20bc-85c2-4788-9feb-295885299176.jpg");
        recordFileDTO.setFileName("6b3d20bc-85c2-4788-9feb-295885299176.jpg");
        list.add(recordFileDTO);
        trainBoxManager.addTrainFile(list);
    }

    @Test
    public void handleFailedDataTest() {
        trainBoxManager.handleFailedData();
    }


}
