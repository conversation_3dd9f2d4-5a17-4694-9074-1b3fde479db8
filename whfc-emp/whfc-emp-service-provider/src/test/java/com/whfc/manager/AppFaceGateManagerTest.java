package com.whfc.manager;

import com.whfc.common.util.DateUtil;
import com.whfc.emp.manager.AppFaceGateManager;
import com.whfc.emp.param.EmpAttendSyncDataParam;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @ClasssName AppFaceGateManagerTest
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/1/26 16:15
 * @Version 1.0
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class AppFaceGateManagerTest {

    @Autowired
    private AppFaceGateManager appFaceGateManager;

    @Test
    public void handleRec() {
        EmpAttendSyncDataParam param = new EmpAttendSyncDataParam();
        param.setDirection(0);
        param.setEmpId(9100);
        param.setShowTime(DateUtil.parseDateTime("2021-01-26 16:34:00"));
        param.setEmpName("张三");
        param.setPicture("https://file.whfciot.com/ms/test/emp/img/2021012515393865106.png");
        param.setDeviceKey("84E0F422348306B2");
        param.setDeptId(2);
        appFaceGateManager.handleRec(param);
    }
}
