package com.whfc.redis;

import com.whfc.common.util.JSONUtil;
import com.whfc.emp.entity.AppIndoorPositionStation;
import com.whfc.emp.entity.AppIndoorPositionTag;
import com.whfc.emp.redis.IndoorPositionRedisDao;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2020/6/2 10:12
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class IndoorPositionRedisDaoTest {

    private Integer deptId = 2;

    @Autowired
    private IndoorPositionRedisDao redisDao;

    @Test
    public void testStation() {
        Integer stationId = 1;
        String guid = "test";
        AppIndoorPositionStation station = new AppIndoorPositionStation();
        station.setId(stationId);
        station.setDeptId(2);
        station.setGuid(guid);
        redisDao.setStation(guid, station);

        AppIndoorPositionStation station1 = redisDao.getStation(guid);
        System.out.println(JSONUtil.toString(station));
        System.out.println(JSONUtil.toString(station1));
    }

    @Test
    public void testTag() {
        Integer tagId = 1;
        String guid = "test";
        AppIndoorPositionTag tag = new AppIndoorPositionTag();
        tag.setId(tagId);
        tag.setGuid(guid);

        redisDao.setTag(deptId, guid, tag);
        AppIndoorPositionTag tag1 = redisDao.getTag(deptId, guid);

        System.out.println(JSONUtil.toString(tag));
        System.out.println(JSONUtil.toString(tag1));
    }
}
