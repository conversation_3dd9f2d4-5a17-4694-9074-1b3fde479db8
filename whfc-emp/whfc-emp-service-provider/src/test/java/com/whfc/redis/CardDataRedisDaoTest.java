package com.whfc.redis;

import com.whfc.common.util.DateUtil;
import com.whfc.common.util.JSONUtil;
import com.whfc.emp.dto.AppDeviceCardLogDTO;
import com.whfc.emp.redis.CardDataRedisDao;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2020/6/2 10:12
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class CardDataRedisDaoTest {

    @Autowired
    private CardDataRedisDao cardDataRedisDao;

    @Test
    public void testExists() {

        Integer empId = 1;
        Date date = new Date();

        boolean exists = cardDataRedisDao.exists(empId, date);
        System.out.println(exists);
    }

    @Test
    public void testAdd() {
        Integer empId = 1;
        Date date = new Date();

        List<AppDeviceCardLogDTO> logList = new ArrayList<>();
        for (int i = 10; i > 0; i--) {
            Date time = DateUtil.addMinutes(date, -i);
            AppDeviceCardLogDTO log = new AppDeviceCardLogDTO();
            log.setEmpId(String.valueOf(empId));
            log.setTime(time);
            log.setLng(115.933262 + 0.00001 * i);
            log.setLat(38.95742 + 0.00001 * i);
            logList.add(log);
        }
        cardDataRedisDao.addCardDataLog(empId, date, logList);
    }

    @Test
    public void testQuery() {
        Integer empId = 1;
        Date date = new Date();
        List<?> list = cardDataRedisDao.getCardDataLog(empId, date);
        System.out.println(JSONUtil.toPrettyString(list));
    }
}
