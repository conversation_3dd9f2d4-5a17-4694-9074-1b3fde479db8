package com.whfc.influx;

import com.whfc.common.result.PageData;
import com.whfc.common.util.DateUtil;
import com.whfc.common.util.JSONUtil;
import com.whfc.emp.dto.AppDeviceCardLogDTO;
import com.whfc.emp.entity.AppDeviceCardLog;
import com.whfc.emp.enums.PosState;
import com.whfc.emp.influx.AppDeviceCardLogDao;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2019/12/21 12:08
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class AppDeviceCardLogDaoTest {

    @Autowired
    private AppDeviceCardLogDao logDao;

    @Test
    public void testInsert1() {

        AppDeviceCardLog log = new AppDeviceCardLog();
        log.setTime(new Date());
        log.setEmpId(1);
        log.setDeviceId(1);
        log.setLng(114.4278567);
        log.setLat(30.4792367);
        log.setLngWgs84(114.427856);
        log.setLatWgs84(114.427856);
        log.setLocation("湖北省武汉市洪山区关南园路1");
        log.setPosState(PosState.VALID.getValue());
        log.setPosType(1);
        log.setPosMode(2);
        log.setBatterVolt(3.3);
        log.setBatteryPower(46);
        log.setAlarmValue(8);
        log.setAlarmStill(1);
        log.setAlarmCrash(0);
        log.setAlarmDrop(1);
        log.setAlarmDoff(1);
        log.setAlarmReport(1);
        log.setAlarmSos(0);

        log.setDelFlag(0);
        log.setCreateTime(new Date());
        logDao.insert(log);
    }

    @Test
    public void testSelectByEmpId() {
        Integer empId = 2;
        Date startDate = DateUtil.parseDateTime("2021-01-28 00:00:00");
        Date endDate = DateUtil.parseDateTime("2021-01-28 23:00:00");
        List<AppDeviceCardLogDTO> list = logDao.selectHelmetDataLogListByEmpId(empId, startDate, endDate);
        Assert.assertTrue(list.size() > 0);
        System.out.println(JSONUtil.toPrettyString(list));
    }

    @Test
    public void testSelectByDeviceId() {
        Integer deviceId = 1;
        Date startDate = DateUtil.parseDateTime("2021-01-28 00:00:00");
        Date endDate = DateUtil.parseDateTime("2021-01-28 23:00:00");
        PageData<AppDeviceCardLogDTO> data = logDao.selectHelmetDataLogListByDeviceId(deviceId, startDate, endDate, 1, 10);
        Assert.assertTrue(data.getTotal() > 0);
        System.out.println(JSONUtil.toPrettyString(data));
    }

    @Test
    public void testSelectHealthByEmpId() {
        Integer empId = 69546;
        Date endDate = new Date();
        Date startDate = DateUtil.addDays(endDate, -1);
        AppDeviceCardLogDTO data = logDao.selectHealthByEmpId(empId, startDate, endDate);
        System.out.println(JSONUtil.toPrettyString(data));
    }
}
