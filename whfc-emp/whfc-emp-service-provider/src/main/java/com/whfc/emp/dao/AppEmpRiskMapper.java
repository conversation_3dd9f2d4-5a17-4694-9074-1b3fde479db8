package com.whfc.emp.dao;

import com.whfc.emp.dto.AppEmpRiskDTO;
import com.whfc.emp.entity.AppEmpRisk;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
* <AUTHOR>
* @version 1.0
* @date 2021/9/28 18:15
*/
@Repository
public interface AppEmpRiskMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppEmpRisk record);

    int insertSelective(AppEmpRisk record);

    AppEmpRisk selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppEmpRisk record);

    int updateByPrimaryKey(AppEmpRisk record);

    /**
     * 查找风险告知书列表
     *
     * @param deptId 组织机构ID
     * @return 风险告知书列表
     */
    List<AppEmpRiskDTO> selectRiskList(@Param("deptId") Integer deptId);
}