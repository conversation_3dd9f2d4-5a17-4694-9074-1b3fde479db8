package com.whfc.emp.dao;

import com.whfc.emp.dto.AppIndoorPositionTagLogDTO;
import com.whfc.emp.entity.AppIndoorPositionTagLog;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface AppIndoorPositionTagLogMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppIndoorPositionTagLog record);

    int insertSelective(AppIndoorPositionTagLog record);

    AppIndoorPositionTagLog selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppIndoorPositionTagLog record);

    int updateByPrimaryKey(AppIndoorPositionTagLog record);

    List<AppIndoorPositionTagLogDTO> selectPositionLogList(@Param("tagId") Integer tagId,
                                                           @Param("startTime") Date startTime,
                                                           @Param("endTime") Date endTime);
}