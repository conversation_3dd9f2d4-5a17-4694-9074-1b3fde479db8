package com.whfc.emp.event;

import com.whfc.emp.entity.AppIndoorPositionTag;
import com.whfc.emp.third.EmpThirdSyncFactory;
import com.whfc.emp.third.EmpThirdSyncWapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 室内定位event监听器
 */
@Component
public class IndoorPositionEventListener implements ApplicationListener<IndoorPositionEvent> {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private EmpThirdSyncFactory empThirdSyncFactory;

    @Override
    public void onApplicationEvent(IndoorPositionEvent event) {
        AppIndoorPositionTag tag = event.getTag();
        if (tag == null) {
            return;
        }
        try {
            List<EmpThirdSyncWapper> wapperList = empThirdSyncFactory.sync(tag.getDeptId());
            for (EmpThirdSyncWapper wapper : wapperList) {
                wapper.getSync().syncEmpIndoorPosition(tag, wapper.getConfig());
            }
        } catch (Exception e) {
            logger.error("syncEmpIndoorPosition失败", e);
        }
    }
}
