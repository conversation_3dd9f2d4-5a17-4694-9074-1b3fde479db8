package com.whfc.emp.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.whfc.common.enums.I18nErrorCode;
import com.whfc.common.exception.BizException;
import com.whfc.common.result.PageData;
import com.whfc.common.result.ResultEnum;
import com.whfc.common.util.DateUtil;
import com.whfc.common.util.PageUtil;
import com.whfc.emp.dao.AppEmpAttendRecordMapper;
import com.whfc.emp.dao.AppEmpDayMapper;
import com.whfc.emp.dao.AppEmpMapper;
import com.whfc.emp.dto.AppEmpInputDataDTO;
import com.whfc.emp.entity.AppEmp;
import com.whfc.emp.entity.AppEmpAttendRecord;
import com.whfc.emp.entity.AppEmpDay;
import com.whfc.emp.enums.AttendState;
import com.whfc.emp.enums.AttendType;
import com.whfc.emp.enums.Direction;
import com.whfc.emp.manager.AppEmpDataManager;
import com.whfc.emp.param.AppEmpAttendImportParam;
import com.whfc.emp.param.AppEmpInputDataAddParam;
import com.whfc.emp.service.AppEmpInputDataService;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * @ClasssName AppEmpInputDataServiceImpl
 * @Description 打卡记录
 * <AUTHOR>
 * @Date 2020/12/23 10:12
 * @Version 1.0
 */
@DubboService(interfaceClass = AppEmpInputDataService.class, version = "1.0.0", timeout = 30 * 1000)
public class AppEmpInputDataServiceImpl implements AppEmpInputDataService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private AppEmpAttendRecordMapper appEmpAttendRecordMapper;

    @Autowired
    private AppEmpMapper appEmpMapper;

    @Autowired
    private AppEmpDayMapper appEmpDayMapper;

    @Autowired
    private AppEmpDataManager appEmpDataManager;

    @Override
    public PageData<AppEmpInputDataDTO> list(Integer deptId,
                                             Integer corpId,
                                             Integer groupId,
                                             Integer workTypeId,
                                             Integer pageNum,
                                             Integer pageSize,
                                             Date startTime,
                                             Date endTime,
                                             String keyword) {
        logger.info("查询打卡记录列表, deptId:{}, corpId:{}, groupId:{}, workTypeId:{}, pageNum:{}, pageSize:{}, startTime:{}, endTime:{}, keyword:{}",
                deptId, corpId, groupId, workTypeId, pageNum, pageSize, startTime, endTime, keyword);
        PageHelper.startPage(pageNum, pageSize);
        if (startTime == null) {
            startTime = DateUtil.getDateBegin(new Date());
        }
        if (endTime == null) {
            endTime = new Date();
        }
        PageHelper.startPage(pageNum, pageSize);
        List<AppEmpInputDataDTO> list = appEmpAttendRecordMapper.selectByDeptIdAndDate(deptId, corpId, groupId, workTypeId, startTime, endTime, keyword, null);
        return PageUtil.pageData(PageInfo.of(list));
    }

    @Override
    public List<AppEmpInputDataDTO> getEmpInputData(Integer deptId, Integer corpId, Integer groupId, Integer workTypeId, Date startTime, Date endTime, String keyword, AttendType attendType) throws BizException {
        Integer type = attendType == null ? null : attendType.getValue();
        return appEmpAttendRecordMapper.selectByDeptIdAndDate(deptId, corpId, groupId, workTypeId, startTime, endTime, keyword, type);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(AppEmpInputDataAddParam param) {
        logger.info("后台手动添加考勤数据:param:{}", param);
        Integer projectId = param.getDeptId();
        Integer direction = param.getDirection();
        String empName = param.getEmpName();
        String phone = param.getPhone();
        Date time = param.getTime();
        Integer type = param.getType();
        String photo = param.getPhoto();

        AppEmp appEmp = null;
        List<AppEmp> appEmpList = appEmpMapper.selectByProjectIdAndEmpName(projectId, empName);
        if (appEmpList.size() == 0) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_021.getCode());
        }
        // 只有一个匹配 or 手机号为空
        else if (appEmpList.size() == 1 || StringUtils.isEmpty(phone)) {
            appEmp = appEmpList.get(0);
        }
        // 多个匹配 and 如果手机号不为空
        else {
            appEmp = appEmpList.stream().filter(item -> phone.equals(item.getPhone())).findFirst().get();
        }
        if (appEmp == null) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_022.getCode());
        }

        // 初始化每日考勤信息
        Integer empId = appEmp.getId();
        AppEmpDay appEmpDay = this.appEmpDataManager.getEmpDay(empId, time);

        // 计算考勤状态
        if (Direction.OUT.getValue().equals(direction)) {
            if (appEmpDay.getStartTime() == null) {
                throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_023.getCode());
            }
            // 判断录入时间是否晚于数据库记录时间！如果晚于则用当前时间
            Date endTime = appEmpDay.getEndTime();
            if (endTime == null || time.after(endTime)) {
                appEmpDayMapper.updateEndTime(empId, time);
            }
        } else {
            Date startTime = appEmpDay.getStartTime();
            if (startTime == null || startTime.after(time)) {
                appEmpDayMapper.updateStartTime(empId, time);
                Date endTime = appEmpDay.getEndTime();
                if (endTime != null) {
                    appEmpDayMapper.updateEndTime(empId, endTime);
                }
            }
        }

        appEmpDayMapper.updateAttendStateAndLocaleState(empId, AttendState.ATTEND.getValue(), direction);

        // 考勤记录
        AppEmpAttendRecord record = new AppEmpAttendRecord();
        record.setType(type);
        record.setTime(time);
        record.setEmpId(empId);
        record.setDirection(direction);
        record.setDeptId(appEmp.getDeptId());
        record.setLat(param.getLat());
        record.setLng(param.getLng());
        record.setLocation(param.getLocation());
        record.setPhoto(photo);
        appEmpAttendRecordMapper.insertSelective(record);
    }

    @Override
    public void del(Integer id) {
        logger.info("删除考勤记录,id:{}", id);
        appEmpAttendRecordMapper.deleteLogicById(id);
    }

    @Override
    public void importData(Integer deptId, List<AppEmpAttendImportParam> importList) {
        logger.info("导入打卡记录,deptId:{},importList:{}", deptId, importList);
        if (CollectionUtils.isEmpty(importList)) {
            return;
        }
        for (AppEmpAttendImportParam appEmpAttendImportParam : importList) {
            // 查询用户
            String empName = appEmpAttendImportParam.getEmpName();
            String idCardNo = appEmpAttendImportParam.getIdCardNo();
            AppEmp appEmp = getAppEmp(deptId, empName, idCardNo);
            if (appEmp == null) {
                continue;
            }
            // 初始化每日考勤信息
            Integer empId = appEmp.getId();
            Date time = DateUtil.parseDate(appEmpAttendImportParam.getTime(), "yyyy/MM/dd HH:mm:ss");
            if (time == null) {
                logger.error("时间格式错误,time:{}", appEmpAttendImportParam.getTime());
                continue;
            }
            AppEmpDay appEmpDay = this.appEmpDataManager.getEmpDay(empId, time);
            Integer direction = Direction.parseDesc(appEmpAttendImportParam.getDirection()).getValue();
            // 计算考勤状态
            if (Direction.OUT.getValue().equals(direction)) {
                if (appEmpDay.getStartTime() == null) {
                    logger.error("用户没有开始打卡,empId:{}", empId);
                    continue;
                }
                // 判断录入时间是否晚于数据库记录时间！如果晚于则用当前时间
                Date endTime = appEmpDay.getEndTime();
                if (endTime == null || time.after(endTime)) {
                    appEmpDayMapper.updateEndTime(empId, time);
                }
            } else {
                Date startTime = appEmpDay.getStartTime();
                if (startTime == null || startTime.after(time)) {
                    appEmpDayMapper.updateStartTime(empId, time);
                    Date endTime = appEmpDay.getEndTime();
                    if (endTime != null) {
                        appEmpDayMapper.updateEndTime(empId, endTime);
                    }
                }
            }

            appEmpDayMapper.updateAttendStateAndLocaleState(empId, AttendState.ATTEND.getValue(), direction);

            // 考勤记录
            AppEmpAttendRecord record = new AppEmpAttendRecord();
            record.setType(AttendType.MS.getValue());
            record.setTime(time);
            record.setEmpId(empId);
            record.setDirection(direction);
            record.setDeptId(appEmp.getDeptId());
            appEmpAttendRecordMapper.insertSelective(record);
        }
    }

    /**
     * 查询人员信息
     *
     * @param deptId
     * @param empName
     * @param idCardNo
     * @return
     */
    private AppEmp getAppEmp(Integer deptId, String empName, String idCardNo) {
        List<AppEmp> appEmps = appEmpMapper.selectByProjectIdAndEmpName(deptId, empName);
        if (CollectionUtils.isEmpty(appEmps)) {
            if (StringUtils.isNotBlank(idCardNo)) {
                AppEmp appEmp = appEmpMapper.selectByDeptIdAndIdCardNo(deptId, idCardNo);
                if (appEmp != null) {
                    appEmps.add(appEmp);
                } else {
                    logger.info("用户不存在,empName:{},idCardNo:{}", empName, idCardNo);
                    return null;
                }
            } else {
                logger.info("用户不存在,empName:{}", empName);
                return null;
            }
        }
        AppEmp appEmp = appEmps.get(0);
        if (appEmps.size() > 1) {
            Optional<AppEmp> appEmpOpt = appEmps.stream().filter(emp -> emp.getIdCardNo().equals(idCardNo)).findFirst();
            if (!appEmpOpt.isPresent()) {
                logger.info("用户不存在,empName:{},idCardNo:{}", empName, idCardNo);
                return null;
            }
            appEmp = appEmpOpt.get();
        }
        return appEmp;
    }
}
