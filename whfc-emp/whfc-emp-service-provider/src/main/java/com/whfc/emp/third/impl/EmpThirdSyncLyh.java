package com.whfc.emp.third.impl;

import com.whfc.common.third.ldyjh.LdyjhApi;
import com.whfc.common.third.ldyjh.entity.LdyjhConst;
import com.whfc.common.util.DateUtil;
import com.whfc.emp.dao.AppEmpMapper;
import com.whfc.emp.entity.AppEmp;
import com.whfc.emp.entity.AppSync;
import com.whfc.emp.enums.Direction;
import com.whfc.emp.param.FaceGateRecordParam;
import com.whfc.emp.third.EmpThirdSync;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 人员信息-第三方同步-劳研惠
 *
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/5/7 17:40
 */
@Service
public class EmpThirdSyncLyh extends EmpThirdSyncBase implements EmpThirdSync {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private AppEmpMapper appEmpMapper;

    @Override
    public void syncEmpAttend(FaceGateRecordParam param, AppSync config) {
        String direction = Direction.IN.getValue().equals(param.getDirection()) ? LdyjhConst.IN : LdyjhConst.OUT;
        String datetime = DateUtil.formatDateTime(param.getShowTime());
        Integer deptId = param.getDeptId();
        String personGuid = param.getPersonGuid();
        try {
            logger.info("开始同步劳研惠考勤记录,{},{},{}", deptId, personGuid, datetime);
            AppEmp emp = appEmpMapper.selectByDeptIdAndEmpCode(deptId, personGuid);
            if (emp != null) {
                String idCardNo = emp.getIdCardNo();
                if (StringUtils.isNotEmpty(idCardNo)) {
                    LdyjhApi ldyjhApi = new LdyjhApi(config.getHost(), config.getAppKey(), config.getAppSecret(), config.getExt1());
                    ldyjhApi.insertAttend(idCardNo, direction, datetime);
                }
            }
        } catch (Exception e) {
            logger.error("开始同步劳研惠考勤记录失败，error:{}", e);
        }
    }
}
