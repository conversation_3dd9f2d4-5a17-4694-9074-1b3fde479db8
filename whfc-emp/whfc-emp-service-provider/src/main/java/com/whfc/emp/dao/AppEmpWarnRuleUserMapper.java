package com.whfc.emp.dao;

import com.whfc.emp.entity.AppEmpWarnRuleUser;
import com.whfc.entity.dto.msg.AppMsgToUserDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AppEmpWarnRuleUserMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppEmpWarnRuleUser record);

    int insertSelective(AppEmpWarnRuleUser record);

    AppEmpWarnRuleUser selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppEmpWarnRuleUser record);

    int updateByPrimaryKey(AppEmpWarnRuleUser record);

    /**
     * 使用报警规则id查询报警人员信息
     * @param ruleId
     * @return
     */
    List<AppMsgToUserDTO> selectByRuleId(Integer ruleId);

    /**
     * 软删除
     * @param ruleId
     */
    void deleteByRuleId(Integer ruleId);

    /**
     * 批量新增
     *
     * @param userIdList
     */
    void batchInsert(@Param("list") List<AppMsgToUserDTO> userIdList);
}