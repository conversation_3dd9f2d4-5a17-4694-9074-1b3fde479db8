package com.whfc.emp.config;

import com.whfc.common.entity.DefaultProperties;
import com.whfc.common.mqtt.MqttProperties;
import com.whfc.common.spring.AppContextUtil;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

/**
 * @Description: 人员模块配置文件导入
 * @author: xugcheng
 * @version: 1.0
 * @date: 2019/10/10 10:13
 */
@Configuration
public class EmpConfig {

    @Bean
    public AppContextUtil appContextUtil() {
        return new AppContextUtil();
    }

    @Bean
    public ThreadPoolTaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        taskExecutor.setThreadGroupName("emp-thg");
        taskExecutor.setThreadNamePrefix("emp-th");
        taskExecutor.setCorePoolSize(10);
        taskExecutor.setMaxPoolSize(50);
        taskExecutor.setQueueCapacity(1024);
        taskExecutor.setKeepAliveSeconds(30000);
        return taskExecutor;
    }

    @Bean(name = "mqttProperties")
    @ConfigurationProperties(prefix = "mqtt")
    public MqttProperties getMqttProperties() {
        return new MqttProperties();
    }

    @Bean
    @ConfigurationProperties("whfc.default")
    public DefaultProperties defaultProperties() {
        return new DefaultProperties();
    }
}
