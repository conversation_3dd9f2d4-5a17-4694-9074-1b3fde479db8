package com.whfc.emp.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.whfc.common.enums.BindFlag;
import com.whfc.common.enums.I18nErrorCode;
import com.whfc.common.enums.IndoorBindType;
import com.whfc.common.exception.BizException;
import com.whfc.common.result.PageData;
import com.whfc.common.result.ResultEnum;
import com.whfc.common.third.indoor.msg.LocationMsg;
import com.whfc.common.util.*;
import com.whfc.emp.dao.AppEmpMapper;
import com.whfc.emp.dao.AppIndoorPositionMapMapper;
import com.whfc.emp.dao.AppIndoorPositionStationMapper;
import com.whfc.emp.dao.AppIndoorPositionTagMapper;
import com.whfc.emp.dto.*;
import com.whfc.emp.dto.indoor.AppIndoorPositionSenceDTO;
import com.whfc.emp.entity.AppEmp;
import com.whfc.emp.entity.AppIndoorPositionMap;
import com.whfc.emp.entity.AppIndoorPositionStation;
import com.whfc.emp.entity.AppIndoorPositionTag;
import com.whfc.emp.indoor.IndoorPositionMgr;
import com.whfc.emp.influx.AppIndoorPositionTagLogDao;
import com.whfc.emp.param.indoor.*;
import com.whfc.emp.redis.IndoorPositionRedisDao;
import com.whfc.emp.service.AppIndoorPositionService;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/10/13 10:40
 */
@DubboService(interfaceClass = AppIndoorPositionService.class, version = "1.0.0", timeout = 120 * 1000)
public class AppIndoorPositionServiceImpl implements AppIndoorPositionService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private AppIndoorPositionMapMapper indoorPositionMapMapper;

    @Autowired
    private AppIndoorPositionStationMapper indoorPositionStationMapper;

    @Autowired
    private AppIndoorPositionTagMapper indoorPositionTagMapper;

    @Autowired
    private AppIndoorPositionTagLogDao indoorPositionTagLogDao;

    @Autowired
    private IndoorPositionMgr indoorPositionMgr;

    @Autowired
    private AppEmpMapper appEmpMapper;

    @Autowired
    private IndoorPositionRedisDao indoorPositionRedisDao;

    @Override
    public List<AppIndoorPositionMapDTO> getMapList(Integer deptId) throws BizException {
        return indoorPositionMapMapper.selectByDeptId(deptId);
    }

    @Override
    public PageData<AppIndoorPositionMapDTO> getMapList(Integer deptId, Integer pageNum, Integer pageSize) throws BizException {
        PageHelper.startPage(pageNum, pageSize);
        List<AppIndoorPositionMapDTO> list = indoorPositionMapMapper.selectByDeptId(deptId);
        PageHelper.clearPage();
        return PageUtil.pageData(PageInfo.of(list));
    }

    @Override
    public void addMap(AppIndoorPositionMapAdd param) throws BizException {
        Integer deptId = param.getDeptId();
        String name = param.getName();
        String imgUrl = param.getImgUrl();
        Double pixelWidth = param.getPixelWidth();
        Double pixelLength = param.getPixelLength();
        Double realWidth = param.getRealWidth();
        Double realLength = param.getRealLength();
        String guid = RandomUtil.getGuid();
        Integer areaId = param.getAreaId();
        String areaName = param.getAreaName();

        AppIndoorPositionMap map = new AppIndoorPositionMap();
        map.setDeptId(deptId);
        map.setName(name);
        map.setGuid(guid);
        map.setImgUrl(imgUrl);
        map.setPixelWidth(pixelWidth);
        map.setPixelLength(pixelLength);
        map.setRealWidth(realWidth);
        map.setRealLength(realLength);
        map.setAreaId(areaId);
        map.setAreaName(areaName);

        indoorPositionMapMapper.insertSelective(map);

        // 设置缓存
        indoorPositionRedisDao.setMap(map.getGuid(), map);
    }

    @Override
    public void editMap(AppIndoorPositionMapEdit param) throws BizException {
        String name = param.getName();
        String imgUrl = param.getImgUrl();
        Double pixelWidth = param.getPixelWidth();
        Double pixelLength = param.getPixelLength();
        Double realWidth = param.getRealWidth();
        Double realLength = param.getRealLength();
        String guid = param.getGuid();
        Integer areaId = param.getAreaId();
        String areaName = param.getAreaName();

        AppIndoorPositionMap map = indoorPositionMapMapper.selectByGuid(guid);
        if (map == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.EMP_BE_035.getCode());
        }
        map.setName(name);
        map.setImgUrl(imgUrl);
        map.setPixelWidth(pixelWidth);
        map.setPixelLength(pixelLength);
        map.setRealWidth(realWidth);
        map.setRealLength(realLength);
        map.setAreaId(areaId);
        map.setAreaName(areaName);
        indoorPositionMapMapper.updateByPrimaryKeySelective(map);

        // 设置缓存
        indoorPositionRedisDao.setMap(map.getGuid(), map);
    }

    @Override
    public void delMap(String guid) throws BizException {
        AppIndoorPositionMap map = indoorPositionMapMapper.selectByGuid(guid);
        if (map == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.EMP_BE_035.getCode());
        }
        indoorPositionMapMapper.logicDeleteById(map.getId());

        // 删除缓存
        indoorPositionRedisDao.delMap(guid);
    }

    @Override
    public AppIndoorPositionSenceDTO sceneInfo(String guid) throws BizException {
        AppIndoorPositionMap map = indoorPositionMapMapper.selectByGuid(guid);
        if (map == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.EMP_BE_035.getCode());
        }

        List<AppIndoorPositionStationDTO> list = indoorPositionStationMapper.selectByMapId(map.getId());

        // 定位统计
        Integer deptId = map.getDeptId();
        Date time = DateUtil.getDateBegin(new Date());
        List<AppIndoorPositionStatDTO> positionList = indoorPositionTagMapper.selectPositionStat(deptId, time);
        Map<Integer, Integer> positionMap = CollectionUtil.list2map(positionList, item -> item.getStationId(), item -> item.getPositionNum());
        for (AppIndoorPositionStationDTO station : list) {
            Integer stationId = station.getStationId();
            station.setPositionNum(positionMap.getOrDefault(stationId, 0));
        }

        AppIndoorPositionSenceDTO dto = new AppIndoorPositionSenceDTO();
        dto.setList(list);
        dto.setRatio(map.getRotation());
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sceneEdit(AppIndoorPositionSenceEdit param) throws BizException {
        String guid = param.getGuid();
        List<AppIndoorPositionSenceItem> stationList = param.getStationList();
        AppIndoorPositionMap map = indoorPositionMapMapper.selectByGuid(guid);
        if (map == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.EMP_BE_035.getCode());
        }
        map.setRotation(param.getRatio());
        indoorPositionMapMapper.updateByPrimaryKeySelective(map);

        indoorPositionStationMapper.clearByMapId(map.getId());
        for (AppIndoorPositionSenceItem item : stationList) {
            Integer stationId = item.getStationId();
            AppIndoorPositionStation station = indoorPositionStationMapper.selectByPrimaryKey(stationId);
            if (station != null && station.getDeptId().equals(map.getDeptId())) {
                station.setMapId(map.getId());
                station.setX(item.getX());
                station.setY(item.getY());
                indoorPositionStationMapper.updateByPrimaryKeySelective(station);
            }
        }
    }

    @Override
    public List<AppIndoorPositionStationDTO> getStationList(Integer deptId) throws BizException {
        List<AppIndoorPositionStationDTO> list = indoorPositionStationMapper.selectByDeptId(deptId);
        // 统计当日在线人数
        Date time = cn.hutool.core.date.DateUtil.beginOfDay(new Date()).toJdkDate();
        List<AppIndoorPositionStatDTO> positionStatList = indoorPositionTagMapper.selectPositionStat(deptId, time);
        Map<Integer, Integer> map = positionStatList.stream()
                .collect(Collectors.toMap(AppIndoorPositionStatDTO::getStationId, AppIndoorPositionStatDTO::getPositionNum));

        for (AppIndoorPositionStationDTO stationDTO : list) {
            stationDTO.setPositionNum(map.getOrDefault(stationDTO.getStationId(), 0));
        }
        return list;
    }

    @Override
    public List<AppIndoorPositionStationDTO> stationTagList(Integer deptId) throws BizException {
        // 查询基站列表
        List<AppIndoorPositionStationDTO> list = indoorPositionStationMapper.selectByDeptId(deptId);

        // 查询标签列表
        Date time = cn.hutool.core.date.DateUtil.beginOfDay(new Date()).toJdkDate();
        List<AppIndoorPositionTagDTO> positionTagList = indoorPositionTagMapper.selectPositionList(deptId, time, null, 1, 1);
        Map<Integer, List<AppIndoorPositionTagDTO>> tagMap = positionTagList.stream()
                .collect(Collectors.groupingBy(AppIndoorPositionTagDTO::getStationId));

        for (AppIndoorPositionStationDTO stationDTO : list) {
            Integer stationId = stationDTO.getStationId();
            List<AppIndoorPositionTagDTO> tagList = tagMap.getOrDefault(stationId, Collections.emptyList());
            stationDTO.setTagList(tagList);
            stationDTO.setPositionNum(tagList.size());
        }
        return list;
    }

    @Override
    public PageData<AppIndoorPositionStationDTO> getStationList(Integer deptId, Integer pageNum, Integer pageSize) throws BizException {
        PageHelper.startPage(pageNum, pageSize);
        List<AppIndoorPositionStationDTO> list = indoorPositionStationMapper.selectByDeptId(deptId);
        PageHelper.clearPage();
        return PageUtil.pageData(PageInfo.of(list));
    }

    @Override
    public void addStation(AppIndoorPositionStationAdd param) throws BizException {
        Integer deptId = param.getDeptId();
        String name = param.getName();
        String code = param.getCode();
        Integer mapId = param.getMapId();
        Double x = param.getX();
        Double y = param.getY();
        Double lng = param.getLng();
        Double lat = param.getLat();
        String guid = RandomUtil.getGuid();
        String ext1 = param.getExt1();
        String ext2 = param.getExt2();
        String ext3 = param.getExt3();

        // 验证基站编码不重复
        AppIndoorPositionStation exists = indoorPositionStationMapper.selectByCode(code);
        if (exists != null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.EMP_BE_036.getCode());
        }

        AppIndoorPositionStation station = new AppIndoorPositionStation();
        station.setGuid(guid);
        station.setDeptId(deptId);
        station.setName(name);
        station.setCode(code);
        station.setMapId(mapId);
        station.setX(x);
        station.setY(y);
        station.setLng(lng);
        station.setLat(lat);
        station.setExt1(ext1);
        station.setExt2(ext2);
        station.setExt3(ext3);
        indoorPositionStationMapper.insertSelective(station);

        // 设置缓存
        indoorPositionRedisDao.setStation(guid, station);
    }

    @Override
    public void editStation(AppIndoorPositionStationEdit param) throws BizException {
        String guid = param.getGuid();
        String code = param.getCode();
        String name = param.getName();
        Integer mapId = param.getMapId();
        Double x = param.getX();
        Double y = param.getY();
        Double lng = param.getLng();
        Double lat = param.getLat();
        String ext1 = param.getExt1();
        String ext2 = param.getExt2();
        String ext3 = param.getExt3();
        // 验证基站编码不重复
        AppIndoorPositionStation exits = indoorPositionStationMapper.selectByCode(code);
        if (exits != null && !exits.getGuid().equals(guid)) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.EMP_BE_036.getCode());
        }

        AppIndoorPositionStation station = indoorPositionStationMapper.selectByGuid(guid);
        station.setName(name);
        station.setCode(code);
        station.setMapId(mapId);
        station.setX(x);
        station.setY(y);
        station.setLng(lng);
        station.setLat(lat);
        station.setExt1(ext1);
        station.setExt2(ext2);
        station.setExt3(ext3);
        indoorPositionStationMapper.updateByPrimaryKeySelective(station);

        // 设置缓存
        indoorPositionRedisDao.setStation(guid, station);
    }

    @Override
    public void delStation(String guid) throws BizException {
        AppIndoorPositionStation station = indoorPositionStationMapper.selectByGuid(guid);
        if (station == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.EMP_BE_037.getCode());
        }
        indoorPositionStationMapper.logicDeleteById(station.getId());

        // 设置缓存
        indoorPositionRedisDao.delStation(guid);
    }

    @Override
    public List<AppIndoorPositionTagDTO> getTagList(Integer deptId, String keyword, Integer bindFlag) throws BizException {
        return indoorPositionTagMapper.selectByDeptId(deptId, keyword, bindFlag, null);
    }

    @Override
    public PageData<AppIndoorPositionTagDTO> getTagList(Integer deptId, String keyword, Integer bindFlag, Integer bindType, Integer pageNum, Integer pageSize) throws BizException {
        PageHelper.startPage(pageNum, pageSize);
        List<AppIndoorPositionTagDTO> list = indoorPositionTagMapper.selectByDeptId(deptId, keyword, bindFlag, bindType);
        PageHelper.clearPage();

        for (AppIndoorPositionTagDTO tag : list) {
            Integer empId = tag.getEmpId();
            if (empId != null) {
                AppEmp emp = appEmpMapper.selectByPrimaryKey(empId);
                if (emp != null) {
                    tag.setCorpName(emp.getCorpName());
                }
            }
        }

        return PageUtil.pageData(PageInfo.of(list));
    }

    @Override
    public List<AppIndoorPositionTagLogDTO> getTagLog(Integer deptId, Integer tagId, Date date) throws BizException {

        AppIndoorPositionTag tag = indoorPositionTagMapper.selectByPrimaryKey(tagId);
        if (tag == null || !tag.getDeptId().equals(deptId)) {
            return Collections.emptyList();
        }
        Date startTime = DateUtil.getDateBegin(date);
        Date endTime = DateUtil.getDateEnd(date);
        List<AppIndoorPositionTagLogDTO> list = indoorPositionTagLogDao.selectPositionLogList(tagId, startTime, endTime);

        // 基站信息
        List<AppIndoorPositionStationDTO> stationList = indoorPositionStationMapper.selectByDeptId(deptId);
        Map<Integer, AppIndoorPositionStationDTO> stationMap = CollectionUtil.list2Map(stationList, AppIndoorPositionStationDTO::getStationId);
        for (AppIndoorPositionTagLogDTO log : list) {
            Integer stationId = log.getStationId();
            if (stationMap.containsKey(stationId)) {
                AppIndoorPositionStationDTO station = stationMap.get(stationId);
                if (station != null) {
                    log.setStationName(station.getName());
                }
            }
        }

        return list;
    }

    @Override
    public List<AppIndoorPositionTagLogDTO> getTagLog(Integer deptId, Integer tagId, Date startTime, Date endTime) throws BizException {
        AppIndoorPositionTag tag = indoorPositionTagMapper.selectByPrimaryKey(tagId);
        if (tag == null || !tag.getDeptId().equals(deptId)) {
            return Collections.emptyList();
        }
        startTime = DateUtil.getDateBegin(startTime);
        endTime = DateUtil.getDateEnd(endTime);
        List<AppIndoorPositionTagLogDTO> list = indoorPositionTagLogDao.selectPositionLogList(tagId, startTime, endTime);

        // 基站信息
        List<AppIndoorPositionStationDTO> stationList = indoorPositionStationMapper.selectByDeptId(deptId);
        Map<Integer, AppIndoorPositionStationDTO> stationMap = CollectionUtil.list2Map(stationList, AppIndoorPositionStationDTO::getStationId);
        for (AppIndoorPositionTagLogDTO log : list) {
            Integer stationId = log.getStationId();
            if (stationMap.containsKey(stationId)) {
                AppIndoorPositionStationDTO station = stationMap.get(stationId);
                if (station != null) {
                    log.setStationName(station.getName());
                }
            }
        }

        return list;
    }

    @Override
    public AppIndoorPositionStatDTO getPositionDistanceStat(Integer deptId) throws BizException {
        AppIndoorPositionStatDTO data = indoorPositionTagMapper.selectMaxDistance(deptId);
        return data;
    }

    @Override
    public AppIndoorPositionStatDTO getPositionMapStat(Integer deptId, String guid, Date time) throws BizException {
        AppIndoorPositionMap map = indoorPositionMapMapper.selectByGuid(guid);
        if (map == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.EMP_BE_035.getCode());
        }
        Integer mapId = map.getId();
        AppIndoorPositionStatDTO data = indoorPositionTagMapper.selectMapStat(deptId, mapId, time);
        // 统计工人类型
        List<AppEmpWorkRoleNumDTO> workRoleNumDTOList = indoorPositionTagMapper.selectMapStatByWorkRole(deptId, mapId, time);
        data.setWorkRoleNumList(workRoleNumDTOList);
        return data;
    }

    @Override
    public List<AppIndoorPositionStatDTO> getPositionStat(Integer deptId, Date time) throws BizException {

        // 定位统计
        // time = time != null ? time : DateUtil.getDateBegin(new Date());
        List<AppIndoorPositionStatDTO> positionList = indoorPositionTagMapper.selectPositionStat(deptId, time);
        Map<Integer, AppIndoorPositionStatDTO> positionMap = CollectionUtil.list2Map(positionList, AppIndoorPositionStatDTO::getStationId);

        // 基站列表
        List<AppIndoorPositionStationDTO> stationList = indoorPositionStationMapper.selectByDeptId(deptId);
        for (AppIndoorPositionStationDTO station : stationList) {
            Integer stationId = station.getStationId();
            if (!positionMap.containsKey(stationId)) {
                AppIndoorPositionStatDTO statDTO = new AppIndoorPositionStatDTO();
                statDTO.setStationId(stationId);
                statDTO.setStationName(station.getName());
                statDTO.setPositionNum(0);
                statDTO.setX(station.getX());
                statDTO.setY(station.getY());
                statDTO.setLng(station.getLng());
                statDTO.setLat(station.getLat());
                positionList.add(statDTO);

            } else {
                AppIndoorPositionStatDTO statDTO = positionMap.get(stationId);
                statDTO.setX(station.getX());
                statDTO.setY(station.getY());
                statDTO.setLng(station.getLng());
                statDTO.setLat(station.getLat());
            }
        }


        for (AppIndoorPositionStatDTO position : positionList) {
            position.setMapId(position.getStationId());
            position.setMapName(position.getStationName());
        }

        return positionList;
    }

    @Override
    public List<AppIndoorPositionTagDTO> getPositionList(Integer deptId, Date time, Integer bindFlag, String keyword) throws BizException {
        // time = time != null ? time : DateUtil.getDateBegin(new Date());
        List<AppIndoorPositionTagDTO> list = indoorPositionTagMapper.selectPositionList(deptId, time, keyword, bindFlag, 1);

        // 基站列表
        Date now = new Date();
        Date dateEnd = DateUtil.getDateEnd(now);
        List<AppIndoorPositionStationDTO> stationList = indoorPositionStationMapper.selectByDeptId(deptId);
        Map<Integer, AppIndoorPositionStationDTO> stationMap = CollectionUtil.list2Map(stationList, AppIndoorPositionStationDTO::getStationId);

        for (AppIndoorPositionTagDTO tag : list) {
            Integer stationId = tag.getStationId();
            AppIndoorPositionStationDTO stationDTO = stationMap.get(stationId);
            String stationName = stationDTO != null ? stationDTO.getName() : "";
            tag.setStationName(stationName);

            if ((tag.getDistance() == null || tag.getDistance() == 0) && stationDTO != null) {
                double distance = PositionUtil.getPlaneDistance(stationDTO.getX(), stationDTO.getY(), tag.getX(), tag.getY());
                tag.setDistance(MathUtil.toInteger(String.valueOf(distance)));
            }

            if (tag.getTime().after(dateEnd)) {
                tag.setTime(now);
            }
        }

        return list;
    }

    @Override
    public PageData<AppIndoorPositionTagDTO> getPositionList(Integer deptId, Date time, Integer bindFlag, String keyword, Integer pageNum, Integer pageSize) throws BizException {

        time = time != null ? time : DateUtil.getDateBegin(new Date());
        PageHelper.startPage(pageNum, pageSize);
        List<AppIndoorPositionTagDTO> list = indoorPositionTagMapper.selectPositionList(deptId, time, keyword, bindFlag, 1);
        PageHelper.clearPage();

        // 基站列表
        Date now = new Date();
        Date dateEnd = DateUtil.getDateEnd(now);
        List<AppIndoorPositionStationDTO> stationList = indoorPositionStationMapper.selectByDeptId(deptId);
        Map<Integer, String> stationMap = CollectionUtil.list2map(stationList, AppIndoorPositionStationDTO::getStationId, AppIndoorPositionStationDTO::getName);

        // 人员信息
        List<Integer> empIdList = list.stream().map(AppIndoorPositionTagDTO::getEmpId).collect(Collectors.toList());

        for (AppIndoorPositionTagDTO tag : list) {
            Integer stationId = tag.getStationId();
            String stationName = stationMap.get(stationId);
            tag.setStationName(stationName);

            Integer empId = tag.getEmpId();
            if (empId != null) {
                AppEmp emp = appEmpMapper.selectByPrimaryKey(empId);
                if (emp != null) {
                    tag.setCorpName(emp.getCorpName());
                }
            }

            if (tag.getTime().after(dateEnd)) {
                tag.setTime(now);
            }
        }

        return PageUtil.pageData(PageInfo.of(list));
    }

    @Override
    public void addPosition(LocationMsg locationMsg) throws BizException {
        logger.info("新增室内定位信息,{}", JSONUtil.toString(locationMsg));
        indoorPositionMgr.addPosition(locationMsg);
    }

    @Override
    public void addTag(AppIndoorPositionTagKey param) throws BizException {
        Integer deptId = param.getDeptId();
        String guid = param.getGuid();
        AppIndoorPositionTag tag = indoorPositionTagMapper.selectByDeptIdAndGuid(deptId, guid);
        if (tag != null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.EMP_BE_038.getCode());
        }
        tag = new AppIndoorPositionTag();
        tag.setDeptId(deptId);
        tag.setGuid(guid);
        tag.setCode(guid);
        indoorPositionTagMapper.insertSelective(tag);

        // 设置缓存
        indoorPositionRedisDao.setTag(deptId, guid, tag);
    }

    @Override
    public void editTag(AppIndoorPositionTagEdit param) throws BizException {
        Integer tagId = param.getTagId();
        AppIndoorPositionTag tag = indoorPositionTagMapper.selectByPrimaryKey(tagId);
        if (tag != null) {
            // 更新标签绑定信息
            if (StringUtils.isNotBlank(param.getEmpName())) {
                tag.setEmpId(param.getEmpId());
                tag.setEmpName(param.getEmpName());
                tag.setBindFlag(BindFlag.BIND.getValue());
                tag.setBindType(IndoorBindType.EMP.getValue());
                indoorPositionTagMapper.updateByPrimaryKeySelective(tag);
            } else if (StringUtils.isNotBlank(param.getDeviceCode())) {
                tag.setDeviceCode(param.getDeviceCode());
                tag.setBindFlag(BindFlag.BIND.getValue());
                tag.setBindType(IndoorBindType.MACH.getValue());
                indoorPositionTagMapper.updateByPrimaryKeySelective(tag);
            }
            // 清理标签绑定信息
            else {
                indoorPositionTagMapper.unbindTag(tagId);
            }
            // 设置缓存
            indoorPositionRedisDao.setTag(tag.getDeptId(), tag.getGuid(), tag);
        }
    }

    @Override
    public void delTag(AppIndoorPositionTagKey param) throws BizException {
        Integer deptId = param.getDeptId();
        String guid = param.getGuid();
        AppIndoorPositionTag tag = indoorPositionTagMapper.selectByDeptIdAndGuid(deptId, guid);
        if (tag == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.EMP_BE_039.getCode());
        }
        if (BindFlag.BIND.getValue().equals(tag.getBindFlag())) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.EMP_BE_040.getCode());
        }
        indoorPositionTagMapper.logicDeleteById(tag.getId());

        // 设置缓存
        indoorPositionRedisDao.delTag(deptId, guid);
    }

    @Override
    public void bindTag(AppIndoorPositionTagBind param) throws BizException {
        Integer deptId = param.getDeptId();
        String guid = param.getGuid();
        Integer empId = param.getEmpId();
        AppIndoorPositionTag tag = indoorPositionTagMapper.selectByDeptIdAndGuid(deptId, guid);
        if (tag == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.EMP_BE_039.getCode());
        }
        Integer bindType = param.getBindType();
        if (bindType == null) {
            bindType = StringUtils.isNotBlank(param.getEmpName()) ? IndoorBindType.EMP.getValue() : IndoorBindType.MACH.getValue();
        }
        if (Objects.equals(IndoorBindType.EMP.getValue(), bindType)) {
            // 绑定人员
            String empName = param.getEmpName();
            // 验证是否重复绑定
            AppIndoorPositionTag empTag = indoorPositionTagMapper.selectByDeptIdAndEmpId(deptId, empId);
            if (empTag != null) {
                throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.EMP_BE_040.getCode());
            }
            tag.setEmpId(empId);
            tag.setEmpName(empName);
            tag.setBindType(bindType);
            tag.setBindFlag(BindFlag.BIND.getValue());

        } else if (Objects.equals(IndoorBindType.MACH.getValue(), bindType)) {
            // 绑定设备
            String deviceCode = param.getDeviceCode();
            // 验证是否重复绑定
            AppIndoorPositionTag deviceTag = indoorPositionTagMapper.selectByDeptIdAndDeviceCode(deptId, deviceCode);
            if (deviceTag != null) {
                throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.EMP_BE_040.getCode());
            }
            tag.setDeviceCode(deviceCode);
            tag.setBindType(bindType);
            tag.setBindFlag(BindFlag.BIND.getValue());
        }
        // 更新标签信息
        indoorPositionTagMapper.updateByPrimaryKeySelective(tag);

        // 设置缓存
        indoorPositionRedisDao.setTag(deptId, guid, tag);
    }

    @Override
    public void unbindTag(AppIndoorPositionTagKey param) throws BizException {
        Integer deptId = param.getDeptId();
        String guid = param.getGuid();
        AppIndoorPositionTag tag = indoorPositionTagMapper.selectByDeptIdAndGuid(deptId, guid);
        if (tag == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.EMP_BE_039.getCode());
        }
        indoorPositionTagMapper.unbindTag(tag.getId());

        // 设置缓存
        tag.setEmpId(null);
        tag.setEmpName(null);
        tag.setBindFlag(BindFlag.UNBIND.getValue());
        indoorPositionRedisDao.setTag(deptId, guid, tag);
    }
}
