package com.whfc.emp.dao;

import com.whfc.emp.dto.AppPayrollEmpDTO;
import com.whfc.emp.entity.AppPayrollEmp;
import com.whfc.emp.param.AppPayrollDetailEditParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: hw
 * @date: 2021-10-21 17:40
 * @description: //todo
 */
public interface AppPayrollEmpMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppPayrollEmp record);

    int insertSelective(AppPayrollEmp record);

    AppPayrollEmp selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppPayrollEmp record);

    int updateByPrimaryKey(AppPayrollEmp record);

    /**
     * 根据payrollId查看人员信息
     *
     * @param payrollId
     * @return
     */
    List<AppPayrollEmpDTO> selectByPayrollId(@Param("payrollId") Integer payrollId);

    /**
     * 修改员工工资基本信息
     *
     * @param request
     */
    void updateByPayrollIdAndEmpId(AppPayrollDetailEditParam request);

    void deleteLogicByPayrollId(@Param("payrollId") Integer payrollId);

    void batchInsert(@Param("list") List<AppPayrollEmpDTO> list);

    /**
     * 根据人员id查找列表
     *
     * @param empId
     * @return
     */
    List<AppPayrollEmpDTO> selectByEmpId(@Param("empId") Integer empId);

    /**
     * 查找指定工资记录下的人员工资记录
     *
     * @param payrollId 工资记录
     * @param empId     人员
     * @return 工资记录
     */
    AppPayrollEmp selectPayrollEmp(@Param("payrollId") Integer payrollId, @Param("empId") Integer empId);
}