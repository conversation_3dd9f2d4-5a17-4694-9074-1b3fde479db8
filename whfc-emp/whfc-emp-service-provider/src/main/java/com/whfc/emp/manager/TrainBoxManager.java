package com.whfc.emp.manager;

import com.whfc.emp.dto.train.TrainPaperDTO;
import com.whfc.emp.dto.train.TrainRecordDTO;
import com.whfc.emp.dto.train.TrainRecordEmpDTO;
import com.whfc.emp.dto.train.TrainRecordFileDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-08-26 18:00
 */
public interface TrainBoxManager {

    /**
     * 添加培训记录
     *
     * @param trainRecordList 培训记录列表
     */
    void addTrainRecord(List<TrainRecordDTO> trainRecordList);

    /**
     * 添加培训人员
     *
     * @param trainRecordEmpList 培训人员列表
     */
    void addTrainEmp(List<TrainRecordEmpDTO> trainRecordEmpList);

    /**
     * 添加培训试卷
     *
     * @param trainPaperList 培训试卷列表
     */
    void addTrainPaper(List<TrainPaperDTO> trainPaperList);

    /**
     * 添加培训文件
     *
     * @param trainRecordFileList 培训文件列表
     */
    void addTrainFile(List<TrainRecordFileDTO> trainRecordFileList);

    /**
     * 处理失败数据
     */
    void handleFailedData();


}
