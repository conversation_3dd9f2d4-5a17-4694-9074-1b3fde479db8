package com.whfc.emp.service.impl;

import com.whfc.common.enums.LocaleState;
import com.whfc.common.enums.SysDeptType;
import com.whfc.common.exception.BizException;
import com.whfc.common.util.CollectionUtil;
import com.whfc.common.util.DateUtil;
import com.whfc.common.util.MathUtil;
import com.whfc.emp.dao.*;
import com.whfc.emp.dto.*;
import com.whfc.emp.dto.train.AppBoardEmpGroupCountDTO;
import com.whfc.emp.enums.AppEmpWarnRuleType;
import com.whfc.emp.enums.AttendState;
import com.whfc.emp.enums.Direction;
import com.whfc.emp.enums.EmpAgeType;
import com.whfc.emp.param.EmpAttendPlan;
import com.whfc.emp.service.AppEmpBoardService;
import com.whfc.fuum.dto.SysDeptDTO;
import com.whfc.fuum.service.SysDeptService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClasssName AppEmpBoardServiceImpl
 * @Description 大屏服务
 * <AUTHOR>
 * @Date 2021/1/7 19:19
 * @Version 1.0
 */
@DubboService(interfaceClass = AppEmpBoardService.class, version = "1.0.0", timeout = 30 * 1000)
public class AppEmpBoardServiceImpl implements AppEmpBoardService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private AppEmpMapper appEmpMapper;

    @Autowired
    private AppEmpEnterRecordMapper appEmpEnterRecordMapper;

    @Autowired
    private AppEmpAttendRecordMapper appEmpAttendRecordMapper;

    @Autowired
    private AppEmpWarnMapper appEmpWarnMapper;

    @Autowired
    private AppEmpDataMapper appEmpDataMapper;

    @Autowired
    private AppEmpDayMapper appEmpDayMapper;

    @Autowired
    private AppEmpGroupMapper appEmpGroupMapper;

    @Autowired
    private AppFaceGateMapper appFaceGateMapper;

    @Autowired
    private AppFaceGateRecordMapper appFaceGateRecordMapper;

    @Autowired
    private AppEmpDeptDayMapper appEmpDeptDayMapper;

    @Autowired
    private AppTrainMapper appTrainMapper;

    @Autowired
    private AppIndoorPositionStationMapper appIndoorPositionStationMapper;

    @DubboReference(interfaceClass = SysDeptService.class, version = "1.0.0")
    private SysDeptService sysDeptService;

    @Override
    public AppBoardEmpOverviewDTO getEmpData(Integer deptId) throws BizException {
        logger.info("大屏-人员,deptId:{}", deptId);
        AppBoardEmpOverviewDTO data = new AppBoardEmpOverviewDTO();
        // 查找用户及其子组织结构
        List<AppEmpDTO> appEmpList = appEmpMapper.selectAnaEmpByDeptId(deptId);

        Map<Integer, AppEmpWorkRoleNumDTO> workRoleNumMap = new HashMap<>();

        // 在岗工种
        Set<AppWorkTypeDTO> workTypeSet = new HashSet<>();
        for (AppEmpDTO appEmpDTO : appEmpList) {
            AppWorkTypeDTO appWorkTypeDTO = new AppWorkTypeDTO();
            appWorkTypeDTO.setWorkTypeId(appEmpDTO.getWorkTypeId());
            appWorkTypeDTO.setWorkTypeName(appEmpDTO.getWorkTypeName());
            Integer workRoleId = appEmpDTO.getWorkRoleId();
            appWorkTypeDTO.setWorkRoleId(workRoleId);
            workTypeSet.add(appWorkTypeDTO);

            Integer attendState = appEmpDTO.getAttendState();
            Integer localeState = appEmpDTO.getLocaleState();
            // 获取当前工人类型的统计
            AppEmpWorkRoleNumDTO appEmpWorkRoleNumDTO = workRoleNumMap.get(workRoleId);
            if (ObjectUtils.isEmpty(appEmpWorkRoleNumDTO)) {
                appEmpWorkRoleNumDTO = new AppEmpWorkRoleNumDTO();
                appEmpWorkRoleNumDTO.setWorkRoleId(workRoleId);
                appEmpWorkRoleNumDTO.setWorkRoleName(appEmpDTO.getWorkRoleName());
                appEmpWorkRoleNumDTO.setNum(0);
                appEmpWorkRoleNumDTO.setAttendNum(0);
                appEmpWorkRoleNumDTO.setLocaleNum(0);
                workRoleNumMap.put(workRoleId, appEmpWorkRoleNumDTO);
            }
            // 设置人数
            appEmpWorkRoleNumDTO.setNum(appEmpWorkRoleNumDTO.getNum() + 1);
            // 统计出勤人数
            if (AttendState.ATTEND.getValue().equals(attendState)) {
                appEmpWorkRoleNumDTO.setAttendNum(appEmpWorkRoleNumDTO.getAttendNum() + 1);
                // 统计在场人数
                if (LocaleState.IN.getValue().equals(localeState)) {
                    appEmpWorkRoleNumDTO.setLocaleNum(appEmpWorkRoleNumDTO.getLocaleNum() + 1);
                }
            }
        }
        AppBoardEmpNumDTO appBoardEmpNumDTO = new AppBoardEmpNumDTO();
        appBoardEmpNumDTO.setWorkRoleNumList(new ArrayList<>(workRoleNumMap.values()));

        List<AppEmpAnaNumDTO> workTypeEmpList = new ArrayList<>();
        List<AppEmpAnaNumDTO> attendStateEmpList = new ArrayList<>();
        for (AppWorkTypeDTO workType : workTypeSet) {
            Integer workTypeId = workType.getWorkTypeId();
            String workTypeName = workType.getWorkTypeName();
            Integer workRoleId = workType.getWorkRoleId();
            Integer localeNum = 0;
            Integer offLocaleNum = 0;
            Integer absenceNum = 0;
            Integer num = 0;

            for (AppEmpDTO appEmpDTO : appEmpList) {
                Integer localeState = appEmpDTO.getLocaleState();
                Integer attendState = appEmpDTO.getAttendState();
                Integer workTypeId1 = appEmpDTO.getWorkTypeId();
                Integer workRoleId1 = appEmpDTO.getWorkRoleId();
                if (workRoleId != null && workRoleId.equals(workRoleId1) && workTypeId.equals(workTypeId1)) {
                    num++;

                    if (AttendState.ABSENCE.getValue().equals(attendState)) {
                        absenceNum++;
                    }
                    if (AttendState.ATTEND.getValue().equals(attendState) && LocaleState.IN.getValue().equals(localeState)) {
                        localeNum++;
                    }
                    if (AttendState.ATTEND.getValue().equals(attendState) && LocaleState.OUT.getValue().equals(localeState)) {
                        offLocaleNum++;
                    }
                }
            }
            if (workTypeId != null) {
                // 在岗工种统计
                AppEmpAnaNumDTO workTypeAnaNumDTO = new AppEmpAnaNumDTO();
                workTypeAnaNumDTO.setNum(num);
                workTypeAnaNumDTO.setName(workTypeName);
                workTypeAnaNumDTO.setWorkRoleId(workRoleId);
                workTypeAnaNumDTO.setId(workTypeId);
                workTypeEmpList.add(workTypeAnaNumDTO);
            }
            // 今日出勤状态
            AppEmpAnaNumDTO appEmpAnaNumDTO = new AppEmpAnaNumDTO();
            appEmpAnaNumDTO.setWorkRoleId(workRoleId);
            appEmpAnaNumDTO.setName(workTypeName);
            appEmpAnaNumDTO.setLocaleNum(localeNum);
            appEmpAnaNumDTO.setOffLocaleNum(offLocaleNum);
            appEmpAnaNumDTO.setAbsenceNum(absenceNum);
            attendStateEmpList.add(appEmpAnaNumDTO);
        }

        workTypeEmpList.sort((x, y) -> y.getId() - x.getId());
        appBoardEmpNumDTO.setWorkTypeEmpList(workTypeEmpList);
        appBoardEmpNumDTO.setAttendStateEmpList(attendStateEmpList);
        data.setAppBoardEmpNumDTO(appBoardEmpNumDTO);

        // 过去7天的人员进出场统计
        Date now = new Date();
        Date today = DateUtil.getDateBegin(now);
        Date startDate = DateUtil.addDays(today, -6);
        List<AppEmpAnaWeekDataDTO> enterStatList = appEmpEnterRecordMapper.selectDayStatStat(deptId, startDate, today);
        Map<Date, AppEmpAnaWeekDataDTO> enterStatMap = CollectionUtil.list2Map(enterStatList, AppEmpAnaWeekDataDTO::getDate);
        List<Date> dateList = DateUtil.getDateListBetween(startDate, today);
        List<AppEmpAnaWeekDataDTO> weekDataList = new ArrayList<>(dateList.size());
        for (Date date : dateList) {
            AppEmpAnaWeekDataDTO record = enterStatMap.containsKey(date) ? enterStatMap.get(date) : new AppEmpAnaWeekDataDTO(date, 0, 0);
            weekDataList.add(record);
        }

        // 人员打卡记录
        Date endTime = DateUtil.getDateEnd(today);
        List<BoardEmpAttendRecordDTO> appEmpAttendRecordList = appEmpAttendRecordMapper.selectByDeptIdAndTime(deptId, today, endTime);
        data.setAttendRecordList(appEmpAttendRecordList);

        // 查找人员报警数量
        Date yesterday = DateUtil.addDays(now, -1);
        Integer dropWarnCnt = appEmpWarnMapper.countWarnNum(Collections.singletonList(deptId), AppEmpWarnRuleType.DROP.value(), yesterday, now);
        Integer sosWarnCnt = appEmpWarnMapper.countWarnNum(Collections.singletonList(deptId), AppEmpWarnRuleType.SOS.value(), yesterday, now);
        Integer doffWarnCnt = appEmpWarnMapper.countWarnNum(Collections.singletonList(deptId), AppEmpWarnRuleType.DOFF.value(), yesterday, now);
        Integer fallWarnCnt = appEmpWarnMapper.countWarnNum(Collections.singletonList(deptId), AppEmpWarnRuleType.FALL.value(), yesterday, now);


        data.setWeekDataList(weekDataList);
        data.setDoffWarnCnt(doffWarnCnt);
        data.setDropWarnCnt(dropWarnCnt);
        data.setSosWarnCnt(sosWarnCnt);
        data.setFallWarnCnt(fallWarnCnt);

        return data;
    }

    @Override
    public List<GisEmpDTO> getGisEmpData(Integer deptId) throws BizException {
        logger.info("获取人员数据,deptId:{}", deptId);
        return appEmpMapper.selectGisEmpData(deptId);
    }

    @Override
    public List<EmpDataDTO> getAttendData(Integer deptId, Date startDate, Date endDate) throws BizException {
        logger.info("获取每天的出勤数据统计,deptId:{}", deptId);
        List<EmpDataDTO> empDataList = appEmpDayMapper.selectEmpNumTotalPerDay(deptId, startDate, endDate);
        empDataList.sort(Comparator.comparing(EmpDataDTO::getDate));
        for (EmpDataDTO empData : empDataList) {
            Integer empTotal = empData.getEmpTotal();
            // 获取计划出勤
            EmpAttendPlan empAttendPlan = appEmpDeptDayMapper.selectByDeptIdAndDate(deptId, empData.getDate());
            if (empAttendPlan != null) {
                empTotal = empAttendPlan.getPlanAttendNum();
            }
            Integer attendNum = empData.getAttendNum();
            Double rate = empTotal > 0 ? MathUtil.divide(attendNum, empTotal, 2) : 0;
            empData.setRate(rate);
        }
        return empDataList;
    }

    @Override
    public List<AppBoardEmpGroupCountDTO> countGroupData(Integer deptId) throws BizException {
        return appEmpGroupMapper.countByCorp(deptId);
    }

    @Override
    public AppBoardEmpAnalysisDTO empAnalysis(Integer deptId, Date startDate, Date endDate) throws BizException {

        if (endDate != null) {
            endDate = DateUtil.getDateEnd(endDate);
        }
        Integer empNum = appEmpMapper.selectPostEmpNum(Collections.singletonList(deptId));
        Integer empAttendNum = appEmpDataMapper.countAttendNum(Collections.singletonList(deptId));
        AppBoardEmpAnalysisDTO data = new AppBoardEmpAnalysisDTO();
        data.setTotal(empNum);
        data.setAttendNum(empAttendNum);
        if (empAttendNum == null || empAttendNum == 0) {
            data.setAttendRate(0D);
        } else {
            data.setAttendRate(MathUtil.round(MathUtil.divide(empAttendNum, empNum, 4) * 100, 2));
        }
        //进出场统计
        AppEmpAnaWeekDataDTO stat = appEmpEnterRecordMapper.selectTotalStat(deptId, startDate, endDate);
        data.setEmpEnterNum(stat.getEnterNum());
        data.setEmpOuterNum(stat.getOuterNum());
        return data;
    }

    @Override
    public AppEmpBoardRecordAnalysisDTO empRecordAnalysis(Integer deptId, Date startDate, Date endDate) throws BizException {
        if (endDate != null) {
            endDate = DateUtil.getDateEnd(endDate);
        }
        AppEmpBoardRecordAnalysisDTO data = appFaceGateRecordMapper.selectRecordAnalysis(deptId, startDate, endDate);
        if (data == null) {
            data = new AppEmpBoardRecordAnalysisDTO();
            data.setInNum(0);
            data.setOutNum(0);
        }
        List<AppFaceGateRecordDTO> records = new ArrayList<>();
        AppFaceGateRecordDTO in = appFaceGateRecordMapper.selectLatestRecord(deptId, Direction.IN.getValue(), startDate, endDate);
        if (in != null) {
            records.add(in);
        }
        AppFaceGateRecordDTO out = appFaceGateRecordMapper.selectLatestRecord(deptId, Direction.OUT.getValue(), startDate, endDate);
        if (out != null) {
            records.add(out);
        }
        data.setRecords(records);
        return data;
    }

    @Override
    public AppEmpBoardRecordAnalysisDTO empRecordAnalysis2(Integer deptId, Date startDate, Date endDate) throws BizException {
        if (endDate != null) {
            endDate = DateUtil.getDateEnd(endDate);
        }
        List<AppFaceGateRecordDTO> inList = appFaceGateRecordMapper.selectRecordList(deptId, Direction.IN.getValue(), startDate, endDate);
        List<AppFaceGateRecordDTO> outList = appFaceGateRecordMapper.selectRecordList(deptId, Direction.OUT.getValue(), startDate, endDate);
        Map<Integer, List<AppFaceGateRecordDTO>> inMap = CollectionUtil.groupBy(inList, AppFaceGateRecordDTO::getEmpId);
        Map<Integer, List<AppFaceGateRecordDTO>> outMap = CollectionUtil.groupBy(outList, AppFaceGateRecordDTO::getEmpId);
        Set<Integer> empIdList = new TreeSet<>();
        empIdList.addAll(inMap.keySet());
        empIdList.addAll(outMap.keySet());
        logger.info("人员ID集合:{}", empIdList);
        List<AppFaceGateRecordDTO> records = new ArrayList<>(empIdList.size());
        for (Integer empId : empIdList) {
            AppFaceGateRecordDTO recordDTO = null;
            if (inMap.containsKey(empId)) {
                List<AppFaceGateRecordDTO> inRecordList = inMap.get(empId);
                recordDTO = recordDTO == null ? inRecordList.get(0) : recordDTO;
                recordDTO.setInTime(inRecordList.get(0).getShowTime());
                recordDTO.setInImgUrl(inRecordList.get(0).getPhotoUrl());
                recordDTO.setDirection(null);
                recordDTO.setShowTime(null);
                recordDTO.setPhotoUrl(null);
            }
            if (outMap.containsKey(empId)) {
                List<AppFaceGateRecordDTO> outRecordList = outMap.get(empId);
                recordDTO = recordDTO == null ? outRecordList.get(0) : recordDTO;
                recordDTO.setOutTime(outRecordList.get(0).getShowTime());
                recordDTO.setOutImgUrl(outRecordList.get(0).getPhotoUrl());
                recordDTO.setDirection(null);
                recordDTO.setShowTime(null);
                recordDTO.setPhotoUrl(null);
            }
            records.add(recordDTO);
        }
        AppEmpBoardRecordAnalysisDTO data = new AppEmpBoardRecordAnalysisDTO();
        data.setInNum(inList.size());
        data.setOutNum(outList.size());
        data.setRecords(records.stream().limit(20).collect(Collectors.toList()));
        return data;
    }

    /***********企业看板************/

    @Override
    public AppEmpNumDTO getEmpNum(List<Integer> deptIds) throws BizException {
        logger.info("大屏-在岗人数,deptIds:{}", deptIds);
        Integer empNum = appEmpMapper.selectPostEmpNum(deptIds);
        Integer empAttendNum = appEmpDataMapper.countAttendNum(deptIds);
        Integer empLocaleNum = appEmpDataMapper.countLocaleNum(deptIds);

        AppEmpNumDTO data = new AppEmpNumDTO();
        data.setEmpNum(empNum);
        data.setEmpAttendNum(empAttendNum);
        data.setEmpLocaleNum(empLocaleNum);
        return data;
    }

    @Override
    public AppEmpDeviceNumDTO getEmpDeviceNum(List<Integer> deptIds) throws BizException {
        logger.info("大屏-设备统计,deptIds:{}", deptIds);

        Integer deviceTotal = 0;
        Integer deviceOnline = 0;

        //闸机数量
        AppEmpDeviceNumDTO face = appFaceGateMapper.statFaceGate(deptIds);
        deviceTotal += face.getDeviceTotal();
        deviceOnline += face.getDeviceOnline();

        //基站数量

        AppEmpDeviceNumDTO data = new AppEmpDeviceNumDTO();
        data.setDeviceTotal(deviceTotal);
        data.setDeviceOnline(deviceOnline);
        return data;
    }

    @Override
    public List<EmpDataDTO> getAttendData(List<Integer> deptIds, Date startDate, Date endDate) throws BizException {
        logger.info("获取每天的出勤数据统计,deptIds:{}", deptIds);
        List<EmpDataDTO> empDataList = appEmpDayMapper.selectEmpDayAttend(deptIds, startDate, endDate);
        empDataList.sort(Comparator.comparing(EmpDataDTO::getDate));
        for (EmpDataDTO empData : empDataList) {
            Integer empTotal = empData.getEmpTotal();
            Integer attendNum = empData.getAttendNum();
            Double rate = empTotal > 0 ? MathUtil.divide(attendNum, empTotal, 2) : 0;
            empData.setRate(rate);
        }
        return empDataList;
    }

    @Override
    public List<EmpDataDTO> enterpriseAttendDay(Integer deptId) throws BizException {

        //查询所有项目
        List<SysDeptDTO> deptList = sysDeptService.getDescendantDeptListById(deptId);
        Map<Integer, String> deptMap = deptList.stream().filter(o -> SysDeptType.isProject(o.getType())).collect(Collectors.toMap(SysDeptDTO::getId, SysDeptDTO::getName));
        Set<Integer> deptIds = deptMap.keySet();

        //查询考勤数据
        Date endDate = new Date();
        Date startDate = DateUtil.addDays(endDate, -5);
        List<EmpDataDTO> empDataList = appEmpDayMapper.selectEmpDayAttend(deptIds, startDate, endDate);

        //出勤率
        for (EmpDataDTO empData : empDataList) {
            Integer empTotal = empData.getEmpTotal();
            Integer attendNum = empData.getAttendNum();
            Double rate = empTotal > 0 ? MathUtil.divide(attendNum * 100, empTotal, 1) : 0;
            empData.setRate(rate);
        }
        return empDataList;
    }

    @Override
    public List<EmpDataDTO> enterpriseAttendMonth(Integer deptId) throws BizException {

        //查询所有项目
        List<SysDeptDTO> deptList = sysDeptService.getDescendantDeptListById(deptId);
        Map<Integer, String> deptMap = deptList.stream().filter(o -> SysDeptType.isProject(o.getType())).collect(Collectors.toMap(SysDeptDTO::getId, SysDeptDTO::getName));
        Set<Integer> deptIds = deptMap.keySet();

        //查询考勤数据
        Date endDate = new Date();
        Date startDate = DateUtil.getMonthBegin(DateUtil.addMonth(endDate, -5));
        List<EmpDataDTO> empDataList = appEmpDayMapper.selectEmpMonthAttend(deptIds, startDate, endDate);

        //出勤率
        for (EmpDataDTO empData : empDataList) {
            Integer empTotal = empData.getEmpTotal();
            Integer attendNum = empData.getAttendNum();
            Double rate = empTotal > 0 ? MathUtil.divide(attendNum * 100, empTotal, 1) : 0;
            empData.setRate(rate);
        }
        return empDataList;
    }

    @Override
    public List<EmpDataDTO> enterpriseAttendWeek(Integer deptId) throws BizException {
        //查询所有项目
        List<SysDeptDTO> deptList = sysDeptService.getDescendantDeptListById(deptId);
        Map<Integer, String> deptMap = deptList.stream().filter(o -> SysDeptType.isProject(o.getType())).collect(Collectors.toMap(SysDeptDTO::getId, SysDeptDTO::getName));
        Set<Integer> deptIds = deptMap.keySet();

        //查询考勤数据
        Date endDate = new Date();
        Date startDate = DateUtil.getWeekBegin(DateUtil.addWeek(endDate, -5));
        List<EmpDataDTO> empDataList = appEmpDayMapper.selectEmpWeekAttend(deptIds, startDate, endDate);

        //出勤率
        for (EmpDataDTO empData : empDataList) {
            Integer empTotal = empData.getEmpTotal();
            Integer attendNum = empData.getAttendNum();
            Double rate = empTotal > 0 ? MathUtil.divide(attendNum * 100, empTotal, 1) : 0;
            empData.setRate(rate);
        }
        return empDataList;
    }

    @Override
    public List<EmpDataDTO> enterpriseAttendProject(Integer deptId) throws BizException {
        //查询所有项目
        List<SysDeptDTO> deptList = sysDeptService.getDescendantDeptListById(deptId);
        Map<Integer, String> deptMap = deptList.stream().filter(o -> SysDeptType.isProject(o.getType())).collect(Collectors.toMap(SysDeptDTO::getId, SysDeptDTO::getName));
        Set<Integer> deptIds = deptMap.keySet();

        //查询考勤数据
        Date today = new Date();
        List<EmpDataDTO> empDataList = appEmpDayMapper.selectEmpProjectAttend(deptIds, today, today);

        //出勤率
        for (EmpDataDTO empData : empDataList) {
            Integer empTotal = empData.getEmpTotal();
            Integer attendNum = empData.getAttendNum();
            Double rate = empTotal > 0 ? MathUtil.divide(attendNum * 100, empTotal, 1) : 0;
            empData.setRate(rate);
            empData.setDeptName(deptMap.get(empData.getDeptId()));
        }
        return empDataList;
    }

    @Override
    public List<AppEmpAnaWeekDataDTO> enterpriseEnterStat(Integer deptId) throws BizException {

        //查询所有项目
        List<SysDeptDTO> deptList = sysDeptService.getDescendantDeptListById(deptId);
        Map<Integer, String> deptMap = deptList.stream().filter(o -> SysDeptType.isProject(o.getType())).collect(Collectors.toMap(SysDeptDTO::getId, SysDeptDTO::getName));
        Set<Integer> deptIds = deptMap.keySet();

        //查询数据
        Date endDate = new Date();
        Date startDate = DateUtil.addDays(endDate, -6);
        List<AppEmpAnaWeekDataDTO> enterList = appEmpEnterRecordMapper.selectEnterpriseEnterRecordStat(deptIds, startDate, endDate);

        //按日期对齐
        List<Date> dateList = DateUtil.getDateListBetween(startDate, endDate);
        List<AppEmpAnaWeekDataDTO> itemList = this.alignDayData(enterList, dateList);

        return itemList;
    }

    @Override
    public List<AppEmpAgeDTO> enterpriseAgeStat(Integer deptId) throws BizException {

        //查询所有项目
        List<SysDeptDTO> deptList = sysDeptService.getDescendantDeptListById(deptId);
        Map<Integer, String> deptMap = deptList.stream().filter(o -> SysDeptType.isProject(o.getType())).collect(Collectors.toMap(SysDeptDTO::getId, SysDeptDTO::getName));
        Set<Integer> deptIds = deptMap.keySet();

        //查询数据
        List<EmpAge> ageList = appEmpMapper.selectEnterpriseAgeStat(deptIds);

        //数据对齐
        //按按年龄分类
        Map<EmpAgeType, Integer> ageTypeMap = ageList.stream().collect(Collectors.groupingBy(x -> EmpAgeType.parseByAge(x.getAge()), Collectors.summingInt(EmpAge::getNum)));
        EmpAgeType[] ageTypes = EmpAgeType.values();
        List<AppEmpAgeDTO> retList = new ArrayList<>(ageTypes.length);
        for (EmpAgeType ageType : ageTypes) {
            Integer num = ageTypeMap.getOrDefault(ageType, 0);
            AppEmpAgeDTO ageDTO = new AppEmpAgeDTO(ageType.getType());
            ageDTO.setTypeName(ageType.getDesc());
            ageDTO.setAgeNum(num);
            retList.add(ageDTO);
        }
        return retList;
    }

    @Override
    public List<AppEmpAttendGroupDTO> enterpriseCorpStat(Integer deptId) throws BizException {

        //查询所有项目
        List<SysDeptDTO> deptList = sysDeptService.getDescendantDeptListById(deptId);
        Map<Integer, String> deptMap = deptList.stream().filter(o -> SysDeptType.isProject(o.getType())).collect(Collectors.toMap(SysDeptDTO::getId, SysDeptDTO::getName));
        Set<Integer> deptIds = deptMap.keySet();

        //人员分类统计
        List<AppEmpAttendGroupDTO> list = appEmpMapper.selectEnterpriseCorpStat(deptIds);

        return list;
    }

    @Override
    public List<AppEmpAttendGroupDTO> enterpriseWorkStat(Integer deptId) throws BizException {

        //查询所有项目
        List<SysDeptDTO> deptList = sysDeptService.getDescendantDeptListById(deptId);
        Map<Integer, String> deptMap = deptList.stream().filter(o -> SysDeptType.isProject(o.getType())).collect(Collectors.toMap(SysDeptDTO::getId, SysDeptDTO::getName));
        Set<Integer> deptIds = deptMap.keySet();


        //人员分类统计
        List<AppEmpAttendGroupDTO> list = appEmpMapper.selectEnterpriseWorkStat(deptIds);

        return list;
    }

    @Override
    public List<AppEmpAttendGroupDTO> enterpriseSpecWorkStat(Integer deptId) throws BizException {
        //查询所有项目
        List<SysDeptDTO> deptList = sysDeptService.getDescendantDeptListById(deptId);
        Map<Integer, String> deptMap = deptList.stream().filter(o -> SysDeptType.isProject(o.getType())).collect(Collectors.toMap(SysDeptDTO::getId, SysDeptDTO::getName));
        Set<Integer> deptIds = deptMap.keySet();


        //人员分类统计
        List<AppEmpAttendGroupDTO> list = appEmpMapper.selectEnterpriseSpecWorkStat(deptIds);

        return list;
    }

    @Override
    public List<AppTrainStatDTO> enterpriseTrainStat(Integer deptId) throws BizException {

        //查询所有项目
        List<SysDeptDTO> deptList = sysDeptService.getDescendantDeptListById(deptId);
        Map<Integer, String> deptMap = deptList.stream().filter(o -> SysDeptType.isProject(o.getType())).collect(Collectors.toMap(SysDeptDTO::getId, SysDeptDTO::getName));
        Set<Integer> deptIds = deptMap.keySet();

        //查询数据
        List<AppTrainStatDTO> trainStatList = appTrainMapper.selectEnterpriseTrainStat(deptIds);

        //数据不起
        for (AppTrainStatDTO trainStat : trainStatList) {
            trainStat.setDeptName(deptMap.get(trainStat.getDeptId()));
        }
        return trainStatList;
    }

    /**
     * 按日期对齐入职数据
     *
     * @param dataList
     * @param dateList
     * @return
     */
    private List<AppEmpAnaWeekDataDTO> alignDayData(List<AppEmpAnaWeekDataDTO> dataList, List<Date> dateList) {
        Map<String, AppEmpAnaWeekDataDTO> dataMap = CollectionUtil.list2Map(dataList, dto -> DateUtil.formatDate(dto.getDate()));
        List<AppEmpAnaWeekDataDTO> itemList = new ArrayList<>(dateList.size());
        for (Date date : dateList) {
            String key = DateUtil.formatDate(date);
            AppEmpAnaWeekDataDTO item = dataMap.get(key);
            if (item == null) {
                item = new AppEmpAnaWeekDataDTO(date, 0, 0);
            }
            itemList.add(item);
        }
        return itemList;
    }
}
