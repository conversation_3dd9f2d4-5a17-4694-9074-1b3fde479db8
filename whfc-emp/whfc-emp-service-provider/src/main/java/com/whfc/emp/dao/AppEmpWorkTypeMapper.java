package com.whfc.emp.dao;

import com.whfc.emp.dto.AppWorkTypeDTO;
import com.whfc.emp.entity.AppEmpWorkType;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AppEmpWorkTypeMapper {

    int insertSelective(AppEmpWorkType record);

    AppEmpWorkType selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppEmpWorkType record);

    /**
     * 根据项目id查找
     *
     * @param deptId
     * @return
     */
    List<AppWorkTypeDTO> selectByDeptId(@Param("deptId") Integer deptId);

    /**
     * 根据项目id和工种名称查找
     *
     * @param deptId
     * @param name
     * @return
     */
    AppEmpWorkType selectByDeptIdAndName(@Param("deptId") Integer deptId, @Param("name") String name);

    /**
     * 使用租子机构id和工种编码查询
     *
     * @param deptId
     * @param workCode
     * @return
     */
    AppEmpWorkType selectByDeptIdAndWorkCode(@Param("deptId") Integer deptId, @Param("workCode") String workCode);

    /**
     * 批量插入
     *
     * @param list
     */
    void batchInsert(@Param("list") List<AppWorkTypeDTO> list);

    /**
     * 根据id逻辑删除
     *
     * @param id
     */
    void deleteLogicById(@Param("id") Integer id);
}