package com.whfc.emp.job;

import com.alibaba.fastjson.JSONObject;
import com.whfc.XxlJobConfig;
import com.whfc.common.constant.QueueConst;
import com.whfc.common.face.kyy.cmd.RequestPersons;
import com.whfc.common.util.JSONUtil;
import com.whfc.emp.dao.AppFaceGateMapper;
import com.whfc.emp.entity.AppFaceGate;
import com.whfc.emp.enums.FaceGateType;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.whfc.common.face.kyy.KyyCmd.PAGE_SIZE;
import static com.whfc.common.face.kyy.KyyCmd.REQUEST_PERSONS;

/**
 * @Description: 快优易闸机定时任务
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/10/30 11:26
 */
@Component
@ConditionalOnBean(XxlJobConfig.class)
public class FaceGateKyyJob {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private AppFaceGateMapper appFaceGateMapper;

    @Autowired
    private AmqpTemplate amqpTemplate;

    /**
     * 快优易闸机-查询人员信息
     */
    @XxlJob("kyyQueryPersons")
    public void queryPersons() {
        List<AppFaceGate> faceGateList = appFaceGateMapper.selectByPlatform(FaceGateType.KYY.getCode());
        for (AppFaceGate faceGate : faceGateList) {
            logger.info("kyy闸机查询人员:{}", faceGate.getDeviceKey());
            this.queryPerson(faceGate);
        }
    }

    /**
     * 快优易闸机-查询所有人员
     */
    @XxlJob("kyyQueryPerson")
    public void queryPerson() {

        //获取参数
        String paramStr = XxlJobHelper.getJobParam();
        if (StringUtils.isEmpty(paramStr)) {
            XxlJobHelper.handleFail("未配置参数");
            return;
        }
        JSONObject params = JSONObject.parseObject(paramStr);
        String deviceKey = params.getString("deviceKey");
        AppFaceGate faceGate = appFaceGateMapper.selectByDeviceKey(deviceKey);
        if (faceGate != null && FaceGateType.KYY.getCode().equals(faceGate.getPlatform())) {
            logger.info("kyy闸机查询人员:{}", faceGate.getDeviceKey());
            this.queryPerson(faceGate);
        }
    }

    /**
     * 查询闸机所有人员信息
     *
     * @param faceGate
     */
    private void queryPerson(AppFaceGate faceGate) {
        RequestPersons requestPersons = new RequestPersons();
        requestPersons.setDevice_sn(faceGate.getDeviceKey());
        requestPersons.setCmd(REQUEST_PERSONS);
        requestPersons.setRole(-1);
        requestPersons.setPage_no(1);
        requestPersons.setPage_size(PAGE_SIZE);
        requestPersons.setImage_flag(1);
        amqpTemplate.convertAndSend(QueueConst.KYY_DOWN_MSG, JSONUtil.toString(requestPersons));
    }
}
