package com.whfc.emp.dao;

import com.whfc.emp.dto.AppIndoorPositionMapDTO;
import com.whfc.emp.entity.AppIndoorPositionMap;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AppIndoorPositionMapMapper {
    int deleteByPrimaryKey(Integer id);

    int insertSelective(AppIndoorPositionMap record);

    AppIndoorPositionMap selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppIndoorPositionMap record);

    /**
     * 查询项目地图
     *
     * @param deptId
     * @return
     */
    List<AppIndoorPositionMapDTO> selectByDeptId(@Param("deptId") Integer deptId);

    AppIndoorPositionMap selectByGuid(@Param("guid") String guid);

    int logicDeleteById(@Param("id") Integer id);
}