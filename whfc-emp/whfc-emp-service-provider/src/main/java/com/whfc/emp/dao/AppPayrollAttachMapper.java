package com.whfc.emp.dao;

import com.whfc.emp.dto.AppPayrollAttachDTO;
import com.whfc.emp.entity.AppPayrollAttach;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AppPayrollAttachMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppPayrollAttach record);

    int insertSelective(AppPayrollAttach record);

    AppPayrollAttach selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppPayrollAttach record);

    int updateByPrimaryKey(AppPayrollAttach record);

    /**
     * 查看附件数
     *
     * @param payrollId
     * @return
     */
    Integer selectAttachNumByPayrollId(@Param("payrollId") Integer payrollId);

    /**
     * 查看附件信息
     *
     * @param payrollId
     * @return
     */
    List<AppPayrollAttachDTO> selectByPayrollId(@Param("payrollId") Integer payrollId);


    void deleteLogicByAttachId(@Param("attachId") Integer attachId);
}