package com.whfc.emp.mqtt;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.whfc.common.constant.QueueConst;
import com.whfc.common.face.szyc.*;
import com.whfc.common.file.FileHandler;
import com.whfc.common.file.FilePathConfig;
import com.whfc.common.util.*;
import com.whfc.emp.dao.AppEmpMapper;
import com.whfc.emp.dao.AppFaceGateMapper;
import com.whfc.emp.dao.AppFaceGatePersonMapper;
import com.whfc.emp.dto.AppFaceGateDTO;
import com.whfc.emp.entity.AppEmp;
import com.whfc.emp.entity.AppFaceGate;
import com.whfc.emp.entity.AppFaceGatePerson;
import com.whfc.emp.enums.EmpSyncImgType;
import com.whfc.emp.enums.FaceGateSyncOp;
import com.whfc.emp.enums.FaceGateType;
import com.whfc.emp.enums.TaskType;
import com.whfc.emp.manager.AppFaceGateManager;
import com.whfc.emp.param.EmpAttendSyncDataParam;
import com.whfc.emp.param.EmpInfoSyncDataParam;
import com.whfc.emp.redis.FaceGateAttendRedisDao;
import com.whfc.emp.redis.MqttEmpRedisDao;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.locks.LockSupport;
import java.util.stream.Collectors;

/**
 * 人脸识别数据处理(标准MQTT协议)
 */
@Component
public class MqttMessageFaceStdProcessor implements MqttMessageProcessor {

    private static final Logger logger = org.slf4j.LoggerFactory.getLogger(MqttMessageFaceStdProcessor.class);

    /**
     * 同一个人同一个闸机刷脸时间间隔大于1分钟
     */
    private static final Integer INTERVAL_TIME = 1;

    //MQTT topic 前缀
    private static final String MQTT_TOPIC_PREFIX = "mqtt/face/";

    @Autowired
    private AppFaceGateMapper appFaceGateMapper;

    @Autowired
    private AppFaceGateManager appFaceGateManager;

    @Autowired
    private FaceGateAttendRedisDao faceGateAttendRedisDao;

    @Autowired
    private FilePathConfig filePathConfig;

    @Autowired
    private FileHandler fileHandler;

    @Autowired
    private AmqpTemplate amqpTemplate;

    @Autowired
    private MqttEmpRedisDao mqttEmpRedisDao;

    @Autowired
    private AppEmpMapper appEmpMapper;

    @Autowired
    private AppFaceGatePersonMapper appFaceGatePersonMapper;

    @Autowired(required = false)
    private MqttConfig.MqttMessageSender mqttMessageSender;

    @Override
    public String getTopicPrefix() {
        return MQTT_TOPIC_PREFIX;
    }

    @Override
    public List<String> getSubTopicList() {
        List<AppFaceGate> deviceList1 = appFaceGateMapper.selectByPlatform(FaceGateType.MQTT.getCode());
        List<AppFaceGate> deviceList2 = appFaceGateMapper.selectByPlatform(FaceGateType.MQTT_HQSX_ZJJ.getCode());
        List<String> snList1 = deviceList1.stream().map(AppFaceGate::getDeviceKey).collect(Collectors.toList());
        List<String> snList2 = deviceList2.stream().map(AppFaceGate::getDeviceKey).collect(Collectors.toList());
        List<String> snList = new ArrayList<>(snList1.size() + snList2.size());
        snList.addAll(snList1);
        snList.addAll(snList2);

        List<String> topicList = new ArrayList<>(snList.size() * 4);
        for (String sn : snList) {
            // 人员识别
            String rec = MessageFormat.format(SzycConst.TOPIC_REC, sn);
            // 回复平台
            String ack = MessageFormat.format(SzycConst.TOPIC_ACK, sn);
            // 陌生人抓拍记录
            String snap = MessageFormat.format(SzycConst.TOPIC_SNAP, sn);
            // 二维码扫码信息
            String qrCode = MessageFormat.format(SzycConst.TOPIC_QRCODE, sn);
            //心跳
            String heart = SzycConst.TOPIC_HEART;

            topicList.add(snap);
            topicList.add(ack);
            topicList.add(rec);
            topicList.add(qrCode);
            topicList.add(heart);
        }
        return topicList;
    }

    @Override
    public void processData(String topic, byte[] payload) {
        String message = new String(payload);
        JSONObject jsonObject = JSON.parseObject(message);
        String operator = jsonObject.getString(SzycConst.OPERATOR);
        String msg = jsonObject.toString();
        logger.info("MQTT事件监听器接收到消息,operator:{},{}", operator, msg.length() > 500 ? msg.substring(0, 500) : msg);
        //查询人员列表ack
        if (SzycConst.OP_QUERY_PERSON_ACK.equals(operator)) {
            Person person = jsonObject.getObject(SzycConst.INFO, Person.class);
            handlerPersonList(person);
        }
        //查询人员详细信息ack
        else if (SzycConst.OP_SEARCH_PERSON_ACK.equals(operator)) {
            PersonInfo personInfo = jsonObject.getObject(SzycConst.INFO, PersonInfo.class);
            String pic = jsonObject.getString("pic");
            handlerPersonInfo(personInfo, pic);
        }
        //考勤记录主动推送
        else if (SzycConst.OP_RECPUSH.equals(operator)) {
            RecRecord recRecord = jsonObject.getObject(SzycConst.INFO, RecRecord.class);
            handlerRecRecord(recRecord);
        }
        //陌生人推送
        else if (SzycConst.OP_STRSNAPPUSH.equals(operator)) {
            SnapRecord snapRecord = jsonObject.getObject(SzycConst.INFO, SnapRecord.class);
            handlerSnapRecord(snapRecord);
        }
        //批量添加人员ack
        else if (SzycConst.OP_ADD_PERSONS_ACK.equals(operator)) {
            AddPersonsResult result = jsonObject.getObject(SzycConst.INFO, AddPersonsResult.class);
            handlerAddPersonResult(result);
        }
        //删除人员ack
        else if (SzycConst.OP_DEL_PERSONS_ACK.equals(operator)) {
            DelPersonsResult result = jsonObject.getObject(SzycConst.INFO, DelPersonsResult.class);
            handlerDelPersonResult(result);
        }
        //心跳
        else if (SzycConst.OP_HEART.equals(operator)) {
            HeartInfo heartInfo = jsonObject.getObject(SzycConst.INFO, HeartInfo.class);
            handleHeartBeat(heartInfo);
        }
    }

    /**
     * 处理人员列表
     *
     * @param person 人员信息
     */
    private void handlerPersonList(Person person) {
        logger.info("MQTT处理人员CustomId, person:{}", person);
        if (person == null) {
            return;
        }
        //获取人员唯一ID
        String customIds = person.getCustomId();
        if (StringUtils.isBlank(customIds)) {
            return;
        }
        //处理人员ID
        String[] customIdArrays = customIds.split(SzycConst.CUSTOM_ID_REGEX);
        //去重转换为集合
        List<String> customIdList = Arrays.stream(customIdArrays).distinct().collect(Collectors.toList());
        logger.info("MQTT闸机人数:{}", person.getFacesluiceId(), customIdList.size());
        //取出闸机ID
        String deviceKey = person.getFacesluiceId();
        AppFaceGateDTO faceGateDTO = appFaceGateManager.getByDeviceKey(deviceKey);
        if (faceGateDTO == null) {
            logger.warn("人员入库-闸机未入后台数据库, deviceKey:{} ", deviceKey);
            return;
        }
        //取出缓存的 empCode列表
        Integer deptId = faceGateDTO.getDeptId();
        List<String> empCodeList = mqttEmpRedisDao.getEmpCodeList(deptId);
        //去掉已保存的 empCode
        customIdList.removeAll(empCodeList);
        // 人员信息
        List<AppEmp> empList = new ArrayList<>();
        for (String customId : customIdList) {
            //保存查询人员详情消息到队列中
            saveSearchPersonToQueue(customId, deviceKey);
            //保存customId到缓存中
            empCodeList.add(customId);
            try {
                //验证是否存在
                AppEmp appEmp = appEmpMapper.selectByDeptIdAndEmpCode(deptId, customId);
                if (appEmp != null) {
                    continue;
                }
                appEmp = new AppEmp();
                appEmp.setDeptId(deptId);
                appEmp.setEmpCode(customId);
                empList.add(appEmp);
            } catch (Exception e) {
                logger.warn("保存人员失败, deptId:{}, empCode:{}", deptId, customId);
                empCodeList.remove(customId);
            }
        }
        //保存人员
        if (empList.size() > 0) {
            appEmpMapper.batchInsertEmp(empList);
        }
        //保存到缓存中
        mqttEmpRedisDao.saveEmpCodeList(deptId, empCodeList);
    }

    /**
     * 处理人员信息
     *
     * @param personInfo 人员信息
     */
    private void handlerPersonInfo(PersonInfo personInfo, String pic) {
        logger.info("MQTT保存人员信息, personInfo:{}", personInfo);
        if (personInfo == null) {
            return;
        }
        String deviceKey = personInfo.getFacesluiceId();
        AppFaceGateDTO faceGateDTO = appFaceGateManager.getByDeviceKey(deviceKey);
        if (faceGateDTO == null) {
            logger.warn("考勤记录同步-闸机未入后台数据库, deviceKey:{} ", deviceKey);
            return;
        }
        EmpInfoSyncDataParam param = new EmpInfoSyncDataParam();
        param.setSyncType(FaceGateSyncOp.EMP_ADD_OR_UPDATE.getValue());
        param.setImgType(EmpSyncImgType.BASE64.getValue());
        param.setDeptId(faceGateDTO.getDeptId());
        param.setEmpCode(personInfo.getCustomId());
        String name = null;
        if (StringUtils.isNotBlank(personInfo.getName())) {
            name = personInfo.getName().trim();
        }
        param.setEmpName(name);
        param.setGender(convertGender(personInfo.getGender()));
        String idCardNo = null;
        if (StringUtils.isNotBlank(personInfo.getIdCard())) {
            idCardNo = personInfo.getIdCard().trim();
        }
        param.setIdCardNo(idCardNo);
        String address = null;
        if (StringUtils.isNotBlank(personInfo.getAddress())) {
            address = personInfo.getAddress().trim();
        }
        param.setAddress(address);
        String telNum = null;
        if (StringUtils.isNotBlank(personInfo.getTelnum1())) {
            telNum = personInfo.getTelnum1().trim();
        }
        param.setPhone(telNum);
        if (StringUtils.isNotBlank(pic)) {
            //去掉图片前缀
            String prefix = "^data:image/\\w+;base64,";
            pic = pic.replaceAll(prefix, "");
            param.setAvatar(pic);
        }
        //推送消息
        String jsonStr = JSONUtil.toString(param);
        amqpTemplate.convertAndSend(QueueConst.EMP_FACEGATE_EMP_INFO, jsonStr);
        //置空人员查询数据
        MqttConfig.SEARCH_PERSON = null;
        // 解开锁
        LockSupport.unpark(MqttConfig.MQTT_THREAD);
    }

    /**
     * 处理识别记录
     *
     * @param recRecord 识别记录
     */
    private void handlerRecRecord(RecRecord recRecord) {
        logger.info("MQTT处理考勤记录, recRecord:{}", recRecord);
        if (recRecord == null) {
            return;
        }
        String personGuid = recRecord.getCustomId();
        String deviceKey = recRecord.getFacesluiceId();

        //PushAck,在断网续传模式下，开启认证记录推送信息时，平台接收到认证记录信息需要回复人脸
//        PushAckInfo info = new PushAckInfo(REC_PUSH_ACK, recRecord.getRecordID());
//        PushAck ackAck = new PushAck(info);
//        String topic = MessageFormat.format(SzycConst.TOPIC_REQ, deviceKey);
//        mqttMessageSender.sendToMqtt(topic, JSON.toJSONString(ackAck));

        //1.验证闸机-唯一标识
        AppFaceGateDTO faceGateDTO = appFaceGateManager.getByDeviceKey(deviceKey);
        if (faceGateDTO == null) {
            logger.warn("考勤记录同步-闸机未入后台数据库, deviceKey:{} ", deviceKey);
            return;
        }
        // (2)验证时间-过滤重复记录
        Date time = recRecord.getTime();
        //从缓存中获取上一次打卡记录
        Date latestShowTime = faceGateAttendRedisDao.getShowTime(deviceKey, personGuid);
        if (latestShowTime != null && DateUtil.addMinutes(latestShowTime, INTERVAL_TIME).after(time)) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            logger.info("两次刷脸时间相同, time:{}, latestShowTime:{}", sdf.format(time), sdf.format(latestShowTime));
            return;
        }
        //保存刷脸时间到缓存中
        faceGateAttendRedisDao.setShowTime(deviceKey, personGuid, time);

        //保存考勤信息
        EmpAttendSyncDataParam param = new EmpAttendSyncDataParam();
        param.setDeviceName(faceGateDTO.getName());
        param.setDeviceKey(deviceKey);
        param.setPersonGuid(personGuid);
        param.setEmpName(recRecord.getPersionName());
        param.setDeptId(faceGateDTO.getDeptId());
        param.setFaceGateId(faceGateDTO.getFaceGateId());
        param.setShowTime(time);
        param.setDirection(faceGateDTO.getDirection());
        param.setTemperature(recRecord.getTemperature());
        //处理图片
        String pic = recRecord.getPic();
        if (StringUtils.isNotBlank(pic)) {
            String imgUrl = uploadImg(pic, time);
            param.setPicture(imgUrl);
        }

        //推送消息到考勤记录中
        String jsonStr = JSON.toJSONString(param);
        amqpTemplate.convertAndSend(QueueConst.EMP_FACEGATE_ATTEND_DATA, jsonStr);

    }

    /**
     * 处理陌生人抓怕
     *
     * @param snapRecord 陌生人抓怕
     */
    private void handlerSnapRecord(SnapRecord snapRecord) {
        logger.info("MQTT处理陌生人抓拍记录, recRecord:{}", snapRecord);
        if (snapRecord == null) {
            return;
        }
        String deviceKey = snapRecord.getFacesluiceId();
        Date time = snapRecord.getTime();

        //PushAck,在断网续传模式下，开启陌生人抓拍推送信息时，平台接收到陌生人抓拍信息需要回复
//        PushAckInfo info = new PushAckInfo(SNAP_PUSH_ACK, snapRecord.getSnapID());
//        PushAck ackAck = new PushAck(info);
//        String topic = MessageFormat.format(SzycConst.TOPIC_REQ, deviceKey);
//        mqttMessageSender.sendToMqtt(topic, JSON.toJSONString(ackAck));

        //验证闸机信息
        AppFaceGateDTO faceGateDTO = appFaceGateManager.getByDeviceKey(deviceKey);
        if (faceGateDTO == null) {
            logger.warn("考勤记录同步-闸机未入后台数据库, deviceKey:{} ", deviceKey);
            return;
        }

        //保存考勤信息
        EmpAttendSyncDataParam param = new EmpAttendSyncDataParam();
        param.setDeviceKey(deviceKey);
        param.setDeptId(faceGateDTO.getDeptId());
        param.setFaceGateId(faceGateDTO.getFaceGateId());
        param.setShowTime(time);
        param.setDirection(faceGateDTO.getDirection());
        param.setPersonGuid("");
        //处理图片
        String pic = snapRecord.getPic();
        if (StringUtils.isNotBlank(pic)) {
            String imgUrl = uploadImg(pic, time);
            param.setPicture(imgUrl);
        }

        //推送消息到考勤记录中
        String jsonStr = JSON.toJSONString(param);
        amqpTemplate.convertAndSend(QueueConst.EMP_FACEGATE_ATTEND_DATA, jsonStr);
    }

    /**
     * 批量授权人员处理
     *
     * @param result 人员授权结果
     */
    private void handlerAddPersonResult(AddPersonsResult result) {
        String deviceKey = result.getFacesluiceId();
        List<AddPersonState> addErrInfo = result.getAddErrInfo();
        List<AddPersonState> addSucInfo = result.getAddSucInfo();
        //添加成功的人员列表
        if (addSucInfo != null && addSucInfo.size() > 0) {
            for (AddPersonState addPersonState : addSucInfo) {
                AppFaceGatePerson faceGatePerson = appFaceGatePersonMapper.selectByPersonGuidAndDeviceKey(addPersonState.getCustomId(), deviceKey);
                if (faceGatePerson == null) {
                    continue;
                }
                faceGatePerson.setTaskType(TaskType.EMP_AUTH.getValue());
                faceGatePerson.setMessage(null);
                appFaceGatePersonMapper.updateByPrimaryKeySelective(faceGatePerson);
            }
        }
        //添加失败的人员列表
        if (addErrInfo != null && addErrInfo.size() > 0) {
            for (AddPersonState addPersonState : addErrInfo) {
                AppFaceGatePerson faceGatePerson = appFaceGatePersonMapper.selectByPersonGuidAndDeviceKey(addPersonState.getCustomId(), deviceKey);
                if (faceGatePerson == null) {
                    continue;
                }
                faceGatePerson.setTaskType(TaskType.EMP_AUTH_ERROR.getValue());
                faceGatePerson.setMessage(SzycErrorCodeUtils.parseAddPersonsErrorCode(addPersonState.getErrcode()));
                appFaceGatePersonMapper.updateByPrimaryKeySelective(faceGatePerson);
            }
        }
    }

    /**
     * 批量取消授权
     *
     * @param result 取消授权结果
     */
    private void handlerDelPersonResult(DelPersonsResult result) {
        String deviceKey = result.getFacesluiceId();
        List<PersonInfo> delSucInfo = result.getDelSucInfo();
        List<PersonInfo> delErrInfo = result.getDelErrInfo();
//        for (PersonInfo personInfo : delSucInfo) {
//            AppFaceGatePerson faceGatePerson = appFaceGatePersonMapper.selectByPersonGuidAndDeviceKey(personInfo.getCustomId(), deviceKey);
//            if (faceGatePerson != null) {
//                appFaceGatePersonMapper.deleteByPrimaryKey(faceGatePerson.getId());
//            }
//        }
        if (delSucInfo != null && delSucInfo.size() > 0) {
            List<String> personGuids = delSucInfo.stream().map(PersonInfo::getCustomId).collect(Collectors.toList());
            logger.info("批量取消授权成功, deviceKey:{}, personGuids:{}", deviceKey, personGuids);
            appFaceGatePersonMapper.delBYDeviceKeyAndParsonGuid(deviceKey, personGuids);
        }
        if (delErrInfo != null && delErrInfo.size() > 0) {
            List<String> personGuids = delErrInfo.stream().map(PersonInfo::getCustomId).collect(Collectors.toList());
            logger.info("批量取消授权失败, deviceKey:{}, personGuids:{}", deviceKey, personGuids);
            appFaceGatePersonMapper.delBYDeviceKeyAndParsonGuid(deviceKey, personGuids);
        }
    }

    /**
     * 心跳处理
     *
     * @param heartInfo
     */
    private void handleHeartBeat(HeartInfo heartInfo) {
        appFaceGateManager.handleHeart(heartInfo.getFacesluiceId());
    }


    /**
     * 保存查询人员到队列中
     *
     * @param customId 人员唯一标识
     */
    private void saveSearchPersonToQueue(String customId, String deviceKey) {
        String messageId = Long.toString(System.currentTimeMillis());
        Person p = new Person();
        p.setCustomId(customId);
        p.setPicture(1);
        SearchPerson searchPerson = new SearchPerson(messageId, p);
        String topic = MessageFormat.format(SzycConst.TOPIC_REQ, deviceKey);
        searchPerson.setTopic(topic);
        mqttEmpRedisDao.saveMqttTopic(searchPerson);
    }

    /**
     * MQTT-风潮 性别标识转换
     * MQTT性别字段标识   0-男  1-女
     * 风潮性别标识       1-男 2-女
     *
     * @param gender MQTT性别标识
     * @return 风潮性别标识
     */
    private Integer convertGender(String gender) {
        int sex = 1;
        if (gender != null && 1 == Integer.parseInt(gender)) {
            sex = 2;
        }
        return sex;
    }

    /**
     * 上传识别照片
     *
     * @param base64Str base64
     * @return 图片地址
     */
    private String uploadImg(String base64Str, Date time) {
        String imgUrl = "";
        if (StringUtils.isEmpty(base64Str)) {
            return imgUrl;
        }
        try {
            String imgData = Base64Util.getImageData(base64Str);
            byte[] imageByte = Base64Util.decode(imgData);
            byte[] dstBytes = ImageUtil.compressPicForScale(imageByte, 45);
            ByteArrayInputStream inputStream = new ByteArrayInputStream(dstBytes);
            String ossPath = "emp/face/" + DateUtil.formatDate(time, "yyyyMMdd");
            String path = filePathConfig.getFilePath(ossPath, RandomUtil.getRandomFileName(), "jpg");
            imgUrl = fileHandler.upload(path, inputStream);
        } catch (Exception e) {
            logger.warn("考勤识别上传base64图片失败", e);
        }
        return imgUrl;
    }
}
