package com.whfc.emp.dao;

import com.whfc.emp.dto.AppFenceDTO;
import com.whfc.emp.entity.AppEmpWarnRuleFence;
import com.whfc.emp.param.AppEmpWarnAddParam;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
public interface AppEmpWarnRuleFenceMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppEmpWarnRuleFence record);

    int insertSelective(AppEmpWarnRuleFence record);

    AppEmpWarnRuleFence selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppEmpWarnRuleFence record);

    int updateByPrimaryKey(AppEmpWarnRuleFence record);

    /**
     * 逻辑删除
     *
     * @param ruleId
     */
    void deleteLogicByRuleId(@Param("ruleId") Integer ruleId);

    /**
     * 插入
     *
     * @param param
     */
    void insertSelectiveByParam(AppEmpWarnAddParam param);

    /**
     * 根据规则id 查找电子围栏
     *
     * @param ruleId
     * @return
     */
    AppFenceDTO selectByRuleId(@Param("ruleId") Integer ruleId);
}