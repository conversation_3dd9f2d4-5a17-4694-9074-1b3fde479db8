package com.whfc.emp.dao;

import com.whfc.emp.entity.AppTrainAttachment;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AppTrainAttachmentMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppTrainAttachment record);

    int insertSelective(AppTrainAttachment record);

    AppTrainAttachment selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppTrainAttachment record);

    int updateByPrimaryKey(AppTrainAttachment record);

    /**
     * 批量添加附件
     *
     * @param trainId
     * @param type
     * @param trainPhotos
     */
    void insertAll(@Param("trainId") Integer trainId,
                   @Param("type") Integer type,
                   @Param("trainPhotos") List<String> trainPhotos);

    /**
     * 使用培训id查询附件
     *
     * @param trainId
     * @return
     */
    List<AppTrainAttachment> selectByTrainId(Integer trainId);

    /**
     * 软删除附件
     *
     * @param trainId
     */
    void delByTrainId(Integer trainId);
}