package com.whfc.emp.dao;

import com.whfc.emp.dto.AppEmpApplyDTO;
import com.whfc.emp.dto.AppEmpApplyNumDTO;
import com.whfc.emp.entity.AppEmpApply;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface AppEmpApplyMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppEmpApply record);

    int insertSelective(AppEmpApply record);

    AppEmpApply selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppEmpApply record);

    int updateByPrimaryKey(AppEmpApply record);

    /**
     * 查询审核记录
     *
     * @param deptId
     * @param state
     * @param startTime
     * @param endTime
     * @return
     */
    List<AppEmpApplyDTO> selectByDeptId(@Param("deptId") Integer deptId, @Param("state") Integer state,
                                        @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 统计人员申请信息
     *
     * @param deptId
     * @param startTime
     * @param endTime
     * @return
     */
    AppEmpApplyNumDTO selectNumBy(@Param("deptId") Integer deptId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 删除申请人员
     *
     * @param applyEmpId
     */
    void del(Integer applyEmpId);

    /**
     * 项目 身份证号查找人员
     *
     * @param deptId
     * @param idCardNo
     * @return
     */
    AppEmpApply selectByDeptIdAndIdCardNo(@Param("deptId") Integer deptId, @Param("idCardNo") String idCardNo);
}