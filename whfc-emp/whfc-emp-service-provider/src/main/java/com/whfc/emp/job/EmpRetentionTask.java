package com.whfc.emp.job;

import com.alibaba.fastjson.JSONObject;
import com.whfc.XxlJobConfig;
import com.whfc.common.constant.QueueConst;
import com.whfc.common.enums.RecMode;
import com.whfc.common.util.DateUtil;
import com.whfc.common.util.JSONUtil;
import com.whfc.emp.dao.AppEmpDataMapper;
import com.whfc.emp.dao.AppFaceGateMapper;
import com.whfc.emp.dao.AppIndoorPositionTagMapper;
import com.whfc.emp.dto.AppEmpDTO;
import com.whfc.emp.dto.AppFaceGateDTO;
import com.whfc.emp.dto.indoor.AppIndoorUwbTTL;
import com.whfc.emp.entity.AppIndoorPositionTag;
import com.whfc.emp.enums.Direction;
import com.whfc.emp.param.EmpAttendSyncDataParam;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.stream.Collectors;

/**
 * 人员滞留检测Task
 */
@Component
@ConditionalOnBean(XxlJobConfig.class)
public class EmpRetentionTask {

    private Logger logger = LoggerFactory.getLogger(EmpRetentionTask.class);

    private static final Random random = new Random();

    @Autowired
    private AppIndoorPositionTagMapper indoorPositionTagMapper;

    @Autowired
    private AppEmpDataMapper appEmpDataMapper;

    @Autowired
    private AppFaceGateMapper faceGateMapper;

    @Autowired
    private AmqpTemplate amqpTemplate;

    /**
     * 人员滞留检测
     */
    @XxlJob("emp-retention-check")
    public void retentionCheck() {
        try {
            XxlJobHelper.log("人员滞留检测");
            String paramStr = XxlJobHelper.getJobParam();
            Date now = new Date();
            if (StringUtils.isNotEmpty(paramStr)) {
                JSONObject param = JSONObject.parseObject(paramStr);
                if (param.containsKey("deptIds")) {
                    List<Integer> deptIds = param.getObject("deptIds", List.class);
                    Integer minutes = param.getInteger("minutes");
                    logger.info("人员滞留检测,deptIds:{},minutes:{}", deptIds, minutes);
                    Date endTime = DateUtil.addMinutes(now, -minutes);
                    for (Integer deptId : deptIds) {
                        List<AppFaceGateDTO> faceGateList = faceGateMapper.selectByDeptId(deptId, null);
                        Map<Integer, List<AppFaceGateDTO>> areaMap = faceGateList.stream().filter(dto -> dto.getAreaId() != null && Direction.OUT.getValue().equals(dto.getDirection())).collect(Collectors.groupingBy(AppFaceGateDTO::getAreaId));
                        for (Integer areaId : areaMap.keySet()) {
                            List<AppFaceGateDTO> faceGates = areaMap.get(areaId);
                            AppFaceGateDTO faceGate = faceGates.get(0);
                            String areaName = faceGate.getAreaName();
                            List<AppEmpDTO> localeEmpList = appEmpDataMapper.selectAreaLocaleEmpListByTime(deptId, areaId, null, endTime, null);
                            logger.info("人员滞留检测,deptId:{},areaId:{},areaName:{},faceGate:{},localeEmpList:{}", deptId, areaId, areaName, faceGate.getName(), localeEmpList.size());
                            for (AppEmpDTO emp : localeEmpList) {
                                logger.info("人员滞留检测,deptId:{},areaId:{},areaName:{},emp:{},localeTime:{}", deptId, areaId, areaName, emp.getEmpName(), DateUtil.formatDateTime(emp.getLocaleTime()));

                                EmpAttendSyncDataParam dataParam = new EmpAttendSyncDataParam();
                                dataParam.setRecMode(RecMode.FACE.getValue());
                                dataParam.setDeptId(deptId);
                                dataParam.setShowTime(now);
                                dataParam.setPlatform(faceGate.getPlatform());
                                dataParam.setDeviceKey(faceGate.getDeviceKey());
                                dataParam.setFaceGateId(faceGate.getFaceGateId());
                                dataParam.setDeviceName(faceGate.getName());
                                dataParam.setDirection(faceGate.getDirection());
                                dataParam.setLng(faceGate.getLng());
                                dataParam.setLat(faceGate.getLat());
                                dataParam.setAreaId(faceGate.getAreaId());
                                dataParam.setAreaName(faceGate.getAreaName());
                                dataParam.setEmpId(emp.getEmpId());
                                dataParam.setEmpName(emp.getEmpName());
                                dataParam.setPersonGuid("");
                                dataParam.setIdCardNo("");
                                //推送消息
                                String jsonStr = JSONUtil.toString(dataParam);
                                amqpTemplate.convertAndSend(QueueConst.EMP_FACEGATE_ATTEND_DATA, jsonStr);
                            }
                        }
                    }
                }
            }

        } catch (Exception ex) {
            XxlJobHelper.handleFail("人员滞留检测,出现异常 error:" + ex.getMessage());
        }
    }

    /**
     * UWB佩戴检测
     * 1.在洞内,且进洞时间在20分钟-11小时40分钟 2.uwb定位时间超过20分钟 ==> 自动生成uwb定位数据(位置200-300米,时间20-25分钟)
     */
    @XxlJob("emp-uwb-check")
    public void uwbCheck() {
        try {
            XxlJobHelper.log("UWB佩戴检测");
            String paramStr = XxlJobHelper.getJobParam();
            Date now = new Date();
            if (StringUtils.isNotEmpty(paramStr)) {
                JSONObject param = JSONObject.parseObject(paramStr);
                if (param.containsKey("deptIds")) {
                    List<Integer> deptIds = param.getObject("deptIds", List.class);
                    logger.info("UWB佩戴检测,deptIds:{}", deptIds);
                    Date startTime = DateUtil.addMinutes(now, -700);  //700分钟前
                    Date endTime = DateUtil.addMinutes(now, -20); //20分钟前
                    for (Integer deptId : deptIds) {
                        List<AppFaceGateDTO> faceGateList = faceGateMapper.selectByDeptId(deptId, null);
                        Map<Integer, List<AppFaceGateDTO>> areaMap = faceGateList.stream().filter(dto -> dto.getAreaId() != null && Direction.OUT.getValue().equals(dto.getDirection())).collect(Collectors.groupingBy(AppFaceGateDTO::getAreaId));
                        for (Integer areaId : areaMap.keySet()) {
                            List<AppFaceGateDTO> faceGates = areaMap.get(areaId);
                            AppFaceGateDTO faceGate = faceGates.get(0);
                            String areaName = faceGate.getAreaName();
                            List<AppEmpDTO> localeEmpList = appEmpDataMapper.selectAreaLocaleEmpListByTime(deptId, areaId, startTime, endTime, null);
                            logger.info("UWB佩戴检测,deptId:{},areaId:{},areaName:{},localeEmpList:{}", deptId, areaId, areaName, localeEmpList.size());
                            for (AppEmpDTO emp : localeEmpList) {
                                AppIndoorPositionTag tag = indoorPositionTagMapper.selectByDeptIdAndEmpId(deptId, emp.getEmpId());
                                if (tag != null) {
                                    boolean push = false;
                                    //无定位数据
                                    if (tag.getTime() == null) {
                                        push = true;
                                    }
                                    //有定位数据
                                    if (tag.getTime() != null && now.getTime() - tag.getTime().getTime() > 20 * 60 * 1000) {
                                        push = true;
                                    }
                                    if (push) {
                                        logger.info("UWB佩戴检测,deptId:{},areaId:{},areaName:{},emp:{},localeTime:{},tagTime:{}", deptId, areaId, areaName, emp.getEmpName(), DateUtil.formatDateTime(emp.getLocaleTime()), DateUtil.formatDateTime(tag.getTime()));
                                        int ttl = random.nextInt(60) * 1000;            // 延迟秒数
                                        int distance = random.nextInt(100) + 200;       // 200-300米
                                        AppIndoorUwbTTL ttlDTO = new AppIndoorUwbTTL();
                                        ttlDTO.setDeptId(deptId);
                                        ttlDTO.setEmpId(emp.getEmpId());
                                        ttlDTO.setTtl(ttl);
                                        ttlDTO.setHoleDistance(distance);
                                        amqpTemplate.convertAndSend(QueueConst.UWB_TTL_EXCHANGE, QueueConst.UWB_TTL_QUEUE, JSONUtil.toString(ttlDTO), message -> {
                                            message.getMessageProperties().setExpiration(String.valueOf(ttl));
                                            return message;
                                        });
                                    }
                                }
                            }
                        }
                    }
                }
            }

        } catch (Exception ex) {
            XxlJobHelper.handleFail("UWB佩戴检测,出现异常 error:" + ex.getMessage());
        }
    }
}
