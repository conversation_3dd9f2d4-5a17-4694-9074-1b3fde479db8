package com.whfc.emp.redis.impl;

import com.whfc.common.util.JSONUtil;
import com.whfc.emp.entity.AppIndoorPositionMap;
import com.whfc.emp.entity.AppIndoorPositionStation;
import com.whfc.emp.entity.AppIndoorPositionTag;
import com.whfc.emp.redis.IndoorPositionRedisDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

@Component
public class IndoorPositionRedisDaoImpl implements IndoorPositionRedisDao {

    private Logger logger = LoggerFactory.getLogger(IndoorPositionRedisDaoImpl.class);

    private static final String map_key = "indoor-map";

    private static final String station_key = "indoor-station";

    private static final String tag_key = "indoor-tag::%s";

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Override
    public void setMap(String guid, AppIndoorPositionMap map) {
        String key = map_key;
        String val = JSONUtil.toString(map);
        redisTemplate.opsForHash().put(key, guid, val);
    }

    @Override
    public AppIndoorPositionMap getMap(String guid) {
        String key = map_key;
        if (redisTemplate.opsForHash().hasKey(key, guid)) {
            Object val = redisTemplate.opsForHash().get(key, guid);
            return JSONUtil.parseObject(String.valueOf(val), AppIndoorPositionMap.class);
        }
        return null;
    }

    @Override
    public void delMap(String guid) {
        String key = map_key;
        redisTemplate.opsForHash().delete(key, guid);
    }

    @Override
    public void setStation(String guid, AppIndoorPositionStation station) {
        String key = station_key;
        String val = JSONUtil.toString(station);
        redisTemplate.opsForHash().put(key, guid, val);
    }

    @Override
    public AppIndoorPositionStation getStation(String guid) {
        String key = station_key;
        if (redisTemplate.opsForHash().hasKey(key, guid)) {
            Object val = redisTemplate.opsForHash().get(key, guid);
            return JSONUtil.parseObject(String.valueOf(val), AppIndoorPositionStation.class);
        }
        return null;
    }

    @Override
    public void delStation(String guid) {
        String key = station_key;
        redisTemplate.opsForHash().delete(key, guid);
    }

    @Override
    public void setTag(Integer deptId, String guid, AppIndoorPositionTag tag) {
        String key = String.format(tag_key, deptId);
        String val = JSONUtil.toString(tag);
        redisTemplate.opsForHash().put(key, guid, val);
    }

    @Override
    public AppIndoorPositionTag getTag(Integer deptId, String guid) {
        String key = String.format(tag_key, deptId);
        if (redisTemplate.opsForHash().hasKey(key, guid)) {
            Object val = redisTemplate.opsForHash().get(key, guid);
            return JSONUtil.parseObject(String.valueOf(val), AppIndoorPositionTag.class);
        }
        return null;
    }

    @Override
    public void delTag(Integer deptId, String guid) {
        String key = String.format(tag_key, deptId);
        redisTemplate.opsForHash().delete(key, guid);
    }
}
