package com.whfc.emp.third.impl;

import com.whfc.common.exception.BizException;
import com.whfc.common.third.wuhu.WhHdApi;
import com.whfc.common.util.Base64Util;
import com.whfc.emp.entity.AppEmp;
import com.whfc.emp.entity.AppSync;
import com.whfc.emp.third.EmpThirdVerify;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * @Description: 人员信息-第三方核验-芜湖互动系统人证核验
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/6/30 14:45
 */
@Service
public class EmpThirdVerifyWuhu implements EmpThirdVerify {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Override
    public void verifyEmp(AppEmp emp, AppSync config) throws BizException {

        String idCard = emp.getIdCardNo();
        String userName = emp.getEmpName();
        String idCardImg = Base64Util.getUrlImageToBase64(emp.getIdCardFront());
        String faceImg = Base64Util.getUrlImageToBase64(emp.getAvatar());

        String host = config.getHost();
        String appCode = config.getAppKey();
        String appSecret = config.getAppSecret();
        String xmbm = config.getExt1();
        String equNo = config.getExt2();
        WhHdApi api = new WhHdApi(host, appCode, appSecret, xmbm, equNo);
        api.ryrz(idCard, userName, idCardImg, faceImg);
    }
}
