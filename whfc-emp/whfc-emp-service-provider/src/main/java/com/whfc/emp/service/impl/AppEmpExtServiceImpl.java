package com.whfc.emp.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.whfc.common.enums.EnableState;
import com.whfc.common.enums.I18nErrorCode;
import com.whfc.common.exception.BizException;
import com.whfc.common.result.PageData;
import com.whfc.common.result.ResultEnum;
import com.whfc.common.util.PageUtil;
import com.whfc.emp.dao.*;
import com.whfc.emp.dto.*;
import com.whfc.emp.entity.*;
import com.whfc.emp.enums.EmpHealthInfoType;
import com.whfc.emp.param.*;
import com.whfc.emp.service.AppEmpExtService;
import com.whfc.emp.third.EmpThirdSyncFactory;
import com.whfc.emp.third.EmpThirdSyncWapper;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2022/11/7 14:32
 */
@DubboService(interfaceClass = AppEmpExtService.class, version = "1.0.0", timeout = 30 * 1000)
public class AppEmpExtServiceImpl implements AppEmpExtService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private AppEmpMapper appEmpMapper;

    @Autowired
    private AppEmpContractMapper appEmpContractMapper;

    @Autowired
    private AppEmpContractAttachMapper appEmpContractAttachMapper;

    @Autowired
    private AppEmpBankMapper appEmpBankMapper;

    @Autowired
    private AppEmpConfessMapper appEmpConfessMapper;

    @Autowired
    private AppEmpConfessImgMapper appEmpConfessImgMapper;

    @Autowired
    private AppEmpHealthReportMapper appEmpHealthReportMapper;

    @Autowired
    private AppEmpHealthReportImgMapper appEmpHealthReportImgMapper;

    @Autowired
    private AppEmpRewardPunishmentMapper appEmpRewardPunishmentMapper;

    @Autowired
    private AppEmpBlackListMapper appEmpBlackListMapper;

    @Autowired
    private AppEmpRiskMapper appEmpRiskMapper;

    @Autowired
    private AppEmpRiskEmpMapper appEmpRiskEmpMapper;

    @Autowired
    private AppEmpCertMapper appEmpCertMapper;

    @Autowired
    private AppEmpCertAttachMapper appEmpCertAttachMapper;

    @Autowired
    private EmpThirdSyncFactory empThirdSyncFactory;

    /********合同*********/

    @Override
    public List<AppEmpContractDTO> contractList(Integer empId) throws BizException {
        logger.info("合同列表:empId:{}", empId);
        List<AppEmpContractDTO> list = appEmpContractMapper.selectByEmpId(empId);
        for (AppEmpContractDTO contractDTO : list) {
            Integer id = contractDTO.getId();
            List<AppAttachDTO> attachList = appEmpContractAttachMapper.selectByContractId(id);
            contractDTO.setFileAttachList(attachList);
            contractDTO.setFileNo(attachList.size());
        }
        return list;
    }

    @Override
    public void addContract(AppContractAddParam param) throws BizException {
        logger.info("添加合同,param:{}", param);

        //查询人员
        AppEmp emp = appEmpMapper.selectByPrimaryKey(param.getEmpId());
        Integer deptId = emp.getDeptId();

        // 根据合同编号去重
        String contractNo = param.getContractNo();
        AppEmpContract appEmpContract = appEmpContractMapper.selectByContractNo(deptId, contractNo);
        if (appEmpContract != null) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_005.getCode());
        }

        // 插入合同
        AppEmpContract record = new AppEmpContract();
        BeanUtils.copyProperties(param, record);
        record.setDeptId(deptId);
        appEmpContractMapper.insertSelective(record);
        Integer id = record.getId();

        // 插入合同附件
        List<AppAttachDTO> fileAttachList = param.getFileAttachList();
        if (fileAttachList != null && fileAttachList.size() > 0) {
            List<AppEmpContractAttach> list = new ArrayList();
            for (AppAttachDTO attachDTO : fileAttachList) {
                AppEmpContractAttach attach = new AppEmpContractAttach();
                attach.setContractId(id);
                attach.setName(attachDTO.getName());
                attach.setUrl(attachDTO.getUrl());
                list.add(attach);
            }
            appEmpContractAttachMapper.batchInsert(list);
        }
    }

    @Override
    public void editContract(AppContractEditParam param) throws BizException {
        logger.info("添加合同,编辑合同:{}", JSONObject.toJSONString(param));
        Integer contractId = param.getId();
        String contractNo = param.getContractNo();

        //已有合同
        AppEmpContract contract = appEmpContractMapper.selectByPrimaryKey(contractId);
        Integer deptId = contract.getDeptId();

        // 根据合同编号去重
        AppEmpContract appEmpContract = appEmpContractMapper.selectByContractNo(deptId, contractNo);
        if (appEmpContract != null && !appEmpContract.getId().equals(contractId)) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_005.getCode());
        }

        // 编辑合同
        AppEmpContract record = new AppEmpContract();
        BeanUtils.copyProperties(param, record);
        record.setDeptId(deptId);
        appEmpContractMapper.updateByPrimaryKeySelective(record);

        // 插入合同附件
        appEmpContractAttachMapper.deleteLogicByContractId(contractId);
        List<AppAttachDTO> fileAttachList = param.getFileAttachList();
        if (fileAttachList != null && fileAttachList.size() > 0) {
            List<AppEmpContractAttach> list = new ArrayList();
            for (AppAttachDTO attachDTO : fileAttachList) {
                AppEmpContractAttach attach = new AppEmpContractAttach();
                attach.setName(attachDTO.getName());
                attach.setContractId(contractId);
                attach.setUrl(attachDTO.getUrl());
                list.add(attach);
            }
            appEmpContractAttachMapper.batchInsert(list);
        }
    }

    @Override
    public void delContract(Integer id) throws BizException {
        logger.info("删除合同:id:{}", id);
        appEmpContractMapper.deleteLogicById(id);
    }

    @Override
    public void thirdSyncContract(Integer id) throws BizException {
        logger.info("人员劳动合同-第三方同步,合同id:{}", id);
        AppEmpContract contract = appEmpContractMapper.selectByPrimaryKey(id);
        Integer empId = contract.getEmpId();
        AppEmp emp = appEmpMapper.selectByPrimaryKey(empId);
        Integer deptId = emp.getDeptId();
        List<EmpThirdSyncWapper> wapperList = empThirdSyncFactory.sync(deptId);
        for (EmpThirdSyncWapper wapper : wapperList) {
            wapper.getSync().syncEmpContract(emp, contract, wapper.getConfig());
        }
    }

    /********证书*********/

    @Override
    public List<AppEmpCertDTO> certList(Integer empId) throws BizException {
        logger.info("人员证书列表,empId:{}", empId);
        List<AppEmpCertDTO> list = appEmpCertMapper.selectByEmpId(empId);
        for (AppEmpCertDTO empCertDTO : list) {
            Integer certId = empCertDTO.getCertId();
            List<AppAttachDTO> attachList = appEmpCertAttachMapper.selectByCertId(certId);
            empCertDTO.setFileAttachList(attachList);
            empCertDTO.setFileNo(attachList.size());
        }
        return list;
    }

    @Override
    public void addCert(AppEmpCertAddParam request) throws BizException {
        logger.info("添加证书:request:{}", request);

        // 验证人员信息
        Integer empId = request.getEmpId();
        AppEmp appEmp = appEmpMapper.selectByPrimaryKey(empId);
        if (appEmp == null) {
            return;
        }

        // 验证证书编号
        Integer deptId = appEmp.getDeptId();
        String certCode = request.getCertCode();
        AppEmpCert appEmpCert = appEmpCertMapper.selectByCertCode(deptId, certCode);
        if (appEmpCert != null) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_009.getCode());
        }

        // 插入证书
        AppEmpCert cert = new AppEmpCert();
        cert.setDeptId(deptId);
        cert.setEmpId(empId);
        cert.setCertTypeId(request.getCertTypeId());
        cert.setCertTypeName(request.getCertTypeName());
        cert.setOperationItemId(request.getOperationItemId());
        cert.setOperationItemName(request.getOperationItemName());
        cert.setCertName(request.getCertName());
        cert.setCertCode(request.getCertCode());
        cert.setLevel(request.getLevel());
        cert.setCertStartDate(request.getCertStartDate());
        cert.setCertExpireDate(request.getCertExpireDate());
        cert.setCertGrantOrg(request.getCertGrantOrg());
        appEmpCertMapper.insertSelective(cert);
        Integer certId = cert.getId();

        // 插入证书附件
        List<AppAttachDTO> fileAttachList = request.getFileAttachList();
        if (fileAttachList != null && fileAttachList.size() > 0) {
            appEmpCertAttachMapper.batchInsert(deptId, certId, fileAttachList);
            appEmpCertMapper.updateCertFile(certId, fileAttachList.get(0).getUrl());
        }

    }

    @Override
    public void editCert(AppEmpCertEditParam request) throws BizException {
        logger.info("编辑证书,request：{}", JSONObject.toJSONString(request));

        // 验证证书
        Integer certId = request.getCertId();
        AppEmpCert cert = appEmpCertMapper.selectByPrimaryKey(certId);
        if (cert == null) {
            return;
        }

        // 根据证书编号去重
        Integer deptId = cert.getDeptId();
        String certCode = request.getCertCode();
        AppEmpCert appEmpCert = appEmpCertMapper.selectByCertCode(deptId, certCode);
        if (appEmpCert != null && !appEmpCert.getId().equals(certId)) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_009.getCode());
        }

        // 编辑证书
        cert.setDeptId(deptId);
        cert.setCertTypeName(request.getCertTypeName());
        cert.setCertTypeId(request.getCertTypeId());
        cert.setOperationItemName(request.getOperationItemName());
        cert.setOperationItemId(request.getOperationItemId());
        cert.setCertName(request.getCertName());
        cert.setCertCode(request.getCertCode());
        cert.setLevel(request.getLevel());
        cert.setCertStartDate(request.getCertStartDate());
        cert.setCertExpireDate(request.getCertExpireDate());
        cert.setCertGrantOrg(request.getCertGrantOrg());
        appEmpCertMapper.updateByPrimaryKeySelective(cert);

        // 插入证书附件
        appEmpCertAttachMapper.deleteLogicByCertId(certId);
        List<AppAttachDTO> fileAttachList = request.getFileAttachList();
        if (fileAttachList != null && fileAttachList.size() > 0) {
            appEmpCertAttachMapper.batchInsert(deptId, certId, fileAttachList);
            appEmpCertMapper.updateCertFile(certId, fileAttachList.get(0).getUrl());
        }
    }

    @Override
    public void delCert(Integer certId) throws BizException {
        logger.info("删除人员证书:certId:{}", certId);
        // 逻辑删除证书
        appEmpCertMapper.deleteLogic(certId);
        // 逻辑删除证书附件
        appEmpCertAttachMapper.deleteLogicByCertId(certId);
    }

    @Override
    public void syncCert(Integer certId) throws BizException {
        AppEmpCert cert = appEmpCertMapper.selectByPrimaryKey(certId);
        if (cert == null) {
            return;
        }
        Integer deptId = cert.getDeptId();
        List<EmpThirdSyncWapper> wapperList = empThirdSyncFactory.sync(deptId);
        for (EmpThirdSyncWapper wapper : wapperList) {
            wapper.getSync().syncEmpCert(cert, wapper.getConfig());
        }
    }

    /********银行卡*********/

    @Override
    public List<AppEmpBankDTO> bankList(Integer empId) throws BizException {
        logger.info("人员银行账户列表:empId:{}", empId);
        return appEmpBankMapper.selectBankDTOList(empId);
    }

    @Override
    public void addBank(AppEmpBankAddParam param) throws BizException {
        logger.info("添加银行账户,param:{}", param);
        Integer empId = param.getEmpId();
        String bankCode = param.getBankCode();
        String bankName = param.getBankName();
        String bankNumber = param.getBankNumber();

        // 根据银行账号去重
        AppEmpBank appEmpBank = appEmpBankMapper.selectByBankNumber(bankNumber);
        if (appEmpBank != null) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_010.getCode());
        }

        AppEmpBank record = new AppEmpBank();
        record.setEmpId(empId);
        record.setBankCode(bankCode);
        record.setBankNumber(bankNumber);
        record.setBankName(bankName);
        appEmpBankMapper.insertSelective(record);
    }

    @Override
    public void editBank(AppEmpBankEditParam param) throws BizException {
        logger.info("编辑银行账户,param:{}", JSONObject.toJSONString(param));
        Integer bankId = param.getBankId();
        String bankCode = param.getBankCode();
        String bankName = param.getBankName();
        String bankNumber = param.getBankNumber();

        // 根据银行账号去重
        AppEmpBank appEmpBank = appEmpBankMapper.selectByBankNumber(bankNumber);
        if (appEmpBank != null && !bankId.equals(appEmpBank.getId())) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_010.getCode());
        }

        AppEmpBank record = new AppEmpBank();
        record.setId(bankId);
        record.setBankCode(bankCode);
        record.setBankNumber(bankNumber);
        record.setBankName(bankName);
        appEmpBankMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public void enableBank(EnableBankParam param) throws BizException {
        logger.info("启用禁用银行账户,param:{}", param);
        Integer bankId = param.getBankId();
        Integer enableFlag = param.getEnableFlag();
        AppEmpBank record = new AppEmpBank();
        record.setId(bankId);
        record.setEnableFlag(enableFlag);
        appEmpBankMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public void delBank(Integer bankId) throws BizException {
        logger.info("删除银行账户,bankId:{}", bankId);
        AppEmpBank appEmpBank = appEmpBankMapper.selectByPrimaryKey(bankId);
        if (appEmpBank == null) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_011.getCode());
        }
        if (EnableState.ENABLED.getValue().equals(appEmpBank.getEnableFlag())) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_018.getCode());
        }
        appEmpBankMapper.deleteLogicById(bankId);
    }


    /********安全交底*********/

    @Override
    public PageData<AppEmpConfessDTO> confessList(Integer empId, Integer pageNum, Integer pageSize) throws BizException {
        logger.info("获取安全交底列表, empId:{}, pageNum:{}, pageSize:{}", empId, pageNum, pageSize);
        PageHelper.startPage(pageNum, pageSize);
        List<AppEmpConfessDTO> list = appEmpConfessMapper.selectByEmpId(empId);
        PageHelper.clearPage();
        //获取图片
        for (AppEmpConfessDTO appEmpConfessDTO : list) {
            List<String> imgUrlList = appEmpConfessImgMapper.selectImgUrlByConfessId(appEmpConfessDTO.getConfessId());
            appEmpConfessDTO.setImgUrlList(imgUrlList);
        }
        return PageUtil.pageData(PageInfo.of(list));
    }

    @Override
    public void addConfess(AppEmpConfessAddParam param) throws BizException {
        logger.info("新增安全交底, param:{}", param);
        AppEmpConfess confess = new AppEmpConfess();
        BeanUtils.copyProperties(param, confess);
        appEmpConfessMapper.insertSelective(confess);

        //保存安全交底图片
        List<String> imgUrlList = param.getImgUrlList();
        if (imgUrlList == null || imgUrlList.isEmpty()) {
            return;
        }
        saveConfessImg(confess.getId(), imgUrlList);
    }

    @Override
    public void editConfess(AppEmpConfessEditParam param) throws BizException {
        logger.info("编辑安全交底, param:{}", param);
        AppEmpConfess confess = appEmpConfessMapper.selectByPrimaryKey(param.getConfessId());
        if (confess == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.EMP_BE_019.getCode());
        }
        BeanUtils.copyProperties(param, confess);
        appEmpConfessMapper.updateByPrimaryKeySelective(confess);

        //清除旧的图片
        appEmpConfessImgMapper.logicDelByConfessId(confess.getId());
        //保存安全交底图片
        List<String> imgUrlList = param.getImgUrlList();
        if (imgUrlList == null || imgUrlList.isEmpty()) {
            return;
        }
        saveConfessImg(confess.getId(), imgUrlList);

    }

    @Override
    public void delConfess(Integer confessId) throws BizException {
        logger.info("删除安全交底, confessId:{}", confessId);
        appEmpConfessMapper.logicDel(confessId);
        //清除图片
        appEmpConfessImgMapper.logicDelByConfessId(confessId);
    }

    @Override
    public void saveConfessSignImg(Integer confessId, String imgUrl) throws BizException {
        logger.info("保存安全交底图片, confessId:{}, imgUrl:{}", confessId, imgUrl);
        appEmpConfessMapper.updateSignImg(confessId, imgUrl);
    }

    /**
     * 保存安全交底图片
     *
     * @param confessId  安全交底ID
     * @param imgUrlList 图片地址
     */
    private void saveConfessImg(Integer confessId, List<String> imgUrlList) {
        for (String imgUrl : imgUrlList) {
            AppEmpConfessImg confessImg = new AppEmpConfessImg();
            confessImg.setConfessId(confessId);
            confessImg.setImgUrl(imgUrl);
            appEmpConfessImgMapper.insertSelective(confessImg);
        }
    }

    /********防疫信息*********/

    @Override
    public PageData<AppEmpHealthReportDTO> healthList(Integer empId, EmpHealthInfoType type, Integer pageNum, Integer pageSize) throws BizException {
        logger.info("获取防疫信息列表, empId:{}, type:{}, pageNum:{}, pageSize:{}", empId, type, pageNum, pageSize);
        PageHelper.startPage(pageNum, pageSize);
        if (empId == null || type == null) {
            return PageUtil.emptyPage();
        }
        List<AppEmpHealthReportDTO> list = appEmpHealthReportMapper.selectByEmpIdAndType(empId, type.getValue());
        PageHelper.clearPage();
        //获取图片
        if (list.isEmpty()) {
            return PageUtil.emptyPage();
        }
        for (AppEmpHealthReportDTO healthReportDTO : list) {
            List<String> imgList = appEmpHealthReportImgMapper.selectImgList(healthReportDTO.getHealthReportId());
            healthReportDTO.setImgList(imgList);
        }
        return PageUtil.pageData(PageInfo.of(list));
    }

    @Override
    public void addHealth(EmpHealthInfoAddParam param) throws BizException {
        logger.info("新增防疫信息, param:{}", param);
        AppEmpHealthReport healthReport = new AppEmpHealthReport();
        BeanUtils.copyProperties(param, healthReport);
        appEmpHealthReportMapper.insertSelective(healthReport);
        saveHealthImg(healthReport.getId(), param.getImgList());
    }

    @Override
    public void editHealth(EmpHealthInfoEditParam param) throws BizException {
        logger.info("修改防疫信息列表, param:{}", param);
        AppEmpHealthReport healthReport = appEmpHealthReportMapper.selectByPrimaryKey(param.getHealthReportId());
        if (healthReport == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.EMP_BE_020.getCode());
        }
        BeanUtils.copyProperties(param, healthReport);
        appEmpHealthReportMapper.updateByPrimaryKeySelective(healthReport);
        //删除图片
        appEmpHealthReportImgMapper.logDel(healthReport.getId());
        //保存图片
        saveHealthImg(healthReport.getId(), param.getImgList());

    }

    @Override
    public void delHealth(Integer healthInfoId) throws BizException {
        logger.info("删除防疫信息, healthInfoId:{}", healthInfoId);
        //删除防疫信息
        appEmpHealthReportMapper.logDel(healthInfoId);
        //删除防疫信息图片
        appEmpHealthReportImgMapper.logDel(healthInfoId);
    }

    /**
     * 保存防疫图片
     *
     * @param healthReportId 防疫信息ID
     * @param imgList        图片列表
     */
    private void saveHealthImg(Integer healthReportId, List<String> imgList) {
        //保存图片
        if (imgList == null || imgList.isEmpty()) {
            return;
        }
        for (String imgUrl : imgList) {
            AppEmpHealthReportImg img = new AppEmpHealthReportImg();
            img.setHealthReportId(healthReportId);
            img.setImgUrl(imgUrl);
            appEmpHealthReportImgMapper.insertSelective(img);
        }
    }

    /********奖惩信息*********/

    @Override
    public PageData<AppEmpRewardDTO> rewardList(Integer projectId, Integer pageNum, Integer pageSize, Date startDate, Date endDate, String keyword) {
        logger.info("奖惩记录列表,projectId:{},pageNum:{},pageSize:{},startDate:{},endDate:{},keyword:{}", projectId, pageNum, pageSize, startDate, endDate, keyword);
        PageHelper.startPage(pageNum, pageSize);
        List<AppEmpRewardDTO> list = appEmpRewardPunishmentMapper.selectByProjectId(projectId, startDate, endDate, keyword);
        return PageUtil.pageData(PageInfo.of(list));
    }

    @Override
    public List<AppEmpRewardDTO> rewardList(Integer empId) {
        logger.info("根据人员id查找奖惩记录列表,empId:{}", empId);
        List<AppEmpRewardDTO> list = appEmpRewardPunishmentMapper.selectByEmpId(empId);
        return list;
    }

    @Override
    public void addReward(AppEmpRewardAddParam param) {
        logger.info("添加奖惩记录,param:{}", param);
        AppEmpRewardPunishment record = new AppEmpRewardPunishment();
        BeanUtils.copyProperties(param, record);
        appEmpRewardPunishmentMapper.insertSelective(record);
    }

    @Override
    public void delReward(Integer id) {
        logger.info("删除奖惩记录,id:{}", id);
        appEmpRewardPunishmentMapper.deleteLogicById(id);
    }


    /********黑名单*********/

    @Override
    public PageData<AppEmpBlackDTO> blackList(Integer deptId, Integer pageNum, Integer pageSize, String keyword) {
        logger.info("查询人员黑名单列表，deptId：{}，pageNum：{}，pageSize：{}，keyword:{}", deptId, pageNum, pageSize, keyword);
        PageHelper.startPage(pageNum, pageSize);
        List<AppEmpBlackDTO> empBlackDTOList = appEmpBlackListMapper.selectByDeptIds(deptId, keyword);
        for (AppEmpBlackDTO appEmpBlackDTO : empBlackDTOList) {
            Integer empId = appEmpBlackDTO.getEmpId();
            AppEmp appEmp = appEmpMapper.selectByPrimaryKey(empId);
            if (appEmp != null) {
                appEmpBlackDTO.setGroupName(appEmp.getGroupName());
            }
        }
        PageInfo<AppEmpBlackDTO> of = PageInfo.of(empBlackDTOList);
        return PageUtil.pageData(of);
    }

    @Override
    public void blackAdd(AppEmpBlackAddParam request) {
        logger.info("添加人员黑名单,request:{}", request);
        List<AppEmpDTO> empList = request.getEmpList();
        String reason = request.getReason();
        Date time = new Date();
        List<AppEmpBlackList> list = new ArrayList();
        for (AppEmpDTO emp : empList) {
            AppEmpBlackList empBlackList = new AppEmpBlackList();
            empBlackList.setDeptId(emp.getDeptId());
            empBlackList.setEmpId(emp.getEmpId());
            empBlackList.setEmpName(emp.getEmpName());
            empBlackList.setIdCardNo(emp.getIdCardNo());
            empBlackList.setReason(reason);
            empBlackList.setTime(time);
            empBlackList.setUserId(request.getUserId());
            empBlackList.setUserName(request.getUserName());
            list.add(empBlackList);
        }
        appEmpBlackListMapper.batchInsert(list);
    }

    @Override
    public void blackDel(Integer id) {
        logger.info("删除黑名单，id:{}", id);
        appEmpBlackListMapper.deleteLogicById(id);
    }

    @Override
    public List<AppEmpDTO> getBlackEmpList(Integer deptId) {
        logger.info("查询黑名单:deptId:{}", deptId);
        List<AppEmpBlackDTO> empBlackDTOList = appEmpBlackListMapper.selectByDeptIds(deptId, null);
        List<Integer> empIdList = empBlackDTOList.stream().map(AppEmpBlackDTO::getEmpId).collect(Collectors.toList());
        return appEmpMapper.selectEmpListExclude(deptId, empIdList);
    }

    /********风险告知*********/

    @Override
    public PageData<AppEmpRiskDTO> riskList(Integer empId, Integer pageNum, Integer pageSize) throws BizException {
        logger.info("获取风险告知书列表, empId:{}, pageNum:{}, pageSize:{}", empId, pageNum, pageSize);
        AppEmp appEmp = appEmpMapper.selectByPrimaryKey(empId);
        if (appEmp == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.EMP_BE_001.getCode());
        }
        PageHelper.startPage(pageNum, pageSize);
        //获取风险告知书列表
        List<AppEmpRiskDTO> list = appEmpRiskMapper.selectRiskList(appEmp.getDeptId());
        PageHelper.clearPage();
        for (AppEmpRiskDTO appEmpRiskDTO : list) {
            //获取风险告知书签名情况
            AppEmpRiskEmp riskEmp = appEmpRiskEmpMapper.selectEmpList(appEmpRiskDTO.getRiskId(), empId);
            if (riskEmp != null) {
                appEmpRiskDTO.setSignTime(riskEmp.getSignTime());
                appEmpRiskDTO.setSignImgUrl(riskEmp.getSignImgUrl());
            }
        }
        return PageUtil.pageData(PageInfo.of(list));
    }

    @Override
    public void saveRiskSignImg(Integer riskId, Integer empId, String imgUrl) throws BizException {
        logger.info("保存风险告知书签名图片, riskId:{}, empId:{}, imgUrl:{}", riskId, empId, imgUrl);
        if (empId == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.EMP_BE_001.getCode());
        }
        AppEmpRiskEmp riskEmp = appEmpRiskEmpMapper.selectEmpList(riskId, empId);
        Date date = new Date();
        if (riskEmp != null) {
            riskEmp.setSignImgUrl(imgUrl);
            riskEmp.setSignTime(date);
            appEmpRiskEmpMapper.updateByPrimaryKeySelective(riskEmp);
        } else {
            riskEmp = new AppEmpRiskEmp();
            riskEmp.setRiskId(riskId);
            riskEmp.setEmpId(empId);
            riskEmp.setSignTime(date);
            riskEmp.setSignImgUrl(imgUrl);
            appEmpRiskEmpMapper.insertSelective(riskEmp);
        }
    }
}
