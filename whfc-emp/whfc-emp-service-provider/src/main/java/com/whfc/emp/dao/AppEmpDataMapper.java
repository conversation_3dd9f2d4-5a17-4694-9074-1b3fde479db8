package com.whfc.emp.dao;

import com.whfc.emp.dto.AppEmpAnaNumDTO;
import com.whfc.emp.dto.AppEmpDTO;
import com.whfc.emp.dto.AppEmpWorkRoleNumDTO;
import com.whfc.emp.dto.area.AppAreaAttendStat;
import com.whfc.emp.entity.AppEmpData;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface AppEmpDataMapper {
    int deleteByPrimaryKey(Integer id);

    int insertSelective(AppEmpData record);

    AppEmpData selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppEmpData record);

    /**
     * 插入 or 更新
     *
     * @param record
     * @return
     */
    void insertOrUpdate(AppEmpData record);

    /**
     * 查询人员数据
     *
     * @param empId
     * @return
     */
    AppEmpData selectByEmpId(@Param("empId") Integer empId);

    /**
     * 批量更新人员的在线状态为离线
     *
     * @return
     */
    int batchUpdateNetStateOffline();

    /**
     * 批量初始化人员的出勤状态
     *
     * @return
     */
    int batchUpdateAttendStateAbsence();

    /**
     * 更新人员-在场内
     *
     * @param empId
     * @param areaId
     * @param areaName
     * @param localeTime
     * @return
     */
    int updateEmpLocaleIn(@Param("empId") Integer empId, @Param("areaId") Integer areaId, @Param("areaName") String areaName, @Param("localeTime") Date localeTime);

    /**
     * 更新人员-在场外
     *
     * @param empId
     * @return
     */
    int updateEmpLocaleOut(@Param("empId") Integer empId);

    /**
     * 更新人员-出勤状态
     *
     * @param empId
     * @param attendState
     * @return
     */
    int updateEmpAttendState(@Param("empId") Integer empId, @Param("attendState") Integer attendState);

    /**
     * 清除人员数据
     *
     * @param empId
     * @return
     */
    int clearEmpData(@Param("empId") Integer empId);

    /**
     * 查找出勤人数
     *
     * @param deptIds
     * @return
     */
    Integer countAttendNum(@Param("deptIds") List<Integer> deptIds);

    /**
     * 统计场内人数
     *
     * @param deptIds
     * @return
     */
    Integer countLocaleNum(@Param("deptIds") List<Integer> deptIds);

    /**
     * 查询出勤统计
     *
     * @param deptId
     * @return
     */
    AppEmpAnaNumDTO selectAttendNum(@Param("deptId") Integer deptId);

    /**
     * 根据工人类型查询出勤统计
     *
     * @param deptId
     * @return
     */
    List<AppEmpWorkRoleNumDTO> countEmpAttendNumByWorkRole(@Param("deptId") Integer deptId);

    /**
     * 查询区域-在场统计
     *
     * @param deptId
     * @param areaId
     * @return
     */
    AppAreaAttendStat selectAreaLocaleStat(@Param("deptId") Integer deptId, @Param("areaId") Integer areaId);

    /**
     * 根据工人类型 查询区域-在场统计
     *
     * @param deptId
     * @param areaId
     * @return
     */
    List<AppEmpWorkRoleNumDTO> selectAreaLocaleStatByWorkRole(@Param("deptId") Integer deptId, @Param("areaId") Integer areaId);

    /**
     * 查询区域-在场统计(按班组)
     *
     * @param deptId
     * @param areaId
     * @return
     */
    List<AppAreaAttendStat> selectAreaLocaleGroupStat(@Param("deptId") Integer deptId, @Param("areaId") Integer areaId);

    /**
     * 查询区域-在场人员列表
     *
     * @param deptId
     * @param areaId
     * @return
     */
    List<AppEmpDTO> selectAreaLocaleEmpList(@Param("deptId") Integer deptId, @Param("areaId") Integer areaId);

    List<AppEmpDTO> selectAreaLocaleEmpListByTime(@Param("deptId") Integer deptId,
                                                  @Param("areaId") Integer areaId,
                                                  @Param("startTime") Date startTime,
                                                  @Param("endTime") Date endTime,
                                                  @Param("keyword") String keyword);

    /**
     * 根据人员ID集合查询人员数据
     * @param empIds
     * @return
     */
    List<AppEmpData> selectByEmpIdList(@Param("empIds") List<Integer> empIds);

}