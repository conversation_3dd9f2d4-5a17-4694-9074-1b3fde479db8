package com.whfc.emp.factory.impl;

import com.whfc.common.enums.Gender;
import com.whfc.common.enums.I18nErrorCode;
import com.whfc.common.exception.BizException;
import com.whfc.common.face.ruitong.*;
import com.whfc.common.file.FileHandler;
import com.whfc.common.geometry.Point;
import com.whfc.common.result.ResultEnum;
import com.whfc.common.util.Base64Util;
import com.whfc.common.util.ImageUtil;
import com.whfc.common.util.JSONUtil;
import com.whfc.emp.dao.AppEmpMapper;
import com.whfc.emp.dao.AppFaceGateMapper;
import com.whfc.emp.dao.AppFaceGatePersonMapper;
import com.whfc.emp.entity.AppEmp;
import com.whfc.emp.entity.AppFaceGate;
import com.whfc.emp.entity.AppFaceGateConfig;
import com.whfc.emp.entity.AppFaceGatePerson;
import com.whfc.emp.enums.TaskType;
import com.whfc.emp.factory.FaceGateManager;
import com.whfc.emp.mqtt.MqttConfig;
import com.whfc.emp.param.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.integration.core.MessageProducer;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * MQTT(睿瞳)
 *
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/1/5 16:38
 */
@Service("mqttRtFaceGateManagerImpl")
public class MqttRtFaceGateMangerImpl implements FaceGateManager {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Value("${mqtt.enabled}")
    private Boolean mqttEnable;

    @Autowired
    private AppFaceGateMapper appFaceGateMapper;

    @Autowired
    private AppFaceGatePersonMapper appFaceGatePersonMapper;

    @Autowired
    private AppEmpMapper appEmpMapper;

    @Autowired
    private FileHandler fileHandler;

    @Autowired(required = false)
    private MqttConfig.MqttMessageSender mqttMessageSender;

    @Autowired(required = false)
    private MessageProducer messageProducer;

    @Override
    public void add(AppFaceGateAddParam request, AppFaceGateConfig config) {
        String deviceKey = request.getDeviceKey();
        String name = request.getName();
        Integer deptId = request.getDeptId();
        Point point = request.getPoint();

        AppFaceGate faceGate = new AppFaceGate();
        faceGate.setDeviceKey(deviceKey);
        faceGate.setDeptId(deptId);
        faceGate.setName(name);
        faceGate.setDirection(request.getDirection());
        faceGate.setPlatform(request.getPlatform());
        faceGate.setModel(request.getModel());
        // 增加闸机位置信息
        if (point != null) {
            Double lat = point.getLat();
            Double lng = point.getLng();
            faceGate.setLng(lng);
            faceGate.setLat(lat);
        }
        appFaceGateMapper.insertSelective(faceGate);
    }

    @Override
    public void del(Integer faceGateId, AppFaceGateConfig config) {
        Integer count = appFaceGatePersonMapper.countByFaceGateId(faceGateId);
        if (count > 0) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_031.getCode());
        }
        appFaceGateMapper.deleteLogicById(faceGateId);
    }

    @Override
    public String faceGateGrantEmdAdd(FaceGateGrantEmdAddParam request, AppFaceGateConfig config) {
        if (mqttEnable == null || !mqttEnable) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.EMP_BE_027.getCode());
        }
        String deviceKey = request.getDeviceKey();
        Integer personId = request.getPersonId();
        Integer empId = request.getEmpId();

        AppEmp appEmp = appEmpMapper.selectByPrimaryKey(empId);
        String empCode = appEmp.getEmpCode();
        String avatar = appEmp.getAvatar();

        SyncDeviceWhiteInfo syncInfo = new SyncDeviceWhiteInfo();
        syncInfo.setUserType("white");
        syncInfo.setName(appEmp.getEmpName());
        syncInfo.setSex(Gender.parseValue(appEmp.getGender()));
        syncInfo.setNation(appEmp.getNation());
        syncInfo.setIdno(empCode);
        syncInfo.setPeopleStartDate("2020-01-01 00:00:00");
        syncInfo.setPeopleEndDate("2099-01-01 00:00:00");
        syncInfo.setPassAlgo(false);    //必须带图片
        syncInfo.setPicType(0);         //图片类型:base64
        syncInfo.setPicData1(this.getImageBase64(avatar));
        syncInfo.setPicUrl(null);

        SyncDeviceWhiteInfoMsg msg = new SyncDeviceWhiteInfoMsg();
        msg.setAction(RtConst.ACTION_SYNC_DEVICE_WHITEINFO);
        msg.setUuid("add_" + personId);
        msg.setData(syncInfo);

        String topic = RtConst.getDownTopic(deviceKey);
        String payload = JSONUtil.toString(msg);
        mqttMessageSender.sendToMqtt(topic, 0, payload);

        return null;
    }

    @Override
    public String faceGateGrantEmdImgAdd(FaceGateGrantEmdImgAddParam request, AppFaceGateConfig config) {
        return null;
    }

    @Override
    public String faceGateGrantEmdAuth(FaceGateGrantEmdAuthParam request, AppFaceGateConfig config) {
        return null;
    }

    @Override
    public void faceGateBatchGrantEmpAuth(AppFaceGateGrantEmpParam request, AppFaceGateConfig config) {

    }

    @Override
    public void faceGateRevokeEmp(AppFaceGateGrantEmpParam request, AppFaceGateConfig config) {
        if (mqttEnable == null || !mqttEnable) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.EMP_BE_027.getCode());
        }
        List<Integer> empIdList = request.getEmpIdList();
        if (empIdList == null || empIdList.isEmpty()) {
            return;
        }
        Integer faceGateId = request.getFaceGateId();
        AppFaceGate appFaceGate = appFaceGateMapper.selectByPrimaryKey(faceGateId);
        if (appFaceGate == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.EMP_BE_003.getCode());
        }
        String deviceKey = appFaceGate.getDeviceKey();
        for (Integer empId : empIdList) {
            AppFaceGatePerson appFaceGatePerson = appFaceGatePersonMapper.selectByFaceGateIdAndEmpId(faceGateId, empId);
            if (appFaceGatePerson != null) {
                appFaceGatePerson.setTaskType(TaskType.EMP_UN_AUTH.getValue());
                appFaceGatePersonMapper.updateByPrimaryKeySelective(appFaceGatePerson);

                // 发送批量同步人员的MQ消息
                DelDeviceWhiteInfo delInfo = new DelDeviceWhiteInfo(appFaceGatePerson.getPersonGuid());

                DelDeviceWhiteInfoMsg msg = new DelDeviceWhiteInfoMsg();
                msg.setAction(RtConst.ACTION_DEL_DEVICE_WHITEINFO);
                msg.setUuid("del_" + System.currentTimeMillis());
                msg.setData(Collections.singletonList(delInfo));

                String topic = RtConst.getDownTopic(deviceKey);
                String payload = JSONUtil.toString(msg);
                mqttMessageSender.sendToMqtt(topic, 0, payload);
            }
        }

    }

    @Override
    public String deviceAuthorizationPerson(String deviceKey, String name, String imgUrl, AppFaceGateConfig config) {
        return null;
    }

    @Override
    public String deviceAuthorizationCancel(String deviceKey, String personGuid, AppFaceGateConfig config) {
        return null;
    }

    @Override
    public void openDoor(String deviceKey, AppFaceGateConfig config) {

    }

    @Override
    public void sync(Integer faceGateId, AppFaceGateConfig config) {

    }

    @Override
    public String getToken(AppFaceGateConfig config) {
        return null;
    }

    /**
     * 获取图片base64(照片压缩)
     *
     * @param url
     * @return
     */
    private String getImageBase64(String url) {
        logger.info("获取图片base64,{}", url);
        url = fileHandler.getDownloadUrl(url);
        logger.info("获取图片base64,{}", url);
        byte[] imageData = Base64Util.getUrlImageData(url);
        //logger.debug("获取图片base64,{},压缩前:{}", url, imageData.length);
        byte[] compressed = ImageUtil.compressPicForScale(imageData, 1024);
        //logger.debug("获取图片base64,{},压缩后:{}", url, compressed.length);
        return Base64Util.encodeToString(compressed);
    }
}
