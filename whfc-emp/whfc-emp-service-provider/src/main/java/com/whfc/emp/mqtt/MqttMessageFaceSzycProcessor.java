package com.whfc.emp.mqtt;

import com.alibaba.fastjson.JSON;
import com.whfc.common.constant.QueueConst;
import com.whfc.common.face.szycq.*;
import com.whfc.common.file.FileHandler;
import com.whfc.common.file.FilePathConfig;
import com.whfc.common.util.*;
import com.whfc.emp.dao.AppFaceGatePersonMapper;
import com.whfc.emp.dto.AppFaceGateDTO;
import com.whfc.emp.entity.AppFaceGatePerson;
import com.whfc.emp.enums.TaskType;
import com.whfc.emp.manager.AppFaceGateManager;
import com.whfc.emp.param.EmpAttendSyncDataParam;
import com.whfc.emp.redis.FaceGateAttendRedisDao;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 人脸识别数据处理(玉川Q系列)
 */
@Component
public class MqttMessageFaceSzycProcessor implements MqttMessageProcessor {

    private static final Logger logger = LoggerFactory.getLogger(MqttMessageFaceSzycProcessor.class);

    /**
     * 同一个人同一个闸机刷脸时间间隔大于1分钟
     */
    private static final Integer INTERVAL_TIME = 1;

    //MQTT-SZYC topic 前缀
    private static final String MQTT_SZYC_TOPIC_PREFIX = "/hiot";

    //MQTT-SZYC 添加人员-ack
    private static final String addPersonAck = SzycqConst.getAddPersonAckTopic();

    //MQTT-SZYC 删除人员-ack
    private static final String delPersonAck = SzycqConst.getDelPersonAckTopic();

    //MQTT-SZYC 识别记录
    private static final String record = SzycqConst.getRecordTopic();

    //MQTT-SZYC 心跳
    private static final String heart = SzycqConst.getHeartTopic();

    @Autowired
    private AppFaceGateManager appFaceGateManager;

    @Autowired
    private FaceGateAttendRedisDao faceGateAttendRedisDao;

    @Autowired
    private FilePathConfig filePathConfig;

    @Autowired
    private FileHandler fileHandler;

    @Autowired
    private AmqpTemplate amqpTemplate;

    @Autowired
    private AppFaceGatePersonMapper appFaceGatePersonMapper;

    @Autowired(required = false)
    private MqttConfig.MqttMessageSender mqttMessageSender;

    @Override
    public String getTopicPrefix() {
        return MQTT_SZYC_TOPIC_PREFIX;
    }

    @Override
    public List<String> getSubTopicList() {
        //添加人员-ack
        String addPersonAck = SzycqConst.getAddPersonAckTopicShare();
        //删除人员-ack
        String delPersonAck = SzycqConst.getDelPersonAckTopicShare();
        //识别记录
        String record = SzycqConst.getRecordTopicShare();
        //心跳
        String heart = SzycqConst.getHeartTopic();

        List<String> topicList = new ArrayList<>();
        topicList.add(addPersonAck);
        topicList.add(delPersonAck);
        topicList.add(record);
        topicList.add(heart);
        return topicList;
    }

    @Override
    public void processData(String topic, byte[] payload) {
        String message = new String(payload);
        logger.info("MQTT(深圳玉川)事件监听器接收到消息,topic:{},message:{}", topic, message.length() > 500 ? message.substring(0, 500) : message);
        //识别记录
        if (record.equalsIgnoreCase(topic)) {
            RecordMsg msg = JSONUtil.parseObject(message, RecordMsg.class);
            this.handleRecord(msg);
        }
        //添加人员ack
        else if (addPersonAck.equalsIgnoreCase(topic)) {
            AddPersonReplyMsg msg = JSONUtil.parseObject(message, AddPersonReplyMsg.class);
            this.handleAddPerson(msg);
        }
        //删除人员ack
        else if (delPersonAck.equalsIgnoreCase(topic)) {
            DelPersonReplyMsg msg = JSONUtil.parseObject(message, DelPersonReplyMsg.class);
            this.handleDelPerson(msg);
        }
        //心跳请求
        else if (heart.equalsIgnoreCase(topic)) {
            HeartMsg msg = JSONUtil.parseObject(message, HeartMsg.class);
            this.handleHeartMsg(msg);
        }
    }

    /**
     * 批量授权人员处理
     *
     * @param msg 人员授权结果
     */
    private void handleAddPerson(AddPersonReplyMsg msg) {
        String deviceKey = msg.getMac_address();
        AddPersonReply data = msg.getData();
        if (data != null) {
            List<String> success_person_uuids = data.getSuccess_person_uuids();
            List<String> failed_person_uuids = data.getFailed_person_uuids();
            if (success_person_uuids != null && success_person_uuids.size() > 0) {
                logger.info("批量授权成功, deviceKey:{}, personGuids:{}", deviceKey, success_person_uuids);
                for (String personGuid : success_person_uuids) {
                    AppFaceGatePerson faceGatePerson = appFaceGatePersonMapper.selectByPersonGuidAndDeviceKey(personGuid, deviceKey);
                    if (faceGatePerson == null) {
                        continue;
                    }
                    faceGatePerson.setTaskType(TaskType.EMP_AUTH.getValue());
                    faceGatePerson.setMessage(null);
                    appFaceGatePersonMapper.updateByPrimaryKeySelective(faceGatePerson);
                }
            }
            if (failed_person_uuids != null && failed_person_uuids.size() > 0) {
                logger.info("批量授权失败, deviceKey:{}, personGuids:{}", deviceKey, failed_person_uuids);
                for (String personGuid : failed_person_uuids) {
                    AppFaceGatePerson faceGatePerson = appFaceGatePersonMapper.selectByPersonGuidAndDeviceKey(personGuid, deviceKey);
                    if (faceGatePerson == null) {
                        continue;
                    }
                    faceGatePerson.setTaskType(TaskType.EMP_AUTH_ERROR.getValue());
                    faceGatePerson.setMessage("");
                    appFaceGatePersonMapper.updateByPrimaryKeySelective(faceGatePerson);
                }
            }
        }
    }

    /**
     * 批量取消授权
     *
     * @param msg 取消授权结果
     */
    private void handleDelPerson(DelPersonReplyMsg msg) {
        String deviceKey = msg.getMac_address();
        DelPersonReply data = msg.getData();
        if (data != null) {
            List<String> success_person_uuids = data.getSuccess_person_uuids();
            List<String> failed_person_uuids = data.getFailed_person_uuids();
            if (success_person_uuids != null && success_person_uuids.size() > 0) {
                logger.info("批量取消授权成功, deviceKey:{}, personGuids:{}", deviceKey, success_person_uuids);
                appFaceGatePersonMapper.delBYDeviceKeyAndParsonGuid(deviceKey, success_person_uuids);
            }
            if (failed_person_uuids != null && failed_person_uuids.size() > 0) {
                logger.info("批量取消授权失败, deviceKey:{}, personGuids:{}", deviceKey, failed_person_uuids);
                appFaceGatePersonMapper.delBYDeviceKeyAndParsonGuid(deviceKey, failed_person_uuids);
            }
        }
    }

    /**
     * 识别记录处理
     *
     * @param msg
     */
    private void handleRecord(RecordMsg msg) {
        if (msg == null) {
            return;
        }

        //record-ack
        String recordAckTopic = SzycqConst.getRecordAckTopic(msg.getData().getMac_address());
        RecordAckMsg ackMsg = new RecordAckMsg();
        ackMsg.setCode(0);
        ackMsg.setMsg("success");
        ackMsg.setMessage_uuid(msg.getMessage_uuid());
        mqttMessageSender.sendToMqtt(recordAckTopic, JSONUtil.toString(ackMsg));


        Record recRecord = msg.getData();
        logger.debug("MQTT处理考勤记录, recRecord:{}", JSONUtil.toString(recRecord));
        // (1)验证闸机-唯一标识
        String personGuid = recRecord.getPerson_uuid();
        String deviceKey = recRecord.getMac_address();
        AppFaceGateDTO faceGateDTO = appFaceGateManager.getByDeviceKey(deviceKey);
        if (faceGateDTO == null) {
            logger.warn("考勤记录同步-闸机未入后台数据库, deviceKey:{} ", deviceKey);
            return;
        }
        // (2)验证时间-过滤重复记录
        Date time = new Date(recRecord.getRecog_time() * 1000);
        //从缓存中获取上一次打卡记录
        Date latestShowTime = faceGateAttendRedisDao.getShowTime(deviceKey, personGuid);
        if (latestShowTime != null && DateUtil.addMinutes(latestShowTime, INTERVAL_TIME).after(time)) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            logger.info("两次刷脸时间相同, time:{}, latestShowTime:{}", sdf.format(time), sdf.format(latestShowTime));
            return;
        }
        //保存刷脸时间到缓存中
        faceGateAttendRedisDao.setShowTime(deviceKey, personGuid, time);

        //保存考勤信息
        EmpAttendSyncDataParam param = new EmpAttendSyncDataParam();
        param.setDeviceName(faceGateDTO.getName());
        param.setDeviceKey(deviceKey);
        param.setPersonGuid(personGuid);
        param.setShowTime(time);
        param.setEmpName(recRecord.getName());
        param.setTemperature(recRecord.getTemperature());
        param.setDeptId(faceGateDTO.getDeptId());
        param.setFaceGateId(faceGateDTO.getFaceGateId());
        param.setDirection(faceGateDTO.getDirection());
        //处理图片
        String pic = recRecord.getSnap_image();
        if (StringUtils.isNotBlank(pic)) {
            String imgUrl = uploadImg(pic, time);
            param.setPicture(imgUrl);
        }

        //推送消息到考勤记录中
        String jsonStr = JSON.toJSONString(param);
        amqpTemplate.convertAndSend(QueueConst.EMP_FACEGATE_ATTEND_DATA, jsonStr);
    }

    /**
     * 心跳处理
     *
     * @param msg 心跳消息
     */
    private void handleHeartMsg(HeartMsg msg) {
        appFaceGateManager.handleHeart(msg.getMac_address());

//        String topic = SzycqConst.heartAckTopic(msg.getMac_address());
//        HeartAckMsg heartAckMsg = new HeartAckMsg();
//        heartAckMsg.setTime(System.currentTimeMillis());
//
//        mqttMessageSender.sendToMqtt(topic, JSON.toJSONString(heartAckMsg));

    }

    /**
     * 上传识别照片
     *
     * @param base64Str base64
     * @return 图片地址
     */
    private String uploadImg(String base64Str, Date time) {
        String imgUrl = "";
        if (StringUtils.isEmpty(base64Str)) {
            return imgUrl;
        }
        try {
            String imgData = Base64Util.getImageData(base64Str);
            byte[] imageByte = Base64Util.decode(imgData);
            byte[] dstBytes = ImageUtil.compressPicForScale(imageByte, 45);
            ByteArrayInputStream inputStream = new ByteArrayInputStream(dstBytes);
            String ossPath = "emp/face/" + DateUtil.formatDate(time, "yyyyMMdd");
            String path = filePathConfig.getFilePath(ossPath, RandomUtil.getRandomFileName(), "jpg");
            imgUrl = fileHandler.upload(path, inputStream);
        } catch (Exception e) {
            logger.warn("考勤识别上传base64图片失败", e);
        }
        return imgUrl;
    }
}
