package com.whfc.emp.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.whfc.common.enums.FenceType;
import com.whfc.common.enums.I18nErrorCode;
import com.whfc.common.exception.BizException;
import com.whfc.common.geometry.GeometryUtil;
import com.whfc.common.geometry.Point;
import com.whfc.common.geometry.Polygon;
import com.whfc.common.result.PageData;
import com.whfc.common.result.ResultEnum;
import com.whfc.common.util.PageUtil;
import com.whfc.emp.dao.AppFenceEmpMapper;
import com.whfc.emp.dao.AppFenceMapper;
import com.whfc.emp.dao.AppFenceTimeMapper;
import com.whfc.emp.dto.AppFenceDTO;
import com.whfc.emp.dto.AppFenceEmpDTO;
import com.whfc.emp.dto.AppFenceTimeDTO;
import com.whfc.emp.entity.AppFence;
import com.whfc.emp.entity.AppFenceEmp;
import com.whfc.emp.param.*;
import com.whfc.emp.service.AppFenceService;
import com.whfc.fuum.service.SysDeptService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClasssName AppFenceServiceImpl
 * @Description 智能安全帽-电子围栏
 * <AUTHOR>
 * @Date 2020/12/24 15:50
 * @Version 1.0
 */
@DubboService(interfaceClass = AppFenceService.class, version = "1.0.0")
public class AppFenceServiceImpl implements AppFenceService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private AppFenceMapper appFenceMapper;

    @Autowired
    private AppFenceEmpMapper appFenceEmpMapper;

    @Autowired
    private AppFenceTimeMapper appFenceTimeMapper;

    @DubboReference(interfaceClass = SysDeptService.class, version = "1.0.0")
    private SysDeptService sysDeptService;

    @Override
    public List<AppFenceDTO> list(AppFenceListParam request) {
        logger.info("人员管理-电子围栏列表,request:{}", request);
        String deptName = sysDeptService.getDeptNameById(request.getDeptId());
        List<AppFenceDTO> list = appFenceMapper.selectByParam(request);
        for (AppFenceDTO appFenceDTO : list) {
            //设置项目名称
            appFenceDTO.setDeptName(deptName);
            //转换空间几何对象
            this.translate(appFenceDTO);
        }
        return list;
    }

    @Override
    public Integer add(AppFenceAddParam request) {
        logger.info("人员管理-添加电子围栏,request:{}", request);
        Integer id = request.getId();
        Integer type = request.getType();
        String polygon = null;
        String center = null;
        //多边形
        if (FenceType.POLYGON.value().equals(type)) {
            List<Point> polygonPointList = request.getPolygonPointList();
            if (polygonPointList == null || polygonPointList.size() < 3) {
                throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_061.getCode());
            }
            //首位相连
            polygonPointList.add(polygonPointList.get(0));
            polygon = GeometryUtil.encodePolygon(new Polygon(polygonPointList));
        }
        //圆形
        else if (FenceType.CIRCLE.value().equals(type)) {
            Point centerPoint = request.getCenterPoint();
            Double radius = request.getRadius();
            if (centerPoint == null || radius == null) {
                throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_062.getCode());
            }
            center = GeometryUtil.encodePoint(centerPoint);
        }
        AppFence appFence = new AppFence();
        appFence.setId(id);
        appFence.setName(request.getName());
        appFence.setDeptId(request.getDeptId());
        appFence.setType(type);
        appFence.setPolygon(polygon);
        appFence.setCenter(center);
        appFence.setRadius(request.getRadius());
        if (id == null) {
            //新增
            appFenceMapper.insertSelectiveByParam(appFence);
            id = appFence.getId();
        } else {
            //编辑
            appFenceMapper.updateSelectiveByParam(appFence);
        }
        return id;
    }

    @Override
    public void del(Integer id) {
        logger.info("人员管理-删除电子围栏,id:{}", id);
        List<AppFenceEmpDTO> list = appFenceEmpMapper.selectByFenceId(id, null, null, null);
        if (!list.isEmpty()) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_066.getCode());
        }
        appFenceMapper.deleteLogicById(id);
    }

    @Override
    public PageData<AppFenceEmpDTO> empList(Integer fenceId, Integer pageNum, Integer pageSize, Integer groupId, Integer workTypeId, String keyword) {
        logger.info("电子围栏考勤人员列表,fenceId:{},pageNum:{},pageSize:{},groupId:{},workTypeId:{},keyword:{}", fenceId, pageNum, pageSize, groupId, workTypeId, keyword);
        PageHelper.startPage(pageNum, pageSize);
        List<AppFenceEmpDTO> list = appFenceEmpMapper.selectByFenceId(fenceId, groupId, workTypeId, keyword);
        PageHelper.clearPage();
        return PageUtil.pageData(PageInfo.of(list));
    }

    @Override
    public void addEmp(AppFenceEmpAddParam request) {
        logger.info("电子围栏添加人员,request:{}", request);
        List<Integer> empList = request.getEmpList();
        Integer fenceId = request.getFenceId();
        List<AppFenceEmp> fenceEmpList = new ArrayList<>();
        for (Integer empId : empList) {
            AppFenceEmp appFenceEmp = new AppFenceEmp();
            appFenceEmp.setEmpId(empId);
            appFenceEmp.setFenceId(fenceId);
            fenceEmpList.add(appFenceEmp);
        }
        appFenceEmpMapper.insertOrUpdate(fenceEmpList);
    }

    @Override
    public void delEmp(AppFenceEmpDelParam request) {
        logger.info("删除考勤人员,request:{}", request);
        appFenceEmpMapper.deleteLogic(request.getFenceId(), request.getEmpId());
    }

    @Override
    public List<AppFenceDTO> listByEmpId(Integer empId) {
        logger.info("根据人员id查询所在的电子围栏列表,empId:{}", empId);
        List<AppFenceDTO> list = appFenceEmpMapper.selectByEmpId(empId);
        for (AppFenceDTO appFenceDTO : list) {
            this.translate(appFenceDTO);
        }
        return list;
    }

    @Override
    public PageData<AppFenceEmpDTO> unGrantList(Integer deptId, Integer fenceId, Integer pageNum, Integer pageSize, Integer groupId, String keyword) {
        logger.info("电子围栏考勤未授权人员列表,deptId:{},fenceId:{},pageNum:{},pageSize:{},groupId:{},keyword:{}", deptId, fenceId, pageNum, pageSize, groupId, keyword);
        PageHelper.startPage(pageNum, pageSize);
        List<AppFenceEmpDTO> list = appFenceEmpMapper.selectUnGrantEmp(deptId, fenceId, groupId, keyword);
        return PageUtil.pageData(PageInfo.of(list));
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void setTimeList(AppFenceTimeParam request) throws BizException {
        logger.info("设置电子围栏时间范围, params:{}", request);
        Integer fenceId = request.getFenceId();
        appFenceTimeMapper.logicDelByFenceId(fenceId);
        List<AppFenceTimeDTO> timeList = request.getTimeList();
        if (timeList.isEmpty()) {
            return;
        }
        timeList.forEach(fenceTime -> fenceTime.setFenceId(fenceId));
        appFenceTimeMapper.batchInsert(timeList);
    }

    @Override
    public List<AppFenceTimeDTO> getTimeList(Integer fenceId) throws BizException {
        return appFenceTimeMapper.selectByFenceId(fenceId);
    }

    /**
     * 转换空间几何对象
     *
     * @param appFenceDTO
     */
    private void translate(AppFenceDTO appFenceDTO) {
        Integer fenceType = appFenceDTO.getType();
        if (FenceType.POLYGON.value().equals(fenceType)) {
            String wkt = appFenceDTO.getPolygon();
            Polygon polygon = GeometryUtil.decodePolygon(wkt);
            appFenceDTO.setPolygonPointList(polygon.getPointList());
        }
        if (FenceType.MULTIPOLYGON.value().equals(fenceType)) {
            String wkt = appFenceDTO.getPolygon();
            List<List<Point>> pointList = GeometryUtil.decodeMultiPolygon(wkt);
            appFenceDTO.setMultiPolygonPointList(pointList);
        }
        //圆形
        else if (FenceType.CIRCLE.value().equals(fenceType)) {
            Point point = GeometryUtil.decodePoint(appFenceDTO.getCenter());
            appFenceDTO.setCenterPoint(point);
        }
    }
}
