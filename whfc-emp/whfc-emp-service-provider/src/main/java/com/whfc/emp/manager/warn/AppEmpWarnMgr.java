package com.whfc.emp.manager.warn;

import com.whfc.emp.dto.EmpWarnCheckDTO;
import com.whfc.emp.dto.EmpWarnRuleDTO;

/**
 * @Description: 人员报警管理器-接口
 * @author: xug<PERSON>
 * @version: 1.0
 * @date: 2023/10/10 15:41
 */
public interface AppEmpWarnMgr {

    /**
     * 报警监测
     *
     * @param checkDTO
     * @param ruleDTO
     */
    void checkWarn(EmpWarnCheckDTO checkDTO, EmpWarnRuleDTO ruleDTO);
}
