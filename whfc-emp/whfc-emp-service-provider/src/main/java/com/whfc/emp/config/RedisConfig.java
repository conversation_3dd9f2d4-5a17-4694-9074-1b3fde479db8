package com.whfc.emp.config;

import com.whfc.emp.dto.AppDeviceCardLogCacheDTO;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2019/11/1 14:59
 */
@Configuration
public class RedisConfig {
    @Bean
    public RedisTemplate<String, AppDeviceCardLogCacheDTO> cardDataTemplate(RedisConnectionFactory connectionFactory) {
        Jackson2JsonRedisSerializer redisSerializer = new Jackson2JsonRedisSerializer(AppDeviceCardLogCacheDTO.class);
        RedisTemplate<String, AppDeviceCardLogCacheDTO> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(connectionFactory);
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setValueSerializer(redisSerializer);
        return redisTemplate;
    }
}