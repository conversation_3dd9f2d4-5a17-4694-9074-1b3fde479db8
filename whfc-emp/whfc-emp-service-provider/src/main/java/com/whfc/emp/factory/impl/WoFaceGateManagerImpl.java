package com.whfc.emp.factory.impl;

import com.whfc.common.enums.I18nErrorCode;
import com.whfc.common.exception.BizException;
import com.whfc.common.face.wo.WoFaceApi;
import com.whfc.common.face.wotu.WotuApiAddDeviceResult;
import com.whfc.common.face.wotu.WotuApiAddFaceImgResult;
import com.whfc.common.face.wotu.WotuApiAddPersonResult;
import com.whfc.common.face.wotu.WotuApiResult;
import com.whfc.common.geometry.Point;
import com.whfc.common.result.ResultEnum;
import com.whfc.emp.dao.AppEmpMapper;
import com.whfc.emp.dao.AppFaceGateFaceMapper;
import com.whfc.emp.dao.AppFaceGateMapper;
import com.whfc.emp.dao.AppFaceGatePersonMapper;
import com.whfc.emp.entity.*;
import com.whfc.emp.enums.FaceGateType;
import com.whfc.emp.enums.TaskType;
import com.whfc.emp.factory.FaceGateManager;
import com.whfc.emp.param.*;
import com.whfc.emp.redis.FaceGateApiTokenRedisDao;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.regex.Pattern;

/**
 * @Description wo平台
 * <AUTHOR>
 * @Date 2021/1/21 16:38
 * @Version 1.0
 */
@Service("woFaceGateManagerImpl")
public class WoFaceGateManagerImpl implements FaceGateManager {

    private static final Logger logger = LoggerFactory.getLogger(WoFaceGateManagerImpl.class);

    @Autowired
    private AppEmpMapper appEmpMapper;

    @Autowired
    private AppFaceGateMapper appFaceGateMapper;

    @Autowired
    private AppFaceGatePersonMapper appFaceGatePersonMapper;

    @Autowired
    private AppFaceGateFaceMapper appFaceGateFaceMapper;

    @Autowired
    private FaceGateApiTokenRedisDao faceGateApiTokenRedisDao;

    /***
     * 默认为本地识别
     */
    private final static String type = "1";

    /**
     * 成功标识
     */
    private final static String WO_SUS1000 = "WO_SUS1000";

    /**
     * 设备离线
     */
    private final static String WO_EXP_104 = "WO_EXP-104";

    /**
     * 设备已添加
     */
    private final static String WO_EXP_103 = "WO_EXP-103";

    /**
     * 人员不存在
     */
    private final static String WO_EXP_400 = "WO_EXP-400";

    /**
     * 名称匹配
     */
    private final static String NAME_PATTERN = "^[\\u4e00-\\u9fa5a-zA-Z\\d\\s_]*$";

    @Override
    public void add(AppFaceGateAddParam request, AppFaceGateConfig config) {
        String deviceKey = request.getDeviceKey();
        String name = request.getName();
        Integer deptId = request.getDeptId();
        Point point = request.getPoint();
        if (!Pattern.matches(NAME_PATTERN, name)) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_029.getCode());
        }
        AppFaceGate appFaceGate = appFaceGateMapper.selectByDeviceKey(deviceKey);
        if (appFaceGate != null) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_028.getCode());
        }

        WoFaceApi woFaceApi = this.getApi(config);
        String token = this.getToken(config);
        WotuApiResult<WotuApiAddDeviceResult> result = woFaceApi.deviceCreate(deviceKey, name, deptId + "", type, token);
        if (WO_EXP_104.equalsIgnoreCase(result.getCode())) {
            throw new BizException(ResultEnum.API_ERROR.getCode(), I18nErrorCode.BASE_BE_001.getCode());
        }
        if (WO_EXP_103.equalsIgnoreCase(result.getCode())) {
            throw new BizException(ResultEnum.API_ERROR.getCode(), I18nErrorCode.BASE_BE_002.getCode());
        }
        if (WO_SUS1000.equalsIgnoreCase(result.getCode())) {
            AppFaceGate record = new AppFaceGate();
            record.setDeptId(deptId);
            record.setName(name);
            record.setModel(request.getModel());
            record.setDirection(request.getDirection());
            record.setPlatform(FaceGateType.WO.getCode());
            record.setDeviceKey(request.getDeviceKey());
            record.setModel(request.getModel());
            // 增加闸机位置信息
            if (point != null) {
                Double lat = point.getLat();
                Double lng = point.getLng();
                record.setLng(lng);
                record.setLat(lat);
            }
            appFaceGateMapper.insertSelective(record);
        } else {
            throw new BizException(ResultEnum.API_ERROR.getCode(), result.getMsg());
        }
    }

    @Override
    public void del(Integer faceGateId, AppFaceGateConfig config) {
        AppFaceGate faceGate = appFaceGateMapper.selectByPrimaryKey(faceGateId);
        if (faceGate == null) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_003.getCode());
        }
        Integer count = appFaceGatePersonMapper.countByFaceGateId(faceGateId);
        if (count > 0) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_031.getCode());
        }
        String deviceKey = faceGate.getDeviceKey();
        WoFaceApi woFaceApi = this.getApi(config);
        String token = this.getToken(config);
        WotuApiResult wotuApiResult = woFaceApi.deviceDel(deviceKey, token);
        if (WO_SUS1000.equals(wotuApiResult.getCode())) {
            //成功
            appFaceGateMapper.deleteLogicById(faceGateId);
        }
    }

    @Override
    public String faceGateGrantEmdAdd(FaceGateGrantEmdAddParam request, AppFaceGateConfig config) {
        Integer empId = request.getEmpId();
        Integer personId = request.getPersonId();
        AppEmp appPerson = appEmpMapper.selectByPrimaryKey(empId);
        if (appPerson == null) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_001.getCode());
        }
        String name = appPerson.getEmpName();
        String phone = appPerson.getPhone();
        if (!Pattern.matches(NAME_PATTERN, name)) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_029.getCode());
        }
        //人员注册
        WoFaceApi woFaceApi = this.getApi(config);
        String token = this.getToken(config);
        WotuApiResult<WotuApiAddPersonResult> result = woFaceApi.personCreate(name, phone, token);
        if (WO_SUS1000.equals(result.getCode())) {
            //人员注册成功
            WotuApiAddPersonResult data = result.getData();
            String personGuid = data.getGuid();
//            appPerson.setEmpCode(personGuid);
//            appEmpMapper.updateByPrimaryKeySelective(appPerson);

            appFaceGatePersonMapper.updateGrantInfo(personId, TaskType.EMP_ADD.getValue(), personGuid);
            return personGuid;
        } else {
            logger.info("人员注册失败,name:{},code:{},message:{}", name, result.getCode(), result.getMsg());
            appFaceGatePersonMapper.updateTaskType(personId, TaskType.EMP_ADD_ERROR.getValue(), "人员注册失败");
            return null;
        }
    }

    @Override
    public String faceGateGrantEmdImgAdd(FaceGateGrantEmdImgAddParam request, AppFaceGateConfig config) {
        Integer personId = request.getPersonId();
        String personGuid = request.getPersonGuid();
        String imgUrl = request.getImgUrl();
        //照片注册
        WoFaceApi woFaceApi = this.getApi(config);
        String token = this.getToken(config);
        WotuApiResult<WotuApiAddFaceImgResult> faceResult = woFaceApi.faceImgCreate(personGuid, imgUrl, token);
        if (WO_SUS1000.equals(faceResult.getCode())) {
            //照片注册接口调用成功
            AppFaceGatePerson appFaceGatePerson = appFaceGatePersonMapper.selectByPrimaryKey(personId);
            WotuApiAddFaceImgResult face = faceResult.getData();
            AppFaceGateFace appFaceGateFace = new AppFaceGateFace();
            appFaceGateFace.setDeptId(appFaceGatePerson.getDeptId());
            appFaceGateFace.setDeviceKey(appFaceGatePerson.getDeviceKey());
            appFaceGateFace.setEmpId(appFaceGatePerson.getEmpId());
            appFaceGateFace.setFaceGateId(appFaceGatePerson.getFaceGateId());
            appFaceGateFace.setFaceGuid(face.getGuid());
            appFaceGateFace.setFaceUrl(face.getFaceUrl());
            appFaceGateFace.setPersonGuid(personGuid);
            appFaceGateFaceMapper.insertSelective(appFaceGateFace);

            appFaceGatePersonMapper.updateTaskType(personId, TaskType.EMP_IMG_ADD.getValue(), "照片注册成功");

            return personGuid;
        } else {
            appFaceGatePersonMapper.updateTaskType(personId, TaskType.EMP_IMG_ADD_ERROR.getValue(), "照片注册失败");
            return null;
        }
    }

    @Override
    public String faceGateGrantEmdAuth(FaceGateGrantEmdAuthParam request, AppFaceGateConfig config) {
        String deviceKey = request.getDeviceKey();
        String personGuid = request.getPersonGuid();
        Integer personId = request.getPersonId();
        WoFaceApi woFaceApi = this.getApi(config);
        String token = this.getToken(config);
        WotuApiResult result = woFaceApi.setPeople(deviceKey, personGuid, type, token);
        if (WO_SUS1000.equals(result.getCode())) {
            appFaceGatePersonMapper.updateTaskType(personId, TaskType.EMP_AUTH.getValue(), "人员授权成功");
            return personGuid;
        } else {
            appFaceGatePersonMapper.updateTaskType(personId, TaskType.EMP_AUTH_ERROR.getValue(), "人员授权失败");
            return null;
        }
    }

    @Override
    public void faceGateBatchGrantEmpAuth(AppFaceGateGrantEmpParam request, AppFaceGateConfig config) {

    }

    @Override
    public void faceGateRevokeEmp(AppFaceGateGrantEmpParam request, AppFaceGateConfig config) {
        Integer faceGateId = request.getFaceGateId();
        List<Integer> empIdList = request.getEmpIdList();

        AppFaceGate faceGate = appFaceGateMapper.selectByPrimaryKey(faceGateId);
        if (faceGate == null) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_003.getCode());
        }
        String deviceKey = faceGate.getDeviceKey();
        WoFaceApi woFaceApi = this.getApi(config);
        String token = this.getToken(config);
        for (Integer empId : empIdList) {
            List<String> personGuids = appFaceGatePersonMapper.selectPersonGuidByFaceGateIdAndEmpId(faceGateId, empId);
            if (personGuids != null && personGuids.size() > 0) {
                for (String personGuid : personGuids) {
                    if (StringUtils.isNotBlank(personGuid)) {
                        WotuApiResult wotuApiResult = woFaceApi.deleteSomePeople(deviceKey, personGuid, type, token);
                        //成功 or 人员不存在
                        if (WO_SUS1000.equals(wotuApiResult.getCode()) || WO_EXP_400.equals(wotuApiResult.getCode())) {
                            appFaceGatePersonMapper.delBYDeviceKeyAndParsonGuid(deviceKey, personGuids);
                        }
                        //失败
                        else {
                            logger.warn("删除授权失败，personGuid：{},{}", personGuid, wotuApiResult.getCode());
                        }
                    } else {
                        appFaceGatePersonMapper.delByDeviceKeyAndEmpId(deviceKey, empId);
                    }
                }
            }
        }


    }

    @Override
    public String deviceAuthorizationPerson(String deviceKey, String name, String imgUrl, AppFaceGateConfig config) {
        if (!Pattern.matches(NAME_PATTERN, name)) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_029.getCode());
        }
        //人员注册
        WoFaceApi woFaceApi = this.getApi(config);
        String token = this.getToken(config);
        WotuApiResult<WotuApiAddPersonResult> personResult = woFaceApi.personCreate(name, null, token);
        if (WO_SUS1000.equals(personResult.getCode())) {
            //人员注册成功
            WotuApiAddPersonResult data = personResult.getData();
            String personGuid = data.getGuid();
            WotuApiResult<WotuApiAddFaceImgResult> faceResult = woFaceApi.faceImgCreate(personGuid, imgUrl, token);
            if (WO_SUS1000.equals(faceResult.getCode())) {
                WotuApiResult result = woFaceApi.setPeople(deviceKey, personGuid, type, token);
                if (WO_SUS1000.equals(result.getCode())) {
                    return personGuid;
                }
            }
        }
        throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_049.getCode());
    }

    @Override
    public String deviceAuthorizationCancel(String deviceKey, String personGuid, AppFaceGateConfig config) {
        String result = "";
        WoFaceApi woFaceApi = this.getApi(config);
        String token = this.getToken(config);
        WotuApiResult wotuApiResult = woFaceApi.deleteSomePeople(deviceKey, personGuid, type, token);
        if (wotuApiResult != null) {
            result = wotuApiResult.getCode();
        }
        return result;
    }

    @Override
    public void openDoor(String deviceKey, AppFaceGateConfig config) {
        WoFaceApi woFaceApi = this.getApi(config);
        String token = this.getToken(config);
        WotuApiResult wotuApiResult = woFaceApi.interactive(deviceKey, token);
        if (!WO_SUS1000.equals(wotuApiResult.getCode())) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_050.getCode());
        }
    }

    @Override
    public void sync(Integer faceGateId, AppFaceGateConfig config) {

    }

    @Override
    public String getToken(AppFaceGateConfig config) {
        String configId = String.valueOf(config.getId());
        String accessToken = faceGateApiTokenRedisDao.getAccessToken(configId);
        if (StringUtils.isEmpty(accessToken)) {
            //刷新accessToken
            WoFaceApi api = this.getApi(config);
            accessToken = api.auth();
            if (StringUtils.isEmpty(accessToken)) {
                throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.SYS_BE_035.getCode());
            }
            //缓存accessToken
            faceGateApiTokenRedisDao.setAccessToken(configId, accessToken);
        }
        return accessToken;
    }

    /**
     * 根据配置获取API
     *
     * @param config
     * @return
     */
    private WoFaceApi getApi(AppFaceGateConfig config) {
        String appId = config.getAppId();
        String appKey = config.getAppKey();
        String appSecret = config.getAppSecret();
        WoFaceApi api = new WoFaceApi(appId, appKey, appSecret);
        //todo 缓存优化
        return api;
    }

    public static void main(String[] args) {
        String str = "南陵县何湾镇中心卫生院康复楼及公共卫生服务大楼项目进门";
        System.out.println(str.length());
    }
}
