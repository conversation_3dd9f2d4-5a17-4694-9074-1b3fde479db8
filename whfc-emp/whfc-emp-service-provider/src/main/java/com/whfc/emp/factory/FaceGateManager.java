package com.whfc.emp.factory;


import com.whfc.emp.entity.AppFaceGateConfig;
import com.whfc.emp.param.*;

/**
 * @Description 闸机服务
 * <AUTHOR>
 * @Date 2021/1/4 10:52
 * @Version 1.0
 */
public interface FaceGateManager {

    /**
     * 添加闸机
     *
     * @param request
     */
    void add(AppFaceGateAddParam request, AppFaceGateConfig config);

    /**
     * 删除闸机
     */
    void del(Integer faceGateId, AppFaceGateConfig config);

    /**
     * 闸机人员注册
     *
     * @param request
     */
    String faceGateGrantEmdAdd(FaceGateGrantEmdAddParam request, AppFaceGateConfig config);

    /**
     * 闸机人员照片注册
     *
     * @param request
     */
    String faceGateGrantEmdImgAdd(FaceGateGrantEmdImgAddParam request, AppFaceGateConfig config);

    /**
     * 闸机人员授权
     *
     * @param request
     */
    String faceGateGrantEmdAuth(FaceGateGrantEmdAuthParam request, AppFaceGateConfig config);

    /**
     * 闸机人员批量授权
     *
     * @param request
     */
    void faceGateBatchGrantEmpAuth(AppFaceGateGrantEmpParam request, AppFaceGateConfig config);

    /**
     * 闸机-取消人员授权
     */
    void faceGateRevokeEmp(AppFaceGateGrantEmpParam request, AppFaceGateConfig config);

    /**
     * 闸机人员授权
     *
     * @param deviceKey
     * @param name
     * @param imgUrl
     * @return
     */
    String deviceAuthorizationPerson(String deviceKey, String name, String imgUrl, AppFaceGateConfig config);

    /**
     * 闸机取消人员授权
     *
     * @param deviceKey
     * @param personGuid
     */
    String deviceAuthorizationCancel(String deviceKey, String personGuid, AppFaceGateConfig config);

    /**
     * 闸机开门
     *
     * @param deviceKey
     */
    void openDoor(String deviceKey, AppFaceGateConfig config);

    /**
     * 同步闸机授权人员
     *
     * @param faceGateId
     */
    void sync(Integer faceGateId, AppFaceGateConfig config);

    /**
     * 获取token
     *
     * @return
     */
    String getToken(AppFaceGateConfig config);
}
