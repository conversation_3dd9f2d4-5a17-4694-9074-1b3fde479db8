package com.whfc.emp.manager;

import com.whfc.fuum.dto.AppCorpDTO;
import com.whfc.fuum.service.AppCorpService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

@Component
public class AppCorpManger {

    @DubboReference(interfaceClass = AppCorpService.class, version = "1.0.0")
    private AppCorpService appCorpService;

    public AppCorpDTO getCorpInfo(Integer corpId) {
        return appCorpService.detail(corpId);
    }
}
