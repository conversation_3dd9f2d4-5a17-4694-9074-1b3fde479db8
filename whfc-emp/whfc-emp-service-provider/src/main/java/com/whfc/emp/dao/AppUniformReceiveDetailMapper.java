package com.whfc.emp.dao;

import com.whfc.emp.dto.uniform.AppUniformReceiveDetailDTO;
import com.whfc.emp.entity.AppUniformReceiveDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AppUniformReceiveDetailMapper {
    int deleteByPrimaryKey(Integer id);

    int insertSelective(AppUniformReceiveDetail record);

    AppUniformReceiveDetail selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppUniformReceiveDetail record);

    /**
     * 查询明细列表
     *
     * @param receiveId
     * @return
     */
    List<AppUniformReceiveDetailDTO> selectByReceiveId(@Param("receiveId") Integer receiveId);

    /**
     * 查询明细列表
     * @param receiveIds
     * @return
     */
    List<AppUniformReceiveDetailDTO> selectByReceiveIds(@Param("receiveIds") List<Integer> receiveIds);

    /**
     * 批量插入
     *
     * @param list
     * @return
     */
    int batchInsert(@Param("list") List<AppUniformReceiveDetailDTO> list);

    /**
     * 逻辑删除
     *
     * @param receiveId
     * @return
     */
    int logicDeleteByReceiveId(@Param("receiveId") Integer receiveId);

    /**
     * 查询物品列表
     *
     * @param deptId
     * @return
     */
    List<String> selectItemList(@Param("deptId") Integer deptId);
}