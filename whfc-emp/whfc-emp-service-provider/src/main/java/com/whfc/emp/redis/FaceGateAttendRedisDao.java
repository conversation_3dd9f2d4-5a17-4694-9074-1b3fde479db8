package com.whfc.emp.redis;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-08-16 11:21
 */
public interface FaceGateAttendRedisDao {

    /**
     * 设置 人员过闸时间
     *
     * @param deviceKey  闸机序列号
     * @param personGuid 人员GUID
     * @param time       过闸时间
     */
    void setShowTime(String deviceKey, String personGuid, Date time);

    /**
     * 获取人员过闸时间
     *
     * @param deviceKey  闸机序列号
     * @param personGuid 人员GUID
     * @return 人员过闸时间
     */
    Date getShowTime(String deviceKey, String personGuid);


}
