package com.whfc.emp.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.whfc.common.enums.I18nErrorCode;
import com.whfc.common.enums.SyncFlag;
import com.whfc.common.exception.BizException;
import com.whfc.common.result.PageData;
import com.whfc.common.result.ResultEnum;
import com.whfc.common.util.MathUtil;
import com.whfc.common.util.PageUtil;
import com.whfc.common.util.RandomUtil;
import com.whfc.emp.dao.*;
import com.whfc.emp.dto.AppEmpDTO;
import com.whfc.emp.dto.AppTrainDTO;
import com.whfc.emp.dto.AppTrainEmpDTO;
import com.whfc.emp.dto.TrainEmpDTO;
import com.whfc.emp.entity.*;
import com.whfc.emp.enums.AppAttachmentType;
import com.whfc.emp.param.*;
import com.whfc.emp.service.AppTrainService;
import com.whfc.emp.third.EmpThirdSyncFactory;
import com.whfc.emp.third.EmpThirdSyncWapper;
import com.whfc.fuum.dto.SysDictDTO;
import com.whfc.fuum.service.SysDictService;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @ClasssName AppTrainServiceImpl
 * @Description 人员管理-培训
 * <AUTHOR>
 * @Date 2020/11/26 16:18
 * @Version 1.0
 */
@DubboService(interfaceClass = AppTrainService.class, version = "1.0.0", timeout = 120 * 1000)
public class AppTrainServiceImpl implements AppTrainService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    private static final List<String> enterTrainTypeNameList = Arrays.asList("入场三级教育");

    @Autowired
    private AppTrainMapper appTrainMapper;

    @Autowired
    private AppTrainEmpMapper appTrainEmpMapper;

    @Autowired
    private AppTrainEmpImgMapper appTrainEmpImgMapper;

    @Autowired
    private AppEmpMapper appEmpMapper;

    @Autowired
    private AppTrainAttachmentMapper appTrainAttachmentMapper;

    @Autowired
    private EmpThirdSyncFactory empThirdSyncFactory;

    @DubboReference(interfaceClass = SysDictService.class, version = "1.0.0")
    private SysDictService sysDictService;

    @Override
    public PageData<AppTrainDTO> list(AppTrainListParam request) throws BizException {
        logger.info("人员管理-培训列表,request:{}", request);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        List<AppTrainDTO> list = appTrainMapper.selectByParam(request);
        list.forEach(appTrainDTO -> {
            List<AppTrainAttachment> attachments = appTrainAttachmentMapper.selectByTrainId(appTrainDTO.getTrainId());
            if (attachments != null && !attachments.isEmpty()) {
                List<String> trainPhoto = attachments.stream().filter(appTrainAttachment ->
                        AppAttachmentType.PHOTO.getValue().equals(appTrainAttachment.getType())).map(AppTrainAttachment::getUrl).collect(Collectors.toList());
                List<String> attachment = attachments.stream().filter(appTrainAttachment ->
                        AppAttachmentType.ATTACHMENT.getValue().equals(appTrainAttachment.getType())).map(AppTrainAttachment::getUrl).collect(Collectors.toList());
                appTrainDTO.setTrainPhoto(trainPhoto);
                appTrainDTO.setAttachment(attachment);
            }
        });
        PageHelper.clearPage();
        return PageUtil.pageData(PageInfo.of(list));
    }

    @Override
    public PageData<AppTrainEmpDTO> list(Integer empId, Integer pageNum, Integer pageSize) throws BizException {
        logger.info("人员管理-培训列表,empId:{}，pageNum：{}，pageSize：{}", empId, pageNum, pageSize);
        PageHelper.startPage(pageNum, pageSize);
        List<AppTrainEmpDTO> list = appTrainMapper.selectByEmpId(empId);
        PageHelper.clearPage();
        //获取工种类型 以及培训图片
        for (AppTrainEmpDTO appTrainEmpDTO : list) {
            List<String> imgUrlList = appTrainEmpImgMapper.selectImgUrlList(appTrainEmpDTO.getTrainEmpId());
            appTrainEmpDTO.setImgUrlList(imgUrlList);
        }
        return PageUtil.pageData(PageInfo.of(list));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(AppTrainAddParam request) throws BizException {
        logger.info("人员管理-新增培训,request:{}", request);

        List<String> trainPhoto = request.getTrainPhoto();
        List<String> attachment = request.getAttachment();
        Integer trainType = request.getTrainType();

        //验证培训类型
        SysDictDTO dict = sysDictService.getDictById(trainType);
        if (dict == null) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), I18nErrorCode.EMP_BE_070.getCode());
        }

        //新增培训
        AppTrain record = new AppTrain();
        record.setGuid(RandomUtil.getGuid());
        BeanUtils.copyProperties(request, record);
        record.setTrainType(trainType);
        record.setTrainTypeCode(dict.getCode());
        record.setTrainTypeName(dict.getName());
        appTrainMapper.insertSelective(record);
        if (trainPhoto != null && !trainPhoto.isEmpty()) {
            trainPhoto = trainPhoto.stream().filter(StringUtils::isNoneBlank).collect(Collectors.toList());
            if (trainPhoto.size() > 0) {
                appTrainAttachmentMapper.insertAll(record.getId(), AppAttachmentType.PHOTO.getValue(), trainPhoto);
            }
        }
        if (attachment != null && !attachment.isEmpty()) {
            attachment = attachment.stream().filter(StringUtils::isNoneBlank).collect(Collectors.toList());
            if (attachment.size() > 0) {
                appTrainAttachmentMapper.insertAll(record.getId(), AppAttachmentType.ATTACHMENT.getValue(), attachment);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(AppTrainEditParam request) throws BizException {
        logger.info("人员管理-编辑培训,request:{}", request);

        List<String> trainPhoto = request.getTrainPhoto();
        List<String> attachment = request.getAttachment();
        Integer trainType = request.getTrainType();

        //验证培训类型
        SysDictDTO dict = sysDictService.getDictById(trainType);
        if (dict == null) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), I18nErrorCode.EMP_BE_070.getCode());
        }

        //修改培训
        AppTrain record = new AppTrain();
        BeanUtils.copyProperties(request, record);
        record.setId(request.getTrainId());
        record.setTrainType(trainType);
        record.setTrainTypeCode(dict.getCode());
        record.setTrainTypeName(dict.getName());
        appTrainMapper.updateByPrimaryKeySelective(record);

        appTrainAttachmentMapper.delByTrainId(request.getTrainId());
        if (trainPhoto != null && !trainPhoto.isEmpty()) {
            trainPhoto = trainPhoto.stream().filter(StringUtils::isNoneBlank).collect(Collectors.toList());
            if (trainPhoto.size() > 0) {
                appTrainAttachmentMapper.insertAll(record.getId(), AppAttachmentType.PHOTO.getValue(), trainPhoto);
            }
        }
        if (attachment != null && !attachment.isEmpty()) {
            attachment = attachment.stream().filter(StringUtils::isNoneBlank).collect(Collectors.toList());
            if (attachment.size() > 0) {
                appTrainAttachmentMapper.insertAll(record.getId(), AppAttachmentType.ATTACHMENT.getValue(), attachment);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void del(Integer trainId) throws BizException {
        logger.info("人员管理-删除培训,trainId:{}", trainId);
        appTrainMapper.deleteLogicById(trainId);
        appTrainAttachmentMapper.delByTrainId(trainId);
    }

    @Override
    public void complete(Integer trainId) throws BizException {
        logger.info("人员管理-完成培训,trainId:{}", trainId);
        appTrainMapper.updateState(trainId);
    }

    @Override
    public PageData<AppTrainEmpDTO> trainEmpList(AppTrainEmpListParam request) throws BizException {
        logger.info("人员管理-培训人员列表,request:{}", request);
        PageData<AppTrainEmpDTO> pageData = new PageData<>();
        Integer trainId = request.getTrainId();

        //查询培训员工的列表
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        List<TrainEmpDTO> trainEmpList = appTrainEmpMapper.selectTrainEmpByParam(request);
        PageHelper.clearPage();
        //查找成绩图片
        for (TrainEmpDTO trainEmpDTO : trainEmpList) {
            List<String> imgUrlList = appTrainEmpImgMapper.selectImgUrlList(trainEmpDTO.getTrainEmpId());
            trainEmpDTO.setImgUrlList(imgUrlList);
        }
        PageInfo<TrainEmpDTO> pageInfo = PageInfo.of(trainEmpList);

        //查找培训的总体信息
        AppTrainEmpDTO detail = new AppTrainEmpDTO();
        AppTrain appTrain = appTrainMapper.selectByPrimaryKey(trainId);
        String trainTypeName = sysDictService.getDictName(appTrain.getTrainType());
        BeanUtils.copyProperties(appTrain, detail);
        detail.setTrainId(trainId);
        detail.setTrainTypeName(trainTypeName);
        //查询培训通过的员工数
        Integer totalNum = appTrainEmpMapper.countEmp(trainId);
        Integer passNum = appTrainEmpMapper.countPassEmp(trainId);
        double qualifiedRate = totalNum == 0 ? 0 : MathUtil.round(1.0 * passNum / totalNum, 3);
        detail.setEmpNum(totalNum);
        detail.setQualifiedRate(qualifiedRate);
        detail.setEmpList(trainEmpList);
        pageData.setDetail(detail);
        pageData.setTotal(pageInfo.getTotal());
        pageData.setPages(pageInfo.getPages());
        return pageData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void trainEmpAdd(AppTrainEmpAddParam request) throws BizException {
        logger.info("人员管理-添加培训人员,request:{}", request);
        Integer trainId = request.getTrainId();
        AppTrain appTrain = appTrainMapper.selectByPrimaryKey(trainId);
        if (appTrain == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.EMP_BE_044.getCode());
        }
        List<Integer> empIdList = request.getEmpIdList();
        for (Integer empId : empIdList) {
            Integer count = appTrainEmpMapper.countByTrainIdAndEmpId(trainId, empId);
            if (count > 0) {
                continue;//该员工已存在
            }
            AppEmp appEmp = appEmpMapper.selectByPrimaryKey(empId);
            String empName = appEmp.getEmpName();
            AppTrainEmp record = new AppTrainEmp();
            record.setEmpId(empId);
            record.setEmpName(empName);
            record.setTrainId(trainId);
            appTrainEmpMapper.insertSelective(record);
            //如果当前培训是 入场三级教育  需要更新人员 入场三级教育培训状态
            if (enterTrainTypeNameList.contains(appTrain.getTrainTypeName())) {
                appEmpMapper.updateEmpEnterTrainFlag(empId, 1);
            }
        }
    }

    @Override
    public void trainEmpDel(AppTrainEmpDelParam request) throws BizException {
        logger.info("人员管理-删除培训人员,request:{}", request);
        appTrainEmpMapper.deleteByTrainIdAndEmpId(request.getTrainId(), request.getEmpId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void score(AppTrainScoreParam request) throws BizException {
        logger.info("人员管理-添加修改培训分数,request:{}", request);

        AppTrainEmp appTrainEmp = appTrainEmpMapper.selectTrainEmp(request.getTrainId(), request.getEmpId());
        if (appTrainEmp == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.EMP_BE_045.getCode());
        }

        Double score = request.getScore();
        Integer passFlag = request.getPassFlag();
        Integer trainId = request.getTrainId();
        Integer empId = request.getEmpId();
        AppTrain appTrain = appTrainMapper.selectByPrimaryKey(trainId);
        if (appTrain == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.EMP_BE_044.getCode());
        }
        appTrainEmpMapper.updateScoreByTrainIdAndEmpId(score, passFlag, trainId, empId);

        //如果当前培训是 入场三级教育  需要更新人员 入场三级教育培训状态
        if (enterTrainTypeNameList.contains(appTrain.getTrainTypeName())) {
            appEmpMapper.updateEmpEnterTrainFlag(empId, 1);
        }
        //保存培训图片
        saveEmpImg(appTrainEmp.getId(), request.getImgUrlList());
    }

    @Override
    public PageData<AppEmpDTO> getEmpList(Integer pageNum, Integer pageSize, Integer trainId, String keyword, Integer groupId) throws BizException {
        logger.info("人员管理-可添加培训人员列表,pageNum:{},pageSize:{},trainId:{},keyword:{},groupId:{}",
                pageNum, pageSize, trainId, keyword, groupId);
        AppTrain appTrain = appTrainMapper.selectByPrimaryKey(trainId);
        if (appTrain == null) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), I18nErrorCode.EMP_BE_044.getCode());
        }
        Integer deptId = appTrain.getDeptId();

        List<TrainEmpDTO> trainEmpList = appTrainEmpMapper.selectByTrainId(trainId);
        List<Integer> empIdList = trainEmpList.stream().map(TrainEmpDTO::getEmpId).collect(Collectors.toList());

        PageHelper.startPage(pageNum, pageSize);
        List<AppEmpDTO> list = appEmpMapper.selectEmpListExcludes(deptId, empIdList, groupId, keyword);
        PageHelper.clearPage();
        PageInfo<AppEmpDTO> pageInfo = PageInfo.of(list);
        return PageUtil.pageData(pageInfo);
    }

    @Override
    public void saveSignImg(Integer trainEmpId, String signImgUrl) throws BizException {
        logger.info("人员管理-保存签名图片,trainEmpId:{},signImgUrl:{}", trainEmpId, signImgUrl);
        AppTrainEmp trainEmp = appTrainEmpMapper.selectByPrimaryKey(trainEmpId);
        if (trainEmp == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.EMP_BE_045.getCode());
        }
        trainEmp.setSignImgUrl(signImgUrl);
        appTrainEmpMapper.updateByPrimaryKeySelective(trainEmp);
    }

    @Override
    public void saveTrainEmpImg(Integer trainEmpId, List<String> imgUrlList) throws BizException {
        saveEmpImg(trainEmpId, imgUrlList);
    }

    @Override
    public TrainEmpDTO getTrainEmpInfo(Integer trainEmpId) throws BizException {
        AppTrainEmp trainEmp = appTrainEmpMapper.selectByPrimaryKey(trainEmpId);
        if (trainEmp == null) {
            return null;
        }
        TrainEmpDTO trainEmpDTO = new TrainEmpDTO();
        BeanUtils.copyProperties(trainEmp, trainEmpDTO);
        trainEmpDTO.setTrainEmpId(trainEmp.getId());
        return trainEmpDTO;
    }

    @Override
    public void thirdSyncTrain(Integer trainId) throws BizException {
        logger.info("培训第三方同步,trainId:{}", trainId);
        AppTrain train = appTrainMapper.selectByPrimaryKey(trainId);
        if (train.getState() != 1) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.EMP_BE_046.getCode());
        }
        Integer deptId = train.getDeptId();
        List<EmpThirdSyncWapper> wapperList = empThirdSyncFactory.sync(deptId);
        for (EmpThirdSyncWapper wapper : wapperList) {
            CompletableFuture.runAsync(() -> {
                try {
                    wapper.getSync().syncEmpTrain(train, wapper.getConfig());
                    appTrainMapper.updateSyncFlag(trainId, SyncFlag.SUCCESS.getValue());
                } catch (Exception e) {
                    appTrainMapper.updateSyncFlag(trainId, SyncFlag.FAIL.getValue());
                    logger.error("同步培训数据失败,trainId:{},deptId:{},config:{}", trainId, deptId, wapper.getConfig(), e);
                }
            });
        }
    }

    /**
     * 保存培训成绩图片
     *
     * @param trainEmpId 培训人员ID
     * @param imgUrlList 图片列表
     */
    private void saveEmpImg(Integer trainEmpId, List<String> imgUrlList) throws BizException {
        //删除旧的成绩图片
        appTrainEmpImgMapper.logicDel(trainEmpId);
        if (imgUrlList == null || imgUrlList.isEmpty()) {
            return;
        }
        for (String imgUrl : imgUrlList) {
            AppTrainEmpImg trainEmpImg = new AppTrainEmpImg();
            trainEmpImg.setTrainEmpId(trainEmpId);
            trainEmpImg.setImgUrl(imgUrl);
            appTrainEmpImgMapper.insertSelective(trainEmpImg);
        }
    }
}
