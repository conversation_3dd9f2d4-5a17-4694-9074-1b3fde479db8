package com.whfc.emp.dao;

import com.whfc.emp.dto.AppFaceGateVisitorDTO;
import com.whfc.emp.entity.AppFaceGateVisitor;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface AppFaceGateVisitorMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppFaceGateVisitor record);

    int insertSelective(AppFaceGateVisitor record);

    AppFaceGateVisitor selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppFaceGateVisitor record);

    int updateByPrimaryKey(AppFaceGateVisitor record);

    /**
     * 查询访客审批列表
     *
     * @param deptId
     * @param state
     * @param startTime
     * @param endTime
     * @return
     */
    List<AppFaceGateVisitorDTO> selectBYDeptId(@Param("deptId") Integer deptId,
                                               @Param("state") Integer state,
                                               @Param("startTime") Date startTime,
                                               @Param("endTime") Date endTime);

    /**
     * 软删除
     *
     * @param visitorId
     * @return
     */
    int updateDelByVisitorId(Integer visitorId);

    /**
     * 查询临时工信息
     *
     * @param faceGateId
     * @param openid
     * @return
     */
    AppFaceGateVisitor selectDayLaborerByOpenIdAndFaceGateId(@Param("faceGateId") Integer faceGateId, @Param("openid") String openid);

    /**
     * 查询已过期人员并删除授权
     *
     * @param time
     * @return
     */
    List<AppFaceGateVisitor> selectOverdue(String time);
}