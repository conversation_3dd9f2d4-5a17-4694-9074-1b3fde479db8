package com.whfc.emp.queue;

import com.alibaba.fastjson.JSONObject;
import com.whfc.common.constant.QueueConst;
import com.whfc.common.enums.Gender;
import com.whfc.common.face.kyy.KyyCmd;
import com.whfc.common.face.kyy.KyyError;
import com.whfc.common.face.kyy.cmd.*;
import com.whfc.common.face.kyy.entity.Person;
import com.whfc.common.util.JSONUtil;
import com.whfc.emp.dao.AppEmpMapper;
import com.whfc.emp.dao.AppFaceGateMapper;
import com.whfc.emp.dao.AppFaceGatePersonMapper;
import com.whfc.emp.entity.AppFaceGate;
import com.whfc.emp.entity.AppFaceGatePerson;
import com.whfc.emp.enums.*;
import com.whfc.emp.param.EmpInfoSyncDataParam;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

import static com.whfc.common.face.kyy.KyyCmd.*;

/**
 * @Description: 快优易闸机上传消息处理
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/10/30 9:32
 */
@Component
@RabbitListener(queuesToDeclare = {@Queue(name = QueueConst.KYY_UP_MSG)}, concurrency = "1-2")
public class KyyUpMessageListener {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private AppEmpMapper appEmpMapper;

    @Autowired
    private AppFaceGatePersonMapper appFaceGatePersonMapper;

    @Autowired
    private AppFaceGateMapper appFaceGateMapper;

    @Autowired
    private AmqpTemplate amqpTemplate;

    @RabbitHandler
    public void process(String msg) {
        try {
            logger.info("kyy|up|msg|{}", msg.length() > 200 ? msg.substring(0, 200) : msg);
            JSONObject json = JSONObject.parseObject(msg);
            if (json != null) {
                String cmd = json.getString("cmd");
                String ack = json.getString("reply");
                //心跳推送
                if (KyyCmd.HEART_BEAT.equals(cmd)) {
                    HeartBeat heartBeat = JSONUtil.parseObject(msg, HeartBeat.class);
                    this.handleHeartBeat(heartBeat);
                }

                //抓拍数据推送
                else if (KyyCmd.FACE.equals(cmd)) {
                    Capture capture = JSONUtil.parseObject(msg, Capture.class);
                    this.handleCapture(capture);
                }

                //新增人员-响应
                else if (KyyCmd.UPLOAD_PERSON.equals(cmd) && ACK.equals(ack)) {
                    UploadPersonAck uploadPersonAck = JSONUtil.parseObject(msg, UploadPersonAck.class);
                    this.handleAck(uploadPersonAck);
                }

                //删除人员-响应
                else if (KyyCmd.DELETE_PERSONS.equals(cmd) && ACK.equals(ack)) {
                    DeletePersonsAck deletePersonsAck = JSONUtil.parseObject(msg, DeletePersonsAck.class);
                    this.handleAck(deletePersonsAck);
                }

                //查询人员-响应
                else if (KyyCmd.REQUEST_PERSONS.equals(cmd)) {
                    RequestPersonsAck requestPersonsAck = JSONUtil.parseObject(msg, RequestPersonsAck.class);
                    this.handleAck(requestPersonsAck);
                }

                //获取所有人ID-响应
                else if (KyyCmd.GET_ALL_PERSON_ID.equals(cmd)) {
                    GetAllPersonIdAck allPersonIdAck = JSONUtil.parseObject(msg, GetAllPersonIdAck.class);
                    this.handleAck(allPersonIdAck);
                }

                //历史数据查询-响应
                else if (KyyCmd.REQUEST_RECORDS.equals(cmd)) {
                    RequestRecordsAck requestRecordsAck = JSONUtil.parseObject(msg, RequestRecordsAck.class);
                    this.handleAck(requestRecordsAck);
                }

                //历史数据删除-响应
                else if (KyyCmd.DELETE_RECORD.equals(cmd)) {
                    DeleteRecordAck deleteRecordAck = JSONUtil.parseObject(msg, DeleteRecordAck.class);
                    this.handleAck(deleteRecordAck);
                }
            }
        } catch (Exception ex) {
            logger.error("kyy|up|msg,消息处理失败", ex);
        }
    }

    /**
     * 处理-心跳推送
     *
     * @param heartBeat
     */
    private void handleHeartBeat(HeartBeat heartBeat) {
        //处理数据
        String deviceKey = heartBeat.getDevice_sn();
        AppFaceGate faceGate = appFaceGateMapper.selectByDeviceKey(deviceKey);
        if (faceGate != null) {
            appFaceGateMapper.updateState(faceGate.getId(), NetState.ONLINE.getValue(), new Date());
        }
        //发送响应
        HeartBeatAck heartBeatAck = new HeartBeatAck();
        heartBeatAck.setCmd(heartBeat.getCmd());
        heartBeatAck.setDevice_sn(heartBeat.getDevice_sn());
        heartBeatAck.setCode(SUCCESS);
        heartBeatAck.setReply(ACK);
        //amqpTemplate.convertAndSend(QueueConst.KYY_DOWN_MSG, JSONUtil.toString(heartBeatAck));
    }

    /**
     * 处理-抓拍推送
     *
     * @param capture
     */
    private void handleCapture(Capture capture) {
        //处理数据

        //发送响应
    }

    /**
     * 处理-新增人员授权响应
     *
     * @param result 人员授权结果
     */
    private void handleAck(UploadPersonAck result) {
        Integer code = result.getCode();
        String deviceKey = result.getDevice_sn();
        String personGuid = result.getId();
        logger.info("添加人员-响应,deviceKey:{},personGuid:{},code:{}", deviceKey, personGuid, code);
        AppFaceGatePerson faceGatePerson = appFaceGatePersonMapper.selectByPersonGuidAndDeviceKey(personGuid, deviceKey);
        if (faceGatePerson == null) {
            return;
        }
        //新增人员成功
        if (SUCCESS.equals(code)) {
            appFaceGatePersonMapper.updateTaskType(faceGatePerson.getId(), TaskType.EMP_AUTH.getValue(), "");
        }
        //新增人员失败
        else {
            appFaceGatePersonMapper.updateTaskType(faceGatePerson.getId(), TaskType.EMP_AUTH_ERROR.getValue(), KyyError.getErrorMsg(String.valueOf(code)));
        }
    }

    /**
     * 处理-删除人员授权响应
     *
     * @param result
     */
    private void handleAck(DeletePersonsAck result) {
        Integer code = result.getCode();
        String deviceKey = result.getDevice_sn();
        Integer empId = Integer.valueOf(result.getId());
        logger.info("删除人员,code:{},deviceKey:{},id:{}", code, deviceKey, empId);
        AppFaceGate faceGate = appFaceGateMapper.selectByDeviceKey(deviceKey);
        if (faceGate == null) {
            return;
        }
        AppFaceGatePerson faceGatePerson = appFaceGatePersonMapper.selectByFaceGateIdAndEmpId(faceGate.getId(), empId);
        if (faceGatePerson == null) {
            return;
        }
        appFaceGatePersonMapper.updateTaskType(faceGatePerson.getId(), TaskType.EMP_UN_AUTH.getValue(), KyyError.getErrorMsg(String.valueOf(code)));
    }

    /**
     * 处理-查询人员授权响应
     *
     * @param result
     */
    private void handleAck(RequestPersonsAck result) {
        Integer code = result.getCode();
        String deviceSn = result.getDevice_sn();
        Integer total = result.getTotal();
        Integer page_no = result.getPage_no();
        Integer count = result.getCount();
        List<Person> persons = result.getPersons();

        AppFaceGate faceGate = appFaceGateMapper.selectByDeviceKey(deviceSn);
        if (faceGate == null) {
            return;
        }

        //同步人员信息
        if (persons != null) {
            try {
                for (Person person : persons) {
                    String id = person.getId();
                    String name = person.getName();
                    String imageBase64 = null;
                    if (person.getReg_images() != null && person.getReg_images().size() > 0) {
                        imageBase64 = person.getReg_images().get(0).getImage_data();
                    }

                    EmpInfoSyncDataParam param = new EmpInfoSyncDataParam();
                    param.setSyncType(FaceGateSyncOp.EMP_ADD_OR_UPDATE.getValue());
                    param.setImgType(EmpSyncImgType.BASE64.getValue());
                    param.setDeptId(faceGate.getDeptId());
                    param.setEmpCode(id);
                    param.setEmpName(name);
                    param.setGender(Gender.unknown.getValue());
                    param.setAvatar(imageBase64);
                    param.setPlatform(FaceGateType.KYY.getCode());
                    //推送消息
                    String jsonStr = JSONUtil.toString(param);
                    amqpTemplate.convertAndSend(QueueConst.EMP_FACEGATE_EMP_INFO, jsonStr);
                }
            } catch (Exception ex) {
                logger.error("同步人员信息失败", ex);
            }
        }

        //查询下一页人员信息
        if (PAGE_SIZE.equals(count)) {
            Integer nextPageNo = SUCCESS.equals(code) ? page_no + 1 : page_no;
            RequestPersons requestPersons = new RequestPersons();
            requestPersons.setDevice_sn(deviceSn);
            requestPersons.setCmd(REQUEST_PERSONS);
            requestPersons.setRole(-1);
            requestPersons.setPage_no(nextPageNo);
            requestPersons.setPage_size(PAGE_SIZE);
            requestPersons.setImage_flag(1);
            amqpTemplate.convertAndSend(QueueConst.KYY_DOWN_MSG, JSONUtil.toString(requestPersons));
        }
    }

    /**
     * 处理-查询人员ID响应
     *
     * @param result
     */
    private void handleAck(GetAllPersonIdAck result) {
        Integer code = result.getCode();
        String deviceKey = result.getDevice_sn();
        String ids = result.getIds();
        String[] idList = StringUtils.split(ids, ",");
        logger.info("删除人员,code:{},deviceKey:{},size:{},ids:{}", code, deviceKey, idList.length, ids);
    }

    /**
     * 处理-查询记录响应
     *
     * @param result
     */
    private void handleAck(RequestRecordsAck result) {

    }

    /**
     * 处理-删除记录D响应
     *
     * @param result
     */
    private void handleAck(DeleteRecordAck result) {

    }
}
