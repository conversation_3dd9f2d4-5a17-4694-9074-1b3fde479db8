package com.whfc.emp.factory.impl;

import com.whfc.common.geometry.Point;
import com.whfc.emp.dao.AppFaceGateMapper;
import com.whfc.emp.entity.AppFaceGate;
import com.whfc.emp.entity.AppFaceGateConfig;
import com.whfc.emp.factory.FaceGateManager;
import com.whfc.emp.param.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description 离线闸机
 * <AUTHOR>
 * @Date 2021/1/27 14:20
 * @Version 1.0
 */
@Service("faceGateManagerImpl")
public class FaceGateManagerImpl implements FaceGateManager {

    @Autowired
    private AppFaceGateMapper appFaceGateMapper;

    @Override
    public void add(AppFaceGateAddParam request, AppFaceGateConfig config) {
        String deviceKey = request.getDeviceKey();
        String name = request.getName();
        Integer deptId = request.getDeptId();
        Point point = request.getPoint();

        AppFaceGate record = new AppFaceGate();
        record.setDeviceKey(deviceKey);
        record.setDeptId(deptId);
        record.setName(name);
        record.setDirection(request.getDirection());
        record.setPlatform(request.getPlatform());
        record.setModel(request.getModel());
        // 增加闸机位置信息
        if (point != null) {
            Double lat = point.getLat();
            Double lng = point.getLng();
            record.setLng(lng);
            record.setLat(lat);
        }
        appFaceGateMapper.insertSelective(record);
    }

    @Override
    public void del(Integer faceGateId, AppFaceGateConfig config) {
        appFaceGateMapper.deleteLogicById(faceGateId);
    }

    @Override
    public String faceGateGrantEmdAdd(FaceGateGrantEmdAddParam request, AppFaceGateConfig config) {
        return null;
    }

    @Override
    public String faceGateGrantEmdImgAdd(FaceGateGrantEmdImgAddParam request, AppFaceGateConfig config) {
        return null;
    }

    @Override
    public String faceGateGrantEmdAuth(FaceGateGrantEmdAuthParam request, AppFaceGateConfig config) {
        return null;
    }

    @Override
    public void faceGateBatchGrantEmpAuth(AppFaceGateGrantEmpParam request, AppFaceGateConfig config) {

    }

    @Override
    public void faceGateRevokeEmp(AppFaceGateGrantEmpParam request, AppFaceGateConfig config) {

    }

    @Override
    public String deviceAuthorizationPerson(String deviceKey, String name, String imgUrl, AppFaceGateConfig config) {
        return null;
    }

    @Override
    public String deviceAuthorizationCancel(String deviceKey, String personGuid, AppFaceGateConfig config) {
        return null;
    }

    @Override
    public void openDoor(String deviceKey, AppFaceGateConfig config) {

    }

    @Override
    public void sync(Integer faceGateId, AppFaceGateConfig config) {

    }

    @Override
    public String getToken(AppFaceGateConfig config) {
        return null;
    }
}
