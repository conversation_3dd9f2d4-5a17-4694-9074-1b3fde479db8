package com.whfc.emp.dao;

import com.whfc.emp.dto.AppEmpRewardDTO;
import com.whfc.emp.entity.AppEmpRewardPunishment;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface AppEmpRewardPunishmentMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppEmpRewardPunishment record);

    int insertSelective(AppEmpRewardPunishment record);

    AppEmpRewardPunishment selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppEmpRewardPunishment record);

    int updateByPrimaryKey(AppEmpRewardPunishment record);

    /**
     * 查找记录列表
     *
     * @param projectId
     * @param startDate
     * @param endDate
     * @param keyword
     * @return
     */
    List<AppEmpRewardDTO> selectByProjectId(@Param("projectId") Integer projectId,
                                            @Param("startDate") Date startDate,
                                            @Param("endDate") Date endDate,
                                            @Param("keyword") String keyword);

    /**
     * 逻辑删除
     *
     * @param id
     */
    void deleteLogicById(@Param("id") Integer id);

    /**
     * 根据人员id查找
     *
     * @param empId
     * @return
     */
    List<AppEmpRewardDTO> selectByEmpId(@Param("empId") Integer empId);
}