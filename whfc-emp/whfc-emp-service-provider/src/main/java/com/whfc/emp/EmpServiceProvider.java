package com.whfc.emp;

import com.alibaba.nacos.spring.context.annotation.config.NacosPropertySource;
import com.alibaba.nacos.spring.context.annotation.config.NacosPropertySources;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2019/10/21 11:05
 */
@SpringBootApplication(scanBasePackages = "com.whfc")
@EnableScheduling
@EnableAsync
@NacosPropertySources(value = {
        @NacosPropertySource(dataId = "com.whfc.emp.properties", autoRefreshed = true),
        @NacosPropertySource(dataId = "com.whfc.common.properties", autoRefreshed = true)
})
public class EmpServiceProvider {

    public static void main(String[] args) {
        SpringApplication.run(EmpServiceProvider.class, args);
    }
}
