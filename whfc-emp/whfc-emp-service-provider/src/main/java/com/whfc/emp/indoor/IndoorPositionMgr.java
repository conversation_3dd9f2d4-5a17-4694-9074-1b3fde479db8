package com.whfc.emp.indoor;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson2.JSON;
import com.whfc.common.constant.QueueConst;
import com.whfc.common.enums.BindFlag;
import com.whfc.common.iot.forthink.entity.ForthinkDistance;
import com.whfc.common.iot.forthink.entity.ForthinkTofDataMsg;
import com.whfc.common.iot.forthink.entity.ForthinkWarnRecord;
import com.whfc.common.iot.forthink.entity.ForthinkWarnRecordQueryRespMsg;
import com.whfc.common.redis.RedisConst;
import com.whfc.common.redis.RedisService;
import com.whfc.common.spring.AppContextUtil;
import com.whfc.common.third.indoor.msg.LocationMsg;
import com.whfc.common.third.indoor.uwb.UwbRealPosition;
import com.whfc.common.util.DateUtil;
import com.whfc.common.util.JSONUtil;
import com.whfc.common.util.MathUtil;
import com.whfc.common.util.PositionUtil;
import com.whfc.emp.dao.AppIndoorPositionMapMapper;
import com.whfc.emp.dao.AppIndoorPositionStationMapper;
import com.whfc.emp.dao.AppIndoorPositionTagMapper;
import com.whfc.emp.entity.AppIndoorPositionMap;
import com.whfc.emp.entity.AppIndoorPositionStation;
import com.whfc.emp.entity.AppIndoorPositionTag;
import com.whfc.emp.entity.AppIndoorPositionTagLog;
import com.whfc.emp.enums.NetState;
import com.whfc.emp.event.IndoorPositionEvent;
import com.whfc.emp.influx.AppIndoorPositionTagLogDao;
import com.whfc.emp.redis.IndoorPositionRedisDao;
import com.whfc.entity.dto.emp.IndoorTagDataDTO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @Description: 室内定位数据处理
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/10/13 10:44
 */
@Component
public class IndoorPositionMgr {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private AppIndoorPositionMapMapper indoorPositionMapMapper;

    @Autowired
    private AppIndoorPositionStationMapper indoorPositionStationMapper;

    @Autowired
    private AppIndoorPositionTagMapper indoorPositionTagMapper;

    @Autowired
    private AppIndoorPositionTagLogDao indoorPositionTagLogDao;

    @Autowired
    private IndoorPositionRedisDao indoorPositionRedisDao;

    @Autowired
    private RedisService redisService;

    @Autowired
    private AmqpTemplate amqpTemplate;

    /**
     * 添加标签定位数据
     *
     * @param msg
     */
    public void addPosition(LocationMsg msg) {
        String tagId = msg.getTagId();
        String mapId = msg.getMapId();
        String stationId = msg.getMainStationId();
        // 验证地图
        AppIndoorPositionMap map = this.getMap(mapId);
        if (map == null) {
            logger.info("地图不存在,mapId:{}", mapId);
            return;
        }
        // 验证基站
        AppIndoorPositionStation station = this.getStation(stationId);
        if (station == null) {
            logger.info("基站不存在,stationId:{}", stationId);
            return;
        }
        // 验证标签
        AppIndoorPositionTag tag = this.getTag(station.getDeptId(), tagId);
        if (tag == null) {
            logger.info("标签不存在,tagId:{}", tagId);
            tag = new AppIndoorPositionTag();
            tag.setDeptId(station.getDeptId());
            tag.setGuid(tagId);
            indoorPositionTagMapper.insertSelective(tag);
        }

        // 计算距离
        if (msg.getDistance() == null || msg.getDistance() == 0) {
            double distance = PositionUtil.getPlaneDistance(station.getX(), station.getY(), msg.getX(), msg.getY());
            msg.setDistance(MathUtil.toInteger(String.valueOf(distance)));
        }

        // 更新标签定位数据
        Date now = new Date();
        tag.setTime(now);
        tag.setX(msg.getX());
        tag.setY(msg.getY());
        tag.setLng(msg.getLng());
        tag.setLat(msg.getLat());
        tag.setDistance(msg.getDistance());
        tag.setBatteryPower(msg.getVbatPercent());
        tag.setMapId(map.getId());
        tag.setStationId(station.getId());
        tag.setStatus(NetState.ONLINE.getValue());
        indoorPositionTagMapper.updateByPrimaryKeySelective(tag);

        // 新增标签定位历史数据
        AppIndoorPositionTagLog log = new AppIndoorPositionTagLog();
        BeanUtils.copyProperties(tag, log);
        log.setId(null);
        log.setTagId(tag.getId());
        indoorPositionTagLogDao.insert(log);
    }

    /**
     * 新增蓝牙标签扫描
     *
     * @param imei 基站imei
     * @param mac  标签mac
     */
    public void addBleScan(String imei, String mac) {

        // 验证基站
        AppIndoorPositionStation station = this.getStation(imei);
        if (station == null) {
            logger.info("基站不存在,imei:{}", imei);
            return;
        }

        // 验证标签
        Date now = new Date();
        Integer deptId = station.getDeptId();
        AppIndoorPositionTag tag = this.getTag(deptId, mac);
        if (tag == null) {
            logger.info("标签不存在,mac:{}", mac);
            tag = new AppIndoorPositionTag();
            tag.setDeptId(deptId);
            tag.setGuid(mac);
            tag.setCode(mac);
            tag.setTime(now);
            tag.setX(station.getX());
            tag.setY(station.getY());
            tag.setLng(station.getLng());
            tag.setLat(station.getLat());
            tag.setDistance(0);
            tag.setStationId(station.getId());
            tag.setStatus(NetState.ONLINE.getValue());
            indoorPositionTagMapper.insertSelective(tag);
        }
        // 更新标签定位数据
        else {
            tag.setTime(now);
            tag.setX(station.getX());
            tag.setY(station.getY());
            tag.setLng(station.getLng());
            tag.setLat(station.getLat());
            tag.setDistance(0);
            tag.setStationId(station.getId());
            tag.setStatus(NetState.ONLINE.getValue());
            indoorPositionTagMapper.updateByPrimaryKeySelective(tag);
        }
    }

    /**
     * 新增UWB定位数据
     *
     * @param data
     */
    public void addUwbData(UwbRealPosition data) {
        Integer deptId = data.getDeptId();
        String tagId = data.getTagId();
        Date time = data.getTime();
        Double x = data.getPersonX();
        Double y = data.getPersonY();
        Double z = data.getPersonZ();
        Double stationX = data.getAnchorX();
        Double stationY = data.getAnchorY();
        Double stationZ = data.getAnchorZ();
        Integer batteryPower = data.getTagPower();
        Integer state = data.getTagState();
        Integer sos = data.getTagSos();
        Double distance = data.getAnchorDis();
        Double distance1 = data.getGateDis();

        // 验证标签
        // AppIndoorPositionTag tag = indoorPositionTagMapper.selectByDeptIdAndGuid(deptId, tagId);
        AppIndoorPositionTag tag = this.getTag(deptId, tagId);
        // 验证标签
        if (tag == null) {
            logger.info("标签不存在,tagId:{}", tagId);
            tag = new AppIndoorPositionTag();
            tag.setDeptId(deptId);
            tag.setGuid(tagId);
            tag.setCode(tagId);
            indoorPositionTagMapper.insertSelective(tag);
            return;
        }
        tag.setTime(time);
        tag.setX(x);
        tag.setY(y);
        tag.setZ(z);
        tag.setLng(null);
        tag.setLat(null);
        tag.setBatteryPower(batteryPower);
        tag.setState(state);
        tag.setSos(sos);
        tag.setStatus(NetState.ONLINE.getValue());
        tag.setDistance(distance != null ? MathUtil.toInteger(String.valueOf(distance)) : null);
        tag.setDistance1(distance1 != null ? MathUtil.toInteger(String.valueOf(distance1)) : null);
        tag.setStationX(stationX);
        tag.setStationY(stationY);
        tag.setStationZ(stationZ);
        indoorPositionTagMapper.updateByPrimaryKeySelective(tag);

        // 已绑定人员的标签,发布事件
        if (BindFlag.BIND.getValue().equals(tag.getBindFlag()) && tag.getEmpId() != null) {
            AppContextUtil.context().publishEvent(new IndoorPositionEvent(tag));
        }
    }

    /**
     * 新增uwb测距数据
     *
     * @param tofData
     */
    public void addFtkTofData(ForthinkTofDataMsg tofData) {
        String tagId = String.valueOf(tofData.getAddr());
        String stationId = String.valueOf(tofData.getDataAddr());
        List<ForthinkDistance> dataList = tofData.getDataList();
        Integer distance = null;
        if (dataList != null && !dataList.isEmpty()) {
            ForthinkDistance distanceData = dataList.get(0);
            distance = distanceData.getDistance() / 100;
        }

        logger.info("ftk-tof-data,tagId:{},stationId:{},distance:{}", tagId, stationId, distance);

        // 验证基站
        AppIndoorPositionStation station = this.getStation(stationId);
        if (station == null) {
            logger.info("基站不存在,imei:{}", stationId);
            return;
        }
        // 保存设备上线信息  默认超过30分钟没上线即为离线
        redisService.set(String.format(RedisConst.UWB_DEVICE_ONLINE, stationId), "1", 30, TimeUnit.MINUTES);

        // 验证标签
        Date now = new Date();
        Integer deptId = station.getDeptId();
        AppIndoorPositionTag tag = this.getTag(deptId, tagId);
        if (tag == null) {
            logger.info("标签不存在,mac:{}", tagId);
            return;
        }

        tag.setTime(now);
        tag.setX(station.getX());
        tag.setY(station.getY());
        tag.setLng(station.getLng());
        tag.setLat(station.getLat());
        tag.setDistance(distance);
        tag.setStationId(station.getId());
        tag.setStatus(NetState.ONLINE.getValue());
        indoorPositionTagMapper.updateByPrimaryKeySelective(tag);

        // 发送数据校验是否报警
        if (tag.getBindType() == null) {
            logger.info("标签未绑定,tag:{}", JSON.toJSONString(tag));
            return;
        }
        if (distance == null) {
            logger.info("距离数据为空,tofData:{}", JSON.toJSONString(tofData));
            return;
        }
        IndoorTagDataDTO indoorTagDataDTO = new IndoorTagDataDTO();
        indoorTagDataDTO.setDeptId(deptId);
        indoorTagDataDTO.setTagId(tag.getId());
        indoorTagDataDTO.setTagName(tag.getName());
        indoorTagDataDTO.setBindType(tag.getBindType());
        indoorTagDataDTO.setTime(now);
        indoorTagDataDTO.setEmpId(tag.getEmpId());
        indoorTagDataDTO.setEmpName(tag.getEmpName());
        indoorTagDataDTO.setDeviceCode(tag.getDeviceCode());
        indoorTagDataDTO.setDeviceCode(tag.getDeviceCode());
        indoorTagDataDTO.setStationId(station.getId());
        indoorTagDataDTO.setStationName(station.getName());
        indoorTagDataDTO.setDistance(distance * 1.0);
        amqpTemplate.convertAndSend(QueueConst.EMP_INDOOR_UWB_TAG_DATA, JSONUtil.toString(indoorTagDataDTO));
    }

    /**
     * 新增uwb报警记录
     *
     * @param data
     */
    public void addFtkWarnRecord(ForthinkWarnRecordQueryRespMsg data) {

        // 验证基站
        String stationId = String.valueOf(data.getStation());
        AppIndoorPositionStation station = this.getStation(stationId);
        if (station == null) {
            logger.info("基站不存在,stationId:{}", stationId);
            return;
        }

        // 报警记录
        List<ForthinkWarnRecord> warnList = data.getWarnList();
        for (ForthinkWarnRecord warn : warnList) {
            int start = warn.getStart();
            int duration = warn.getDuration();
            int minDistance = warn.getMinDistance();
            Date time = new Date(start * 1000L);
            logger.info("ftk-warn-data,station:{},seq:{},flag:{},num:{},tag:{},start:{},duration:{},distance:{}",
                    data.getStation(), data.getSeq(), data.getFlag(), data.getNum(), warn.getTag(), DateUtil.formatDateTime(time), duration, minDistance);

            // 验证start时间,避免重复处理
            String startKey = String.format(RedisConst.UWB_WARN_RECORD_QUERY_START, data.getStation());
            String startStr = redisService.get(startKey);
            if (StringUtils.isNotEmpty(startStr)) {
                long lastStart = Long.parseLong(startStr);
                if (start <= lastStart) {
                    continue;
                }
            }
            redisService.set(startKey, String.valueOf(start));

            // 验证标签
            String tagId = String.valueOf(warn.getTag());
            AppIndoorPositionTag tag = this.getTag(station.getDeptId(), tagId);
            if (tag == null) {
                logger.info("标签不存在,tagId:{}", tagId);
                return;
            }

            // 发送报警记录
            if (tag.getBindType() == null) {
                logger.info("标签未绑定,tag:{}", JSON.toJSONString(tag));
                return;
            }
            double distance = minDistance > 0 ? NumberUtil.div(minDistance, 100.0, 2) : 0;
            IndoorTagDataDTO indoorTagDataDTO = new IndoorTagDataDTO();
            BeanUtils.copyProperties(tag, indoorTagDataDTO);
            indoorTagDataDTO.setTime(time);
            indoorTagDataDTO.setStationId(station.getId());
            indoorTagDataDTO.setStationName(station.getName());
            indoorTagDataDTO.setDistance(distance);
            amqpTemplate.convertAndSend(QueueConst.EMP_INDOOR_UWB_TAG_WARN, JSONUtil.toString(indoorTagDataDTO));
        }


    }

    private AppIndoorPositionMap getMap(String guid) {
        AppIndoorPositionMap map = indoorPositionRedisDao.getMap(guid);
        if (map == null) {
            map = indoorPositionMapMapper.selectByGuid(guid);
            if (map != null) {
                indoorPositionRedisDao.setMap(guid, map);
            }
        }
        return map;
    }

    private AppIndoorPositionStation getStation(String guid) {
        AppIndoorPositionStation station = indoorPositionRedisDao.getStation(guid);
        if (station == null) {
            station = indoorPositionStationMapper.selectByCode(guid);
            if (station != null) {
                indoorPositionRedisDao.setStation(guid, station);
            }
        }
        return station;
    }

    private AppIndoorPositionTag getTag(Integer deptId, String guid) {
        AppIndoorPositionTag tag = indoorPositionRedisDao.getTag(deptId, guid);
        if (tag == null) {
            tag = indoorPositionTagMapper.selectByDeptIdAndGuid(deptId, guid);
            if (tag != null) {
                indoorPositionRedisDao.setTag(deptId, guid, tag);
            }
        }
        return tag;
    }
}
