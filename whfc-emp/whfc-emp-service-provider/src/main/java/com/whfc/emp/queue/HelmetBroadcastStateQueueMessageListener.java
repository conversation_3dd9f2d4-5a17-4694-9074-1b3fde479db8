package com.whfc.emp.queue;

import com.alibaba.fastjson.JSON;
import com.whfc.common.constant.QueueConst;
import com.whfc.emp.dto.AppBroadcastRecordEmpDTO;
import com.whfc.emp.manager.AppBroadcastManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> qzexing
 * @version : 1.0
 * @date : 2020/7/3 16:58
 */
@Component
@RabbitListener(queuesToDeclare = {@Queue(name = QueueConst.EMP_HELMET_BOARDCAST_STATE)}, concurrency = "1-2")
public class HelmetBroadcastStateQueueMessageListener {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private AppBroadcastManager appBroadcastManager;


    @RabbitHandler
    public void process(String msg) {
        logger.info("更新人员广播状态,msg:{}", msg);
        AppBroadcastRecordEmpDTO recordEmpDTO = JSON.parseObject(msg, AppBroadcastRecordEmpDTO.class);
        if (recordEmpDTO == null) {
            return;
        }
        appBroadcastManager.updateBroadcastState(recordEmpDTO.getId(), recordEmpDTO.getCode(), recordEmpDTO.getState());
    }
}
