package com.whfc.emp.dao;

import com.whfc.emp.dto.AppFenceTimeDTO;
import com.whfc.emp.entity.AppFenceTime;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/20 15:06
 */

public interface AppFenceTimeMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppFenceTime record);

    int insertSelective(AppFenceTime record);

    AppFenceTime selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppFenceTime record);

    int updateByPrimaryKey(AppFenceTime record);

    /**
     * 根据电子围栏ID查找
     *
     * @param fenceId 电子围栏ID
     * @return
     */
    List<AppFenceTimeDTO> selectByFenceId(@Param("fenceId") Integer fenceId);

    /**
     * 逻辑删除时间
     *
     * @param fenceId 电子围栏ID
     */
    void logicDelByFenceId(@Param("fenceId") Integer fenceId);

    /**
     * 批量插入时间
     *
     * @param timeList 时间范围
     */
    void batchInsert(@Param("timeList") List<AppFenceTimeDTO> timeList);

    /**
     * 根据电子围栏ID查找
     * @param fenceIds
     * @return
     */
    List<AppFenceTimeDTO> selectByFenceIds(@Param("fenceIds")List<Integer> fenceIds);
}