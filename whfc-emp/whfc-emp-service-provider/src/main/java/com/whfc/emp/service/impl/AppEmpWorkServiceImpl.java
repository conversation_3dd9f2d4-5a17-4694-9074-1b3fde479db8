package com.whfc.emp.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.whfc.common.enums.I18nErrorCode;
import com.whfc.common.exception.BizException;
import com.whfc.common.result.PageData;
import com.whfc.common.result.ResultEnum;
import com.whfc.common.util.PageUtil;
import com.whfc.emp.dao.AppEmpMapper;
import com.whfc.emp.dao.AppEmpWorkTypeMapper;
import com.whfc.emp.dto.AppWorkTypeDTO;
import com.whfc.emp.entity.AppEmpWorkType;
import com.whfc.emp.param.WorkTypeAddParam;
import com.whfc.emp.param.WorkTypeEditParam;
import com.whfc.emp.service.AppEmpWorkService;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 人员模块-工种字典
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/12/24 9:50
 */
@DubboService(interfaceClass = AppEmpWorkService.class, version = "1.0.0")
public class AppEmpWorkServiceImpl implements AppEmpWorkService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 全局的工种所在的deptId
     */
    private static final Integer DEPT_ID = 0;

    @Autowired
    private AppEmpWorkTypeMapper appEmpWorkTypeMapper;

    @Autowired
    private AppEmpMapper appEmpMapper;

    @Override
    public PageData<AppWorkTypeDTO> workTypeList(Integer deptId, Integer pageNum, Integer pageSize) {
        logger.info("工种列表(分页),deptId:{},pageNum:{},pageSize:{}", deptId, pageNum, pageSize);
        PageHelper.startPage(pageNum, pageSize);
        List<AppWorkTypeDTO> list = appEmpWorkTypeMapper.selectByDeptId(deptId);
        return PageUtil.pageData(PageInfo.of(list));
    }

    @Override
    public List<AppWorkTypeDTO> workTypeList(Integer deptId) {
        logger.info("工种列表(不分页)，deptId：{}", deptId);
        List<AppWorkTypeDTO> list = appEmpWorkTypeMapper.selectByDeptId(deptId);
        if (list.isEmpty()) {
            list = appEmpWorkTypeMapper.selectByDeptId(DEPT_ID);
            if (!list.isEmpty()) {
                list.forEach(appWorkTypeDTO -> appWorkTypeDTO.setDeptId(deptId));
                appEmpWorkTypeMapper.batchInsert(list);
            }
        }
        return list;
    }

    @Override
    public void workTypeAdd(WorkTypeAddParam param) {
        logger.info("添加工种，param：{}", param);
        Integer deptId = param.getDeptId();
        String name = param.getWorkTypeName();
        String workCode = param.getWorkCode();
        AppEmpWorkType appEmpWorkType = appEmpWorkTypeMapper.selectByDeptIdAndName(deptId, name);
        if (appEmpWorkType != null) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_063.getCode());
        }
        if (StringUtils.isNotEmpty(workCode)) {
            AppEmpWorkType workType = appEmpWorkTypeMapper.selectByDeptIdAndWorkCode(deptId, workCode);
            if (workType != null) {
                throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_064.getCode());
            }
        }
        AppEmpWorkType record = new AppEmpWorkType();
        record.setName(name);
        record.setDeptId(deptId);
        record.setWorkCode(workCode);
        record.setSpec(param.getSpec());
        record.setNeedCert(param.getNeedCert());
        appEmpWorkTypeMapper.insertSelective(record);
    }

    @Override
    public void workTypeEdit(WorkTypeEditParam param) {
        logger.info("编辑工种，param：{}", param);
        Integer deptId = param.getDeptId();
        Integer workTypeId = param.getWorkTypeId();
        String workTypeName = param.getWorkTypeName();
        String workCode = param.getWorkCode();
        AppEmpWorkType appEmpWorkType = appEmpWorkTypeMapper.selectByDeptIdAndName(deptId, workTypeName);
        if (appEmpWorkType != null && !appEmpWorkType.getId().equals(workTypeId)) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_063.getCode());
        }
        if (StringUtils.isNotEmpty(workCode)) {
            AppEmpWorkType workType = appEmpWorkTypeMapper.selectByDeptIdAndWorkCode(deptId, workCode);
            if (workType != null && !param.getWorkTypeId().equals(workType.getId())) {
                throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_064.getCode());
            }
        }
        AppEmpWorkType record = new AppEmpWorkType();
        record.setName(workTypeName);
        record.setId(workTypeId);
        record.setWorkCode(workCode);
        record.setSpec(param.getSpec());
        record.setNeedCert(param.getNeedCert());
        appEmpWorkTypeMapper.updateByPrimaryKeySelective(record);

        //更新人员工种信息
        appEmpMapper.updateWorkTypeName(deptId, workTypeId, workTypeName);
    }

    @Override
    public void workTypeDel(Integer id) {
        logger.info("删除工种，id：{}", id);

        AppEmpWorkType workType = appEmpWorkTypeMapper.selectByPrimaryKey(id);
        if (workType == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.EMP_BE_041.getCode());
        }

        int count = appEmpMapper.countByWorkTypeId(workType.getDeptId(), id);
        if (count > 0) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.EMP_BE_042.getCode());
        }
        appEmpWorkTypeMapper.deleteLogicById(id);
    }
}
