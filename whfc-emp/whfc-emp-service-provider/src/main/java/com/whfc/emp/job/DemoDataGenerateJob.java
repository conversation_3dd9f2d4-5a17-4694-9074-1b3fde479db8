package com.whfc.emp.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.whfc.XxlJobConfig;
import com.whfc.common.constant.QueueConst;
import com.whfc.common.enums.DevicePlatform;
import com.whfc.common.iot.cncit.entity.CncitUpMsgS;
import com.whfc.common.util.DateUtil;
import com.whfc.common.util.Gps;
import com.whfc.common.util.MathUtil;
import com.whfc.emp.entity.AppDeviceCardLog;
import com.whfc.emp.manager.AppEmpDataManager;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Description 生成人员生成演示数据
 * <AUTHOR>
 * @Date 2021-09-07 11:43
 * @Version 1.0
 */
@Component
@ConditionalOnBean(XxlJobConfig.class)
public class DemoDataGenerateJob {

    @Autowired
    private AmqpTemplate amqpTemplate;

    /**
     * 人员设备电量
     */
    private final AtomicInteger empBatteryPower = new AtomicInteger(100);

    /**
     * 健康设备电量
     */
    private final AtomicInteger healthBatteryPower = new AtomicInteger(100);

    @Autowired
    private AppEmpDataManager appEmpDataManager;

    /**
     * 生成人员数据
     */
    @XxlJob("generateEmpData")
    public void generateEmpData() {
        XxlJobHelper.log("开始生成人员数据...");
        String paramStr = XxlJobHelper.getJobParam();
        JSONObject param = null;
        if (StringUtils.isEmpty(paramStr)) {
            XxlJobHelper.handleFail("没有配置参数");
            return;
        }
        param = JSONObject.parseObject(paramStr);
        List<Date> dates = new ArrayList<>();
        if (param.containsKey("time")) {
            Date date = param.getDate("time");
            Date startDate = DateUtil.getDateBegin(date);
            Date enhdDate = DateUtil.getDateEnd(date);
            dates = DateUtil.getDayListOfMinute(startDate, enhdDate, 10);
        } else {
            dates.add(new Date());
        }
        Random random = new Random();
//        List<Integer> list = param.getJSONArray("deviceIds").toJavaList(Integer.class);
        // 弃用风潮自定义device 改用厂商自己的设备序列号
        List<String> snList = param.getJSONArray("snList").toJavaList(String.class);
        for (Date date : dates) {
            if (!isRunTime(date)) {
                XxlJobHelper.log("当前不是运行时间");
                continue;
            }
            for (String sn : snList) {
                CncitUpMsgS cncitUpMsgS = new CncitUpMsgS();
                cncitUpMsgS.setDeviceCode(sn);
                cncitUpMsgS.setTime(DateUtil.addSeconds(date, random.nextInt(60)));
                cncitUpMsgS.setLatFlag("N");
                cncitUpMsgS.setLatWgs84(randomLngLat().getLat());
                cncitUpMsgS.setLngFlag("E");
                cncitUpMsgS.setLngWgs84(randomLngLat().getLng());
                cncitUpMsgS.setSpeed(MathUtil.round(random.nextDouble() * 3, 3));
                cncitUpMsgS.setRotation(random.nextInt(70));
                cncitUpMsgS.setVersion("VER2");
                cncitUpMsgS.setGeoNum(random.nextInt(20));
                cncitUpMsgS.setLevelFactor(MathUtil.round(random.nextDouble(), 2));
                cncitUpMsgS.setRssi(random.nextInt(20));
                cncitUpMsgS.setAlarmValue(0);
                cncitUpMsgS.setAlarmCrash(0);
                cncitUpMsgS.setAlarmSos(0);
                cncitUpMsgS.setAlarmDrop(0);
                cncitUpMsgS.setAlarmDoff(0);
                cncitUpMsgS.setAlarmStill(0);
                cncitUpMsgS.setCardStateValue(8);
                cncitUpMsgS.setPosType(0);
                cncitUpMsgS.setPosState(1);
                cncitUpMsgS.setPosMode(2);
                cncitUpMsgS.setBatterVolt(MathUtil.round(random.nextDouble(), 2) + 3);
                cncitUpMsgS.setBatteryPower(empBatteryPower.get() - random.nextInt(5));
                cncitUpMsgS.setAccelerationX(0);
                cncitUpMsgS.setAccelerationY(0);
                cncitUpMsgS.setAccelerationZ(0);
                try {
                    XxlJobHelper.log("empData:{}", cncitUpMsgS);
                    amqpTemplate.convertAndSend(QueueConst.EMP_HELMET_DATA, JSON.toJSONString(cncitUpMsgS));
                } catch (Exception e) {
                    XxlJobHelper.handleFail("生成设备数据失败. error:" + e.getMessage());
                }
            }
            //电量递减
            empBatteryPower.set(empBatteryPower.get() - 5);
            if (empBatteryPower.get() < 10) {
                empBatteryPower.set(100);
            }
        }
    }

    /**
     * 生成人员健康数据
     */
    @XxlJob("generateHealthData")
    public void generateHealthData() {
        XxlJobHelper.log("开始生成人员健康数据...");
        String paramStr = XxlJobHelper.getJobParam();
        if (StringUtils.isEmpty(paramStr)) {
            XxlJobHelper.handleFail("没有配置参数");
            return;
        }

        JSONObject param = JSONObject.parseObject(paramStr);
        List<Date> dates = new ArrayList<>();
        if (param.containsKey("time")) {
            Date date = param.getDate("time");
            Date startDate = DateUtil.getDateBegin(date);
            Date endDate = DateUtil.getDateEnd(date);
            dates = DateUtil.getDayListOfMinute(startDate, endDate, 10);
        } else {
            dates.add(new Date());
        }

        Random random = new Random();
        List<String> snList = param.getJSONArray("snList").toJavaList(String.class);
        for (Date date : dates) {
            if (!isRunTime(date)) {
                XxlJobHelper.log("当前不是运行时间");
                continue;
            }

            for (String sn : snList) {
                // 设置位置信息
                Gps gps = randomLngLat();
                // 设置电量信息
                Integer batteryPower = healthBatteryPower.get() - random.nextInt(3);
                // 体温 正常范围 36.3-37.2
                double bodyTemp = MathUtil.round(36.3 + random.nextDouble() * 0.9, 1);
                // 心率 正常范围 60-100
                int heartRate = 60 + random.nextInt(41);
                // 血压 正常范围 收缩压90-140/舒张压60-90
                Integer diastolicPressure = (90 + random.nextInt(51));
                Integer systolicPressure = (60 + random.nextInt(31));
                // 血氧 正常范围 95-100
                int bloodOxygen = 95 + random.nextInt(6);
                // 血糖 正常范围 3.9-6.1
                double bloodSugar = MathUtil.round(3.9 + random.nextDouble() * 2.2, 1);

                // 设置健康数据到消息中
                AppDeviceCardLog cardLog = new AppDeviceCardLog();
                cardLog.setDeviceCode(sn);
                cardLog.setPlatform(DevicePlatform.demo.name());
                cardLog.setTime(DateUtil.addSeconds(date, random.nextInt(60)));
                cardLog.setLat(gps.getLat());
                cardLog.setLatWgs84(gps.getLat());
                cardLog.setLng(gps.getLng());
                cardLog.setLngWgs84(gps.getLng());
                cardLog.setBatteryPower(batteryPower);
                cardLog.setBodyTemp(bodyTemp);
                cardLog.setHeartRate(heartRate);
                cardLog.setDiastolicPressure(diastolicPressure);
                cardLog.setSystolicPressure(systolicPressure);
                cardLog.setBloodOxygen(bloodOxygen);
                cardLog.setBloodSugar(bloodSugar);
                appEmpDataManager.addEmpDeviceData(cardLog);
            }

            // 电量递减
            healthBatteryPower.set(healthBatteryPower.get() - 2);
            if (healthBatteryPower.get() < 10) {
                healthBatteryPower.set(100);
            }
        }

        XxlJobHelper.log("人员健康数据生成完成");
    }

    /**
     * 生成随机经纬度
     *
     * @return 经纬度
     */
    private Gps randomLngLat() {
        //经纬度区间
        double minLat = 36.430285D;
        double maxLat = 36.480405D;
        double minLng = 115.913216D;
        double maxLng = 115.921456D;
        BigDecimal db = new BigDecimal(Math.random() * (maxLng - minLng) + minLng);
        double lon = db.setScale(6, BigDecimal.ROUND_HALF_UP).doubleValue();
        db = new BigDecimal(Math.random() * (maxLat - minLat) + minLat);
        double lat = db.setScale(6, BigDecimal.ROUND_HALF_UP).doubleValue();
        return new Gps(lat, lon);
    }

    /**
     * 是否是运行时间
     *
     * @param date 时间
     * @return 是否是运行时间
     */
    private boolean isRunTime(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        if (hour < 8 || hour > 18 || hour == 12) {
            return false;
        }
        return true;
    }
}
