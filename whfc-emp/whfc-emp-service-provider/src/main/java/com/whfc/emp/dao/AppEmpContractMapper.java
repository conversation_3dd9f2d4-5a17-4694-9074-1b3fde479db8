package com.whfc.emp.dao;

import com.whfc.emp.dto.AppEmpContractDTO;
import com.whfc.emp.entity.AppEmpContract;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AppEmpContractMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppEmpContract record);

    int insertSelective(AppEmpContract record);

    AppEmpContract selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppEmpContract record);

    int updateByPrimaryKey(AppEmpContract record);

    /**
     * 根据人员id查找
     *
     * @param empId
     * @return
     */
    List<AppEmpContractDTO> selectByEmpId(@Param("empId") Integer empId);

    /**
     * 根据合同编号查找
     *
     * @param deptId     项目ID
     * @param contractNo 合同编号
     * @return
     */
    AppEmpContract selectByContractNo(@Param("deptId") Integer deptId, @Param("contractNo") String contractNo);

    /**
     * 根据id逻辑删除
     *
     * @param id
     */
    void deleteLogicById(@Param("id") Integer id);
}