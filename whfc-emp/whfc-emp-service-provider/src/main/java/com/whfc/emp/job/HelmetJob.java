package com.whfc.emp.job;

import com.whfc.XxlJobConfig;
import com.whfc.common.enums.DevicePlatform;
import com.whfc.common.result.PageData;
import com.whfc.common.third.baibutonget.BaibutongetApi;
import com.whfc.common.third.baibutonget.entity.*;
import com.whfc.common.util.DateUtil;
import com.whfc.emp.dao.AppEmpDeviceMapper;
import com.whfc.emp.dao.AppHelmetConfigMapper;
import com.whfc.emp.dao.AppHelmetFileMapper;
import com.whfc.emp.entity.AppDeviceCardLog;
import com.whfc.emp.entity.AppEmpDevice;
import com.whfc.emp.entity.AppHelmetConfig;
import com.whfc.emp.entity.AppHelmetFile;
import com.whfc.emp.manager.AppEmpDataManager;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 智能安全帽-定时任务
 */
@Component
@ConditionalOnBean(XxlJobConfig.class)
public class HelmetJob {

    private static final Logger logger = LoggerFactory.getLogger(HelmetJob.class);

    @Autowired
    private AppHelmetConfigMapper appHelmetConfigMapper;

    @Autowired
    private AppHelmetFileMapper appHelmetFileMapper;

    @Autowired
    private AppEmpDataManager appEmpDataManager;

    @Autowired
    private AppEmpDeviceMapper appEmpDeviceMapper;

    /**
     * 安全帽数据刷新
     */
    @XxlJob("baibutonget-helmet-data")
    public void baibutongetRefreshData() {
        Date date = new Date();
        Date start = DateUtil.getDateBegin(date);
        Date end = date;
        List<AppHelmetConfig> configList = appHelmetConfigMapper.selectByPlatform(DevicePlatform.baibutonget.name());
        logger.info("安全帽配置:{}", configList.size());
        for (AppHelmetConfig config : configList) {
            try {
                Integer deptId = config.getDeptId();
                String host = config.getHost();
                String user = config.getUser();
                String pass = config.getPass();
                String adminId = config.getExt1();
                BaibutongetApi api = new BaibutongetApi(host, user, pass, adminId);

                //获取token
                String pkey = api.getPkey();
                Token tokenObj = api.getToken(pkey);
                if (tokenObj == null) {
                    logger.error("获取安全帽token失败,deptId={},user={}", deptId, config.getUser());
                }
                String token = tokenObj.getToken();

                //获取安全帽组列表
                List<Group> groups = api.getGroupMembers(token);
                for (Group group : groups) {
                    List<Member> members = group.getChildren();
                    logger.info("安全帽组:{},成员:{}", group.getG_name(), members.size());
                    setHelmetData(members, api, token, start, end, date, deptId);
                }

            } catch (Exception ex) {
                logger.error("刷新安全帽数据失败", ex);
                XxlJobHelper.handleFail("刷新安全帽数据失败");
            }
        }
    }

    /**
     * 设置安全帽数据
     *
     * @param members
     * @param api
     * @param token
     * @param start
     * @param end
     * @param date
     * @param deptId
     */
    private void setHelmetData(List<Member> members, BaibutongetApi api, String token, Date start, Date end, Date date, Integer deptId) {
        if (CollectionUtils.isEmpty(members)) {
            return;
        }

        for (Member member : members) {
            logger.info("安全帽:{}-{}", member.getDevice_id(), member.getUser_id());

            //查询绑定人员
            AppEmpDevice appEmpDevice = appEmpDeviceMapper.selectByPlatformAndSn(DevicePlatform.baibutonget.name(), member.getDevice_id());
            if (appEmpDevice == null || 0 == appEmpDevice.getBindFlag()) {
                logger.info("未绑定人员,sn={}", member.getDevice_id());
                continue;
            }

            Integer empId = appEmpDevice.getEmpId();
            Integer empDeviceId = appEmpDevice.getId();
            String platform = appEmpDevice.getPlatform();
            String sn = appEmpDevice.getSn();

            //轨迹
            List<Path> paths = api.getPath(token, member.getUser_id(), start, end);
            List<AppDeviceCardLog> logList = paths.stream().map(path ->
                            toLog(empId, empDeviceId, platform, sn, path))
                    .collect(Collectors.toList());
            logger.info("安全帽:{},定位数量:{}", member.getUser_id(), paths.size());
            appEmpDataManager.addEmpDeviceData(empId, date, logList);

            //图片
            List<Image> images = new ArrayList<>();
            int pageNum = 1;
            int pages = 1;
            do {
                PageData<Image> pageData = api.getImage(token, member.getUser_id(), date, pageNum);
                images.addAll(pageData.getList());
                pages = pageData.getPages();
                pageNum++;
            } while (pageNum <= pages);
            Date lastTime = appHelmetFileMapper.selectLastFileTime(empId);
            List<AppHelmetFile> fileList = new ArrayList<>();
            for (Image image : images) {
                if (image.getTime() * 1000 > lastTime.getTime()) {
                    AppHelmetFile file = toFile(empId, deptId, image);
                    fileList.add(file);
                }
            }
            logger.info("安全帽:{},图片数量:{},新增数量:{}", member.getUser_id(), images.size(), fileList.size());
            if (!fileList.isEmpty()) {
                appHelmetFileMapper.batchInsert(fileList);
            }
        }
    }

    private AppDeviceCardLog toLog(Integer empId, Integer empDeviceId, String platform, String sn, Path path) {
        AppDeviceCardLog log = new AppDeviceCardLog();
        log.setEmpId(empId);
        log.setEmpDeviceId(empDeviceId);
        log.setPlatform(platform);
        log.setDeviceCode(sn);
        log.setTime(new Date(path.getTime() * 1000));
        log.setLng(path.getY_point());
        log.setLat(path.getX_point());
        log.setLngWgs84(path.getY_point());
        log.setLatWgs84(path.getX_point());
        return log;
    }

    private AppHelmetFile toFile(Integer empId, Integer deptId, Image image) {
        AppHelmetFile file = new AppHelmetFile();
        file.setDeptId(deptId);
        file.setEmpId(empId);
        file.setTime(new Date(image.getTime() * 1000));
        file.setFileType(1);
        file.setFileUrl(image.getImage_url());
        file.setLng(image.getY_point());
        file.setLat(image.getX_point());
        return file;
    }
}
