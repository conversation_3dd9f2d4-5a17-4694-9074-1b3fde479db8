package com.whfc.emp.factory.impl;

import com.whfc.common.constant.QueueConst;
import com.whfc.common.enums.I18nErrorCode;
import com.whfc.common.exception.BizException;
import com.whfc.common.face.kyy.KyyCmd;
import com.whfc.common.face.kyy.cmd.DeletePersons;
import com.whfc.common.face.kyy.cmd.UploadPerson;
import com.whfc.common.file.FileHandler;
import com.whfc.common.geometry.Point;
import com.whfc.common.result.ResultEnum;
import com.whfc.common.util.Base64Util;
import com.whfc.common.util.JSONUtil;
import com.whfc.emp.dao.AppEmpMapper;
import com.whfc.emp.dao.AppFaceGateMapper;
import com.whfc.emp.dao.AppFaceGatePersonMapper;
import com.whfc.emp.entity.AppEmp;
import com.whfc.emp.entity.AppFaceGate;
import com.whfc.emp.entity.AppFaceGateConfig;
import com.whfc.emp.factory.FaceGateManager;
import com.whfc.emp.param.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description: 快优易闸机
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/10/29 8:47
 */
@Component(value = "kyyFaceGateManagerImpl")
public class KyyFaceGateManagerImpl implements FaceGateManager {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private AppFaceGateMapper appFaceGateMapper;

    @Autowired
    private AppFaceGatePersonMapper appFaceGatePersonMapper;

    @Autowired
    private AppEmpMapper appEmpMapper;

    @Autowired
    private AmqpTemplate amqpTemplate;

    @Autowired
    private FileHandler fileHandler;

    @Override
    public void add(AppFaceGateAddParam request, AppFaceGateConfig config) {

        Integer deptId = request.getDeptId();
        String name = request.getName();
        String deviceKey = request.getDeviceKey();
        Point point = request.getPoint();

        AppFaceGate record = new AppFaceGate();
        record.setDeviceKey(deviceKey);
        record.setDeptId(deptId);
        record.setName(name);
        record.setDirection(request.getDirection());
        record.setPlatform(request.getPlatform());
        record.setModel(request.getModel());
        // 增加闸机位置信息
        if (point != null) {
            Double lat = point.getLat();
            Double lng = point.getLng();
            record.setLng(lng);
            record.setLat(lat);
        }
        appFaceGateMapper.insertSelective(record);

    }

    @Override
    public void del(Integer faceGateId, AppFaceGateConfig config) {
        Integer count = appFaceGatePersonMapper.countByFaceGateId(faceGateId);
        if (count > 0) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_031.getCode());
        }
        appFaceGateMapper.deleteLogicById(faceGateId);
    }

    @Override
    public String faceGateGrantEmdAdd(FaceGateGrantEmdAddParam request, AppFaceGateConfig config) {

        //构件消息
        String deviceKey = request.getDeviceKey();
        Integer empId = request.getEmpId();
        Integer personId = request.getPersonId();
        AppEmp appEmp = appEmpMapper.selectByPrimaryKey(empId);

        // 获取EmpCode
        String empCode = appEmp.getEmpCode();
        if (StringUtils.isBlank(empCode.trim())) {
            empCode = String.valueOf(empId);
            appEmp.setEmpCode(empCode);
            appEmpMapper.updateByPrimaryKeySelective(appEmp);
        }

        String empImg = this.getImageBase64(appEmp.getAvatar());

        UploadPerson uploadPerson = new UploadPerson();
        uploadPerson.setCmd(KyyCmd.UPLOAD_PERSON);
        uploadPerson.setId(empCode);
        uploadPerson.setName(appEmp.getEmpName());
        uploadPerson.setReg_image(empImg);
        uploadPerson.setCustomer_text(empCode);
        uploadPerson.setDevice_sn(deviceKey);

        // 发送消息
        amqpTemplate.convertAndSend(QueueConst.KYY_DOWN_MSG, JSONUtil.toString(uploadPerson));

        return null;
    }

    @Override
    public String faceGateGrantEmdImgAdd(FaceGateGrantEmdImgAddParam request, AppFaceGateConfig config) {
        return null;
    }

    @Override
    public String faceGateGrantEmdAuth(FaceGateGrantEmdAuthParam request, AppFaceGateConfig config) {
        return null;
    }

    @Override
    public void faceGateBatchGrantEmpAuth(AppFaceGateGrantEmpParam request, AppFaceGateConfig config) {

    }

    @Override
    public void faceGateRevokeEmp(AppFaceGateGrantEmpParam request, AppFaceGateConfig config) {
        List<Integer> empIdList = request.getEmpIdList();
        if (empIdList == null || empIdList.isEmpty()) {
            return;
        }
        AppFaceGate appFaceGate = appFaceGateMapper.selectByPrimaryKey(request.getFaceGateId());
        if (appFaceGate == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.EMP_BE_003.getCode());
        }
        String deviceKey = appFaceGate.getDeviceKey();
        // 发送批量同步人员的MQ消息
        for (Integer empId : empIdList) {
            DeletePersons deletePersons = new DeletePersons();
            deletePersons.setCmd(KyyCmd.DELETE_PERSONS);
            deletePersons.setId(String.valueOf(empId));
            deletePersons.setFlag(-1);
            deletePersons.setDevice_sn(deviceKey);
            amqpTemplate.convertAndSend(QueueConst.KYY_DOWN_MSG, JSONUtil.toString(deletePersons));

            appFaceGatePersonMapper.delByDeviceKeyAndEmpId(deviceKey, empId);
        }
    }

    @Override
    public String deviceAuthorizationPerson(String deviceKey, String name, String imgUrl, AppFaceGateConfig config) {
        return null;
    }

    @Override
    public String deviceAuthorizationCancel(String deviceKey, String personGuid, AppFaceGateConfig config) {
        return null;
    }

    @Override
    public void openDoor(String deviceKey, AppFaceGateConfig config) {

    }

    @Override
    public void sync(Integer faceGateId, AppFaceGateConfig config) {

    }

    @Override
    public String getToken(AppFaceGateConfig config) {
        return null;
    }

    /**
     * 获取图片base64(照片压缩)
     *
     * @param url
     * @return
     */
    private String getImageBase64(String url) {
        logger.info("获取图片base64,{}", url);
        url = fileHandler.getDownloadUrl(url);
        logger.info("获取图片base64,{}", url);
        byte[] imageData = Base64Util.getUrlImageData(url);
        //logger.debug("获取图片base64,{},压缩前:{}", url, imageData.length);
        //byte[] compressed = ImageUtil.compressPicForScale(imageData, IMAGE_SIZE);
        //logger.debug("获取图片base64,{},压缩后:{}", url, compressed.length);
        return Base64Util.encodeToString(imageData);
    }
}
