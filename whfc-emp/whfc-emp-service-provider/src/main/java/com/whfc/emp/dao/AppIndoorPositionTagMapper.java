package com.whfc.emp.dao;

import com.whfc.emp.dto.AppEmpWorkRoleNumDTO;
import com.whfc.emp.dto.AppIndoorPositionStatDTO;
import com.whfc.emp.dto.AppIndoorPositionTagDTO;
import com.whfc.emp.entity.AppIndoorPositionTag;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface AppIndoorPositionTagMapper {

    int insertSelective(AppIndoorPositionTag record);

    AppIndoorPositionTag selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppIndoorPositionTag record);

    /**
     * 获取标签列表
     *
     * @param deptId
     * @param keyword
     * @return
     */
    List<AppIndoorPositionTagDTO> selectByDeptId(@Param("deptId") Integer deptId,
                                                 @Param("keyword") String keyword,
                                                 @Param("bindFlag") Integer bindFlag,
                                                 @Param("bindType") Integer bindType);

    /**
     * 获取定位列表
     *
     * @param deptId
     * @param time
     * @return
     */
    List<AppIndoorPositionTagDTO> selectPositionList(@Param("deptId") Integer deptId,
                                                     @Param("time") Date time,
                                                     @Param("keyword") String keyword,
                                                     @Param("bindFlag") Integer bindFlag,
                                                     @Param("bindType") Integer bindType);

    /**
     * 获取定位统计
     *
     * @param deptId
     * @param time
     * @return
     */
    List<AppIndoorPositionStatDTO> selectPositionStat(@Param("deptId") Integer deptId, @Param("time") Date time);

    /**
     * 统计地图定位统计
     *
     * @param deptId
     * @param mapId
     * @param time
     * @return
     */
    AppIndoorPositionStatDTO selectMapStat(@Param("deptId") Integer deptId, @Param("mapId") Integer mapId, @Param("time") Date time);

    /**
     * 统计地图定位统计
     *
     * @param deptId
     * @param mapId
     * @param time
     * @return
     */
    List<AppEmpWorkRoleNumDTO> selectMapStatByWorkRole(@Param("deptId") Integer deptId, @Param("mapId") Integer mapId, @Param("time") Date time);

    /**
     * 统计最大距离
     *
     * @param deptId
     * @return
     */
    AppIndoorPositionStatDTO selectMaxDistance(@Param("deptId") Integer deptId);

    /**
     * 查找标签
     *
     * @param guid
     * @return
     */
    AppIndoorPositionTag selectByGuid(@Param("guid") String guid);

    /**
     * 查找标签
     *
     * @param deptId
     * @param guid
     * @return
     */
    AppIndoorPositionTag selectByDeptIdAndGuid(@Param("deptId") Integer deptId, @Param("guid") String guid);

    /**
     * 人员标签绑定统计
     *
     * @param deptId
     * @param empId
     * @return
     */
    AppIndoorPositionTag selectByDeptIdAndEmpId(@Param("deptId") Integer deptId, @Param("empId") Integer empId);

    /**
     * 根据项目ID和设备编码查找标签
     *
     * @param deptId
     * @param deviceCode
     * @return
     */
    AppIndoorPositionTag selectByDeptIdAndDeviceCode(@Param("deptId") Integer deptId,
                                                     @Param("deviceCode") String deviceCode);

    /**
     * 清理标签对应人员信息
     *
     * @param tagId
     * @return
     */
    int unbindTag(@Param("tagId") Integer tagId);

    /**
     * 逻辑删除标签
     *
     * @param tagId
     * @return
     */
    int logicDeleteById(@Param("tagId") Integer tagId);


}