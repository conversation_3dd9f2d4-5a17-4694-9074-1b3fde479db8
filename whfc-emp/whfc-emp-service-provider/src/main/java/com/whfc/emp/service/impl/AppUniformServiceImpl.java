package com.whfc.emp.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.data.ImageData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.whfc.common.enums.I18nErrorCode;
import com.whfc.common.exception.BizException;
import com.whfc.common.file.FileHandler;
import com.whfc.common.file.FileUploadUtil;
import com.whfc.common.file.properties.FileExpirationRules;
import com.whfc.common.result.PageData;
import com.whfc.common.result.ResultEnum;
import com.whfc.common.util.*;
import com.whfc.emp.dao.AppEmpMapper;
import com.whfc.emp.dao.AppUniformReceiveDetailMapper;
import com.whfc.emp.dao.AppUniformReceiveMapper;
import com.whfc.emp.dto.uniform.AppUniformReceiveDTO;
import com.whfc.emp.dto.uniform.AppUniformReceiveDetailDTO;
import com.whfc.emp.dto.uniform.AppUniformReceiveExportDTO;
import com.whfc.emp.dto.uniform.AppUniformReceiveExtDTO;
import com.whfc.emp.entity.AppUniformReceive;
import com.whfc.emp.param.uniform.AppUniformReceiveAdd;
import com.whfc.emp.param.uniform.AppUniformReceiveEdit;
import com.whfc.emp.param.uniform.AppUniformReceiveQuery;
import com.whfc.emp.service.AppUniformService;
import com.whfc.entity.dto.OssPathDTO;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.FileInputStream;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/1/25 15:37
 */
@DubboService(interfaceClass = AppUniformService.class, version = "1.0.0", timeout = 180 * 1000)
public class AppUniformServiceImpl implements AppUniformService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    private static Map<String, String> FIELD_MAP = new HashMap<>(16);

    static {
        FIELD_MAP.put("Short sleeve shirt", "amount1");
        FIELD_MAP.put("Long sleeve shirt", "amount2");
        FIELD_MAP.put("Safety helmet", "amount3");
        FIELD_MAP.put("Ear protector", "amount4");
        FIELD_MAP.put("Respirator / Dust mask", "amount5");
        FIELD_MAP.put("Safety harness", "amount6");
        FIELD_MAP.put("Eye protector", "amount7");
        FIELD_MAP.put("Safety boots", "amount8");
        FIELD_MAP.put("Safety rain boots", "amount9");
        FIELD_MAP.put("Reflective belt / vest", "amount10");
    }

    @Autowired
    private AppEmpMapper appEmpMapper;

    @Autowired
    private AppUniformReceiveMapper appUniformReceiveMapper;

    @Autowired
    private AppUniformReceiveDetailMapper appUniformReceiveDetailMapper;

    @Autowired
    private FileUploadUtil fileUploadUtil;

    @Autowired
    private FileHandler fileHandler;

    @Override
    public PageData<AppUniformReceiveDTO> uniformReceiveList(AppUniformReceiveQuery query, Integer pageNum, Integer pageSize) {

        Integer deptId = query.getDeptId();
        String corpName = query.getCorpName();
        String empName = query.getEmpName();
        Date startTime = query.getStartTime();
        Date endTime = query.getEndTime();
        String itemName = query.getItemName();

        if (startTime != null) {
            startTime = DateUtil.getDateBegin(startTime);
        }
        if (endTime != null) {
            endTime = DateUtil.getDateEnd(endTime);
        }

        PageHelper.startPage(pageNum, pageSize);
        List<AppUniformReceiveDTO> list = appUniformReceiveMapper.selectList(deptId, corpName, empName, itemName, startTime, endTime);
        PageHelper.clearPage();

        List<Integer> receiveIdList = list.stream().map(AppUniformReceiveDTO::getReceiveId).collect(Collectors.toList());
        List<AppUniformReceiveDetailDTO> detailList = Collections.emptyList();
        if (receiveIdList.size() > 0) {
            detailList = appUniformReceiveDetailMapper.selectByReceiveIds(receiveIdList);
        }
        Map<Integer, List<AppUniformReceiveDetailDTO>> detailMap = CollectionUtil.groupBy(detailList, AppUniformReceiveDetailDTO::getReceiveId);

        for (AppUniformReceiveDTO dto : list) {
            //图片
            AppUniformReceiveExtDTO extDTO = JSONUtil.parseObject(dto.getExt(), AppUniformReceiveExtDTO.class);
            dto.setImgList(extDTO != null && extDTO.getImgList() != null ? extDTO.getImgList() : Collections.emptyList());
            //物品信息
            dto.setDetailList(detailMap.get(dto.getReceiveId()));
        }

        return PageUtil.pageData(PageInfo.of(list));
    }

    @Override
    public List<AppUniformReceiveDTO> uniformReceiveList(AppUniformReceiveQuery query) {
        Integer deptId = query.getDeptId();
        String corpName = query.getCorpName();
        String empName = query.getEmpName();
        Date startTime = query.getStartTime();
        Date endTime = query.getEndTime();
        String itemName = query.getItemName();

        List<AppUniformReceiveDTO> list = appUniformReceiveMapper.selectList(deptId, corpName, empName, itemName, startTime, endTime);

        List<Integer> receiveIdList = list.stream().map(AppUniformReceiveDTO::getReceiveId).collect(Collectors.toList());
        List<AppUniformReceiveDetailDTO> detailList = Collections.emptyList();
        if (receiveIdList.size() > 0) {
            detailList = appUniformReceiveDetailMapper.selectByReceiveIds(receiveIdList);
        }
        Map<Integer, List<AppUniformReceiveDetailDTO>> detailMap = CollectionUtil.groupBy(detailList, AppUniformReceiveDetailDTO::getReceiveId);

        for (AppUniformReceiveDTO dto : list) {
            //图片
            AppUniformReceiveExtDTO extDTO = JSONUtil.parseObject(dto.getExt(), AppUniformReceiveExtDTO.class);
            dto.setImgList(extDTO != null && extDTO.getImgList() != null ? extDTO.getImgList() : Collections.emptyList());
            //物品信息
            dto.setDetailList(detailMap.get(dto.getReceiveId()));
        }

        return list;
    }

    @Override
    public AppUniformReceiveDTO uniformReceiveDetail(String guid) {

        AppUniformReceiveDTO dto = appUniformReceiveMapper.selectDtoByGuid(guid);
        if (dto == null) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), I18nErrorCode.EMP_BE_071.getCode());
        }

        //图片
        AppUniformReceiveExtDTO extDTO = JSONUtil.parseObject(dto.getExt(), AppUniformReceiveExtDTO.class);
        dto.setImgList(extDTO != null && extDTO.getImgList() != null ? extDTO.getImgList() : Collections.emptyList());

        //物品信息
        List<AppUniformReceiveDetailDTO> detaiList = appUniformReceiveDetailMapper.selectByReceiveId(dto.getReceiveId());
        dto.setDetailList(detaiList);


        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void uniformReceiveAdd(AppUniformReceiveAdd param) {

        Integer deptId = param.getDeptId();
        Integer empId = param.getEmpId();
        String empName = param.getEmpName();
        List<String> imgList = param.getImgList();
        List<AppUniformReceiveDetailDTO> detailList = param.getDetailList();

        AppUniformReceiveExtDTO extDTO = new AppUniformReceiveExtDTO();
        extDTO.setImgList(imgList);
        String ext = JSONUtil.toString(extDTO);

        //领用信息
        AppUniformReceive receive = new AppUniformReceive();
        receive.setDeptId(deptId);
        receive.setGuid(RandomUtil.getGuid());
        receive.setEmpId(empId);
        receive.setEmpName(empName);
        receive.setCompany(param.getCompany());
        receive.setDate(param.getDate());
        receive.setExt(ext);
        receive.setCreateUserId(param.getCreateUserId());
        receive.setCreateUserName(param.getCreateUserName());
        appUniformReceiveMapper.insertSelective(receive);
        Integer receiveId = receive.getId();

        //物资信息
        if (detailList != null && detailList.size() > 0) {
            for (AppUniformReceiveDetailDTO detail : detailList) {
                detail.setDeptId(deptId);
                detail.setReceiveId(receiveId);
            }
            appUniformReceiveDetailMapper.batchInsert(detailList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void uniformReceiveEdit(AppUniformReceiveEdit param) {
        String guid = param.getGuid();
        Integer empId = param.getEmpId();
        String empName = param.getEmpName();
        List<String> imgList = param.getImgList();
        List<AppUniformReceiveDetailDTO> detailList = param.getDetailList();

        AppUniformReceiveExtDTO extDTO = new AppUniformReceiveExtDTO();
        extDTO.setImgList(imgList);
        String ext = JSONUtil.toString(extDTO);

        AppUniformReceive receive = appUniformReceiveMapper.selectByGuid(guid);
        if (receive == null) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), I18nErrorCode.EMP_BE_071.getCode());
        }
        Integer deptId = receive.getDeptId();
        Integer receiveId = receive.getId();
        receive.setEmpId(empId);
        receive.setEmpName(empName);
        receive.setCompany(param.getCompany());
        receive.setDate(param.getDate());
        receive.setExt(ext);
        appUniformReceiveMapper.updateByPrimaryKeySelective(receive);

        //物资信息
        if (detailList != null && detailList.size() > 0) {
            for (AppUniformReceiveDetailDTO detail : detailList) {
                detail.setDeptId(deptId);
                detail.setReceiveId(receiveId);
            }
            appUniformReceiveDetailMapper.logicDeleteByReceiveId(receiveId);
            appUniformReceiveDetailMapper.batchInsert(detailList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void uniformReceiveDel(String guid) {

        AppUniformReceive receive = appUniformReceiveMapper.selectByGuid(guid);
        if (receive == null) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), I18nErrorCode.EMP_BE_071.getCode());
        }
        Integer receiveId = receive.getId();
        appUniformReceiveMapper.logicDeleteById(receiveId);
        appUniformReceiveDetailMapper.logicDeleteByReceiveId(receiveId);
    }

    @Override
    public OssPathDTO uniformReceiveExportExcel(AppUniformReceiveQuery query) {

        Integer deptId = query.getDeptId();
        String corpName = query.getCorpName();
        String empName = query.getEmpName();
        Date startTime = query.getStartTime();
        Date endTime = query.getEndTime();
        String itemName = query.getItemName();
        List<Integer> receiveIds = query.getReceiveIds();

        if (startTime != null) {
            startTime = DateUtil.getDateBegin(startTime);
        }
        if (endTime != null) {
            endTime = DateUtil.getDateEnd(endTime);
        }

        //查询工服领用数据
        List<AppUniformReceiveDTO> list = null;
        if (receiveIds != null && receiveIds.size() > 0) {
            list = appUniformReceiveMapper.selectByReceiveIds(deptId, receiveIds);
        } else {
            list = appUniformReceiveMapper.selectList(deptId, corpName, empName, itemName, startTime, endTime);
        }
        List<AppUniformReceiveDetailDTO> detailList = Collections.emptyList();
        List<Integer> receiveIdList = list.stream().map(AppUniformReceiveDTO::getReceiveId).collect(Collectors.toList());
        if (receiveIdList.size() > 0) {
            detailList = appUniformReceiveDetailMapper.selectByReceiveIds(receiveIdList);
        }
        Map<Integer, List<AppUniformReceiveDetailDTO>> detailMap = CollectionUtil.groupBy(detailList, AppUniformReceiveDetailDTO::getReceiveId);

        //构建导出数据
        List<AppUniformReceiveExportDTO> exportList = new ArrayList<>(list.size());
        for (AppUniformReceiveDTO dto : list) {
            AppUniformReceiveExportDTO exportDTO = new AppUniformReceiveExportDTO();
            exportDTO.setEmpName(dto.getEmpName());
            exportDTO.setDate(DateUtil.formatDate(dto.getDate(), "yyyy-MM-dd"));
            exportDTO.setCorpName(StringUtils.isNotEmpty(dto.getCompany()) ? dto.getCompany() : dto.getCorpName());

            //工衣数据行列转换
            try {
                List<AppUniformReceiveDetailDTO> itemList = detailMap.get(dto.getReceiveId());
                if (itemList != null && itemList.size() > 0) {
                    for (AppUniformReceiveDetailDTO item : itemList) {
                        //数量转换
                        String matName = item.getItemName();
                        Integer amount = item.getItemAmount();
                        if (FIELD_MAP.containsKey(matName)) {
                            String fieldName = FIELD_MAP.get(matName);
                            Field field = exportDTO.getClass().getDeclaredField(fieldName);
                            field.setAccessible(true);
                            field.set(exportDTO, amount);
                        }
                    }
                }
            } catch (Exception ex) {
                logger.error("数据处理失败", ex);
            }

            //领用图片
            AppUniformReceiveExtDTO extDTO = JSONUtil.parseObject(dto.getExt(), AppUniformReceiveExtDTO.class);
            List<String> imgList = extDTO != null && extDTO.getImgList() != null ? extDTO.getImgList() : Collections.emptyList();
            if (imgList.size() > 0) {
                exportDTO.setImg1(getImageCellData(imgList.get(0), 0, 0, 0, 0));
            }
            if (imgList.size() > 1) {
                exportDTO.setImg2(getImageCellData(imgList.get(1), 0, 0, 0, 0));
            }
            if (imgList.size() > 2) {
                exportDTO.setImg3(getImageCellData(imgList.get(2), 0, 0, 0, 0));
            }

            exportList.add(exportDTO);
        }

        //导出数据
        OssPathDTO ossPathDTO = new OssPathDTO();
        try {
            String fileName = RandomUtil.getGuid();
            File destFile = File.createTempFile(fileName, ".xls");
            ClassPathResource resource = new ClassPathResource("templates/uniform-template-zt.xls");
            ExcelWriter excelWriter = EasyExcel.write(destFile).withTemplate(resource.getInputStream()).build();
            WriteSheet writeSheet = EasyExcel.writerSheet().build();

            Map<String, Object> map = new HashMap<>(16);
            excelWriter.fill(map, writeSheet);

            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.FALSE).build();
            excelWriter.fill(exportList, fillConfig, writeSheet);

            excelWriter.finish();

            //上传oss
            String ossKey = "emp/uniform/temp/" + fileName + ".xls";
            String upload = fileUploadUtil.upload(ossKey, new FileInputStream(destFile), FileExpirationRules.DAYS_7);
            ossPathDTO.setPath(upload);
        } catch (Exception e) {
            logger.error("导出excel失败", e);
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.DEFAULT_EXPORT_FILE_FAILED.getCode());
        }
        return ossPathDTO;
    }

    @Override
    public List<String> uniformItemList(Integer deptId) {
        List<String> itemList = appUniformReceiveDetailMapper.selectItemList(deptId);

        Set<String> set = new TreeSet<>();
        set.addAll(FIELD_MAP.keySet());
        set.addAll(itemList);
        return new ArrayList<>(set);
    }

    private WriteCellData<Void> getImageCellData(String url, Integer firstRow, Integer lastRow, Integer firstCol, Integer lastCol) {
        String downloadUrl = fileHandler.getDownloadUrl(url);
        ImageData imageData = new ImageData();
        imageData.setImageType(ImageData.ImageType.PICTURE_TYPE_PNG);
        imageData.setImage(ImageUtil.getFileBytes(downloadUrl));
        imageData.setRelativeFirstRowIndex(firstRow);
        imageData.setRelativeLastRowIndex(lastRow);
        imageData.setRelativeFirstColumnIndex(firstCol);
        imageData.setRelativeLastColumnIndex(lastCol);

        WriteCellStyle style = new WriteCellStyle();
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setHorizontalAlignment(HorizontalAlignment.CENTER);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);

        WriteCellData<Void> cellData = new WriteCellData<>();
        cellData.setImageDataList(Arrays.asList(imageData));
        cellData.setWriteCellStyle(style);
        return cellData;
    }
}
