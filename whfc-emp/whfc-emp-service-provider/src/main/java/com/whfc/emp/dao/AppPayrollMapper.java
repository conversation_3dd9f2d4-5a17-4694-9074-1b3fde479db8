package com.whfc.emp.dao;

import com.whfc.emp.dto.AppPayrollDTO;
import com.whfc.emp.entity.AppPayroll;
import com.whfc.emp.param.AppPayrollListParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: hw
 * @date: 2021-10-21 11:23
 * @description: //todo
 */
public interface AppPayrollMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppPayroll record);

    int insertSelective(AppPayroll record);

    AppPayroll selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppPayroll record);

    int updateByPrimaryKey(AppPayroll record);

    /**
     * 查找工资发放记录
     *
     * @param request
     * @return
     */
    List<AppPayrollDTO> selectByParam(AppPayrollListParam request);

    void deleteLogicById(@Param("payrollId") Integer payrollId);

    /**
     * 提交工资发放
     *
     * @param payrollId
     */
    void updateState(@Param("payrollId") Integer payrollId);

    /**
     * 查找该月份是否已经有工资记录
     *
     * @param deptId
     * @param groupId
     * @param year
     * @param month
     * @return
     */
    Integer countByDeptIdAndCorpId(@Param("deptId") Integer deptId,
                                   @Param("groupId") Integer groupId,
                                   @Param("year") Integer year,
                                   @Param("month") Integer month);
}