package com.whfc.emp.third.impl;

import com.whfc.emp.entity.*;
import com.whfc.emp.param.FaceGateRecordParam;
import com.whfc.emp.third.EmpThirdSync;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: 人员信息-第三方同步-湖南实名制
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/6/30 14:44
 */
@Service
public class EmpThirdSyncBase implements EmpThirdSync {

    @Override
    public void syncEmp(AppEmp emp, AppSync config) {

    }

    @Override
    public void syncEmp(List<AppEmp> empList, AppSync config) {

    }

    @Override
    public void syncEmpContract(AppEmp emp, AppEmpContract contract, AppSync config) {

    }

    @Override
    public void syncEmpTrain(AppTrain train, AppSync config) {

    }

    @Override
    public void syncEmpPayroll(AppPayroll payroll, AppSync config) {

    }

    @Override
    public void syncEmpAttend(FaceGateRecordParam param, AppSync config) {

    }

    @Override
    public void syncEmpIndoorPosition(AppIndoorPositionTag tag, AppSync config) {

    }

    @Override
    public void syncEmpCert(AppEmpCert cert, AppSync config) {

    }
}
