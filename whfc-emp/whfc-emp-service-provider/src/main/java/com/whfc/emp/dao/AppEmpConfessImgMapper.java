package com.whfc.emp.dao;

import com.whfc.emp.entity.AppEmpConfessImg;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/11 11:34
 */
@Repository
public interface AppEmpConfessImgMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppEmpConfessImg record);

    int insertSelective(AppEmpConfessImg record);

    AppEmpConfessImg selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppEmpConfessImg record);

    int updateByPrimaryKey(AppEmpConfessImg record);

    /**
     * 逻辑删除安全交底图片
     *
     * @param confessId 安全交底ID
     */
    void logicDelByConfessId(@Param("confessId") Integer confessId);

    /**
     * 获取安全交底图片
     * @param confessId  安全交底ID
     */
    List<String> selectImgUrlByConfessId(@Param("confessId") Integer confessId);

}