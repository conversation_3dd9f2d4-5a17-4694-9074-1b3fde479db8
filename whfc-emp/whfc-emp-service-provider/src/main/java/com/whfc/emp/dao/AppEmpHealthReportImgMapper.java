package com.whfc.emp.dao;

import com.whfc.emp.entity.AppEmpHealthReportImg;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-09-02 11:27
 */
@Repository
public interface AppEmpHealthReportImgMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppEmpHealthReportImg record);

    int insertSelective(AppEmpHealthReportImg record);

    AppEmpHealthReportImg selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppEmpHealthReportImg record);

    int updateByPrimaryKey(AppEmpHealthReportImg record);

    /**
     * 获取图片列表
     *
     * @param healthReportId 防疫信息ID
     * @return 图片列表
     */
    List<String> selectImgList(@Param("healthReportId") Integer healthReportId);

    /**
     * 删除防疫图片
     *
     * @param healthReportId 防疫信息ID
     */
    void logDel(@Param("healthReportId") Integer healthReportId);
}