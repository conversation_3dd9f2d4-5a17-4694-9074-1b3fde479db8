package com.whfc.emp.dao;

import com.whfc.emp.dto.uniform.AppUniformReceiveDTO;
import com.whfc.emp.entity.AppUniformReceive;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface AppUniformReceiveMapper {
    int deleteByPrimaryKey(Integer id);

    int insertSelective(AppUniformReceive record);

    AppUniformReceive selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppUniformReceive record);

    /**
     * 查询工服领用
     *
     * @param deptId
     * @param empName
     * @param itemName
     * @param startTime
     * @param endTime
     * @return
     */
    List<AppUniformReceiveDTO> selectList(@Param("deptId") Integer deptId,
                                          @Param("corpName") String corpName,
                                          @Param("empName") String empName,
                                          @Param("itemName") String itemName,
                                          @Param("startTime") Date startTime,
                                          @Param("endTime") Date endTime);

    /**
     * 根据领用ID批量查询
     *
     * @param deptId
     * @param receiveIds
     * @return
     */
    List<AppUniformReceiveDTO> selectByReceiveIds(@Param("deptId") Integer deptId,@Param("receiveIds") List<Integer> receiveIds);

    /**
     * 根据guid查询工服领用
     *
     * @param guid
     * @return
     */
    AppUniformReceiveDTO selectDtoByGuid(@Param("guid") String guid);

    /**
     * 根据guid查询工服领用
     *
     * @param guid
     * @return
     */
    AppUniformReceive selectByGuid(@Param("guid") String guid);

    /**
     * 逻辑删除
     *
     * @param id
     * @return
     */
    int logicDeleteById(@Param("id") Integer id);
}