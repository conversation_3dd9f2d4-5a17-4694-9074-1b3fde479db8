package com.whfc.emp.dao;

import com.whfc.emp.dto.AppFaceGateConfigDTO;
import com.whfc.emp.entity.AppFaceGateConfig;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-08-17 11:31
 */
@Repository
public interface AppFaceGateConfigMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppFaceGateConfig record);

    int insertSelective(AppFaceGateConfig record);

    AppFaceGateConfig selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppFaceGateConfig record);

    int updateByPrimaryKey(AppFaceGateConfig record);

    /**
     * 查询当前项目能选择的闸机平台
     *
     * @param deptId
     * @return
     */
    List<AppFaceGateConfigDTO> selectByDeptId(@Param("deptId") Integer deptId);

    /**
     * 使用闸机平台编码和组织机构id查询
     *
     * @param deptId
     * @param platform
     * @return
     */
    AppFaceGateConfig selectByDeptIdAndPlatform(@Param("deptId") Integer deptId, @Param("platform") String platform);

    /**
     * 根据闸机平台查询
     * @param platform
     * @return
     */
    List<AppFaceGateConfig> selectByPlatform(@Param("platform") String platform);

    /**
     * 获取全部闸机列表
     *
     * @return
     */
    List<AppFaceGateConfigDTO> selectAll();

    /**
     * 获取闸机详细信息
     *
     * @param deptId 组织机构ID
     * @return 闸机详细详细
     */
    List<AppFaceGateConfigDTO> selectDetailListByDeptId(@Param("deptId") Integer deptId);

    /**
     * 根据组织机构删除
     *
     * @param deptId
     */
    void loginDelByDeptId(@Param("deptId") Integer deptId);

}