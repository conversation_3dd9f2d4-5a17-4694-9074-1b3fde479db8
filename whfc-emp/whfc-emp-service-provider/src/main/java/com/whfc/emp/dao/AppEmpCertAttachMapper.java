package com.whfc.emp.dao;

import com.whfc.emp.dto.AppAttachDTO;
import com.whfc.emp.entity.AppEmpCertAttach;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AppEmpCertAttachMapper {
    int deleteByPrimaryKey(Integer id);

    int insertSelective(AppEmpCertAttach record);

    AppEmpCertAttach selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppEmpCertAttach record);

    /**
     * 逻辑删除证书附件
     *
     * @param certId
     */
    void deleteLogicByCertId(@Param("certId") Integer certId);

    /**
     * 使用证书id查询证书附件列表
     *
     * @param certId
     * @return
     */
    List<AppAttachDTO> selectByCertId(@Param("certId") Integer certId);

    /**
     * 批量插入
     *
     * @param fileList
     */
    void batchInsert(@Param("deptId") Integer deptId, @Param("certId") Integer certId, @Param("fileList") List<AppAttachDTO> fileList);
}