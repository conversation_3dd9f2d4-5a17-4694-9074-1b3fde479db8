package com.whfc.emp.third;

import com.whfc.common.exception.BizException;
import com.whfc.emp.entity.*;
import com.whfc.emp.param.FaceGateRecordParam;

import java.util.List;

/**
 * @Description: 人员信息-第三方同步
 * @author: xug<PERSON>
 * @version: 1.0
 * @date: 2023/6/30 14:41
 */
public interface EmpThirdSync {

    /**
     * 同步-人员信息
     *
     * @param emp
     * @param config
     * @throws BizException
     */
    void syncEmp(AppEmp emp, AppSync config);

    /**
     * 同步-人员信息
     *
     * @param empList
     * @param config
     */
    void syncEmp(List<AppEmp> empList, AppSync config);

    /**
     * 同步-人员合同
     *
     * @param emp
     * @param contract
     * @param config
     */
    void syncEmpContract(AppEmp emp, AppEmpContract contract, AppSync config);

    /**
     * 同步-人员培训
     *
     * @param train
     * @param config
     */
    void syncEmpTrain(AppTrain train, AppSync config);

    /**
     * 同步-人员工资
     *
     * @param payroll
     * @param config
     */
    void syncEmpPayroll(AppPayroll payroll, AppSync config);

    /**
     * 同步-人员考勤
     *
     * @param param
     * @param config
     */
    void syncEmpAttend(FaceGateRecordParam param, AppSync config);

    /**
     * 同步-人员室内定位
     *
     * @param tag
     * @param config
     */
    void syncEmpIndoorPosition(AppIndoorPositionTag tag, AppSync config);

    /**
     * 同步-人员证书
     *
     * @param cert
     * @param config
     */
    void syncEmpCert(AppEmpCert cert, AppSync config);
}
