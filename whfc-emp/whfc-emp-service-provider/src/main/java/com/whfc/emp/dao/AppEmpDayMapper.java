package com.whfc.emp.dao;

import com.whfc.emp.dto.*;
import com.whfc.emp.entity.AppEmpDay;
import com.whfc.entity.dto.adsq.AdsqStatisticsItemDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;

public interface AppEmpDayMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppEmpDay record);

    int insertSelective(AppEmpDay record);

    AppEmpDay selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppEmpDay record);

    int updateByPrimaryKey(AppEmpDay record);

    /**
     * 根据corpId 和deptId 查找员工
     *
     * @param groupId
     * @param deptId
     * @param monthEnd
     * @param monthBegin
     * @return
     */
    List<Integer> selectEmpIdByCorpIdAndDeptId(@Param("groupId") Integer groupId,
                                               @Param("deptId") Integer deptId,
                                               @Param("monthBegin") Date monthBegin,
                                               @Param("monthEnd") Date monthEnd);

    /**
     * 查看员工出勤天数
     *
     * @param deptId
     * @param empId
     * @param monthBegin
     * @param monthEnd
     * @return
     */
    Integer selectAttendDaysByEmpId(@Param("deptId") Integer deptId,
                                    @Param("empId") Integer empId,
                                    @Param("startTime") Date monthBegin,
                                    @Param("endTime") Date monthEnd);

    /**
     * 查询日考勤人员的详细信息
     *
     * @param deptId
     * @param date
     * @param corpId
     * @param groupId
     * @param keyword
     * @param attendState
     * @return
     */
    List<AttendDayEmpDTO> selectAttendDayEmp(@Param("deptId") Integer deptId,
                                             @Param("date") Date date,
                                             @Param("corpId") Integer corpId,
                                             @Param("groupId") Integer groupId,
                                             @Param("keyword") String keyword,
                                             @Param("attendState") Integer attendState);

    /**
     * 月考勤
     *
     * @param deptId
     * @param corpId
     * @param groupId
     * @param startTime
     * @param endTime
     * @param keyword
     * @return
     */
    List<AppEmpAttendMonthDTO> selectAttendMonthEmp(@Param("deptId") Integer deptId,
                                                    @Param("corpId") Integer corpId,
                                                    @Param("groupId") Integer groupId,
                                                    @Param("startTime") Date startTime,
                                                    @Param("endTime") Date endTime,
                                                    @Param("keyword") String keyword);

    /**
     * 查询员工每天的工时
     *
     * @param empId
     * @param monthBegin
     * @param monthEnd
     * @return
     */
    List<AppEmpDayWorkTimesDTO> selectWorkTimesByEmpId(@Param("empId") Integer empId,
                                                       @Param("startTime") Date monthBegin,
                                                       @Param("endTime") Date monthEnd);

    /**
     * 人员总运行数据-查找每一天人员总数
     *
     * @param deptId
     * @param startDate
     * @param endDate
     * @return
     */
    List<EmpDataDTO> selectEmpNumTotalPerDay(@Param("deptId") Integer deptId,
                                             @Param("startDate") Date startDate,
                                             @Param("endDate") Date endDate);

    /**
     * 人员总运行数据-根据人员类型查找每一天人员总数
     *
     * @param deptId
     * @param startDate
     * @param endDate
     * @return
     */
    List<EmpDataDTO> selectEmpNumTotalPerDayByWorkRole(@Param("deptId") Integer deptId,
                                                       @Param("startDate") Date startDate,
                                                       @Param("endDate") Date endDate);

    /**
     * 人员出勤统计(多项目)
     *
     * @param deptIds
     * @param startDate
     * @param endDate
     * @return
     */
    List<EmpDataDTO> selectEmpDayAttend(@Param("deptIds") Collection<Integer> deptIds,
                                        @Param("startDate") Date startDate,
                                        @Param("endDate") Date endDate);

    /**
     * 人员出勤统计(多项目)
     *
     * @param deptIds
     * @param startDate
     * @param endDate
     * @return
     */
    List<EmpDataDTO> selectEmpMonthAttend(@Param("deptIds") Collection<Integer> deptIds,
                                          @Param("startDate") Date startDate,
                                          @Param("endDate") Date endDate);

    /**
     * 人员出勤统计(多项目)
     *
     * @param deptIds
     * @param startDate
     * @param endDate
     * @return
     */
    List<EmpDataDTO> selectEmpWeekAttend(@Param("deptIds") Collection<Integer> deptIds,
                                         @Param("startDate") Date startDate,
                                         @Param("endDate") Date endDate);

    /**
     * 人员出勤统计(多项目)
     *
     * @param deptIds
     * @param startDate
     * @param endDate
     * @return
     */
    List<EmpDataDTO> selectEmpProjectAttend(@Param("deptIds") Collection<Integer> deptIds,
                                            @Param("startDate") Date startDate,
                                            @Param("endDate") Date endDate);

    /**
     * 查找最近出勤人员
     *
     * @param deptId
     * @return
     */
    List<AppAnaEmpDTO> selectRecentAttendEmp(@Param("deptId") Integer deptId);

    /**
     * 更新人员出勤状态以及在场状态
     *
     * @param empId       人员ID
     * @param attendState 考勤状态
     * @param localeState 在场状态
     * @return
     */
    int updateAttendStateAndLocaleState(@Param("empId") Integer empId,
                                        @Param("attendState") Integer attendState,
                                        @Param("localeState") Integer localeState);

    /**
     * 更新闸机考勤时间
     */
    void updateFaceGateTimes(@Param("empId") Integer empId,
                             @Param("date") Date date,
                             @Param("times") Integer times);

    /**
     * 更新工作考勤时间
     */
    void updateworkTimes(@Param("empId") Integer empId,
                         @Param("date") Date date,
                         @Param("times") Integer times);

    /**
     * 更新人员在现场的状态
     *
     * @param empId
     * @param localeState
     * @return
     */
    int updateLocaleState(@Param("empId") Integer empId,
                          @Param("localeState") Integer localeState);

    /**
     * 更新闸机考勤的下班时间
     *
     * @param empId
     * @param date
     * @param faceGateEndTime
     * @return
     */
    int updateFaceGateEndTime(@Param("empId") Integer empId,
                              @Param("date") Date date,
                              @Param("faceGateEndTime") Date faceGateEndTime);

    /**
     * 更新闸机考勤的下班时间
     *
     * @param empId
     * @param date
     * @return
     */
    int updateEndTime(@Param("empId") Integer empId,
                      @Param("date") Date date);

    /**
     * 修改人员指定日期出勤
     *
     * @param empId
     * @param date
     * @return
     */
    int updateAttend(@Param("empId") Integer empId,
                     @Param("date") Date date,
                     @Param("attendType") Integer attendType);

    /**
     * 更新闸机考勤的上班时间
     *
     * @param empId
     * @param date
     * @param faceGateStartTime
     * @return
     */
    int updateFaceGateStartTime(@Param("empId") Integer empId,
                                @Param("date") Date date,
                                @Param("faceGateStartTime") Date faceGateStartTime);

    /**
     * 更新考勤的上班时间
     *
     * @param empId
     * @param date
     * @return
     */
    int updateStartTime(@Param("empId") Integer empId,
                        @Param("date") Date date);

    /**
     * 查询人员每日数据
     *
     * @param empId
     * @param date
     * @return
     */
    AppEmpDay selectByEmpIdAndDate(@Param("empId") Integer empId, @Param("date") Date date);

    /**
     * 插入 or 更新
     *
     * @param record
     * @return
     */
    void insertOrUpdate(AppEmpDay record);

    /**
     * 批量插入
     *
     * @param empDayList
     * @return
     */
    void batchInsert(List<AppEmpDay> empDayList);

    /**
     * 查询机构的日数据列表
     *
     * @param deptId
     * @param startDate
     * @param endDate
     * @return
     */
    List<WxEmpCurveMapDayDTO> selectWxEmpCurveMapDayDTOListInDept(@Param("deptId") Integer deptId,
                                                                  @Param("startDate") Date startDate,
                                                                  @Param("endDate") Date endDate);

    /**
     * 奥氹四桥月统计
     *
     * @param deptId  组织机构ID
     * @param time    时间
     * @param groupId 班组ID
     * @return 月统计
     */
    List<AdsqStatisticsItemDTO> adsqMonthStatistics(@Param("deptId") Integer deptId,
                                                    @Param("time") Date time,
                                                    @Param("groupId") Integer groupId);

    /**
     * 查询组织机构的人员每日数据
     *
     * @param deptId
     * @param date
     * @return
     */
    List<AppEmpDay> selectEmpDayListByDeptIdAndDate(@Param("deptId") Integer deptId, @Param("date") Date date);

    /**
     * 人员日志
     *
     * @param deptId
     * @param date
     * @return
     */
    Integer countEmpTotal(@Param("deptId") Integer deptId, @Param("date") Date date);

    /**
     * 人员日志
     *
     * @param deptId
     * @param date
     * @return
     */
    List<AppEmpWorkRoleNumDTO> selectEmpLogByWorkRole(@Param("deptId") Integer deptId, @Param("date") Date date);

    /**
     * 人员管理-日考勤记录统计
     *
     * @param deptId
     * @param corpId
     * @param groupId
     * @param date
     * @param keyword
     * @param empId
     * @return
     */
    AppEmpAttendDayNumDTO selectAttendDayEmpNum(@Param("deptId") Integer deptId,
                                                @Param("corpId") Integer corpId,
                                                @Param("groupId") Integer groupId,
                                                @Param("date") Date date,
                                                @Param("keyword") String keyword,
                                                @Param("empId") Integer empId);

    /**
     * 查询项目考勤统计-按时段分组
     *
     * @param deptId
     * @param date
     * @return
     */
    List<AppEmpAttendTimeDTO> selectAttendTimeStat(@Param("deptId") Integer deptId, @Param("date") Date date);
}