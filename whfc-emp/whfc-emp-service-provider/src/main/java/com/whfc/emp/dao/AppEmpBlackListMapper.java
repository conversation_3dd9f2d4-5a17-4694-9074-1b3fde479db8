package com.whfc.emp.dao;

import com.whfc.emp.dto.AppEmpBlackDTO;
import com.whfc.emp.entity.AppEmpBlackList;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AppEmpBlackListMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppEmpBlackList record);

    int insertSelective(AppEmpBlackList record);

    AppEmpBlackList selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppEmpBlackList record);

    int updateByPrimaryKey(AppEmpBlackList record);

    /**
     * 查询人员黑名单
     * @param deptId
     * @param keyword
     * @return
     */
    List<AppEmpBlackDTO> selectByDeptIds(@Param("deptId") Integer deptId,
                                         @Param("keyword") String keyword);

    /**
     * 批量添加黑名单人员
     * @param list
     */
    void batchInsert(@Param("list") List<AppEmpBlackList> list);

    /**
     * 删除黑名单人员
     * @param id
     */
    void deleteLogicById(@Param("id") Integer id);

    /**
     * 检测人员是否在黑名单中
     * @param empId
     * @param deptId
     * @return
     */
    Integer countByEmpIdAndDeptId(@Param("empId") Integer empId, @Param("deptId") Integer deptId);
}