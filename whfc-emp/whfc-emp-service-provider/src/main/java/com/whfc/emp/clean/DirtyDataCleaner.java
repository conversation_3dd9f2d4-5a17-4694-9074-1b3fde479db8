package com.whfc.emp.clean;

import com.whfc.common.file.FileHandler;
import com.whfc.common.util.DateUtil;
import com.whfc.emp.dao.AppEmpAttendRecordMapper;
import com.whfc.emp.dao.AppFaceGateRecordMapper;
import com.whfc.emp.dto.stat.EmpAttendStat;
import com.whfc.emp.dto.stat.FacegateRecEmpStat;
import com.whfc.emp.entity.AppEmpAttendRecord;
import com.whfc.emp.entity.AppFaceGateRecord;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2021-08-06 12:40
 */
@Component
public class DirtyDataCleaner {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    private static final String BASE_URL = "https://file.whfciot.com/";

    private static final Integer PAGE_SIZE = 10;


    @Autowired
    private AppFaceGateRecordMapper faceGateRecordMapper;

    @Autowired
    private AppEmpAttendRecordMapper empAttendRecordMapper;

    @Autowired
    private FileHandler fileHandler;

    /**
     * 清理闸机识别记录脏数据
     */
    public void clearFacegateRecord(Date startTime, Date endTime) {

        //查询识别记录重复统计
        List<FacegateRecEmpStat> empStatList = faceGateRecordMapper.selectEmpRecCount(startTime, endTime);
        logger.info("闸机识别记录脏数据统计:{}", empStatList.size());
        for (FacegateRecEmpStat empStat : empStatList) {
            logger.info("{} - {} - {}", empStat.getEmpId(), DateUtil.formatDateTime(empStat.getShowTime()), empStat.getCnt());

            //查询识别记录
            List<AppFaceGateRecord> recordList = faceGateRecordMapper.selectRecRecordByEmpIdAndTime(empStat.getEmpId(), empStat.getShowTime());

            List<String> ossKeys = new ArrayList<>(recordList.size());
            List<Integer> idList = new ArrayList<>(recordList.size());
            for (int i = 0; i < recordList.size() - 1; i++) {
                AppFaceGateRecord record = recordList.get(i);
                String photoUrl = record.getPhotoUrl();
                if (StringUtils.isNotEmpty(photoUrl)) {
                    if (photoUrl.startsWith(BASE_URL)) {
                        ossKeys.add(photoUrl.replace(BASE_URL, ""));
                    }
                }
                idList.add(recordList.get(i).getId());
            }

            //删除图片
            this.cleanOssKeys(ossKeys);

            //删除多余识别记录
            logger.info("删除记录:{}", idList);
            if (idList.size() > 0) {
                faceGateRecordMapper.batchDelete(idList);
            }
        }
    }

    /**
     * 清理人员考勤记录脏数据
     *
     * @param startTime
     * @param endTime
     */
    public void cleanEmpAttendRecord(Date startTime, Date endTime) {
        //查询人员考勤记录
        List<EmpAttendStat> attendStatList = empAttendRecordMapper.selectEmpAttendRecCount(startTime, endTime);
        logger.info("人员考勤记录脏数据统计:{}", attendStatList.size());
        for (EmpAttendStat attendStat : attendStatList) {
            logger.info("{} - {} - {}", attendStat.getEmpId(), DateUtil.formatDateTime(attendStat.getTime()), attendStat.getCnt());

            //查询识别记录
            List<AppEmpAttendRecord> recordList = empAttendRecordMapper.selectAttendRecordByEmpIdAndTime(attendStat.getEmpId(), attendStat.getTime());
            List<String> ossKeys = new ArrayList<>(recordList.size());
            List<Integer> idList = new ArrayList<>(recordList.size());
            for (int i = 0; i < recordList.size() - 1; i++) {
                AppEmpAttendRecord record = recordList.get(i);
                String photoUrl = record.getPhoto();
                if (StringUtils.isNotEmpty(photoUrl)) {
                    if (photoUrl.startsWith(BASE_URL)) {
                        ossKeys.add(photoUrl.replace(BASE_URL, ""));
                    }
                }
                idList.add(recordList.get(i).getId());
            }

            //删除图片
            this.cleanOssKeys(ossKeys);

            //删除多余识别记录
            logger.info("删除记录:{}", idList);
            if (idList.size() > 0) {
                empAttendRecordMapper.batchDelete(idList);
            }
        }
    }

    private void cleanOssKeys(List<String> ossKeys) {
        //删除图片
        logger.info("删除图片:{}", ossKeys);
        if (ossKeys.size() > 0) {
            int divisor = ossKeys.size() / PAGE_SIZE;
            int remainder = ossKeys.size() % PAGE_SIZE;
            int pages = divisor + ((remainder == 0) ? 0 : 1);
            for (int i = 0; i <= pages; i++) {
                int begin = i * PAGE_SIZE;
                int end = begin + PAGE_SIZE;
                end = ossKeys.size() < end ? ossKeys.size() : end;
                fileHandler.delete(ossKeys.subList(begin, end));
            }
        }
    }
}
