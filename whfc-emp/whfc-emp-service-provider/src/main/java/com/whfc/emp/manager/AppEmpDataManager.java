package com.whfc.emp.manager;

import com.whfc.common.exception.BizException;
import com.whfc.emp.dto.HelmetIconDTO;
import com.whfc.emp.entity.AppDeviceCardLog;
import com.whfc.emp.entity.AppEmpDay;

import java.util.Date;
import java.util.List;

/**
 * @Description 安全帽数据处理服务
 * <AUTHOR>
 * @Date 2020/12/30 14:50
 * @Version 1.0
 */
public interface AppEmpDataManager {

    /**
     * 获取安全帽图标
     *
     * @param sn
     * @return
     */
    HelmetIconDTO getHelmetIcon(String sn);

    /**
     * 查询人员安全帽数据
     *
     * @param empId
     * @param date
     * @param startTime
     * @param endTime
     * @return
     */
    List<AppDeviceCardLog> getHelmetDataLogByEmpId(Integer empId, Date date, Date startTime, Date endTime);

    /**
     * 添加人员硬件数据
     *
     * @param cardLog
     */
    void addEmpDeviceData(AppDeviceCardLog cardLog);

    /**
     * 批量添加安全帽硬件数据
     *
     * @param empId
     * @param date
     * @param logList
     */
    void addEmpDeviceData(Integer empId, Date date, List<AppDeviceCardLog> logList);

    /**
     * 初始化人员每日数据
     *
     * @param deptId
     * @param date
     * @return
     * @throws BizException
     */
    void initEmpDayData(Integer deptId, Date date);

    /**
     * 统计人员每日数据
     *
     * @param deptId
     * @param date
     * @return
     * @throws BizException
     */
    void statEmpDayData(Integer deptId, Date date);

    /**
     * 初始化人员每日统计信息
     *
     * @param empId
     * @param date
     * @return
     */
    AppEmpDay getEmpDay(Integer empId, Date date);

}
