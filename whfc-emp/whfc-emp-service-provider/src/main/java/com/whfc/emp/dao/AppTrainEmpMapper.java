package com.whfc.emp.dao;

import com.whfc.emp.dto.TrainEmpDTO;
import com.whfc.emp.entity.AppTrainEmp;
import com.whfc.emp.param.AppTrainEmpListParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-09-02 18:29
 */

public interface AppTrainEmpMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppTrainEmp record);

    int insertSelective(AppTrainEmp record);

    AppTrainEmp selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppTrainEmp record);

    int updateByPrimaryKey(AppTrainEmp record);

    /**
     * 查询培训人员列表
     *
     * @param request
     * @return
     */
    List<TrainEmpDTO> selectTrainEmpByParam(AppTrainEmpListParam request);

    /**
     * 查询培训人员
     *
     * @param trainId
     * @return
     */
    List<TrainEmpDTO> selectByTrainId(@Param("trainId") Integer trainId);

    /**
     * 查询培训通过的员工数
     *
     * @param trainId
     * @return
     */
    Integer countPassEmp(@Param("trainId") Integer trainId);

    /**
     * 查询培训的员工数
     *
     * @param trainId
     * @return
     */
    Integer countEmp(@Param("trainId") Integer trainId);

    /**
     * 验证同一个培训中不能有重复的人员
     *
     * @param trainId
     * @param empId
     * @return
     */
    Integer countByTrainIdAndEmpId(@Param("trainId") Integer trainId,
                                   @Param("empId") Integer empId);

    /**
     * 人员管理-删除培训人员
     *
     * @param trainId
     * @param empId
     */
    void deleteByTrainIdAndEmpId(@Param("trainId") Integer trainId, @Param("empId") Integer empId);

    /**
     * 人员管理-添加修改培训分数
     *
     * @param score
     * @param passFlag
     * @param trainId
     * @param empId
     */
    void updateScoreByTrainIdAndEmpId(@Param("score") Double score,
                                      @Param("passFlag") Integer passFlag,
                                      @Param("trainId") Integer trainId,
                                      @Param("empId") Integer empId);

    /**
     * 查询人员培训合格的一条记录
     *
     * @param empId
     * @param trainType
     * @return
     */
    TrainEmpDTO selectTrainEmpBYEmpIdAndTrainType(@Param("empId") Integer empId, @Param("trainType") Integer trainType);

    /**
     * 查找培训人员
     *
     * @param trainId 培训记录ID
     * @param empId   人员ID
     * @return 培训人员
     */
    AppTrainEmp selectTrainEmp(@Param("trainId") Integer trainId, @Param("empId") Integer empId);

}