package com.whfc.emp.manager.warn;

import com.whfc.emp.dto.EmpWarnCheckDTO;
import com.whfc.emp.dto.EmpWarnRuleDTO;
import org.springframework.stereotype.Component;

/**
 * @Description: 人员报警管理器-SOS
 * @author: xug<PERSON>
 * @version: 1.0
 * @date: 2023/10/10 15:53
 */
@Component(value = "appEmpWarnMgrHardwareSos")
public class AppEmpWarnMgrHardwareSos extends AppEmpWarnMgrHardware implements AppEmpWarnMgr {

    @Override
    public Integer getAlarmValue(EmpWarnCheckDTO checkDTO) {
        return checkDTO.getAlarmSos();
    }

    @Override
    public boolean checkFrequency(EmpWarnCheckDTO checkDTO, EmpWarnRuleDTO ruleDTO) {
        return true;
    }
}
