package com.whfc.emp.mqtt;

import com.whfc.common.face.szyc.SearchPerson;
import com.whfc.common.mqtt.MqttProperties;
import com.whfc.common.util.RandomUtil;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.integration.annotation.IntegrationComponentScan;
import org.springframework.integration.annotation.MessagingGateway;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.integration.channel.DirectChannel;
import org.springframework.integration.core.MessageProducer;
import org.springframework.integration.mqtt.core.DefaultMqttPahoClientFactory;
import org.springframework.integration.mqtt.core.MqttPahoClientFactory;
import org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter;
import org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler;
import org.springframework.integration.mqtt.support.DefaultPahoMessageConverter;
import org.springframework.integration.mqtt.support.MqttHeaders;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.MessageHandler;
import org.springframework.messaging.handler.annotation.Header;

import java.util.List;

/**
 * MQTT 配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/9 15:06
 */
@Configuration
@IntegrationComponentScan
@ConditionalOnProperty(value = "mqtt.enabled", havingValue = "true")
public class MqttConfig {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    public static Thread MQTT_THREAD = null;

    public static SearchPerson SEARCH_PERSON = null;

    @Autowired
    private MqttProperties mqttProperties;

    @Autowired
    private MqttMessageProcessorFactory mqttMessageProcessorFactory;

    /**
     * MQTT订阅通道
     */
    public static final String MQTT_CHANNEL_IN = "mqttInputChannel";

    /**
     * MQTT发布通道
     */
    public static final String MQTT_CHANNEL_OUT = "mqttOutputChannel";

    /**
     * MQTT客户端
     */
    @Bean
    public MqttPahoClientFactory mqttClientFactory() {
        logger.info("创建MQTT工厂");
        DefaultMqttPahoClientFactory factory = new DefaultMqttPahoClientFactory();
        MqttConnectOptions options = new MqttConnectOptions();
        // 设置连接的用户名
        options.setUserName(mqttProperties.getUsername());
        // 设置连接的密码
        options.setPassword(mqttProperties.getPassword().toCharArray());
        // 设置连接的地址
        options.setServerURIs(new String[]{mqttProperties.getUrl()});
        // 设置超时时间 单位为秒
        options.setConnectionTimeout(10);
        // 设置会话心跳时间 单位为秒 服务器会每隔1.5*20秒的时间向客户端发送心跳判断客户端是否在线
        options.setKeepAliveInterval(20);
        options.setMaxInflight(100);
        factory.setConnectionOptions(options);
        return factory;
    }

    /**
     * MQTT信息通道（发布者）
     */
    @Bean(name = MqttConfig.MQTT_CHANNEL_OUT)
    public MessageChannel mqttOutputChannel() {
        return new DirectChannel();
    }

    /**
     * MQTT消息处理器（生产者）
     */
    @Bean
    @ServiceActivator(inputChannel = MqttConfig.MQTT_CHANNEL_OUT)
    public MessageHandler mqttOutbound() {
        //随机 clientId
        String clientId = mqttProperties.getClientId() + "_ep_" + RandomUtil.getRandomStr(6);
        MqttPahoMessageHandler messageHandler = new MqttPahoMessageHandler(clientId, mqttClientFactory());
        messageHandler.setAsync(true);
        messageHandler.setDefaultQos(0);
        return messageHandler;
    }

    /**
     * MQTT信息通道（消费者）
     */
    @Bean(name = MqttConfig.MQTT_CHANNEL_IN)
    public MessageChannel mqttInputChannel() {
        return new DirectChannel();
    }


    /**
     * MQTT消息订阅绑定（消费者）
     */
    @Primary
    @Bean
    public MessageProducer inbound() {

        //订阅topic
        List<String> topicList = mqttMessageProcessorFactory.getAllSubTopicList();
        String[] topicArray = topicList.toArray(new String[]{});

        logger.info("订阅topic:{}", topicList);

        //clientId
        String clientId = mqttProperties.getClientId() + "_ec_" + RandomUtil.getRandomStr(6);

        DefaultPahoMessageConverter converter = new DefaultPahoMessageConverter();
        converter.setPayloadAsBytes(true);

        //订阅topic
        MqttPahoMessageDrivenChannelAdapter adapter = new MqttPahoMessageDrivenChannelAdapter(clientId, mqttClientFactory(), topicArray);
        adapter.setCompletionTimeout(5000);
        adapter.setConverter(converter);
        adapter.setQos(0);
        adapter.setOutputChannel(mqttInputChannel());
        return adapter;
    }

    /**
     * MQTT消息处理器（消费者）
     */
    @Bean
    @ServiceActivator(inputChannel = MqttConfig.MQTT_CHANNEL_IN)
    public MessageHandler handler() {
        return message -> {
            String topic = String.valueOf(message.getHeaders().get(MqttHeaders.RECEIVED_TOPIC));
            byte[] payload = (byte[]) message.getPayload();
            logger.debug("MQTT接收到消息, topic：{},payload:{}", topic, payload.length);
            MqttMessageProcessor processor = mqttMessageProcessorFactory.getMqttMessageProcessor(topic);
            if (processor != null) {
                processor.processData(topic, payload);
            }
        };
    }

    /**
     * 消息发送中
     */
    @MessagingGateway(defaultRequestChannel = MqttConfig.MQTT_CHANNEL_OUT)
    public interface MqttMessageSender {

        /**
         * 指定主题发送消息
         *
         * @param topic   主题
         * @param payload 消息主体
         */
        void sendToMqtt(@Header(MqttHeaders.TOPIC) String topic, String payload);

        /**
         * 默认主题发送消息
         *
         * @param payload 默认主题
         */
        void sendToMqtt(@Header(MqttHeaders.TOPIC) String topic, byte[] payload);

        /**
         * 指定主题、qos发送消息
         *
         * @param topic   主题
         * @param qos     对消息处理的几种机制。
         *                0 表示的是订阅者没收到消息不会再次发送，消息会丢失。
         *                1 表示的是会尝试重试，一直到接收到消息，但这种情况可能导致订阅者收到多次重复消息。
         *                2 多了一次去重的动作，确保订阅者收到的消息有一次。
         * @param payload 消息主体
         */
        void sendToMqtt(@Header(MqttHeaders.TOPIC) String topic, @Header(MqttHeaders.QOS) int qos, String payload);

        /**
         * 指定主题、qos发送消息
         *
         * @param topic   主题
         * @param qos     对消息处理的几种机制。
         *                0 表示的是订阅者没收到消息不会再次发送，消息会丢失。
         *                1 表示的是会尝试重试，一直到接收到消息，但这种情况可能导致订阅者收到多次重复消息。
         *                2 多了一次去重的动作，确保订阅者收到的消息有一次。
         * @param payload 消息主体
         */
        void sendToMqtt(@Header(MqttHeaders.TOPIC) String topic, @Header(MqttHeaders.QOS) int qos, byte[] payload);

    }
}
