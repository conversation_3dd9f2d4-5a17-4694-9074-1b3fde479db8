package com.whfc.emp.dao;

import com.whfc.emp.dto.AppEmpCertDTO;
import com.whfc.emp.entity.AppEmpCert;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AppEmpCertMapper {
    int deleteByPrimaryKey(Integer id);

    int insertSelective(AppEmpCert record);

    AppEmpCert selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppEmpCert record);

    /**
     * 逻辑删除证书
     * @param certId
     */
    void deleteLogic(@Param("certId") Integer certId);

    /**
     * 更新证书文件
     * @param certId
     * @param fileUrl
     */
    int updateCertFile(@Param("certId") Integer certId,@Param("fileUrl") String fileUrl);

    /**
     * 使用员工信息查询证书列表
     * @param empId
     * @return
     */
    List<AppEmpCertDTO> selectByEmpId(@Param("empId")Integer empId);

    /**
     * 使用证书编号查询证书信息
     * @param certCode
     * @return
     */
    AppEmpCert selectByCertCode(@Param("deptId")Integer deptId,@Param("certCode")String certCode);
}