package com.whfc.emp.job;

import com.alibaba.fastjson.JSONObject;
import com.whfc.XxlJobConfig;
import com.whfc.common.iot.forthink.entity.ForthinkConst;
import com.whfc.common.iot.forthink.entity.ForthinkWarnRecordQueryMsg;
import com.whfc.common.iot.forthink.util.ForthinkMsgUtil;
import com.whfc.common.mqtt.Qos;
import com.whfc.common.redis.RedisConst;
import com.whfc.common.redis.RedisService;
import com.whfc.emp.dao.AppIndoorPositionStationMapper;
import com.whfc.emp.dto.AppIndoorPositionStationDTO;
import com.whfc.emp.mqtt.MqttConfig;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import io.netty.buffer.ByteBuf;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * FTK 定时任务
 */
@Component
@ConditionalOnBean(XxlJobConfig.class)
public class FtkJob {

    private static final Logger logger = LoggerFactory.getLogger(FtkJob.class);

    @Autowired(required = false)
    private MqttConfig.MqttMessageSender mqttMessageSender;

    @Autowired
    private AppIndoorPositionStationMapper stationMapper;

    @Autowired
    private RedisService redisService;

    @XxlJob("ftk-warn-record-query")
    public void ftkWarnRecordQuery() {
        try {
            XxlJobHelper.log("开始获取ftk报警数据.");

            String paramStr = XxlJobHelper.getJobParam();
            if (StringUtils.isEmpty(paramStr)) {
                XxlJobHelper.handleFail("没有配置参数");
                return;
            }
            JSONObject param = JSONObject.parseObject(paramStr);
            List<Integer> deptIds = param.getJSONArray("deptIds").toJavaList(Integer.class);
            for (Integer deptId : deptIds) {
                List<AppIndoorPositionStationDTO> stationList = stationMapper.selectByDeptId(deptId);
                for (AppIndoorPositionStationDTO station : stationList) {
                    String topic = ForthinkConst.getTopicDown(station.getCode());

//                    String seqKey = String.format(RedisConst.UWB_WARN_RECORD_QUERY_SEQ, station.getCode());
//                    String seqStr = redisService.get(seqKey);

                    String flagKey = String.format(RedisConst.UWB_WARN_RECORD_QUERY_FLAG, station.getCode());
                    String flagStr = redisService.get(flagKey);

                    //flag=1 or flag = null,表示报警数据已经回传完毕,定时任务又从0开始查询
                    if (StringUtils.isEmpty(flagStr) || ForthinkConst.FLAG_QUERY_END_YES_STR.equals(flagStr)) {
                        int nextSeq = 0;
                        ForthinkWarnRecordQueryMsg msg = new ForthinkWarnRecordQueryMsg();
                        msg.setVersion(1);
                        msg.setId(0XFFFFFFFF);
                        msg.setSeq(nextSeq);
                        ByteBuf buf = ForthinkMsgUtil.encode(msg);
                        logger.info("发送ftk报警数据 topic:{},flag:{},seq:{},nextSeq:{}", topic, flagStr, "", nextSeq);
                        mqttMessageSender.sendToMqtt(topic, Qos.Qos0, buf.array());

                        //记录下发序号
                        String seqKey = String.format(RedisConst.UWB_WARN_RECORD_QUERY_SEQ, station.getCode());
                        redisService.set(seqKey, String.valueOf(nextSeq));
                    } else {
                        String seqKey = String.format(RedisConst.UWB_WARN_RECORD_QUERY_SEQ, station.getCode());
                        String seqStr = redisService.get(seqKey);
                        int nextSeq = Integer.parseInt(seqStr);
                        ForthinkWarnRecordQueryMsg msg = new ForthinkWarnRecordQueryMsg();
                        msg.setVersion(1);
                        msg.setId(0XFFFFFFFF);
                        msg.setSeq(nextSeq);
                        ByteBuf buf = ForthinkMsgUtil.encode(msg);
                        logger.info("发送ftk报警数据 topic:{},flag:{},seq:{},nextSeq:{}", topic, flagStr, seqStr, nextSeq);
                        mqttMessageSender.sendToMqtt(topic, Qos.Qos0, buf.array());
                    }
                }
            }
        } catch (Exception ex) {
            logger.error("", ex);
            XxlJobHelper.log("开始获取ftk报警数据失败 error:{}", ex);
            XxlJobHelper.handleFail("开始获取ftk报警数据失败 error:" + ex.getLocalizedMessage());
        }
    }
}
