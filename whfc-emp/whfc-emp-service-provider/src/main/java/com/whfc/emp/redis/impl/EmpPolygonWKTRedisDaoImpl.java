package com.whfc.emp.redis.impl;

import com.whfc.emp.redis.EmpPolygonWKTRedisDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import java.text.MessageFormat;

/**
 * <AUTHOR>
 * @description
 * @date 2021-01-22
 */
@Repository
public class EmpPolygonWKTRedisDaoImpl implements EmpPolygonWKTRedisDao {
    @Autowired
    private StringRedisTemplate redisTemplate;

    private static final String KEY = "emp-fence-wkt::{0}";

    @Override
    public void setPolygonWKT(Integer deptId, String value) {
        String key = MessageFormat.format(KEY, deptId);
        redisTemplate.opsForValue().set(key, value);
    }

    @Override
    public String getPolygonWKT(Integer deptId) {
        String key = MessageFormat.format(KEY, deptId);
        return redisTemplate.opsForValue().get(key);
    }

    @Override
    public void delPolygonWKT(Integer deptId) {
        String key = MessageFormat.format(KEY, deptId);
        redisTemplate.delete(key);
    }
}
