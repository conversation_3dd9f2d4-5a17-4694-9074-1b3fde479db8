package com.whfc.emp.third;

import com.whfc.common.exception.BizException;
import com.whfc.emp.entity.AppEmp;
import com.whfc.emp.entity.AppSync;

/**
 * @Description: 人员信息-第三方核验
 * @author: xug<PERSON>
 * @version: 1.0
 * @date: 2023/6/30 14:42
 */
public interface EmpThirdVerify {

    /**
     * 人员核验
     *
     * @param emp
     * @param config
     * @throws BizException
     */
    void verifyEmp(AppEmp emp, AppSync config) throws BizException;
}
