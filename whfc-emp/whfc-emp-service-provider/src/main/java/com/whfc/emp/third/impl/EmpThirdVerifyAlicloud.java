package com.whfc.emp.third.impl;

import com.whfc.common.exception.BizException;
import com.whfc.common.util.Base64Util;
import com.whfc.common.util.IdCardVerifyUtil;
import com.whfc.emp.entity.AppEmp;
import com.whfc.emp.entity.AppSync;
import com.whfc.emp.third.EmpThirdVerify;
import org.springframework.stereotype.Service;

/**
 * @Description: 人员信息-第三方核验-阿里云市场API
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/6/30 14:45
 */
@Service
public class EmpThirdVerifyAlicloud implements EmpThirdVerify {

    @Override
    public void verifyEmp(AppEmp emp, AppSync config) throws BizException {

        String idCard = emp.getIdCardNo();
        String userName = emp.getEmpName();
        String idCardImg = Base64Util.getUrlImageToBase64(emp.getIdCardFront());
        IdCardVerifyUtil.idCardVerify(userName, idCard, idCardImg);
    }
}
