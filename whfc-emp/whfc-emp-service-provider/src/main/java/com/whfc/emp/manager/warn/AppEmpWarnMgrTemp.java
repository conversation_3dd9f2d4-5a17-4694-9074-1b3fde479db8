package com.whfc.emp.manager.warn;

import com.alibaba.fastjson2.JSON;
import com.whfc.emp.dto.EmpWarnCheckDTO;
import com.whfc.emp.dto.EmpWarnRuleDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 体温报警
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/7
 */
@Component(value = "appEmpWarnMgrTemp")
public class AppEmpWarnMgrTemp extends AppEmpWarnMgrBase implements AppEmpWarnMgr {


    private static final String MAX_PARAM_NAME = "maxValue";
    private static final String MIN_PARAM_NAME = "minValue";

    @Override
    public boolean checkValue(EmpWarnCheckDTO checkDTO, EmpWarnRuleDTO ruleDTO) {
        Double bodyTemp = checkDTO.getBodyTemp();
        if (bodyTemp == null) {
            return false;
        }
        String ruleParam = ruleDTO.getRuleParam();
        if (StringUtils.isBlank(ruleParam)) {
            return false;
        }
        // 校验体温是否超过最高值
        Integer maxValue = JSON.parseObject(ruleParam).getInteger(MAX_PARAM_NAME);
        if (maxValue != null && maxValue < bodyTemp) {
            checkDTO.setTriggerKey(bodyTemp + "");
            return true;
        }
        // 校验体温是否低于最低值
        Integer minValue = JSON.parseObject(ruleParam).getInteger(MIN_PARAM_NAME);
        if (minValue != null && minValue > bodyTemp) {
            checkDTO.setTriggerKey(bodyTemp + "");
            return true;
        }
        return false;
    }

    @Override
    public boolean checkFrequency(EmpWarnCheckDTO checkDTO, EmpWarnRuleDTO ruleDTO) {
        return true;
    }
}
