package com.whfc.emp.dao;

import com.whfc.emp.dto.helmet.AppHelmetFileDTO;
import com.whfc.emp.entity.AppHelmetFile;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface AppHelmetFileMapper {
    int deleteByPrimaryKey(Integer id);

    int insertSelective(AppHelmetFile record);

    AppHelmetFile selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppHelmetFile record);

    /**
     * 查询-安全帽-生成图片
     *
     * @param deptId
     * @param empId
     * @return
     */
    List<AppHelmetFileDTO> selectFileList(@Param("deptId") Integer deptId, @Param("empId") Integer empId);

    /**
     * 查询-安全帽-最新图片时间
     * @param empId
     * @return
     */
    Date selectLastFileTime(@Param("empId") Integer empId);

    /**
     * 批量插入
     * @param fileList
     * @return
     */
    int batchInsert(@Param("fileList") List<AppHelmetFile> fileList);
}