package com.whfc.emp.dao;

import com.whfc.emp.entity.AppTrainFailData;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-08-28 16:36
 */
@Repository
public interface AppTrainFailDataMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppTrainFailData record);

    int insertSelective(AppTrainFailData record);

    AppTrainFailData selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppTrainFailData record);

    int updateByPrimaryKey(AppTrainFailData record);

    /**
     * 根据类型查找数据
     *
     * @param type 类型
     * @return 失败数据
     */
    List<AppTrainFailData> selectByType(Integer type);

    /**
     * 根据类型删除数据
     *
     * @param type 类型
     */
    void delByType(Integer type);
}