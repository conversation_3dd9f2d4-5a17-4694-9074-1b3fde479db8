package com.whfc.emp.dao;

import com.whfc.emp.dto.AppEmpDTO;
import com.whfc.emp.dto.AppFenceDTO;
import com.whfc.emp.entity.AppEmpWarnRuleObject;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AppEmpWarnRuleObjectMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppEmpWarnRuleObject record);

    int insertSelective(AppEmpWarnRuleObject record);

    AppEmpWarnRuleObject selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppEmpWarnRuleObject record);

    int updateByPrimaryKey(AppEmpWarnRuleObject record);

    /**
     * 根据报警规则查找
     *
     * @param ruleId
     * @return
     */
    List<AppEmpDTO> selectByRuleId(@Param("ruleId") Integer ruleId);

    /**
     * 逻辑删除
     *
     * @param ruleId
     */
    void deleteLogicByRuleId(@Param("ruleId") Integer ruleId);

    /**
     * 批量插入
     *
     * @param ruleId
     * @param empIdList
     */
    void batchInsert(@Param("ruleId") Integer ruleId, @Param("empIdList") List<Integer> empIdList);

    /**
     * 根据人员id查找
     *
     * @param empId
     * @return
     */
    AppFenceDTO selectByEmpId(@Param("empId") Integer empId);
}