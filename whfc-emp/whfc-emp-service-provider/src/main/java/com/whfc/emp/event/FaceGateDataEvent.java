package com.whfc.emp.event;

import com.whfc.emp.param.FaceGateRecordParam;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-03-15 17:49
 */
public class FaceGateDataEvent extends ApplicationEvent {


    /**
     * 人脸闸机过机数据
     */
    private FaceGateRecordParam param;


    public FaceGateDataEvent(FaceGateRecordParam param) {
        super(param);
        this.param = param;
    }

    public FaceGateRecordParam getParam() {
        return param;
    }

    public void setParam(FaceGateRecordParam param) {
        this.param = param;
    }
}
