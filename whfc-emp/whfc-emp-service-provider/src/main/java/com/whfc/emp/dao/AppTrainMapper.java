package com.whfc.emp.dao;

import com.whfc.emp.dto.AppTrainDTO;
import com.whfc.emp.dto.AppTrainEmpDTO;
import com.whfc.emp.dto.AppTrainStatDTO;
import com.whfc.emp.entity.AppTrain;
import com.whfc.emp.param.AppTrainListParam;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * @author: hw
 * @date: 2021-10-22 15:24
 * @description: //todo
 */
public interface AppTrainMapper {
    int deleteByPrimaryKey(Integer id);

    int insertSelective(AppTrain record);

    AppTrain selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppTrain record);

    /**
     * 获取培训列表
     *
     * @param request
     * @return
     */
    List<AppTrainDTO> selectByParam(AppTrainListParam request);

    /**
     * 删除培训记录
     *
     * @param trainId
     */
    int deleteLogicById(@Param("trainId") Integer trainId);

    /**
     * 完成培训
     *
     * @param trainId
     */
    int updateState(@Param("trainId") Integer trainId);

    /**
     * 完成同步
     *
     * @param trainId
     * @return
     */
    int updateSyncFlag(@Param("trainId") Integer trainId, @Param("syncFlag") Integer syncFlag);

    /**
     * 查询人员培训列表
     *
     * @param empId
     * @return
     */
    List<AppTrainEmpDTO> selectByEmpId(Integer empId);

    /**
     * 根据培训guid查找数据
     *
     * @param guid
     * @return
     */
    AppTrain selectByGuid(String guid);

    /**
     * 查询项目培训统计
     *
     * @param deptId
     * @return
     */
    List<AppTrainStatDTO> selectTrainStat(@Param("deptId") Integer deptId);

    /**
     * 查询企业培训统计
     *
     * @param deptIds
     * @return
     */
    List<AppTrainStatDTO> selectEnterpriseTrainStat(@Param("deptIds") Collection<Integer> deptIds);
}