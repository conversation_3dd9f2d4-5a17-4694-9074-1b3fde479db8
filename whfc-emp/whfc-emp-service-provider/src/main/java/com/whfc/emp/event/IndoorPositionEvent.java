package com.whfc.emp.event;

import com.whfc.emp.entity.AppIndoorPositionTag;
import org.springframework.context.ApplicationEvent;

/**
 * 室内定位event
 *
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/7/18 9:37
 */
public class IndoorPositionEvent extends ApplicationEvent {

    private AppIndoorPositionTag tag;

    public IndoorPositionEvent(AppIndoorPositionTag tag) {
        super(tag);
        this.tag = tag;
    }

    public AppIndoorPositionTag getTag() {
        return tag;
    }

    public void setTag(AppIndoorPositionTag tag) {
        this.tag = tag;
    }
}
