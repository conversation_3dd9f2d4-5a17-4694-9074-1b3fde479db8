package com.whfc.emp.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.whfc.XxlJobConfig;
import com.whfc.common.enums.AppHomeDeviceType;
import com.whfc.common.enums.AppWarnType;
import com.whfc.common.enums.DeviceType;
import com.whfc.common.redis.RedisConst;
import com.whfc.common.redis.RedisService;
import com.whfc.common.util.DateUtil;
import com.whfc.emp.dao.AppEmpDataMapper;
import com.whfc.emp.dao.AppEmpDeviceMapper;
import com.whfc.emp.dao.AppEmpWarnMapper;
import com.whfc.emp.manager.AppEmpDataManager;
import com.whfc.entity.dto.board.AppDeviceStatDTO;
import com.whfc.entity.dto.warn.AppWarnRuleType;
import com.whfc.fuum.dto.SysDeptDTO;
import com.whfc.fuum.service.SysDeptService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description: 人员数据定时任务
 * @author: xugcheng
 * @version: 1.0
 * @date: 2019/9/9 10:44
 */
@Component
@ConditionalOnBean(XxlJobConfig.class)
public class EmpDataJob {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private AppEmpDataMapper appEmpDataMapper;

    @Autowired
    private AppEmpDataManager appEmpDataManager;

    @DubboReference(interfaceClass = SysDeptService.class, version = "1.0.0")
    private SysDeptService sysDeptService;


    @Autowired
    private AppEmpDeviceMapper appEmpDeviceMapper;

    @Autowired
    private AppEmpWarnMapper appEmpWarnMapper;


    @Autowired
    private RedisService redisService;

    /**
     * 刷新安全帽在线状态
     */
    @XxlJob("refreshEmpDeviceNetState")
    public void refreshEmpDeviceNetState() {
        try {
            int count = appEmpDataMapper.batchUpdateNetStateOffline();
            logger.info("本次人员离线数量:{}", count);
        } catch (Exception ex) {
            logger.error("刷新设备在线状态任务,出现异常", ex);
        }
    }

    /**
     * 刷新人员考勤状态(初始化)
     */
    @XxlJob("refreshAttendState")
    public void refreshAttendState() {
        try {
            int count = appEmpDataMapper.batchUpdateAttendStateAbsence();
            XxlJobHelper.log("本次人员缺勤数量:{}", count);
        } catch (Exception ex) {
            XxlJobHelper.handleFail("刷新人员数据的考勤状态,出现异常 error:" + ex.getMessage());
        }
    }

    /**
     * 刷新人员每天数据
     */
    @XxlJob("empDayDataTask")
    public void empDayDataTask() {
        try {
            XxlJobHelper.log("刷新人员每天数据");
            String paramStr = XxlJobHelper.getJobParam();
            Date now = new Date();
            if (StringUtils.isNotEmpty(paramStr)) {
                JSONObject param = JSONObject.parseObject(paramStr);
                if (param.containsKey("date")) {
                    now = param.getDate("date");
                }
            }

            Date today = DateUtil.getDate(now);
            Date yesterday = DateUtil.addDays(today, -1);
            // 查询项目列表
            List<SysDeptDTO> deptList = sysDeptService.getAllProjectList();
            for (SysDeptDTO dept : deptList) {
                // 初始化今天统计数据
                appEmpDataManager.initEmpDayData(dept.getId(), today);
                // 更新昨日统计数据
                appEmpDataManager.statEmpDayData(dept.getId(), yesterday);
            }
        } catch (Exception ex) {
            XxlJobHelper.handleFail("刷新人员每天的统计数据,出现异常 error:" + ex.getMessage());
        }
    }

    @XxlJob("emp-device-state-cache")
    public void cacheEmpDeviceState() {
        // 查询人员设备在线情况
        List<AppDeviceStatDTO> appDeviceStatList = appEmpDeviceMapper.countOnlineDevice();
        if (!CollectionUtils.isEmpty(appDeviceStatList)) {
            // 保存到缓存中
            for (AppDeviceStatDTO appDeviceStatDTO : appDeviceStatList) {
                logger.info("缓存设备在线状态:{}", appDeviceStatDTO);
                if (DeviceType.helmet.getValue().equals(appDeviceStatDTO.getDeviceType())) {
                    // 安全帽

                } else if (DeviceType.smart_bracelet.getValue().equals(appDeviceStatDTO.getDeviceType())) {
                    // 智能手环
                    redisService.hSet(String.format(RedisConst.BOARD_DEVICE_STAT, appDeviceStatDTO.getDeptId()),
                            AppHomeDeviceType.EMP_BRACELET.getCode(), JSON.toJSONString(appDeviceStatDTO));
                }
            }
        }

        Set<Integer> deptIds = appDeviceStatList.stream().map(AppDeviceStatDTO::getDeptId).collect(Collectors.toSet());

        // 查询设备报警情况
        // 默认当天
        Date now = cn.hutool.core.date.DateUtil.date();
        Date startTime = cn.hutool.core.date.DateUtil.beginOfDay(now);
        Date endTime = cn.hutool.core.date.DateUtil.endOfDay(now);
        // 查询报警情况
        List<AppWarnRuleType> appWarnRuleTypeList = appEmpWarnMapper.countWarnRecord(startTime, endTime);

        Map<Integer, AppWarnRuleType> warnRuleTypeMap = appWarnRuleTypeList
                .stream().collect(Collectors.toMap(AppWarnRuleType::getDeptId, v -> v));

        for (Integer deptId : deptIds) {
            AppWarnRuleType warnRuleType = warnRuleTypeMap.get(deptId);
            if (warnRuleType != null) {
                logger.info("缓存设备报警情况:{}", warnRuleType);
                redisService.hSet(String.format(RedisConst.BOARD_WARN_STAT, warnRuleType.getDeptId()),
                        AppWarnType.WARN_EMP_BRACELET.getCode(), JSON.toJSONString(warnRuleType));
            } else {
                redisService.hDelete(String.format(RedisConst.BOARD_WARN_STAT, deptId), AppWarnType.WARN_EMP_BRACELET.getCode());
            }
        }
    }
}
