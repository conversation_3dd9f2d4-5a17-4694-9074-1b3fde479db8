package com.whfc.emp.factory.impl;

import com.whfc.common.enums.I18nErrorCode;
import com.whfc.common.exception.BizException;
import com.whfc.common.face.szyc.SzycConst;
import com.whfc.common.geometry.Point;
import com.whfc.common.result.ResultEnum;
import com.whfc.emp.dao.AppFaceGateMapper;
import com.whfc.emp.entity.AppFaceGate;
import com.whfc.emp.entity.AppFaceGateConfig;
import com.whfc.emp.factory.FaceGateManager;
import com.whfc.emp.param.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.integration.core.MessageProducer;
import org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.MessageFormat;

/**
 * 海清视讯 MQTT 住建局版本
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/18 18:25
 */
@Service("mqttHqsxJjzFaceGateManagerImpl")
public class MqttHqsxJjzFaceGateManagerImpl implements FaceGateManager {

    @Value("${mqtt.enabled}")
    private Boolean mqttEnable;

    @Autowired
    private AppFaceGateMapper appFaceGateMapper;

    @Autowired(required = false)
    private MessageProducer messageProducer;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(AppFaceGateAddParam request, AppFaceGateConfig config) {
        String deviceKey = request.getDeviceKey();
        String name = request.getName();
        Integer deptId = request.getDeptId();
        Point point = request.getPoint();

        AppFaceGate record = new AppFaceGate();
        record.setDeviceKey(deviceKey);
        record.setDeptId(deptId);
        record.setName(name);
        record.setDirection(request.getDirection());
        record.setPlatform(request.getPlatform());
        record.setModel(request.getModel());
        // 增加闸机位置信息
        if (point != null) {
            Double lat = point.getLat();
            Double lng = point.getLng();
            record.setLng(lng);
            record.setLat(lat);
        }
        appFaceGateMapper.insertSelective(record);
        if (!mqttEnable) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.EMP_BE_030.getCode());
        }
        //订阅闸机主题
        MqttPahoMessageDrivenChannelAdapter adapter = (MqttPahoMessageDrivenChannelAdapter) messageProducer;
        String rec = MessageFormat.format(SzycConst.TOPIC_REC, deviceKey);
        adapter.addTopic(rec, 0);
        // 回复平台
        String ack = MessageFormat.format(SzycConst.TOPIC_ACK, deviceKey);
        adapter.addTopic(ack, 0);
        // 陌生人抓拍记录
        String snap = MessageFormat.format(SzycConst.TOPIC_SNAP, deviceKey);
        adapter.addTopic(snap, 0);
        // 二维码扫码信息
        String qrCode = MessageFormat.format(SzycConst.TOPIC_QRCODE, deviceKey);
        adapter.addTopic(qrCode, 0);
    }

    @Override
    public void del(Integer faceGateId, AppFaceGateConfig config) {
        appFaceGateMapper.deleteLogicById(faceGateId);
    }

    @Override
    public String faceGateGrantEmdAdd(FaceGateGrantEmdAddParam request, AppFaceGateConfig config) {
        return null;
    }

    @Override
    public String faceGateGrantEmdImgAdd(FaceGateGrantEmdImgAddParam request, AppFaceGateConfig config) {
        return null;
    }

    @Override
    public String faceGateGrantEmdAuth(FaceGateGrantEmdAuthParam request, AppFaceGateConfig config) {
        return null;
    }

    @Override
    public void faceGateBatchGrantEmpAuth(AppFaceGateGrantEmpParam request, AppFaceGateConfig config) {
    }

    @Override
    public void faceGateRevokeEmp(AppFaceGateGrantEmpParam request, AppFaceGateConfig config) {

    }

    @Override
    public String deviceAuthorizationPerson(String deviceKey, String name, String imgUrl, AppFaceGateConfig config) {
        return null;
    }

    @Override
    public String deviceAuthorizationCancel(String deviceKey, String personGuid, AppFaceGateConfig config) {
        return null;
    }

    @Override
    public void openDoor(String deviceKey, AppFaceGateConfig config) {

    }

    @Override
    public void sync(Integer faceGateId, AppFaceGateConfig config) {

    }

    @Override
    public String getToken(AppFaceGateConfig config) {
        return null;
    }
}
