package com.whfc.emp.redis.impl;

import com.whfc.emp.redis.FaceGateAttendRedisDao;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-08-16 11:23
 */
@Repository
public class FaceGateAttendRedisDaoImpl implements FaceGateAttendRedisDao {

    public static final String REDIS_KEY = "face-gate-time::%s::%s";

    /**
     * 默认过期时间 1天
     */
    public static final long TIMEOUT = 24 * 60;

    @Autowired
    private StringRedisTemplate redisTemplate;


    @Override
    public void setShowTime(String deviceKey, String personGuid, Date time) {
        if (StringUtils.isBlank(deviceKey) || StringUtils.isBlank(personGuid) || time == null) {
            return;
        }
        String key = String.format(REDIS_KEY, deviceKey, personGuid);
        redisTemplate.opsForValue().set(key, time.getTime() + "", TIMEOUT, TimeUnit.MINUTES);
    }

    @Override
    public Date getShowTime(String deviceKey, String personGuid) {
        if (StringUtils.isBlank(deviceKey) || StringUtils.isBlank(personGuid)) {
            return null;
        }
        String key = String.format(REDIS_KEY, deviceKey, personGuid);
        String value = redisTemplate.opsForValue().get(key);
        if (StringUtils.isNotBlank(value)) {
            long time = Long.parseLong(value);
            return new Date(time);
        }
        return null;
    }
}
