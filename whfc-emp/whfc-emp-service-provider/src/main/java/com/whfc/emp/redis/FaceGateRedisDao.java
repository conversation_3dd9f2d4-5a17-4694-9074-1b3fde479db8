package com.whfc.emp.redis;

import com.whfc.emp.dto.AppFaceGateDTO;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-08-16 15:24
 */
public interface FaceGateRedisDao {

    /**
     * 设置闸机缓存
     *
     * @param deviceKey   闸机序列表
     * @param faceGateDTO 闸机信息
     */
    void set(String deviceKey, AppFaceGateDTO faceGateDTO);

    /**
     * 获取闸机缓存
     *
     * @param deviceKey 闸机序列号
     * @return 闸机信息
     */
    AppFaceGateDTO get(String deviceKey);


    /**
     * 删除闸机缓存
     *
     * @param deviceKey 闸机序列号
     */
    void del(String deviceKey);


}
