package com.whfc.emp.manager.warn;

import com.alibaba.fastjson2.JSON;
import com.whfc.emp.dto.EmpWarnCheckDTO;
import com.whfc.emp.dto.EmpWarnRuleDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 低电量报警
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/7
 */
@Component(value = "appEmpWarnMgrLowPower")
public class AppEmpWarnMgrLowPower extends AppEmpWarnMgrBase implements AppEmpWarnMgr {


    private static final String PARAM_NAME = "value";

    @Override
    public boolean checkValue(EmpWarnCheckDTO checkDTO, EmpWarnRuleDTO ruleDTO) {
        Integer batteryPower = checkDTO.getBatteryPower();
        if (batteryPower == null) {
            return false;
        }
        String ruleParam = ruleDTO.getRuleParam();
        if (StringUtils.isBlank(ruleParam)) {
            return false;
        }
        // 校验电量是否低于指定值
        Integer powerValue = JSON.parseObject(ruleParam).getInteger(PARAM_NAME);
        if (powerValue != null && batteryPower < powerValue) {
            checkDTO.setTriggerKey(batteryPower + "");
            return true;
        }
        return false;
    }
}
