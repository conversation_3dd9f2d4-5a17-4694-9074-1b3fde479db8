package com.whfc.emp.queue;

import com.alibaba.fastjson.JSONObject;
import com.whfc.common.constant.QueueConst;
import com.whfc.common.enums.DevicePlatform;
import com.whfc.common.iot.cncit.entity.CncitUpMsgS;
import com.whfc.common.third.map.MapApi;
import com.whfc.common.third.map.MapApiFactory;
import com.whfc.common.third.map.MapLoc;
import com.whfc.common.util.Gps;
import com.whfc.emp.entity.AppDeviceCardLog;
import com.whfc.emp.manager.AppEmpDataManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2019/12/2 11:59
 */
@Component
@RabbitListener(queuesToDeclare = {@Queue(name = QueueConst.EMP_HELMET_DATA)}, concurrency = "1-2")
public class HelmetDataQueueMessageListener {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private AppEmpDataManager appEmpDataManager;

    @Autowired
    private MapApiFactory mapApiFactory;

    @RabbitHandler
    public void process(String msg) {
        try {
            logger.info("helmet|data|{}", msg);
            CncitUpMsgS reqMsg = JSONObject.parseObject(msg, CncitUpMsgS.class);

            //坐标系转换
            MapApi mapApi = mapApiFactory.getMapApi();
            Gps gcj02Gps = mapApi.translateToGcj02(reqMsg.getLngWgs84(), reqMsg.getLatWgs84());
            Double lng = gcj02Gps.getLng();
            Double lat = gcj02Gps.getLat();
            MapLoc loc = mapApi.geocode(lng, lat);

            AppDeviceCardLog cardLog = new AppDeviceCardLog();
            BeanUtils.copyProperties(reqMsg, cardLog);
            cardLog.setLng(lng);
            cardLog.setLat(lat);
            cardLog.setLocation(loc.getAddress());
            cardLog.setPlatform(DevicePlatform.cncit.name());
            cardLog.setDeviceCode(reqMsg.getDeviceCode());
            appEmpDataManager.addEmpDeviceData(cardLog);
        } catch (Exception ex) {
            logger.error("helmet|data,消息处理失败", ex);
        }
    }
}
