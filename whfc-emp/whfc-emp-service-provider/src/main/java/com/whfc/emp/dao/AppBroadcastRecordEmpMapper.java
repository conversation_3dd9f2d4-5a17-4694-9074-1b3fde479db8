package com.whfc.emp.dao;

import com.whfc.emp.dto.AppBroadcastRecordEmpDTO;
import com.whfc.emp.entity.AppBroadcastRecordEmp;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AppBroadcastRecordEmpMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppBroadcastRecordEmp record);

    int insertSelective(AppBroadcastRecordEmp record);

    AppBroadcastRecordEmp selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppBroadcastRecordEmp record);

    int updateByPrimaryKey(AppBroadcastRecordEmp record);

    /**
     * 更新消息状态
     *
     * @param id    ID
     * @param value 状态
     */
    void updateState(@Param("id") Integer id, @Param("state") Integer value);

    /**
     * 根据设备ID查找未完成的记录 未完成是指状态为：未发送、已发送
     *
     * @param deviceId 设备ID
     * @return 记录列表
     */
    List<AppBroadcastRecordEmp> selectUndoneByDeviceId(@Param("deviceId") Integer deviceId);

    /**
     * 获取未完成记录列表 未完成是指状态为：未发送、已发送
     *
     * @return 记录列表
     */
    List<AppBroadcastRecordEmpDTO> selectUndoneRecordEmpList();

    /**
     * 根据记录ID删除记录
     * @param id
     */
    void logicDelId(@Param("id") Integer id);

    /**
     * 查询发送给人员的广播
     * @param empId
     * @return
     */
    List<AppBroadcastRecordEmpDTO> selectBroadcastRecordList(@Param("empId") Integer empId);

}