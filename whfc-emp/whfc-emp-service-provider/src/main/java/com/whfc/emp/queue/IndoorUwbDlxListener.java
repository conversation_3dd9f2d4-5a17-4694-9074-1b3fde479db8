package com.whfc.emp.queue;

import com.whfc.common.constant.QueueConst;
import com.whfc.common.spring.AppContextUtil;
import com.whfc.common.util.JSONUtil;
import com.whfc.emp.dao.AppIndoorPositionTagMapper;
import com.whfc.emp.dto.indoor.AppIndoorUwbTTL;
import com.whfc.emp.entity.AppIndoorPositionTag;
import com.whfc.emp.event.IndoorPositionEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/1/4 11:05
 */
@Component
@RabbitListener(queues = QueueConst.UWB_DLX_QUEUE)
public class IndoorUwbDlxListener {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private AppIndoorPositionTagMapper indoorPositionTagMapper;

    @RabbitHandler
    public void process(String msg) {
        try {
            logger.info("indoor|uwb|dlx|{}", msg);
            AppIndoorUwbTTL ttlDTO = JSONUtil.parseObject(msg, AppIndoorUwbTTL.class);
            if (ttlDTO != null) {
                Integer deptId = ttlDTO.getDeptId();
                Integer empId = ttlDTO.getEmpId();
                Integer holeDistance = ttlDTO.getHoleDistance();
                Date now = new Date();
                //查询人员绑定标签
                AppIndoorPositionTag tag = indoorPositionTagMapper.selectByDeptIdAndEmpId(deptId, empId);
                if (tag != null) {
                    tag.setTime(now);
                    tag.setDistance1(holeDistance);
                    indoorPositionTagMapper.updateByPrimaryKeySelective(tag);

                    //已绑定人员的标签,发布事件
                    AppContextUtil.context().publishEvent(new IndoorPositionEvent(tag));
                }
            }
        } catch (Exception ex) {
            logger.error("indoor|uwb|dlx,消息处理失败", ex);
        }
    }
}
