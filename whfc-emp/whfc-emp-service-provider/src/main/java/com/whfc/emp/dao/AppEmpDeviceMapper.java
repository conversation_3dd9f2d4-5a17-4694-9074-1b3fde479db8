package com.whfc.emp.dao;

import com.whfc.emp.dto.device.AppEmpDeviceDTO;
import com.whfc.emp.entity.AppEmpDevice;
import com.whfc.emp.param.AppEmpDeviceQueryParam;
import com.whfc.entity.dto.board.AppDeviceStatDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Mapper
public interface AppEmpDeviceMapper {

    int insertSelective(AppEmpDevice record);

    AppEmpDevice selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppEmpDevice record);

    /**
     * 查询人员是否绑定
     *
     * @param empId
     * @return
     */
    Integer countByEmpId(@Param("empId") Integer empId);

    /**
     * 查询人员硬件列表
     *
     * @param param 组织机构ID
     * @return
     */
    List<AppEmpDeviceDTO> selectList(@Param("param") AppEmpDeviceQueryParam param);

    /**
     * 查询人员硬件列表在线离线数量
     *
     * @param param
     * @return
     */
    Map<String, Object> countDeviceNetState(@Param("param") AppEmpDeviceQueryParam param);


    /**
     * 查询未初始化的数据
     *
     * @return
     */
    List<AppEmpDevice> selectNoInitData();


    /**
     * 根据sn查询人员硬件
     *
     * @param sn
     * @return
     */
    AppEmpDevice selectByPlatformAndSn(@Param("platform") String platform,
                                       @Param("sn") String sn);

    /**
     * 根据guid查询人员硬件
     *
     * @param guid
     * @return
     */
    AppEmpDevice selectByGuid(@Param("guid") String guid);

    /**
     * 逻辑删除人员硬件
     *
     * @param guid
     */
    void logicDel(@Param("guid") String guid);

    /**
     * 更新人员硬件绑定状态
     *
     * @param empId
     * @param bindTime
     * @param username
     * @param bindFlag
     */
    void updateBindState(@Param("id") Integer id,
                         @Param("empId") Integer empId,
                         @Param("bindTime") Date bindTime,
                         @Param("username") String username,
                         @Param("bindFlag") Integer bindFlag);

    /**
     * 根据人员ID和设备类型查询人员硬件
     *
     * @param empId
     * @param deviceType
     * @return
     */
    AppEmpDevice selectByEmpIdAndType(@Param("empId") Integer empId,
                                      @Param("deviceType") Integer deviceType);

    /**
     * 根据人员ID查询人员硬件
     *
     * @param empId
     * @return
     */
    AppEmpDeviceDTO selectByEmpId(@Param("empId") Integer empId);

    /**
     * 更新人员硬件状态
     *
     * @param id
     * @param netState
     * @param batteryPower
     * @param time
     */
    void updateDeviceState(@Param("id") Integer id,
                           @Param("netState") Integer netState,
                           @Param("batteryPower") Integer batteryPower,
                           @Param("time") Date time);

    /**
     * 查询在线设备
     *
     * @return
     */
    List<AppDeviceStatDTO> countOnlineDevice();

    /**
     * 根据组织机构ID查询设备数量
     * @param deptId
     * @return
     */
    AppDeviceStatDTO countDeviceByDeptId(@Param("deptId") Integer deptId);

}