package com.whfc.emp.dao;

import com.whfc.emp.entity.AppEmpRiskEmp;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/28 18:15
 */
@Repository
public interface AppEmpRiskEmpMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppEmpRiskEmp record);

    int insertSelective(AppEmpRiskEmp record);

    AppEmpRiskEmp selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppEmpRiskEmp record);

    int updateByPrimaryKey(AppEmpRiskEmp record);

    /**
     * 查找风险告知书人员签名图片
     *
     * @param riskId 风险告知书ID
     * @param empId  人员ID
     * @return 风险告知书人员签名图片
     */
    AppEmpRiskEmp selectEmpList(@Param("riskId") Integer riskId, @Param("empId") Integer empId);
}