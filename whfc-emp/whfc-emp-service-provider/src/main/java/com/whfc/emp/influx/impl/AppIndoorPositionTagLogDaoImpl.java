package com.whfc.emp.influx.impl;

import com.whfc.common.enums.DelFlag;
import com.whfc.common.result.PageData;
import com.whfc.common.util.InfluxUtil;
import com.whfc.emp.constant.IndoorMeasurement;
import com.whfc.emp.dto.AppIndoorPositionTagLogDTO;
import com.whfc.emp.entity.AppIndoorPositionTagLog;
import com.whfc.emp.influx.AppIndoorPositionTagLogDao;
import org.influxdb.InfluxDB;
import org.influxdb.dto.BatchPoints;
import org.influxdb.dto.Point;
import org.influxdb.dto.Query;
import org.influxdb.dto.QueryResult;
import org.influxdb.impl.InfluxDBMapper;
import org.influxdb.querybuilder.Select;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.ObjectUtils;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static org.influxdb.querybuilder.BuiltQuery.QueryBuilder.*;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/12/8 11:46
 */
@Repository
public class AppIndoorPositionTagLogDaoImpl implements AppIndoorPositionTagLogDao {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 数据库名称
     */
    private static final String database = IndoorMeasurement.DATABASE;

    /**
     * 表名
     */
    private static final String measurement = IndoorMeasurement.MEASUREMENT;

    /**
     * 保留策略:保存10年数据
     */
    private static final String retentionPolicy = IndoorMeasurement.RETENTION_POLICY;

    /**
     * 时间单位:秒
     */
    private static final TimeUnit timeUnit = TimeUnit.SECONDS;

    /**
     * 默认查询字段
     */
    private static String[] COLUMNS = new String[]{"time", "stationId", "x", "y", "lng", "lat", "distance", "batteryPower", "serverTime"};

    private static String[] BLE_COLUMNS = new String[]{"time", "stationId"};

    @Autowired
    private InfluxDB influxDB;

    @Autowired
    private InfluxDBMapper influxDBMapper;

    @Override
    public void insert(AppIndoorPositionTagLog record) {
        logger.debug("influxdb的insert方法");
        influxDB.setDatabase(database);
        influxDB.setRetentionPolicy(retentionPolicy);
        influxDB.write(this.buildDataPoint(record));
    }

    @Override
    public void batchInsert(List<AppIndoorPositionTagLog> records) {
        logger.debug("influxdb的batchInsert方法");
        BatchPoints.Builder batchBuiler = BatchPoints.builder();
        for (AppIndoorPositionTagLog record : records) {
            Point point = this.buildDataPoint(record);
            batchBuiler.point(point);
        }
        influxDB.setDatabase(database);
        influxDB.setRetentionPolicy(retentionPolicy);
        influxDB.write(batchBuiler.build());
    }

    @Override
    public List<AppIndoorPositionTagLogDTO> selectPositionLogList(Integer tagId, Date startTime, Date endTime) {
        Query query = select(BLE_COLUMNS).from(database, measurement)
                .where(eq("tagId", String.valueOf(tagId)))
                .and(gte("time", startTime.toInstant().toString()))
                .and(lte("time", endTime.toInstant().toString()))
                .orderBy(asc());
        logger.info(query.getCommand());
        List<AppIndoorPositionTagLogDTO> list = influxDBMapper.query(query, AppIndoorPositionTagLogDTO.class);
        this.transformTime(list);
        return list;
    }

    @Override
    public PageData<AppIndoorPositionTagLogDTO> selectPositionLogList(Integer tagId, Date startTime, Date endTime, Integer pageNum, Integer pageSize) {
        //统计总数量
        Query countQuery = select().count("serverTime")
                .from(database, measurement)
                .where(eq("tagId", String.valueOf(tagId)))
                .and(gte("time", startTime.toInstant().toString()))
                .and(lte("time", endTime.toInstant().toString()));
        logger.info(countQuery.getCommand());
        QueryResult queryResult = influxDB.query(countQuery, TimeUnit.MILLISECONDS);
        Map<String, Object> resultMap = InfluxUtil.parseQueryResultMap(queryResult);
        int total = Double.valueOf(resultMap.getOrDefault("count", 0).toString()).intValue();
        logger.info("数量总数量total={}", total);

        //分页查询
        int pages = (total / pageSize) + ((total % pageSize) == 0 ? 0 : 1);
        int offset = (pageNum - 1) * pageSize;
        int limit = pageSize;
        List<AppIndoorPositionTagLogDTO> list = Collections.EMPTY_LIST;
        if (pageNum <= pages) {
            Select select = select(BLE_COLUMNS).from(database, measurement)
                    .where(eq("tagId", String.valueOf(tagId)))
                    .and(eq("delFlag", DelFlag.UNDELETE.getValue()))
                    .andNested()
                    .and(gte("time", startTime.toInstant().toString()))
                    .and(lte("time", endTime.toInstant().toString()))
                    .close()
                    .orderBy(desc());
            Query query = offset == 0 ? select.limit(limit) : select.limit(limit, offset);
            logger.info(query.getCommand());
            list = influxDBMapper.query(query, AppIndoorPositionTagLogDTO.class);
            this.transformTime(list);
        }

        PageData<AppIndoorPositionTagLogDTO> pageData = new PageData<>();
        pageData.setList(list);
        pageData.setTotal((long) total);
        pageData.setPages(pages);
        pageData.setPageNum(pageNum);
        pageData.setPageSize(pageSize);
        return pageData;
    }


    /**
     * 构建数据点
     *
     * @param record
     * @return
     */
    private Point buildDataPoint(AppIndoorPositionTagLog record) {
        logger.debug("influxdb的buildDataPoint方法");
        Point.Builder builder = Point
                .measurement(measurement)
                .time(record.getTime().toInstant().getEpochSecond(), timeUnit);
        if (!ObjectUtils.isEmpty(record.getTagId())) {
            builder.tag("tagId", String.valueOf(record.getTagId()));
        }
        if (!ObjectUtils.isEmpty(record.getMapId())) {
            builder.addField("mapId", record.getMapId());
        }
        if (!ObjectUtils.isEmpty(record.getStationId())) {
            builder.addField("stationId", record.getStationId());
        }
        if (!ObjectUtils.isEmpty(record.getX())) {
            builder.addField("x", record.getX());
        }
        if (!ObjectUtils.isEmpty(record.getY())) {
            builder.addField("y", record.getY());
        }
        if (!ObjectUtils.isEmpty(record.getLng())) {
            builder.addField("lng", record.getLng());
        }
        if (!ObjectUtils.isEmpty(record.getLat())) {
            builder.addField("lat", record.getLat());
        }
        if (!ObjectUtils.isEmpty(record.getDistance())) {
            builder.addField("distance", record.getDistance());
        }
        if (!ObjectUtils.isEmpty(record.getBatteryPower())) {
            builder.addField("batteryPower", record.getBatteryPower());
        }
        if (!ObjectUtils.isEmpty(record.getDelFlag())) {
            builder.addField("delFlag", record.getDelFlag());
        }
        builder.addField("serverTime", System.currentTimeMillis() / 1000);
        builder.addField("ts", record.getTime().getTime() / 1000);
        return builder.build();
    }

    /**
     * 时间类型转换
     *
     * @param list
     */
    private void transformTime(List<AppIndoorPositionTagLogDTO> list) {
        for (AppIndoorPositionTagLogDTO dto : list) {
            dto.setTime(Date.from(dto.getDeviceTime()));
            if (!ObjectUtils.isEmpty(dto.getServerTime())) {
                dto.setCreateTime(new Date(dto.getServerTime() * 1000));
            }
        }
    }
}
