package com.whfc.emp.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.whfc.common.enums.I18nErrorCode;
import com.whfc.common.exception.BizException;
import com.whfc.common.result.PageData;
import com.whfc.common.result.ResultEnum;
import com.whfc.common.util.PageUtil;
import com.whfc.emp.dao.AppEmpGroupMapper;
import com.whfc.emp.dao.AppEmpMapper;
import com.whfc.emp.dto.AppEmpCorpDTO;
import com.whfc.emp.dto.AppGroupDTO;
import com.whfc.emp.entity.AppEmpGroup;
import com.whfc.emp.param.AppEmpGroupAddParam;
import com.whfc.emp.param.AppEmpGroupEditParam;
import com.whfc.emp.service.AppGroupService;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 人员班组服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/12/29 10:02
 */
@DubboService(interfaceClass = AppGroupService.class, version = "1.0.0", timeout = 60 * 1000)
public class AppGroupServiceImpl implements AppGroupService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private AppEmpGroupMapper appEmpGroupMapper;

    @Autowired
    private AppEmpMapper appEmpMapper;

    @Override
    public PageData<AppGroupDTO> list(Integer projectId, Integer pageNum, Integer pageSize, String keyword, Integer corpId) {
        logger.info("班组列表,projectId:{},pageNum:{},pageSize:{},keyword:{},corpId:{}", projectId, pageNum, pageSize, keyword, corpId);
        PageHelper.startPage(pageNum, pageSize);
        List<AppGroupDTO> list = appEmpGroupMapper.selectByProjectId(projectId, corpId, keyword);
        return PageUtil.pageData(PageInfo.of(list));
    }

    @Override
    public List<AppGroupDTO> groupListByCorpId(Integer corpId) {
        logger.info("根据合作单位查找班组列表,corpId:{}", corpId);
        return appEmpGroupMapper.selectByCorpId(corpId);
    }

    @Override
    public List<AppGroupDTO> groupListByProjectId(Integer projectId) {
        logger.info("根据项目id查找班组,projectId:{}", projectId);
        return appEmpGroupMapper.selectByProjectId(projectId, null, null);
    }

    @Override
    public List<AppGroupDTO> list(Integer deptId, String keyword) throws BizException {
        return appEmpGroupMapper.selectByDeptId(deptId, keyword);
    }

    @Override
    public List<AppEmpCorpDTO> groupListByDeptId(Integer deptId, String keyword) {
        logger.info("根据组织机构查找班组列表,deptId:{}，keyword：{}", deptId, keyword);
        List<AppGroupDTO> list = appEmpGroupMapper.selectByDeptId(deptId, keyword);
        List<AppEmpCorpDTO> data = new ArrayList<>();
        list.forEach(appGroupDTO -> {
            AppEmpCorpDTO appEmpCorpDTO = new AppEmpCorpDTO();
            appEmpCorpDTO.setCorpId(appGroupDTO.getCorpId());
            appEmpCorpDTO.setCorpName(appGroupDTO.getCorpName());
            appEmpCorpDTO.setGroupList(new ArrayList<>());
            data.add(appEmpCorpDTO);
        });
        List<AppEmpCorpDTO> result = data.stream().distinct().collect(Collectors.toList());

        list.forEach(appGroupDTO -> {
            Integer corpId = appGroupDTO.getCorpId();
            result.forEach(appEmpCorpDTO -> {
                Integer corpId1 = appEmpCorpDTO.getCorpId();
                if (corpId.equals(corpId1)) {
                    appGroupDTO.setCorpId(null);
                    appGroupDTO.setCorpName(null);
                    appEmpCorpDTO.getGroupList().add(appGroupDTO);
                }
            });
        });
        return result;
    }

    @Override
    public List<Integer> corpIdList(List<Integer> deptIds) throws BizException {
        return appEmpGroupMapper.selectCorpIdList(deptIds);
    }

    @Override
    public void add(AppEmpGroupAddParam param) {
        logger.info("添加班组,param:{}", param);
        AppEmpGroup record = new AppEmpGroup();
        BeanUtils.copyProperties(param, record);
        Integer deptId = param.getDeptId();
        String groupCode = param.getGroupCode();
        if (StringUtils.isNotEmpty(groupCode)) {
            AppEmpGroup appEmpGroup = appEmpGroupMapper.selectGroupCodeAndDeptId(groupCode, deptId);
            if (appEmpGroup != null) {
                throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_024.getCode());
            }
        }
        AppEmpGroup appEmpGroup = appEmpGroupMapper.selectByCorpIdAndName(param.getCorpId(), param.getGroupName());
        if (appEmpGroup != null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.EMP_BE_026.getCode());
        }
        appEmpGroupMapper.insertSelective(record);
    }

    @Override
    public void edit(AppEmpGroupEditParam param) {
        logger.info("编辑班组,param:{}", param);
        Integer groupId = param.getGroupId();
        String groupCode = param.getGroupCode();
        AppEmpGroup appEmpGroup = appEmpGroupMapper.selectByPrimaryKey(groupId);
        if (appEmpGroup == null) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_002.getCode());
        }
        Integer deptId = appEmpGroup.getDeptId();
        if (StringUtils.isNotEmpty(groupCode)) {
            AppEmpGroup empGroup = appEmpGroupMapper.selectGroupCodeAndDeptId(groupCode, deptId);
            if (empGroup != null && !groupId.equals(empGroup.getId())) {
                throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_024.getCode());
            }
        }
        BeanUtils.copyProperties(param, appEmpGroup);
        appEmpGroupMapper.updateByPrimaryKeySelective(appEmpGroup);
    }

    @Override
    public void del(Integer groupId) {
        logger.info("删除班组,groupId:{}", groupId);
        AppEmpGroup group = appEmpGroupMapper.selectByPrimaryKey(groupId);
        if (group == null) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_002.getCode());
        }
        int count = appEmpMapper.countByGroup(group.getDeptId(), groupId);
        if (count > 0) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_025.getCode());
        }
        appEmpGroupMapper.deleteLogicById(groupId);
    }
}
