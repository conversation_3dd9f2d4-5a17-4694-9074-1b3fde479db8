package com.whfc.emp.dao;

import com.whfc.emp.entity.AppTrainPaper;
import org.springframework.stereotype.Repository;

/**
* <AUTHOR>
* @version 1.0
* @date 2021-08-26 18:41
*/
@Repository
public interface AppTrainPaperMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppTrainPaper record);

    int insertSelective(AppTrainPaper record);

    AppTrainPaper selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppTrainPaper record);

    int updateByPrimaryKey(AppTrainPaper record);
}