package com.whfc.emp.manager.warn;

import com.whfc.emp.dto.EmpWarnCheckDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * @Description: 人员报警管理器-安全帽硬件报警
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/10/10 15:53
 */
@Component(value = "appEmpWarnMgrHardwareDoff")
public class AppEmpWarnMgrHardwareDoff extends AppEmpWarnMgrHardware implements AppEmpWarnMgr {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Override
    public Integer getAlarmValue(EmpWarnCheckDTO checkDTO) {
        return checkDTO.getAlarmDoff();
    }
}
