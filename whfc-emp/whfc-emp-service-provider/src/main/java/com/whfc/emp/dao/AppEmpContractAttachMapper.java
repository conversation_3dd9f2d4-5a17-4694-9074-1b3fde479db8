package com.whfc.emp.dao;

import com.whfc.emp.dto.AppAttachDTO;
import com.whfc.emp.entity.AppEmpContractAttach;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AppEmpContractAttachMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppEmpContractAttach record);

    int insertSelective(AppEmpContractAttach record);

    AppEmpContractAttach selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppEmpContractAttach record);

    int updateByPrimaryKey(AppEmpContractAttach record);

    /**
     * 查找附件
     *
     * @param id
     * @return
     */
    List<AppAttachDTO> selectByContractId(@Param("id") Integer id);

    /**
     * 批量插入
     *
     * @param list
     */
    void batchInsert(@Param("list") List<AppEmpContractAttach> list);

    /**
     * 逻辑删除
     *
     * @param contractId
     */
    void deleteLogicByContractId(@Param("contractId")Integer contractId);
}