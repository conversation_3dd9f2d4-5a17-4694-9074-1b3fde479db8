package com.whfc.emp.manager;

import com.whfc.common.exception.BizException;
import com.whfc.emp.dto.AppFaceGateDTO;
import com.whfc.emp.param.EmpAttendSyncDataParam;
import com.whfc.emp.param.EmpDeviceSyncDataParam;
import com.whfc.emp.param.EmpGroupSyncDataParam;
import com.whfc.emp.param.EmpInfoSyncDataParam;

/**
 * @Description 闸机服务
 * <AUTHOR>
 * @Date 2021/1/7 14:08
 * @Version 1.0
 */
public interface AppFaceGateManager {

    /**
     * 处理闸机识别数据
     *
     * @param dataParam 考勤信息
     * @throws BizException 业务异常
     */
    void handleRec(EmpAttendSyncDataParam dataParam) throws BizException;

    /**
     * 闸机人员同步
     *
     * @param dataParam 人员信息
     * @throws BizException 业务异常
     */
    void handleEmpInfo(EmpInfoSyncDataParam dataParam) throws BizException;

    /**
     * 闸机班组同步
     *
     * @param dataParam 班组信息
     * @throws BizException 业务异常
     */
    void handleGroup(EmpGroupSyncDataParam dataParam) throws BizException;

    /**
     * 闸机设备同步
     *
     * @param dataParam 设备信息
     * @throws BizException 业务异常
     */
    void handleDevice(EmpDeviceSyncDataParam dataParam) throws BizException;

    /**
     * 闸机心跳
     *
     * @param deviceKey
     * @throws BizException
     */
    void handleHeart(String deviceKey) throws BizException;

    /**
     * 根据设备序列号查找闸机
     *
     * @param deviceKey 设备序列号
     * @return 闸机
     * @throws BizException 业务异常
     */
    AppFaceGateDTO getByDeviceKey(String deviceKey) throws BizException;

}
