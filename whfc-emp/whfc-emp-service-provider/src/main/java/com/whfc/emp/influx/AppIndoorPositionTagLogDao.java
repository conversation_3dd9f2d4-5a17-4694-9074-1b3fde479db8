package com.whfc.emp.influx;

import com.whfc.common.result.PageData;
import com.whfc.emp.dto.AppIndoorPositionTagLogDTO;
import com.whfc.emp.entity.AppIndoorPositionTagLog;

import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/12/8 11:43
 */
public interface AppIndoorPositionTagLogDao {

    /**
     * 插入数据
     *
     * @param record
     */
    void insert(AppIndoorPositionTagLog record);

    /**
     * 批量插入数据
     *
     * @param records
     */
    void batchInsert(List<AppIndoorPositionTagLog> records);

    /**
     * 查询定位日志
     *
     * @param tagId
     * @param startTime
     * @param endTime
     * @return
     */
    List<AppIndoorPositionTagLogDTO> selectPositionLogList(Integer tagId, Date startTime, Date endTime);

    /**
     * 查询定位日志
     *
     * @param tagId
     * @param startTime
     * @param endTime
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageData<AppIndoorPositionTagLogDTO> selectPositionLogList(Integer tagId, Date startTime, Date endTime, Integer pageNum, Integer pageSize);
}
