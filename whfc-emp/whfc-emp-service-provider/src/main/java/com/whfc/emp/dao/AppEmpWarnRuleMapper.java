package com.whfc.emp.dao;

import com.whfc.emp.dto.AppWarnRuleDTO;
import com.whfc.emp.dto.EmpWarnRuleDTO;
import com.whfc.emp.entity.AppEmpWarnRule;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AppEmpWarnRuleMapper {

    int insertSelective(AppEmpWarnRule record);

    AppEmpWarnRule selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppEmpWarnRule record);


    /**
     * 查询报警类别Id
     *
     * @param ruleType
     * @param deptId
     * @return
     */
    Integer selectByRuleType(@Param("ruleType") Integer ruleType, @Param("deptId") Integer deptId);

    /**
     * 查找所有的报警规则
     *
     * @param deptId
     * @param warnType
     * @return
     */
    List<AppWarnRuleDTO> selectRulesByDeptId(@Param("deptId") Integer deptId, @Param("warnType") Integer warnType);

    /**
     * 查询启用的报警规则
     *
     * @param deptId
     * @return
     */
    List<EmpWarnRuleDTO> selectRuleList(@Param("deptId") Integer deptId);

    /**
     * 批量添加
     *
     * @param list
     */
    void batchInsert(@Param("list") List<AppEmpWarnRule> list);

    /**
     * 启用/禁用报警规则
     *
     * @param ruleId
     * @param enableFlag
     */
    void updateEnableFlagByRuleId(@Param("ruleId") Integer ruleId, @Param("enableFlag") Integer enableFlag);

    /**
     * 逻辑删除
     *
     * @param ruleId
     */
    void deleteLogic(@Param("ruleId") Integer ruleId);
}