package com.whfc.emp.third.impl;

import com.alibaba.fastjson.JSONObject;
import com.whfc.common.constant.QueueConst;
import com.whfc.common.util.DateUtil;
import com.whfc.emp.entity.AppSync;
import com.whfc.emp.param.FaceGateRecordParam;
import com.whfc.emp.third.EmpThirdSync;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description: 人员信息-第三方同步-电视投屏APP
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/6/30 14:44
 */
@Service
public class EmpThirdSyncSca extends EmpThirdSyncBase implements EmpThirdSync {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private AmqpTemplate amqpTemplate;

    @Override
    public void syncEmpAttend(FaceGateRecordParam param, AppSync config) {
        String time = DateUtil.formatDateTime(param.getShowTime());
        Integer deptId = param.getDeptId();
        String personGuid = param.getPersonGuid();
        logger.info("开始同步sca,{},{},{}", deptId, personGuid, time);

        JSONObject msg = new JSONObject();
        msg.put("deptId", deptId);
        msg.put("showTime", time);
        msg.put("direction", param.getDirection());
        msg.put("empName", param.getEmpName());
        msg.put("photoUrl", param.getPicture());
        amqpTemplate.convertAndSend(QueueConst.SCA_DOWN_MSG, msg.toString());
    }
}
