package com.whfc.emp.mqtt;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.whfc.common.constant.QueueConst;
import com.whfc.common.face.ruitong.*;
import com.whfc.common.file.FileHandler;
import com.whfc.common.file.FilePathConfig;
import com.whfc.common.util.*;
import com.whfc.emp.dao.AppFaceGatePersonMapper;
import com.whfc.emp.dto.AppFaceGateDTO;
import com.whfc.emp.entity.AppFaceGatePerson;
import com.whfc.emp.enums.TaskType;
import com.whfc.emp.manager.AppFaceGateManager;
import com.whfc.emp.param.EmpAttendSyncDataParam;
import com.whfc.emp.redis.FaceGateAttendRedisDao;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 人脸识别数据处理(睿瞳)
 */
@Component
public class MqttMessageFaceRtProcessor implements MqttMessageProcessor {

    private static final Logger logger = org.slf4j.LoggerFactory.getLogger(MqttMessageFaceRtProcessor.class);

    /**
     * 同一个人同一个闸机刷脸时间间隔大于1分钟
     */
    private static final Integer INTERVAL_TIME = 1;

    //MQTT-RT topic前缀
    private static final String MQTT_RT_TOPIC_PREFIX = "openapi/mqtt/v1/";

    @Autowired
    private AppFaceGateManager appFaceGateManager;

    @Autowired
    private FaceGateAttendRedisDao faceGateAttendRedisDao;

    @Autowired
    private FilePathConfig filePathConfig;

    @Autowired
    private FileHandler fileHandler;

    @Autowired
    private AmqpTemplate amqpTemplate;

    @Autowired
    private AppFaceGatePersonMapper appFaceGatePersonMapper;

    @Autowired(required = false)
    private MqttConfig.MqttMessageSender mqttMessageSender;

    @Override
    public String getTopicPrefix() {
        return MQTT_RT_TOPIC_PREFIX;
    }

    @Override
    public List<String> getSubTopicList() {
        String upTopic = RtConst.getUpTopic();
        List<String> topicList = new ArrayList<>();
        topicList.add(upTopic);
        return topicList;
    }

    @Override
    public void processData(String topic, byte[] payload) {
        String message = new String(payload);
        logger.info("MQTT(睿瞳)事件监听器接收到消息,topic:{},message:{}", topic, message.length() > 500 ? message.substring(0, 500) : message);
        String deviceKey = topic.substring(MQTT_RT_TOPIC_PREFIX.length());
        JSONObject jsonObject = JSON.parseObject(message);
        String action = jsonObject.getString("action");
        switch (action) {
            case RtConst.ACTION_HEART:
                RtMsg rtMsg = JSONUtil.parseObject(message, RtMsg.class);
                this.handleHeartMsg(rtMsg);
                break;
            case RtConst.ACTION_UPLOAD_DETECT_RECORD:
                UploadDetectRecordMsg uploadDetectRecordMsg = JSONUtil.parseObject(message, UploadDetectRecordMsg.class);
                uploadDetectRecordMsg.setDeviceno(deviceKey);
                this.handleRecord(uploadDetectRecordMsg);
                break;
            case RtConst.ACTION_SYNC_DEVICE_WHITEINFO:
                SyncDeviceWhiteInfoMsg syncDeviceWhiteInfoMsg = JSONUtil.parseObject(message, SyncDeviceWhiteInfoMsg.class);
                syncDeviceWhiteInfoMsg.setDeviceno(deviceKey);
                this.handleAddPerson(syncDeviceWhiteInfoMsg);
                break;
            case RtConst.ACTION_DEL_DEVICE_WHITEINFO:
                DelDeviceWhiteInfoMsg delDeviceWhiteInfoMsg = JSONUtil.parseObject(message, DelDeviceWhiteInfoMsg.class);
                delDeviceWhiteInfoMsg.setDeviceno(deviceKey);
                this.handleDelPerson(delDeviceWhiteInfoMsg);
                break;
        }
    }

    /**
     * 心跳处理
     *
     * @param msg
     */
    private void handleHeartMsg(RtMsg msg) {
        appFaceGateManager.handleHeart(msg.getDeviceno());
    }

    /**
     * 人员授权处理
     *
     * @param msg
     */
    private void handleAddPerson(SyncDeviceWhiteInfoMsg msg) {
        String deviceKey = msg.getDeviceno();
        String idno = msg.getIdno();
        if (RtConst.SUCCESS.equals(msg.getCode())) {
            logger.info("人员授权成功, deviceKey:{}, idno:{}", deviceKey, idno);
            AppFaceGatePerson faceGatePerson = appFaceGatePersonMapper.selectByPersonGuidAndDeviceKey(idno, deviceKey);
            if (faceGatePerson != null) {
                faceGatePerson.setTaskType(TaskType.EMP_AUTH.getValue());
                faceGatePerson.setMessage(null);
                appFaceGatePersonMapper.updateByPrimaryKeySelective(faceGatePerson);
            }
        } else {
            logger.info("人员授权失败, deviceKey:{}, idno:{}", deviceKey, idno);
            AppFaceGatePerson faceGatePerson = appFaceGatePersonMapper.selectByPersonGuidAndDeviceKey(idno, deviceKey);
            if (faceGatePerson != null) {
                faceGatePerson.setTaskType(TaskType.EMP_AUTH_ERROR.getValue());
                faceGatePerson.setMessage("");
                appFaceGatePersonMapper.updateByPrimaryKeySelective(faceGatePerson);
            }
        }
    }

    /**
     * 人员取消授权处理
     *
     * @param msg
     */
    private void handleDelPerson(DelDeviceWhiteInfoMsg msg) {
        String deviceKey = msg.getDeviceno();
        String idno = msg.getIdno();
        List<String> personGuids = new ArrayList<>();
        personGuids.add(idno);
        if (RtConst.SUCCESS.equals(msg.getCode())) {
            logger.info("取消授权成功, deviceKey:{}, idno:{}", deviceKey, idno);
            appFaceGatePersonMapper.delBYDeviceKeyAndParsonGuid(deviceKey, personGuids);
        } else {
            logger.info("取消授权失败, deviceKey:{}, idno:{}", deviceKey, idno);
            appFaceGatePersonMapper.delBYDeviceKeyAndParsonGuid(deviceKey, personGuids);
        }
    }

    /**
     * 识别记录处理
     *
     * @param msg
     */
    private void handleRecord(UploadDetectRecordMsg msg) {

        try {
            //record-ack
            String deviceKey = msg.getDeviceno();
            String recordAckTopic = RtConst.getDownTopic(deviceKey);

            UploadDetectRecordMsg ackMsg = new UploadDetectRecordMsg();
            ackMsg.setAction(RtConst.ACTION_UPLOAD_DETECT_RECORD_UPFLAG);
            ackMsg.setCode(RtConst.SUCCESS);
            ackMsg.setTableID(msg.getData().getTableId());
            mqttMessageSender.sendToMqtt(recordAckTopic, JSONUtil.toString(ackMsg));


            UploadDetectRecord recRecord = msg.getData();
            logger.debug("MQTT处理考勤记录, recRecord:{}", JSONUtil.toString(recRecord));
            // (1)验证闸机-唯一标识
            String personGuid = recRecord.getIdNum();
            AppFaceGateDTO faceGateDTO = appFaceGateManager.getByDeviceKey(deviceKey);
            if (faceGateDTO == null) {
                logger.warn("考勤记录同步-闸机未入后台数据库, deviceKey:{} ", deviceKey);
                return;
            }
            // (2)验证时间-过滤重复记录
            Date time = DateUtil.parseDateTime(recRecord.getTime());
            //从缓存中获取上一次打卡记录
            Date latestShowTime = faceGateAttendRedisDao.getShowTime(deviceKey, personGuid);
            if (latestShowTime != null && DateUtil.addMinutes(latestShowTime, INTERVAL_TIME).after(time)) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                logger.info("两次刷脸时间相同, time:{}, latestShowTime:{}", sdf.format(time), sdf.format(latestShowTime));
                return;
            }
            //保存刷脸时间到缓存中
            faceGateAttendRedisDao.setShowTime(deviceKey, personGuid, time);

            //保存考勤信息
            EmpAttendSyncDataParam param = new EmpAttendSyncDataParam();
            param.setDeviceName(faceGateDTO.getName());
            param.setDeviceKey(deviceKey);
            param.setPersonGuid(personGuid);
            param.setShowTime(time);
            param.setEmpName(recRecord.getName());
            param.setTemperature(Double.valueOf(recRecord.getTemp()));
            param.setDeptId(faceGateDTO.getDeptId());
            param.setFaceGateId(faceGateDTO.getFaceGateId());
            param.setDirection(faceGateDTO.getDirection());
            //处理图片
            String pic = recRecord.getScenePhoto();
            if (StringUtils.isNotBlank(pic)) {
                String imgUrl = uploadImg(pic, time);
                param.setPicture(imgUrl);
            }

            //推送消息到考勤记录中
            String jsonStr = JSON.toJSONString(param);
            amqpTemplate.convertAndSend(QueueConst.EMP_FACEGATE_ATTEND_DATA, jsonStr);
        } catch (Exception ex) {
            logger.error("处理识别记录失败.", ex);
        }
    }

    /**
     * 上传识别照片
     *
     * @param base64Str base64
     * @return 图片地址
     */
    private String uploadImg(String base64Str, Date time) {
        String imgUrl = "";
        if (StringUtils.isEmpty(base64Str)) {
            return imgUrl;
        }
        try {
            String imgData = Base64Util.getImageData(base64Str);
            byte[] imageByte = Base64Util.decode(imgData);
            byte[] dstBytes = ImageUtil.compressPicForScale(imageByte, 45);
            ByteArrayInputStream inputStream = new ByteArrayInputStream(dstBytes);
            String ossPath = "emp/face/" + DateUtil.formatDate(time, "yyyyMMdd");
            String path = filePathConfig.getFilePath(ossPath, RandomUtil.getRandomFileName(), "jpg");
            imgUrl = fileHandler.upload(path, inputStream);
        } catch (Exception e) {
            logger.warn("考勤识别上传base64图片失败", e);
        }
        return imgUrl;
    }
}
