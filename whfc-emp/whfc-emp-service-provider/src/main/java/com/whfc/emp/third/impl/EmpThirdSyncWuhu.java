package com.whfc.emp.third.impl;

import com.whfc.common.third.wuhu.WhZyApi;
import com.whfc.common.third.wuhu.entity.*;
import com.whfc.common.util.Base64Util;
import com.whfc.common.util.DateUtil;
import com.whfc.common.util.ImageUtil;
import com.whfc.common.util.JSONUtil;
import com.whfc.emp.dao.*;
import com.whfc.emp.dto.AppPayrollEmpDTO;
import com.whfc.emp.dto.TrainEmpDTO;
import com.whfc.emp.entity.*;
import com.whfc.emp.enums.ContractType;
import com.whfc.emp.enums.Direction;
import com.whfc.emp.enums.GenderCode;
import com.whfc.emp.param.FaceGateRecordParam;
import com.whfc.emp.third.EmpThirdSync;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 人员信息-第三方同步-芜湖实名制
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/6/30 14:44
 */
@Service
public class EmpThirdSyncWuhu extends EmpThirdSyncBase implements EmpThirdSync {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 图片大小
     */
    private static final long IMAGE_SIZE = 45;

    @Autowired
    private AppEmpMapper appEmpMapper;

    @Autowired
    private AppEmpWorkTypeMapper appEmpWorkTypeMapper;

    @Autowired
    private AppTrainEmpMapper appTrainEmpMapper;

    @Autowired
    private AppPayrollEmpMapper appPayrollEmpMapper;

    @Autowired
    private AppEmpBankMapper appEmpBankMapper;

    @Override
    public void syncEmp(AppEmp emp, AppSync config) {

        String host = config.getHost();
        String account = config.getAppKey();
        String secret = config.getAppSecret();
        String BDBM = config.getExt1();
        String TYSHXYDMZ = config.getExt2();

        String SFZHM = emp.getIdCardNo();
        String XM = emp.getEmpName();
        String XB = GenderCode.man.getValue().equals(emp.getGender()) ? WhZyConst.MAN : WhZyConst.WOMAN;
        String SJHM = emp.getPhone();
        String CSRQ = DateUtil.formatDate(emp.getBirthday());
        String XZZ = emp.getAddress();
        String grzp = this.getImageBase64(emp.getAvatar());

        AppEmpWorkType workType = appEmpWorkTypeMapper.selectByPrimaryKey(emp.getWorkTypeId());
        String GW = workType.getWorkCode();

        //企业人员同步
        SmzQyry data = new SmzQyry();
        data.setSFZHM(SFZHM);
        data.setTYSHXYDMZ(TYSHXYDMZ);
        data.setXM(XM);
        data.setXB(XB);
        data.setSJHM(SJHM);
        data.setCSRQ(CSRQ);
        data.setXZZ(XZZ);
        data.setGW(GW);
        data.setGRZP(grzp);

        List<SmzQyry> list = new ArrayList<>();
        list.add(data);

        WhZyApi api = new WhZyApi(host, account, secret);
        api.smzQyry(list);

        //项目人员同步
        SmzXmry data1 = new SmzXmry();
        data1.setBDBM(BDBM);
        data1.setTYSHXYDMZ(TYSHXYDMZ);
        data1.setSFZHM(SFZHM);
        data1.setXM(XM);
        data1.setGW(GW);
        data1.setJRXMSJ(DateUtil.formatDate(emp.getEnterTime()));

        List<SmzXmry> list1 = new ArrayList<>();
        list1.add(data1);

        api.smzXmry(list1);
    }

    @Override
    public void syncEmpContract(AppEmp emp, AppEmpContract contract, AppSync config) {

        String host = config.getHost();
        String account = config.getAppKey();
        String secret = config.getAppSecret();
        String BDBM = config.getExt1();
        String TYSHXYDMZ = config.getExt2();

        String SFZHM = emp.getIdCardNo();
        String XM = emp.getEmpName();
        String HTLX = ContractType.FIXED.getValue().equals(contract.getContactType()) ? WhZyConst.FIXED : WhZyConst.NOT_FIXED;
        String YDGZ = String.valueOf(contract.getSalary());
        String YGZ = YDGZ;
        String GZFFXS = contract.getPayWay();
        String GZNR = emp.getWorkTypeName();
        String SXRQ = DateUtil.formatDate(contract.getStartDate());
        String SXRQE = DateUtil.formatDate(contract.getEndDate());

        SmzXmryht data = new SmzXmryht();
        data.setBDBM(BDBM);
        data.setTYSHXYDMZ(TYSHXYDMZ);
        data.setSFZHM(SFZHM);
        data.setXM(XM);
        data.setHTLX(HTLX);
        data.setYDGZ(YDGZ);
        data.setYGZ(YGZ);
        data.setGZFFXS(GZFFXS);
        data.setGZNR(GZNR);
        data.setSXRQ(SXRQ);
        data.setSXRQE(SXRQE);

        List<SmzXmryht> list = new ArrayList<>();
        list.add(data);

        WhZyApi api = new WhZyApi(host, account, secret);
        api.smzXmryht(list);
    }

    @Override
    public void syncEmpTrain(AppTrain train, AppSync config) {
        String host = config.getHost();
        String account = config.getAppKey();
        String secret = config.getAppSecret();
        String BDBM = config.getExt1();
        String TYSHXYDMZ = config.getExt2();

        Integer trainId = train.getId();
        List<TrainEmpDTO> empList = appTrainEmpMapper.selectByTrainId(trainId);
        List<SmzXmrypx> list = new ArrayList<>(empList.size());

        String PXBH = "PX-" + train.getId();
        String PXRQ = DateUtil.formatDate(train.getDate());
        String PXSC = String.valueOf(train.getDuration());
        String PXDD = train.getAddress();
        String PXJG = train.getOrganizer();
        String PXR = train.getTrainer();
        String PXLX = train.getTrainTypeCode();
        String KCMC = train.getName();
        String PXJS = train.getContent();

        for (TrainEmpDTO emp : empList) {
            String XM = emp.getEmpName();

            SmzXmrypx data = new SmzXmrypx();
            data.setBDBM(BDBM);
            data.setSFZHM(TYSHXYDMZ);
            data.setXM(XM);
            data.setPXBH(PXBH);
            data.setPXRQ(PXRQ);
            data.setPXSC(PXSC);
            data.setPXDD(PXDD);
            data.setPXJG(PXJG);
            data.setPXR(PXR);
            data.setPXLX(PXLX);
            data.setKCMC(KCMC);
            data.setPXJS(PXJS);

            list.add(data);
        }

        WhZyApi api = new WhZyApi(host, account, secret);
        api.smzXmrypx(list);
    }

    @Override
    public void syncEmpPayroll(AppPayroll payroll, AppSync config) {

        String host = config.getHost();
        String account = config.getAppKey();
        String secret = config.getAppSecret();
        String BDBM = config.getExt1();
        String TYSHXYDMZ = config.getExt2();

        Integer payrollId = payroll.getId();
        List<AppPayrollEmpDTO> empList = appPayrollEmpMapper.selectByPayrollId(payrollId);
        List<SmzXmrygz> list = new ArrayList<>(empList.size());
        for (AppPayrollEmpDTO emp : empList) {
            String XM = emp.getEmpName();
            AppEmpWorkType workType = appEmpWorkTypeMapper.selectByPrimaryKey(emp.getWorkTypeId());
            String GW = workType.getWorkCode();
            String KSRQ = DateUtil.formatDate(payroll.getStartDate());
            String JSRQ = DateUtil.formatDate(payroll.getEndDate());
            String YHKH = appEmpBankMapper.selectBankNumberByEmpId(emp.getEmpId());
            String CQXS = String.valueOf(emp.getRealAttendDays() * 8);
            String GZJE = String.valueOf(emp.getSalaryReal());

            SmzXmrygz data = new SmzXmrygz();
            data.setBDBM(BDBM);
            data.setSFZHM(TYSHXYDMZ);
            data.setXM(XM);
            data.setGW(GW);
            data.setKSRQ(KSRQ);
            data.setJSRQ(JSRQ);
            data.setYHKH(YHKH);
            data.setCQXS(CQXS);
            data.setGZJE(GZJE);

            list.add(data);
        }

        WhZyApi api = new WhZyApi(host, account, secret);
        api.smzXmrygz(list);
    }

    @Override
    public void syncEmpAttend(FaceGateRecordParam param, AppSync config) {
        logger.info("芜湖实名制-考勤同步,{}", JSONUtil.toString(param));
        String host = config.getHost();
        String account = config.getAppKey();
        String secret = config.getAppSecret();
        String bdbm = config.getExt1();

        Integer deptId = param.getDeptId();
        String deviceKey = param.getDeviceKey();
        String idCardNo = param.getIdCardNo();
        Integer empId = param.getEmpId();
        String personGuid = param.getPersonGuid();
        AppEmp emp = null;
        //根据身份证查询
        if (StringUtils.isNotBlank(idCardNo)) {
            emp = appEmpMapper.selectByDeptIdAndIdCardNo(deptId, idCardNo);
        }
        //根据人员ID查询
        else if (empId != null) {
            emp = appEmpMapper.selectByPrimaryKey(empId);
        }
        //根据personGuid查询
        else if (StringUtils.isNotBlank(personGuid)) {
            emp = appEmpMapper.selectByDeptIdAndEmpCode(deptId, personGuid);
        }
        idCardNo = emp.getIdCardNo();
        String empName = emp.getEmpName();

        if (emp == null) {
            logger.info("芜湖考勤同步,personGuid:{},不存在", personGuid);
        }
        if (StringUtils.isEmpty(idCardNo)) {
            logger.info("芜湖考勤同步,personGuid:{},身份证缺失", personGuid);
        }
        String imgUrl = StringUtils.isNotBlank(param.getPhotoUrl()) ? param.getPhotoUrl() : emp.getAvatar();
        String dkzp = this.getImageBase64(imgUrl);
        String direction = Direction.IN.getValue().equals(param.getDirection()) ? WhZyConst.IN : WhZyConst.OUT;
        String datetime = DateUtil.formatDateTime(param.getShowTime());

        SmzXmrykq data = new SmzXmrykq();
        data.setBDBM(bdbm);
        data.setSBNO(deviceKey);
        data.setSFZHM(idCardNo);
        data.setXM(empName);
        data.setDKZP(dkzp);
        data.setDKSJ(datetime);
        data.setDKZT(direction);
        data.setLNG(String.valueOf(param.getLng()));
        data.setLAT(String.valueOf(param.getLat()));

        List<SmzXmrykq> list = new ArrayList<>();
        list.add(data);

        WhZyApi api = new WhZyApi(host, account, secret);
        api.smzXmrykq(list);
    }

    /**
     * 获取图片base64(照片压缩)
     *
     * @param url
     * @return
     */
    private String getImageBase64(String url) {
        logger.debug("获取图片base64,{}", url);
        byte[] imageData = Base64Util.getUrlImageData(url);
        logger.debug("获取图片base64,{},压缩前:{}", url, imageData.length);
        byte[] compressed = ImageUtil.compressPicForScale(imageData, IMAGE_SIZE);
        logger.debug("获取图片base64,{},压缩后:{}", url, compressed.length);
        return Base64Util.encodeToString(compressed);
    }
}
