package com.whfc.emp.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.whfc.common.enums.FenceType;
import com.whfc.common.enums.Gender;
import com.whfc.common.enums.I18nErrorCode;
import com.whfc.common.exception.BizException;
import com.whfc.common.file.FileHandler;
import com.whfc.common.file.FilePathConfig;
import com.whfc.common.file.properties.FileExpirationRules;
import com.whfc.common.geometry.GeometryUtil;
import com.whfc.common.geometry.Point;
import com.whfc.common.geometry.Polygon;
import com.whfc.common.result.PageData;
import com.whfc.common.result.ResultEnum;
import com.whfc.common.util.*;
import com.whfc.emp.dao.*;
import com.whfc.emp.dto.*;
import com.whfc.emp.dto.area.AppAreaAttendStat;
import com.whfc.emp.entity.AppEmp;
import com.whfc.emp.enums.EmpAgeType;
import com.whfc.emp.enums.EnterType;
import com.whfc.emp.manager.AppEmpDataManager;
import com.whfc.emp.manager.CommonEmpConfigManager;
import com.whfc.emp.param.EmpAttendPlan;
import com.whfc.emp.service.AppEmpDataService;
import com.whfc.entity.dto.OssPathDTO;
import com.whfc.entity.dto.adsq.AdsqStatisticsItemDTO;
import com.whfc.fuum.dto.AppCorpDTO;
import com.whfc.fuum.service.AppCorpService;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.File;
import java.io.FileInputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description 人员数据服务
 * <AUTHOR>
 * @Date 2020/12/30 11:29
 * @Version 1.0
 */
@DubboService(interfaceClass = AppEmpDataService.class, version = "1.0.0", timeout = 120 * 1000)
public class AppEmpDataServiceImpl implements AppEmpDataService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private AppEmpMapper appEmpMapper;

    @Autowired
    private AppEmpDataManager appEmpDataManager;

    @Autowired
    private AppEmpEnterRecordMapper appEmpEnterRecordMapper;

    @Autowired
    private AppEmpDayMapper appEmpDayMapper;

    @Autowired
    private CommonEmpConfigManager commonEmpConfigManager;

    @Autowired
    private AppFenceMapper appFenceMapper;

    @Autowired
    private AppEmpGroupMapper appEmpGroupMapper;

    @Autowired
    private AppEmpDataMapper appEmpDataMapper;

    @Autowired
    private AppEmpDeptDayMapper appEmpDeptDayMapper;

    @Autowired
    private AppFaceGateMapper faceGateMapper;

    @Autowired
    private AppFaceGateRecordMapper faceGateRecordMapper;

    @Autowired
    private AppTrainMapper appTrainMapper;

    @DubboReference(interfaceClass = AppCorpService.class, version = "1.0.0")
    private AppCorpService appCorpService;

    @Autowired
    private FileHandler fileHandler;

    @Autowired
    private FilePathConfig filePathConfig;


    @Override
    public List<AppWorkTypeDTO> getWorkTypeList(Integer deptId) {
        logger.info(" 获取可供选择的工种或者岗位,deptId：{}", deptId);
        return appEmpMapper.selectWorkTypeList(deptId);
    }

    @Override
    public AppEmpDetailDTO getEmpDetail(Integer empId) {
        logger.info("首页获取人员详细信息,empId:{}", empId);
        AppEmp appEmp = appEmpMapper.selectByPrimaryKey(empId);
        if (appEmp == null) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_001.getCode());
        }
        AppEmpDetailDTO appEmpDetailDTO = new AppEmpDetailDTO();
        BeanUtils.copyProperties(appEmp, appEmpDetailDTO);
        appEmpDetailDTO.setEmpId(empId);
        // 查看最近打卡时间
        Date recentEnterTime = null;
        appEmpDetailDTO.setRecentEnterTime(recentEnterTime);
        // 查看今天的出勤时长
        Date startTime = DateUtil.getDateBegin(new Date());
        Date endTime = DateUtil.getDateEnd(new Date());
        List<AppEmpDayWorkTimesDTO> appEmpDayWorkTimesDTOS = appEmpDayMapper.selectWorkTimesByEmpId(empId, startTime, endTime);
        if (appEmpDayWorkTimesDTOS != null && appEmpDayWorkTimesDTOS.size() > 0) {
            appEmpDetailDTO.setAttendTimes(appEmpDayWorkTimesDTOS.get(0).getWorkTimes());
        } else {
            appEmpDetailDTO.setAttendTimes(0D);
        }
        return appEmpDetailDTO;
    }

    @Override
    public List<AppEmpDTO> getMapEmpList(Integer deptId, Integer groupId, String empName) {
        logger.info("获取地图人员列表,deptId{}", deptId);
        List<AppEmpDTO> empList = appEmpMapper.selectIndexEmpList(deptId, empName, null, null, groupId, null);
        // 安全帽离线或关机时长
        AppEmpSettingDTO data = commonEmpConfigManager.getMinutes(deptId);
        Date time = DateUtil.addMinutes(new Date(), -data.getMinutes());
        String polygonWKT = commonEmpConfigManager.getPolygonWKT(deptId);
        empList = empList.stream().filter(appEmpDTO -> {
            // 筛选出地图显示的人员
            Date time1 = appEmpDTO.getTime();
            Double lat = appEmpDTO.getLat();
            Double lng = appEmpDTO.getLng();
            if (!PositionUtil.contains(FenceType.POLYGON.value(), polygonWKT, null, lat, lng) || time1 == null || time1.before(time)) {
                return false;
            }
            // 人员图标处理
            HelmetIconDTO helmetIcon = appEmpDataManager.getHelmetIcon(appEmpDTO.getColor());
            appEmpDTO.setIconUrl(helmetIcon.getIconUrl());
            return true;
        }).collect(Collectors.toList());
        return empList;
    }

    @Override
    public List<MapEmpPolyDTO> getMapEmpPoly(Integer deptId, Integer groupId, String empName, Integer fenceId) throws BizException {
        logger.info("获取地图页人员聚合,deptId:{},groupId:{},empName:{},fenceId:{}", deptId, groupId, empName, fenceId);
        List<MapEmpPolyDTO> result = new ArrayList<>();
        // 查找电子围栏
        List<AppFenceDTO> list = appFenceMapper.selectByFenceId(deptId, fenceId);
        if (list.size() == 0) {
            return Collections.emptyList();
        }
        // 获取要显示的人员
        List<AppEmpDTO> mapEmpList = this.getMapEmpList(deptId, groupId, empName);

        for (AppFenceDTO appFenceDTO : list) {
            Integer type = appFenceDTO.getType();
            this.translate(appFenceDTO);
            String polygon = appFenceDTO.getPolygon();
            String center = appFenceDTO.getCenter();
            Double radius = appFenceDTO.getRadius();
            String geomWtk = FenceType.CIRCLE.value().equals(type) ? center : polygon;

            List<AppEmpDTO> empList = new ArrayList<>();
            for (AppEmpDTO appEmpDTO : mapEmpList) {
                // 筛选出该电子围栏的人员
                Double lat = appEmpDTO.getLat();
                Double lng = appEmpDTO.getLng();
                if (PositionUtil.contains(type, geomWtk, radius, lat, lng)) {
                    empList.add(appEmpDTO);
                }
            }

            MapEmpPolyDTO data = new MapEmpPolyDTO();
            BeanUtils.copyProperties(appFenceDTO, data);
            data.setEmpList(empList);
            data.setEmpNum(empList.size());
            result.add(data);
        }
        return result;
    }

    @Override
    public List<MapEmpGroupDTO> getMapEmpFvs(Integer deptId, Integer groupId, String empName, Integer fvsDeviceId) throws BizException {
        logger.info("地图人员列表（班组视频监控),deptId:{},groupId:{},empName:{},fvsDeviceId:{}", deptId, groupId, empName, fvsDeviceId);
        // 查找有视频监控的班组
        List<MapEmpGroupDTO> list = appEmpGroupMapper.selectFvsGroup(deptId, groupId, fvsDeviceId);
        if (list.isEmpty()) {
            return Collections.emptyList();
        }

        // 安全帽离线或关机时长
        AppEmpSettingDTO data = commonEmpConfigManager.getMinutes(deptId);
        Date time = DateUtil.addMinutes(new Date(), -data.getMinutes());
        String polygonWkt = commonEmpConfigManager.getPolygonWKT(deptId);

        for (MapEmpGroupDTO mapEmpGroupDTO : list) {
            Integer empGroupId = mapEmpGroupDTO.getGroupId();
            // 查找每个班组下面的人员
            List<AppEmpDTO> empList = appEmpMapper.selectIndexEmpList(deptId, empName, null, null, empGroupId, null);
            List<AppEmpDTO> groupEmpList = new ArrayList<>();
            for (AppEmpDTO appEmpDTO : empList) {
                // 筛选出地图显示的人员
                Date time1 = appEmpDTO.getTime();
                Double lat = appEmpDTO.getLat();
                Double lng = appEmpDTO.getLng();
                if (!PositionUtil.contains(FenceType.POLYGON.value(), polygonWkt, null, lat, lng) || time1 == null || time1.before(time)) {
                    continue;
                }
                // 人员图标处理
                HelmetIconDTO helmetIcon = appEmpDataManager.getHelmetIcon(appEmpDTO.getColor());
                appEmpDTO.setIconUrl(helmetIcon.getIconUrl());
                groupEmpList.add(appEmpDTO);
            }
            mapEmpGroupDTO.setEmpNum(groupEmpList.size());
            mapEmpGroupDTO.setEmpList(groupEmpList);
        }
        return list;
    }

    @Override
    public AppEmpDataDTO getEmpData(Integer deptId) {
        logger.info("人员分析-获取人员分析数据,deptId：{}", deptId);
        Date now = new Date();
        Date today = DateUtil.getDateEnd(now);
        Date startDate = DateUtil.getDateBegin(DateUtil.addDays(today, -6));

        // 人数统计
        AppEmpAnaNumDTO enterEmp = appEmpMapper.selectEmpNumByDeptId(deptId);
        // 统计各人员类型分类的人数
        List<AppEmpWorkRoleNumDTO> workRoleNumDTOList = appEmpMapper.countByWorkRoleId(deptId);
        enterEmp.setWorkRoleNumList(workRoleNumDTOList);

        // 出勤统计
        AppEmpAnaNumDTO todayAttendEmp = appEmpDataMapper.selectAttendNum(deptId);
        // 统计各人员类型分类的出勤的人数
        List<AppEmpWorkRoleNumDTO> workRoleNumAttendDTOList = appEmpDataMapper.countEmpAttendNumByWorkRole(deptId);
        todayAttendEmp.setWorkRoleNumList(workRoleNumAttendDTOList);

        // 补全人员类型出勤类型
        updateAttendWorkROle(workRoleNumDTOList, workRoleNumAttendDTOList);

        // 获取当日计划出勤人数
        // 默认为总人数
        int planAttendNum = enterEmp.getEmpTotal();
        EmpAttendPlan empAttendPlan = appEmpDeptDayMapper.selectByDeptIdAndDate(deptId, now);
        if (empAttendPlan != null) {
            planAttendNum = empAttendPlan.getPlanAttendNum();
        }
        todayAttendEmp.setPlanAttendNum(planAttendNum);

        // 每日统计
        List<EmpDataDTO> attendList = appEmpDayMapper.selectEmpNumTotalPerDay(deptId, startDate, today);
        List<AppEmpAnaWeekDataDTO> enterList = appEmpEnterRecordMapper.selectDayStatStat(deptId, startDate, today);
        Map<Date, EmpDataDTO> attendMap = CollectionUtil.list2Map(attendList, EmpDataDTO::getDate);
        Map<Date, AppEmpAnaWeekDataDTO> enterMap = CollectionUtil.list2Map(enterList, AppEmpAnaWeekDataDTO::getDate);

        List<Date> dateList = DateUtil.getDayList(startDate, today);
        List<AppEmpAnaWeekDataDTO> weekDataList = new ArrayList<>(dateList.size());
        for (Date date : dateList) {
            Integer attendNum = attendMap.containsKey(date) ? attendMap.get(date).getAttendNum() : 0;
            Integer enterNum = enterMap.containsKey(date) ? enterMap.get(date).getEnterNum() : 0;
            Integer outerNum = enterMap.containsKey(date) ? enterMap.get(date).getOuterNum() : 0;
            AppEmpAnaWeekDataDTO record = new AppEmpAnaWeekDataDTO(date, enterNum, outerNum, attendNum);
            weekDataList.add(record);
        }

        AppEmpDataDTO data = new AppEmpDataDTO();
        data.setTodayAttendEmp(todayAttendEmp);
        data.setWeekDataList(weekDataList);
        data.setEnterEmp(enterEmp);
        statisticalAge(deptId, data);
        return data;
    }


    @Override
    public AppEmpAnaNumDTO countEmpNum(Integer deptId) throws BizException {
        return appEmpDataMapper.selectAttendNum(deptId);
    }

    @Override
    public PageData<AppAnaEmpDTO> getRecentAttendEmp(Integer deptId) {
        logger.info("人员分析-获取最近出勤人员,deptId：{}", deptId);
        PageHelper.startPage(1, 20);
        List<AppAnaEmpDTO> list = appEmpDayMapper.selectRecentAttendEmp(deptId);
        PageHelper.clearPage();

        return PageUtil.pageData(PageInfo.of(list));
    }

    @Override
    public PageData<AppAnaEmpDTO> getRecentEnterEmp(Integer deptId) {
        logger.info("人员分析-获取最近进场人员,deptId：{}", deptId);
        PageHelper.startPage(1, 20);
        List<AppAnaEmpDTO> list = appEmpMapper.selectRecentEnterEmp(deptId);
        PageHelper.clearPage();
        return PageUtil.pageData(PageInfo.of(list));
    }

    @Override
    public AppEmpStatisticsDTO getGroupEmp(Integer deptId) throws BizException {
        logger.info("人员分析-班组统计,deptId：{}", deptId);
        // 总人数
        Integer empTotal = appEmpMapper.countByDeptId(deptId);
        // 人员分类统计
        List<AppEmpAttendGroupDTO> list = appEmpMapper.selectGroupNumByDeptId(deptId);
        for (AppEmpAttendGroupDTO groupDTO : list) {
            Double rate = MathUtil.divide(groupDTO.getNum(), empTotal, 4);
            groupDTO.setRate(rate);
        }
        return new AppEmpStatisticsDTO(empTotal, list);
    }

    @Override
    public AppEmpStatisticsDTO getCorpEmp(Integer deptId) throws BizException {
        logger.info("人员分析-合作单位统计,deptId：{}", deptId);

        // 总人数
        Integer empTotal = appEmpMapper.countByDeptId(deptId);

        // 合作单位
        List<AppCorpDTO> appCorpDTOS = appCorpService.list(deptId);
        Map<Integer, String> corpMap = new HashMap<>(appCorpDTOS.size());
        for (AppCorpDTO corp : appCorpDTOS) {
            corpMap.put(corp.getCorpId(), corp.getCorpName());
        }

        // 人员分类统计
        List<AppEmpAttendGroupDTO> list = appEmpMapper.selectCorpNumByDeptId(deptId);
        for (AppEmpAttendGroupDTO groupDTO : list) {
            if (corpMap.containsKey(groupDTO.getId())) {
                groupDTO.setName(corpMap.get(groupDTO.getId()));
            }
            Double rate = MathUtil.divide(groupDTO.getNum(), empTotal, 4);
            groupDTO.setRate(rate);
        }
        return new AppEmpStatisticsDTO(empTotal, list);
    }

    @Override
    public AppEmpStatisticsDTO getWorkEmp(Integer deptId) throws BizException {
        logger.info("人员分析-工种统计,deptId：{}", deptId);
        // 总人数
        Integer empTotal = appEmpMapper.countByDeptId(deptId);

        // 人员分类统计
        List<AppEmpAttendGroupDTO> list = appEmpMapper.selectWorkNumByDeptId(deptId);
        for (AppEmpAttendGroupDTO groupDTO : list) {
            groupDTO.setRate(MathUtil.divide(groupDTO.getNum(), empTotal, 4));
        }
        return new AppEmpStatisticsDTO(empTotal, list);
    }

    @Override
    public List<AppEmpAttendGroupDTO> getGroupAttendData(Integer deptId, Date date) throws BizException {
        return appEmpGroupMapper.selectGroupAttendNum(deptId, date);
    }

    @Override
    public List<AppEmpAttendGroupDTO> getCorpAttendData(Integer deptId, Date date) throws BizException {
        return appEmpGroupMapper.selectCorpAttendNum(deptId, date);
    }

    @Override
    public List<AppEmpAttendGroupDTO> getAreaAttendData(Integer deptId, Date date) throws BizException {

        // 工区出勤人数统计
        Date startTime = DateUtil.getDateBegin(date);
        Date endTime = DateUtil.getDateEnd(date);
        List<AppEmpAttendGroupDTO> attendList = faceGateRecordMapper.selectAreaAttendData(deptId, startTime, endTime);
        Map<Integer, Integer> attendMap = CollectionUtil.list2map(attendList, AppEmpAttendGroupDTO::getAreaId, AppEmpAttendGroupDTO::getNum);

        // 各工区闸机统计
        List<AppFaceGateDTO> faceGateList = faceGateMapper.selectByDeptId(deptId, null);
        Map<Integer, List<AppFaceGateDTO>> areaMap = faceGateList.stream()
                .filter(dto -> dto.getAreaId() != null)
                .collect(Collectors.groupingBy(AppFaceGateDTO::getAreaId));

        Set<Integer> areaIds = areaMap.keySet();
        List<AppEmpAttendGroupDTO> list = new ArrayList<>(areaIds.size());
        for (Integer areaId : areaIds) {
            Integer num = attendMap.getOrDefault(areaId, 0);
            List<AppFaceGateDTO> faceGates = areaMap.get(areaId);
            String areaName = faceGates.get(0).getAreaName();
            AppEmpAttendGroupDTO groupDTO = new AppEmpAttendGroupDTO();
            groupDTO.setAreaId(areaId);
            groupDTO.setAreaName(areaName);
            groupDTO.setNum(num);
            groupDTO.setFaceGateNum(faceGates.size());
            list.add(groupDTO);
        }
        return list;
    }

    @Override
    public List<AppEmpAttendTimeDTO> getAttendDataByTime(Integer deptId, Date date) throws BizException {
        List<AppEmpAttendTimeDTO> list = appEmpDayMapper.selectAttendTimeStat(deptId, date);
        Map<String, Integer> map = CollectionUtil.list2map(list, AppEmpAttendTimeDTO::getTime, AppEmpAttendTimeDTO::getNum);

        List<AppEmpAttendTimeDTO> timeList = new ArrayList<>(24);
        for (int i = 0; i < 24; i++) {
            String key = String.format("%02d", i);
            String time = String.valueOf(i);
            if (map.containsKey(key)) {
                timeList.add(new AppEmpAttendTimeDTO(time, map.get(key)));
            } else {
                timeList.add(new AppEmpAttendTimeDTO(time, 0));
            }
        }
        return timeList;
    }

    @Override
    public List<AppEmpWorkRoleNumDTO> getWorkRoleAttend(Integer deptId) {
        return appEmpDataMapper.countEmpAttendNumByWorkRole(deptId);
    }

    @Override
    public List<AppTrainStatDTO> getTrainStat(Integer deptId) throws BizException {
        List<AppTrainStatDTO> list = appTrainMapper.selectTrainStat(deptId);
        return list;
    }

    @Override
    public AppEmpLogDTO getEmpLog(Integer deptId, Date date) throws BizException {
        AppEmpLogDTO appEmpLogDTO = new AppEmpLogDTO();
        // 查询人员总数
        Integer empTotal = appEmpDayMapper.countEmpTotal(deptId, date);
        // 查询工人类型统计
        List<AppEmpWorkRoleNumDTO> workRoleNumList = appEmpDayMapper.selectEmpLogByWorkRole(deptId, date);
        appEmpLogDTO.setWorkRoleNumList(workRoleNumList);
        appEmpLogDTO.setEmpTotal(empTotal);
        List<AppEmpDTO> list = appEmpEnterRecordMapper.selectEmpLog(deptId, date);
        List<AppEmpDTO> enterList = new ArrayList<>();
        List<AppEmpDTO> outerList = new ArrayList<>();
        list.forEach(appEmpDTO -> {
            if (EnterType.enter.getValue().equals(appEmpDTO.getType())) {
                enterList.add(appEmpDTO);
            } else {
                outerList.add(appEmpDTO);
            }
        });
        appEmpLogDTO.setEnterList(enterList);
        appEmpLogDTO.setOuterList(outerList);
        return appEmpLogDTO;
    }

    /*************区域考勤***************/

    @Override
    public List<AppAreaAttendStat> getAreaAttendAreaList(Integer deptId) throws BizException {

        List<AppFaceGateDTO> faceGateList = faceGateMapper.selectByDeptId(deptId, null);
        Map<Integer, List<AppFaceGateDTO>> areaMap = faceGateList.stream()
                .filter(dto -> dto.getAreaId() != null)
                .collect(Collectors.groupingBy(AppFaceGateDTO::getAreaId));

        List<AppAreaAttendStat> list = new ArrayList<>(areaMap.keySet().size());
        for (Integer areaId : areaMap.keySet()) {
            List<AppFaceGateDTO> faceGates = areaMap.get(areaId);
            String areaName = faceGates.get(0).getAreaName();
            AppAreaAttendStat areaAttendStat = new AppAreaAttendStat();
            areaAttendStat.setAreaId(areaId);
            areaAttendStat.setAreaName(areaName);
            areaAttendStat.setFaceGateNum(faceGates.size());

            list.add(areaAttendStat);
        }
        return list;
    }

    @Override
    public AppAreaAttendStat getAreaAttendAreaStat(Integer deptId, Integer areaId) throws BizException {

        // 查询区域在场人数
        AppAreaAttendStat stat = appEmpDataMapper.selectAreaLocaleStat(deptId, areaId);
        // 根据工人类型查询区域在场人员
        List<AppEmpWorkRoleNumDTO> workRoleNumDTOList = appEmpDataMapper.selectAreaLocaleStatByWorkRole(deptId, areaId);
        stat.setWorkRoleNumList(workRoleNumDTOList);

        // 查询班组在场人数
        List<AppAreaAttendStat> groupList = appEmpDataMapper.selectAreaLocaleGroupStat(deptId, areaId);

        stat.setGroupList(groupList);
        return stat;
    }

    @Override
    public AppAreaAttendStat getAreaLocaleEmpList(Integer deptId, Integer areaId) throws BizException {

        List<AppEmpDTO> localeEmpList = appEmpDataMapper.selectAreaLocaleEmpList(deptId, areaId);
        Map<String, List<AppEmpDTO>> workRoleMap = localeEmpList.stream().collect(Collectors.groupingBy(AppEmpDTO::getWorkRoleName, Collectors.toList()));

        AppAreaAttendStat stat = new AppAreaAttendStat();
        stat.setWorkRoleMap(workRoleMap);
        return stat;
    }

    @Override
    public List<AppEmpDTO> getAreaLocaleEmpList(Integer deptId, Integer areaId, Date startTime, Date endTime, String keyword) throws BizException {
        List<AppEmpDTO> localeEmpList = appEmpDataMapper.selectAreaLocaleEmpListByTime(deptId, areaId, startTime, endTime, keyword);
        return localeEmpList;
    }

    @Override
    public OssPathDTO exportAreaLocaleEmpList(Integer deptId, Integer areaId) throws BizException {

        List<AppEmpDTO> localeEmpList = appEmpDataMapper.selectAreaLocaleEmpList(deptId, areaId);

        String dateStr = DateUtil.formatDate(new Date(), "yyyyMMdd");

        OssPathDTO ossPathDTO = new OssPathDTO();
        try {
            Set<String> includeColumnFiledNames = new HashSet<>();
            includeColumnFiledNames.add("empName");
            includeColumnFiledNames.add("corpName");
            includeColumnFiledNames.add("workRoleName");
            includeColumnFiledNames.add("workTypeName");
            includeColumnFiledNames.add("localeTime");


            String fileName = "场内人员数据" + System.currentTimeMillis();
            File destFile = File.createTempFile(fileName, ".xls");
            ExcelWriter excelWriter = EasyExcel.write(destFile).build();

            WriteSheet writeSheet = EasyExcel.writerSheet(dateStr).head(AppEmpDTO.class).includeColumnFiledNames(includeColumnFiledNames).build();
            excelWriter.write(localeEmpList, writeSheet);
            excelWriter.finish();

            // 上传oss
            String filepath = filePathConfig.getFilePath("emp/temp", fileName, "xls");
            String upload = fileHandler.upload(filepath, new FileInputStream(destFile), FileExpirationRules.DAYS_1);
            ossPathDTO.setPath(upload);
        } catch (Exception e) {
            logger.warn("导出失败", e);
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.DEFAULT_EXPORT_FILE_FAILED.getCode());
        }
        return ossPathDTO;
    }

    @Override
    public PageData<AppFaceGateRecordDTO> getAreaAttendRecordList(Integer deptId, Integer areaId, Integer pageNum, Integer pageSize) throws BizException {

        List<AppFaceGateDTO> faceGateList = faceGateMapper.selectByDeptId(deptId, null);
        List<Integer> faceGateIdList = faceGateList.stream()
                .filter(dto -> dto.getAreaId() != null && dto.getAreaId().equals(areaId))
                .map(AppFaceGateDTO::getFaceGateId)
                .collect(Collectors.toList());
        if (faceGateIdList.isEmpty()) {
            return PageUtil.emptyPage();
        }

        Date now = new Date();
        Date startTime = DateUtil.getDateBegin(now);
        Date endTime = DateUtil.getDateEnd(now);

        PageHelper.startPage(pageNum, pageSize);
        List<AppFaceGateRecordDTO> list = faceGateRecordMapper.selectByDeptIdAndFaceGate(deptId, startTime, endTime, faceGateIdList);
        PageHelper.clearPage();

        return PageUtil.pageData(PageInfo.of(list));
    }

    @Override
    public Integer countAttendNum(Integer deptId) throws BizException {
        return appEmpDataMapper.countAttendNum(Collections.singletonList(deptId));
    }

    /*************项目定制***************/

    @Override
    public List<AdsqStatisticsItemDTO> adsqMonthStatistics(Integer deptId, Date time, Integer groupId) throws BizException {
        return appEmpDayMapper.adsqMonthStatistics(deptId, time, groupId);
    }


    /*************private***************/

    /**
     * 转换空间几何对象
     *
     * @param appFenceDTO 电子围栏对象
     */
    private void translate(AppFenceDTO appFenceDTO) {
        if (FenceType.POLYGON.value().equals(appFenceDTO.getType())) {
            Polygon polygon = GeometryUtil.decodePolygon(appFenceDTO.getPolygon());
            appFenceDTO.setPolygonPointList(polygon.getPointList());
            appFenceDTO.setPolygon(null);
        }
        // 圆形
        String center = appFenceDTO.getCenter();
        if (StringUtils.isNotBlank(center)) {
            Point point = GeometryUtil.decodePoint(center);
            appFenceDTO.setCenterPoint(point);
            appFenceDTO.setCenter(null);
        }
    }

    /**
     * 统计人员年龄
     *
     * @param deptId
     * @return
     */
    private void statisticalAge(Integer deptId, AppEmpDataDTO data) {

        // 人员年龄列表
        List<EmpAge> ageList = appEmpMapper.selectBirthdayByDeptId(deptId);

        // 按按年龄分类
        Map<EmpAgeType, List<EmpAge>> ageTypeMap = CollectionUtil.groupBy(ageList, x -> EmpAgeType.parseByAge(x.getAge()));
        EmpAgeType[] ageTypes = EmpAgeType.values();
        List<AppEmpAgeDTO> retList = new ArrayList<>(ageTypes.length);
        for (EmpAgeType ageType : ageTypes) {
            AppEmpAgeDTO ageDTO = new AppEmpAgeDTO(ageType.getType());
            retList.add(ageDTO);
            if (ageTypeMap.containsKey(ageType)) {
                List<EmpAge> typeAgeList = ageTypeMap.get(ageType);
                Integer ageNum = typeAgeList.size();
                int manNum = 0;
                int womanNum = 0;
                int otherNum = 0;
                for (EmpAge age : typeAgeList) {
                    if (Gender.man.getValue().equals(age.getGender())) {
                        manNum++;
                    } else if (Gender.woman.getValue().equals(age.getGender())) {
                        womanNum++;
                    } else {
                        otherNum++;
                    }
                }
                double manPercent = MathUtil.divide(manNum * 100, ageNum, 2);
                double womanPercent = MathUtil.divide(womanNum * 100, ageNum, 2);
                double otherPercent = MathUtil.divide(otherNum * 100, ageNum, 2);
                ageDTO.setAgeNum(ageNum);
                ageDTO.setManNum(manNum);
                ageDTO.setWomanNum(womanNum);
                ageDTO.setOtherNum(otherNum);
                ageDTO.setManPercent(manPercent);
                ageDTO.setWomanPercent(womanPercent);
                ageDTO.setOtherPercent(otherPercent);
            }
        }

        // 平均年龄
        OptionalDouble optionalAge = ageList.stream().filter(x -> x.getAge() != null).mapToInt(EmpAge::getAge).average();
        double avgAge = optionalAge.isPresent() ? optionalAge.getAsDouble() : 0;
        data.setAverage(MathUtil.doule2int(avgAge));
        data.setEmpAge(retList);
    }

    /**
     * 补全出勤人员类型数据
     *
     * @param workRoleNumDTOList       人员类型
     * @param workRoleNumAttendDTOList 出勤人员类型
     */
    private void updateAttendWorkROle(List<AppEmpWorkRoleNumDTO> workRoleNumDTOList, List<AppEmpWorkRoleNumDTO> workRoleNumAttendDTOList) {
        if (workRoleNumDTOList.size() == workRoleNumAttendDTOList.size()) {
            return;
        }
        for (AppEmpWorkRoleNumDTO workRoleNumDTO : workRoleNumDTOList) {
            boolean flag = false;
            for (AppEmpWorkRoleNumDTO workRoleNumAttendDTO : workRoleNumAttendDTOList) {
                if (workRoleNumDTO.getWorkRoleId().equals(workRoleNumAttendDTO.getWorkRoleId())) {
                    flag = true;
                    break;
                }
            }
            if (!flag) {
                workRoleNumAttendDTOList.add(new AppEmpWorkRoleNumDTO(workRoleNumDTO.getWorkRoleId(), workRoleNumDTO.getWorkRoleName(), 0, 0, 0));
            }
        }
    }

}
