package com.whfc.emp.dao;

import com.whfc.emp.entity.AppHelmetConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AppHelmetConfigMapper {
    int deleteByPrimaryKey(Integer id);

    int insertSelective(AppHelmetConfig record);

    AppHelmetConfig selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppHelmetConfig record);

    AppHelmetConfig selectByDeptIdAndPlatform(@Param("deptId") Integer deptId, @Param("platform") String platform);

    List<AppHelmetConfig> selectByDeptId(@Param("deptId") Integer deptId);

    List<AppHelmetConfig> selectByPlatform(@Param("platform") String platform);
}