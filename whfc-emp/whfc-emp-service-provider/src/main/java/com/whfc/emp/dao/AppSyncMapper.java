package com.whfc.emp.dao;

import com.whfc.emp.entity.AppSync;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AppSyncMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppSync record);

    int insertSelective(AppSync record);

    AppSync selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppSync record);

    int updateByPrimaryKey(AppSync record);

    /**
     * 查询同步平台
     *
     * @param deptId
     * @param platform
     * @return
     */
    AppSync selectByDeptIdAndPlatform(@Param("deptId") Integer deptId, @Param("platform") String platform);

    /**
     * 查询同步平台
     *
     * @param deptId
     * @return
     */
    List<AppSync> selectByDeptId(@Param("deptId") Integer deptId);
}