package com.whfc.emp.mqtt;

import com.whfc.common.iot.forthink.entity.*;
import com.whfc.common.iot.forthink.util.ForthinkMsgUtil;
import com.whfc.common.mqtt.Qos;
import com.whfc.common.redis.RedisConst;
import com.whfc.common.redis.RedisService;
import com.whfc.common.util.CollectionUtil;
import com.whfc.common.util.JSONUtil;
import com.whfc.emp.indoor.IndoorPositionMgr;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import io.netty.buffer.Unpooled;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * UWB测距数据处理(恒高)
 */
@Component
public class MqttMessageFtkProcessor implements MqttMessageProcessor {

    private static final Logger logger = org.slf4j.LoggerFactory.getLogger(MqttMessageFtkProcessor.class);

    //MQTT-FTK topic前缀
    private static final String MQTT_FTK_TOPIC_PREFIX = "/ftk";

    @Autowired
    private IndoorPositionMgr indoorPositionMgr;

    @Autowired
    private RedisService redisService;

    @Autowired(required = false)
    private MqttConfig.MqttMessageSender mqttMessageSender;

    @Override
    public String getTopicPrefix() {
        return MQTT_FTK_TOPIC_PREFIX;
    }

    @Override
    public List<String> getSubTopicList() {
        String upTopic = ForthinkConst.getTopicUp();
        List<String> topicList = new ArrayList<>();
        topicList.add(upTopic);
        return topicList;
    }

    @Override
    public void processData(String topic, byte[] payload) {
        ByteBuf buf = Unpooled.wrappedBuffer(payload);
        logger.info("MQTT处理UWB测距数据, topic:{}, payload:{}", topic, ByteBufUtil.prettyHexDump(buf));
        try {
            List<ForthinkMsg> msgList = ForthinkMsgUtil.decodeMsgList(buf);
            Map<Integer, List<ForthinkMsg>> msgMap = CollectionUtil.groupBy(msgList, ForthinkMsg::getCmd);
            for (Map.Entry<Integer, List<ForthinkMsg>> entry : msgMap.entrySet()) {
                int cmd = entry.getKey();
                List<ForthinkMsg> cmdMsgList = entry.getValue();
                logger.info("ftk-cmd, cmd:{},size:{}", Integer.toHexString(cmd), cmdMsgList.size());
                switch (cmd) {
                    //时间回传
                    case ForthinkConst.CMD_TIME:
                        logger.debug("ftk-time, topic:{},msg:{}", topic, JSONUtil.toString(cmdMsgList));
                        break;
                    //心跳
                    case ForthinkConst.CMD_HEART:
                        logger.debug("ftk-heart, topic:{},msg:{}", topic, JSONUtil.toString(cmdMsgList));
                        break;
                    //距离回传
                    case ForthinkConst.CMD_TOF_DATA:
                        cmdMsgList.forEach(msg -> {
                            ForthinkTofDataMsg tofDataMsg = (ForthinkTofDataMsg) msg;
                            indoorPositionMgr.addFtkTofData(tofDataMsg);
                        });
                        break;
                    //报警记录查询响应
                    case ForthinkConst.CMD_WARN_RECORD_QUERY_RESP:
                        cmdMsgList.forEach(msg -> {
                            ForthinkWarnRecordQueryRespMsg respMsg = (ForthinkWarnRecordQueryRespMsg) msg;
                            logger.info("ftk-cmd:{}, msg:{}", Integer.toHexString(msg.getCmd()), JSONUtil.toString(msg));
                            if (respMsg.getNum() > 0) {
                                indoorPositionMgr.addFtkWarnRecord(respMsg);
                            }
                        });
                        //回复最后一个(序号最大的)
                        ForthinkWarnRecordQueryRespMsg respMsg = (ForthinkWarnRecordQueryRespMsg) cmdMsgList.get(cmdMsgList.size() - 1);
                        this.sendToMqtt(respMsg.getStation(), respMsg.getSeq(), respMsg.getFlag());
                        break;
                    //其他指令
                    default:
                        logger.info("ftk-未知指令, topic:{},msg:{}", topic, JSONUtil.toString(cmdMsgList));
                        break;
                }
            }
        } catch (Exception ex) {
            logger.error("ftk-数据解析错误, topic:{}, payload:{}", topic, ByteBufUtil.prettyHexDump(buf), ex);
        }
    }

    private void sendToMqtt(int station, int seq, int flag) {

        //读取下发序号
        String seqKey = String.format(RedisConst.UWB_WARN_RECORD_QUERY_SEQ, station);
        String lastSeqStr = redisService.get(seqKey);
        int lastSeq = StringUtils.isNumeric(lastSeqStr) ? Integer.parseInt(lastSeqStr) : 0;

        //序号为0，表示从头开始读取数据，若收到读取响应的序号与发送的序号一致，则序号+1，否则下次读取序号保持不变
        int nextSeq = seq == lastSeq ? lastSeq + 1 : lastSeq;

        String flagKey = String.format(RedisConst.UWB_WARN_RECORD_QUERY_FLAG, station);
        redisService.set(flagKey, String.valueOf(flag), 5, TimeUnit.MINUTES);

        //flag=1,表示报警数据已经回传完毕
        if (ForthinkConst.FLAG_QUERY_END_YES == flag) {
            return;
        }

        //发送下一条读取指令
        String topic = ForthinkConst.getTopicDown(String.valueOf(station));
        ForthinkWarnRecordQueryMsg msg = new ForthinkWarnRecordQueryMsg();
        msg.setVersion(1);
        msg.setId(0XFFFFFFFF);
        msg.setSeq(nextSeq);
        ByteBuf buf = ForthinkMsgUtil.encode(msg);
        logger.info("发送ftk报警数据 topic:{},seq:{},nextSeq:{}", topic, seq, nextSeq);
        mqttMessageSender.sendToMqtt(topic, Qos.Qos0, buf.array());

        //记录下发序号
        redisService.set(seqKey, String.valueOf(nextSeq));
    }
}
