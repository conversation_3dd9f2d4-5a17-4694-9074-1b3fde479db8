package com.whfc.emp.mqtt;

import java.util.List;

/**
 * MQTT消息处理接口
 */
public interface MqttMessageProcessor {

    /**
     * 获取Topic前缀
     *
     * @return Topic前缀
     */
    String getTopicPrefix();

    /**
     * 获取订阅的Topic列表
     *
     * @return 订阅的Topic列表
     */
    List<String> getSubTopicList();

    /**
     * 处理MQTT消息
     *
     * @param topic   主题
     * @param payload 数据
     */
    void processData(String topic, byte[] payload);
}
