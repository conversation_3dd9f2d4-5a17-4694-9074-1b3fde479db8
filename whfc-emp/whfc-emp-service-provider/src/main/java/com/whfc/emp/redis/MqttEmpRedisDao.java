package com.whfc.emp.redis;

import com.whfc.common.face.szyc.SearchPerson;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/5 14:14
 */
public interface MqttEmpRedisDao {

    /**
     * 保存人员编码列表
     *
     * @param deptId      组织机构ID
     * @param empCodeList 人员编码列表
     */
    void saveEmpCodeList(Integer deptId, List<String> empCodeList);


    /**
     * 获取人员列表
     *
     * @param deptId 组织机构ID
     * @return 人员列表
     */
    List<String> getEmpCodeList(Integer deptId);

    /**
     * 获取MQTT队列长度
     *
     * @return
     */
    Integer getMqttTopicSize();

    /**
     * 保存人员查询数据
     *
     * @param searchPerson 人员查询
     */
    void saveMqttTopic(SearchPerson searchPerson);

    /**
     * 获取mqtt QueryPerson
     * @return 人员查询
     */
    SearchPerson getMqttTopic();



}
