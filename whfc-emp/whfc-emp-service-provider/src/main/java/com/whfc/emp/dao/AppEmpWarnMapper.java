package com.whfc.emp.dao;

import com.whfc.emp.dto.AppWarnEmpRecordDTO;
import com.whfc.emp.dto.AppWarnEmpRecordNumDTO;
import com.whfc.emp.entity.AppEmpWarn;
import com.whfc.entity.dto.warn.AppWarnRuleType;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface AppEmpWarnMapper {

    int insertSelective(AppEmpWarn record);

    AppEmpWarn selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppEmpWarn record);


    /**
     * 查询报警信息
     *
     * @param deptId
     * @param state
     * @param ruleType
     * @param startTime
     * @param endTime
     * @return
     */
    List<AppWarnEmpRecordDTO> selectByDeptId(@Param("deptId") Integer deptId,
                                             @Param("state") Integer state,
                                             @Param("ruleType") Integer ruleType,
                                             @Param("startTime") Date startTime,
                                             @Param("endTime") Date endTime);

    /**
     * 查询一定时间内报警数
     *
     * @param deptIds
     * @param startTime
     * @param endTime
     * @return
     */
    Integer countWarnNum(@Param("deptIds") List<Integer> deptIds,
                         @Param("ruleType") Integer ruleType,
                         @Param("startTime") Date startTime,
                         @Param("endTime") Date endTime);

    /**
     * 查找人员报警列表
     *
     * @param deptId
     * @param handleState
     * @return
     */
    List<AppWarnEmpRecordDTO> selectEmpWarnList(@Param("deptId") Integer deptId,
                                                @Param("state") Integer handleState);

    /**
     * 人员报警数量
     *
     * @param deptId
     * @param state
     * @param startTime
     * @param endTime
     * @return
     */
    List<AppWarnEmpRecordNumDTO> countWarnTypeNum(@Param("deptId") Integer deptId,
                                                  @Param("state") Integer state,
                                                  @Param("startTime") Date startTime,
                                                  @Param("endTime") Date endTime);


    /**
     * 批量处理报警记录
     *
     * @param warnIdList
     * @param handleResult
     * @param handleRemark
     * @param userId
     * @param userName
     * @param phone
     */
    void batchUpdateState(@Param("warnIdList") List<Integer> warnIdList,
                          @Param("handleResult") String handleResult,
                          @Param("handleRemark") String handleRemark,
                          @Param("userId") Integer userId,
                          @Param("userName") String userName,
                          @Param("phone") String phone);

    /**
     * 删除报警记录
     *
     * @param warnIdList
     */
    void deleteLogicByObjectIds(@Param("warnIdList") List<Integer> warnIdList);

    /**
     * 查询报警记录统计-根据小时
     * @param deptId
     * @param startTime
     * @param endTime
     * @return
     */
    List<AppWarnRuleType> countRecordByHour(@Param("deptId") Integer deptId,
                                            @Param("startTime") Date startTime,
                                            @Param("endTime") Date endTime);

    /**
     * 查询报警记录统计
     * @param startTime
     * @param endTime
     * @return
     */
    List<AppWarnRuleType> countWarnRecord( @Param("startTime") Date startTime,
                                           @Param("endTime") Date endTime);
}