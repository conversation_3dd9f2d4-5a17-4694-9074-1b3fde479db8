package com.whfc.emp.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.whfc.common.enums.*;
import com.whfc.common.exception.BizException;
import com.whfc.common.result.PageData;
import com.whfc.common.result.ResultEnum;
import com.whfc.common.util.DateUtil;
import com.whfc.common.util.PageUtil;
import com.whfc.emp.dao.*;
import com.whfc.emp.dto.AppDeviceCardLogDTO;
import com.whfc.emp.dto.AppEmpDeviceStatNumDTO;
import com.whfc.emp.dto.device.AppEmpDeviceDTO;
import com.whfc.emp.entity.AppEmp;
import com.whfc.emp.entity.AppEmpDevice;
import com.whfc.emp.entity.AppEmpDeviceLog;
import com.whfc.emp.enums.HealthType;
import com.whfc.emp.influx.AppDeviceCardLogDao;
import com.whfc.emp.param.AppEmpDeviceBindParam;
import com.whfc.emp.param.AppEmpDeviceParam;
import com.whfc.emp.param.AppEmpDeviceQueryParam;
import com.whfc.emp.service.AppEmpDeviceService;
import com.whfc.entity.dto.board.AppDeviceStatDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/26
 */
@Slf4j
@DubboService(interfaceClass = AppEmpDeviceService.class, version = "1.0.0")
public class AppEmpDeviceServiceImpl implements AppEmpDeviceService {

    @Autowired
    private AppEmpDeviceMapper appEmpDeviceMapper;

    @Autowired
    private AppEmpDeviceLogMapper appEmpDeviceLogMapper;

    @Autowired
    private AppEmpMapper appEmpMapper;

    @Autowired
    private AppEmpDataMapper appEmpDataMapper;

    @Autowired
    private AppDeviceCardLogDao appDeviceCardLogDao;

    @Autowired
    private AppEmpWarnMapper appEmpWarnMapper;

    @Override
    public PageData<AppEmpDeviceDTO> page(AppEmpDeviceQueryParam param) {
        log.info("获取智能设备数据,param:{}", param);
        Integer pageNum = ObjectUtil.defaultIfNull(param.getPageNum(), 1);
        Integer pageSize = ObjectUtil.defaultIfNull(param.getPageSize(), 10);
        PageHelper.startPage(pageNum, pageSize);
        List<AppEmpDeviceDTO> list = appEmpDeviceMapper.selectList(param);
        PageData<AppEmpDeviceDTO> pageData = PageUtil.pageData(PageInfo.of(list));
        parsePlatform(pageData.getList());
        // 统计设备在线离线数量
        // 去掉在线离线条件 避免影响查询结果
        param.setNetState(null);
        Map<String, Object> map = appEmpDeviceMapper.countDeviceNetState(param);
        pageData.setAttrs(map);
        return pageData;
    }


    @Override
    public List<AppEmpDeviceDTO> list(AppEmpDeviceQueryParam param) {
        log.info("获取智能设备数据,param:{}", param);
        List<AppEmpDeviceDTO> list = appEmpDeviceMapper.selectList(param);
        parsePlatform(list);
        return list;
    }

    @Override
    public List<AppEmpDeviceDTO> dataLog(Integer deptId, Integer corpId, Integer groupId, String keyword, Date date) {
        log.info("获取智能设备历史数据服务,deptId:{},corpId:{},groupId:{},keyword:{},date:{}", deptId, corpId, groupId, keyword, date);
        // 查询所有已绑定人员的设备信息
        AppEmpDeviceQueryParam param = AppEmpDeviceQueryParam.builder()
                .deptId(deptId)
                .corpId(corpId)
                .groupId(groupId)
                .keyword(keyword)
                .bindFlag(1)
                .build();
        List<AppEmpDeviceDTO> list = appEmpDeviceMapper.selectList(param);
        list = this.getDeviceLogData(list, date);
        return list;
    }

    @Override
    public List<AppDeviceCardLogDTO> healthLog(Integer empId, Date date, String healthType) {
        log.info("获取人员历史健康数据,empId:{}, date:{}", empId, date);
        AppEmp appEmp = appEmpMapper.selectByPrimaryKey(empId);
        if (appEmp == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.EMP_BE_001.getCode());
        }
        Date startTime = cn.hutool.core.date.DateUtil.beginOfDay(date);
        Date endTime = cn.hutool.core.date.DateUtil.endOfDay(date);
        List<AppDeviceCardLogDTO> list = appDeviceCardLogDao.selectHelmetDataLogListByEmpId(empId, startTime, endTime);
        if (StringUtils.isNotBlank(healthType)) {
            // 数据处理
            return list.stream()
                    .filter(dto -> {
                        if (HealthType.HEART_RATE.getValue().equals(healthType)) {
                            return ObjectUtil.isNotEmpty(dto.getHeartRate());
                        } else if (HealthType.BLOOD_PRESSURE.getValue().equals(healthType)) {
                            return ObjectUtil.isNotEmpty(dto.getSystolicPressure());
                        } else if (HealthType.BLOOD_SUGAR.getValue().equals(healthType)) {
                            return ObjectUtil.isNotEmpty(dto.getBloodSugar());
                        } else if (HealthType.BLOOD_OXYGEN.getValue().equals(healthType)) {
                            return ObjectUtil.isNotEmpty(dto.getBloodOxygen());
                        } else if (HealthType.BODY_TEMP.getValue().equals(healthType)) {
                            return ObjectUtil.isNotEmpty(dto.getBodyTemp());
                        }
                        return false;
                    })
                    .collect(Collectors.toList());
        }

        return list;
    }

    @Override
    public AppDeviceCardLogDTO healthData(Integer empId, Date date) {
        log.info("获取人员最新健康数据,empId:{}, date:{}", empId, date);
        Date startTime = cn.hutool.core.date.DateUtil.beginOfDay(date);
        Date endTime = cn.hutool.core.date.DateUtil.endOfDay(date);
        return appDeviceCardLogDao.selectHealthByEmpId(empId, startTime, endTime);
    }

    @Override
    public AppEmpDeviceDTO detail(Integer empId) {
        AppEmpDeviceDTO appEmpDevice = appEmpDeviceMapper.selectByEmpId(empId);
        return appEmpDevice;
    }

    @Override
    public void save(Integer deptId, AppEmpDeviceParam param) {
        String sn = param.getSn();
        AppEmpDevice appEmpDevice = appEmpDeviceMapper.selectByPlatformAndSn(param.getPlatform(), sn);
        if (appEmpDevice != null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.BASE_BE_002.getCode());
        }
        appEmpDevice = new AppEmpDevice();
        BeanUtils.copyProperties(param, appEmpDevice);
        appEmpDevice.setDeptId(deptId);
        appEmpDevice.setGuid(IdUtil.fastSimpleUUID());
        appEmpDevice.setBindFlag(0);
        appEmpDeviceMapper.insertSelective(appEmpDevice);
    }

    @Override
    public void edit(AppEmpDeviceParam param) {
        String guid = param.getGuid();
        AppEmpDevice appEmpDevice = appEmpDeviceMapper.selectByGuid(guid);
        if (appEmpDevice == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.MACH_BE_001.getCode());
        }
        String sn = param.getSn();
        String platform = param.getPlatform();
        if (!appEmpDevice.getPlatform().equals(platform) || !appEmpDevice.getSn().equals(sn)) {
            AppEmpDevice empDevice = appEmpDeviceMapper.selectByPlatformAndSn(platform, sn);
            if (empDevice != null) {
                throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.BASE_BE_002.getCode());
            }
        }
        BeanUtils.copyProperties(param, appEmpDevice);
        appEmpDeviceMapper.updateByPrimaryKeySelective(appEmpDevice);
    }

    @Override
    public void del(String guid) {
        AppEmpDevice appEmpDevice = appEmpDeviceMapper.selectByGuid(guid);
        if (appEmpDevice == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.MACH_BE_001.getCode());
        }
        if (appEmpDevice.getBindFlag() == 1) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.BASE_BE_003.getCode());
        }
        appEmpDeviceMapper.logicDel(guid);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bind(Integer userId, String username, AppEmpDeviceBindParam param) {
        AppEmpDevice appEmpDevice = appEmpDeviceMapper.selectByGuid(param.getGuid());
        if (appEmpDevice == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.MACH_BE_001.getCode());
        }
        if (appEmpDevice.getBindFlag() == 1) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.BASE_BE_003.getCode());
        }

        Integer empId = param.getEmpId();
        AppEmp appEmp = appEmpMapper.selectByPrimaryKey(empId);
        if (appEmp == null) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_001.getCode());
        }
        if (appEmp.getPostState().equals(PostState.OUTER.getValue())) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_015.getCode());
        }
        Date now = new Date();
        // 绑定
        appEmpDeviceMapper.updateBindState(appEmpDevice.getId(), empId, now, username, 1);

        // 保存绑定日志
        saveBindLog(empId, appEmpDevice.getId(), userId, username, now, BindFlag.BIND.getValue());

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unbind(Integer userId, String username, String guid) {
        AppEmpDevice appEmpDevice = appEmpDeviceMapper.selectByGuid(guid);
        if (appEmpDevice == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.MACH_BE_001.getCode());
        }
        if (appEmpDevice.getBindFlag() == 0) {
            return;
        }
        Integer empId = appEmpDevice.getEmpId();
        // 解绑
        appEmpDevice.setEmpId(null);
        appEmpDevice.setBindTime(null);
        appEmpDevice.setBindUser(null);
        appEmpDevice.setBindFlag(0);
        appEmpDeviceMapper.updateBindState(appEmpDevice.getId(), null, null, null, 0);

        // 清除app_emp_data中的数据
        appEmpDataMapper.clearEmpData(empId);

        // 保存解绑日志
        saveBindLog(empId, appEmpDevice.getId(), userId, username, new Date(), BindFlag.BIND.getValue());
    }

    @Override
    public AppDeviceStatDTO stat(Integer deptId) {
        AppDeviceStatDTO statDTO = appEmpDeviceMapper.countDeviceByDeptId(deptId);
        // 统计报警数据
        Date startTime = cn.hutool.core.date.DateUtil.beginOfDay(new Date());
        Date endTime = cn.hutool.core.date.DateUtil.endOfDay(new Date());
        Integer warnNum = appEmpWarnMapper.countWarnNum(Collections.singletonList(deptId), null, startTime, endTime);
        statDTO.setWarnNum(warnNum);
        return statDTO;
    }

    /**
     * 保存绑定日志
     *
     * @param empId    员工ID
     * @param deviceId 设备ID
     * @param userId   用户ID
     * @param userName 用户名称
     * @param now      时间
     * @param bindType 绑定类型
     */
    private void saveBindLog(Integer empId, Integer deviceId, Integer userId, String userName, Date now, Integer bindType) {
        // 向app_emp_device_log中插入数据
        AppEmpDeviceLog empDeviceLog = new AppEmpDeviceLog();
        empDeviceLog.setEmpId(empId);
        empDeviceLog.setDeviceId(deviceId);
        empDeviceLog.setTime(now);
        empDeviceLog.setType(bindType);
        empDeviceLog.setUserId(userId);
        empDeviceLog.setUserName(userName);
        appEmpDeviceLogMapper.insertSelective(empDeviceLog);
    }

    /**
     * 获取安全帽历史数据
     *
     * @param list
     * @param date
     * @return
     */
    private List<AppEmpDeviceDTO> getDeviceLogData(List<AppEmpDeviceDTO> list, Date date) {
        if (list.isEmpty()) {
            return Collections.emptyList();
        }
        // 查询历史数据
        List<Integer> empIds = list.stream().map(AppEmpDeviceDTO::getEmpId).collect(Collectors.toList());
        Date startTime = DateUtil.getDateBegin(date);
        Date endTime = DateUtil.getDateEnd(date);
        List<AppDeviceCardLogDTO> appDeviceCardLogList = appDeviceCardLogDao.selectHelmetDTOByEmpIds(empIds, startTime, endTime);

        // 统计通讯次数
        for (AppEmpDeviceDTO appEmpDeviceDTO : list) {
            List<AppEmpDeviceStatNumDTO> numList = statNum(appEmpDeviceDTO, appDeviceCardLogList);
            appEmpDeviceDTO.setNumList(numList);
        }
        // 去掉当天没上线的
        list = list.stream()
                .filter(appEmpDeviceDTO -> !appEmpDeviceDTO.getNumList().isEmpty())
                .collect(Collectors.toList());
        return list;
    }

    /**
     * 硬件平台转换
     *
     * @param list
     */
    private void parsePlatform(List<AppEmpDeviceDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        // 硬件平台转换
        for (AppEmpDeviceDTO appEmpDeviceDTO : list) {
            String platform = DevicePlatform.getPlatformName(appEmpDeviceDTO.getPlatform());
            appEmpDeviceDTO.setPlatform(platform);
        }
    }


    /**
     * 统计各时间段硬件通讯次数
     *
     * @param appEmpDeviceDTO
     * @param appDeviceCardLogList
     */
    private List<AppEmpDeviceStatNumDTO> statNum(AppEmpDeviceDTO appEmpDeviceDTO, List<AppDeviceCardLogDTO> appDeviceCardLogList) {
        Integer empId = appEmpDeviceDTO.getEmpId();
        List<AppEmpDeviceStatNumDTO> numList = new ArrayList<>();

        for (AppDeviceCardLogDTO appDeviceCardLogDTO : appDeviceCardLogList) {
            if (empId.equals(Integer.parseInt(appDeviceCardLogDTO.getEmpId()))) {
                Date startTime = Date.from(appDeviceCardLogDTO.getDeviceTime());
                Integer count = appDeviceCardLogDTO.getCount();
                AppEmpDeviceStatNumDTO data = new AppEmpDeviceStatNumDTO();
                data.setNum(count);
                data.setStartTime(startTime);
                data.setEndTime(DateUtil.addHours(startTime, 2));
                numList.add(data);
            }
        }
        numList.sort(Comparator.comparing(AppEmpDeviceStatNumDTO::getStartTime));
        return numList;
    }

}
