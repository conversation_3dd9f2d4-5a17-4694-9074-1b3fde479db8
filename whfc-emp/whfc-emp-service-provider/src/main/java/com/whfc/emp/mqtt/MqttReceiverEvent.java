package com.whfc.emp.mqtt;

import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/9 16:22
 */
@Deprecated
public class MqttReceiverEvent extends ApplicationEvent {

    private String topic;

    private String message;

    public MqttReceiverEvent(String topic,String message) {
        super(message);
        this.topic = topic;
        this.message = message;
    }

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getMessage() {
        return message;
    }
}
