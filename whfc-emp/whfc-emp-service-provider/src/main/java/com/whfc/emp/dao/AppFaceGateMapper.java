package com.whfc.emp.dao;

import com.whfc.emp.dto.AppEmpDeviceNumDTO;
import com.whfc.emp.dto.AppFaceGateDTO;
import com.whfc.emp.entity.AppFaceGate;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;

public interface AppFaceGateMapper {
    int deleteByPrimaryKey(Integer id);

    int insertSelective(AppFaceGate record);

    AppFaceGate selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppFaceGate record);

    /**
     * 根据deptId 与 keyword查找
     *
     * @param deptId
     * @param keyword
     * @return
     */
    List<AppFaceGateDTO> selectByDeptId(@Param("deptId") Integer deptId, @Param("keyword") String keyword);

    /**
     * 根据deviceKey查找
     *
     * @param deviceKey
     * @return
     */
    AppFaceGate selectByDeviceKey(@Param("deviceKey") String deviceKey);

    /**
     * 根据deviceKey查找
     *
     * @param deptId
     * @param deviceKey
     * @return
     */
    AppFaceGateDTO selectByDeptIdAndDeviceKey(@Param("deptId") Integer deptId, @Param("deviceKey") String deviceKey);

    /**
     * 逻辑删除
     *
     * @param faceGateId
     */
    void deleteLogicById(@Param("faceGateId") Integer faceGateId);

    /**
     * 逻辑删除
     *
     * @param deptId
     * @param deviceKey
     */
    void logicDelByDeptIdAndDeviceKey(@Param("deptId") Integer deptId, @Param("deviceKey") String deviceKey);

    /**
     * 根据平台查找设备
     *
     * @param platform 平台编码
     * @return
     */
    List<AppFaceGate> selectByPlatform(@Param("platform") String platform);

    /**
     * 更新设备状态
     *
     * @param id
     * @param state
     */
    int updateState(@Param("id") Integer id, @Param("state") Integer state, @Param("time") Date time);

    /**
     * 设置设备离线
     */
    int updateOffline();

    /**
     * 统计设备数量
     * @param deptIds
     * @return
     */
    AppEmpDeviceNumDTO statFaceGate(@Param("deptIds") Collection<Integer> deptIds);
}