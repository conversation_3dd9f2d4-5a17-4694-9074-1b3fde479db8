package com.whfc.emp.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.whfc.common.enums.I18nErrorCode;
import com.whfc.common.enums.RecMode;
import com.whfc.common.exception.BizException;
import com.whfc.common.file.FileHandler;
import com.whfc.common.file.FilePathConfig;
import com.whfc.common.file.properties.FileExpirationRules;
import com.whfc.common.geometry.Point;
import com.whfc.common.result.PageData;
import com.whfc.common.result.ResultEnum;
import com.whfc.common.third.map.MapApiFactory;
import com.whfc.common.third.map.MapLoc;
import com.whfc.common.util.DateUtil;
import com.whfc.common.util.FileUtil;
import com.whfc.common.util.PageUtil;
import com.whfc.emp.dao.*;
import com.whfc.emp.dto.*;
import com.whfc.emp.entity.AppEmp;
import com.whfc.emp.entity.AppFaceGate;
import com.whfc.emp.entity.AppFaceGateConfig;
import com.whfc.emp.entity.AppFaceGatePerson;
import com.whfc.emp.enums.*;
import com.whfc.emp.factory.FaceGateManagerFactory;
import com.whfc.emp.factory.FaceGateManagerWapper;
import com.whfc.emp.manager.AppFaceGateManager;
import com.whfc.emp.param.*;
import com.whfc.emp.redis.FaceGateRedisDao;
import com.whfc.emp.service.AppFaceGateService;
import com.whfc.emp.third.EmpThirdSyncFactory;
import com.whfc.emp.third.EmpThirdSyncWapper;
import com.whfc.entity.dto.OssPathDTO;
import com.whfc.fuum.service.SysDeptService;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.ObjectUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 闸机管理
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/1/4 10:34
 */
@DubboService(interfaceClass = AppFaceGateService.class, version = "1.0.0", timeout = 60 * 1000)
public class AppFaceGateServiceImpl implements AppFaceGateService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private AppFaceGateMapper appFaceGateMapper;

    @Autowired
    private FaceGateManagerFactory faceGateManagerFactory;

    @Autowired
    private AppFaceGatePersonMapper appFaceGatePersonMapper;

    @Autowired
    private AppFaceGateConfigMapper appFaceGateConfigMapper;

    @Autowired
    private AppEmpMapper appEmpMapper;

    @Autowired
    private AppFaceGateRecordMapper appFaceGateRecordMapper;

    @Autowired
    private FaceGateRedisDao faceGateRedisDao;

    @Autowired
    private AppFaceGatePlatformMapper appFaceGatePlatformMapper;

    @Autowired
    private MapApiFactory mapApiFactory;

    @Autowired
    private AppFaceGateManager appFaceGateManager;

    @DubboReference(interfaceClass = SysDeptService.class, version = "1.0.0")
    private SysDeptService sysDeptService;

    @Autowired
    private FilePathConfig msFilePathConfig;

    @Autowired
    private FileHandler fileHandler;

    @Autowired
    private EmpThirdSyncFactory empThirdSyncFactory;

    @Override
    public List<AppFaceGateDTO> list(Integer deptId, String keyword) {
        logger.info("获取闸机列表,deptId:{},keyword:{}", deptId, keyword);
        List<AppFaceGateDTO> list = appFaceGateMapper.selectByDeptId(deptId, keyword);
        // 查询闸机授权人数
        List<AppFaceGateDTO> authEmpList = appFaceGatePersonMapper.selectCountByFaceGateId(deptId);
        Map<Integer, Integer> map = authEmpList.stream().collect(Collectors.toMap(AppFaceGateDTO::getFaceGateId, AppFaceGateDTO::getAuthEmpNum));
        for (AppFaceGateDTO appFaceGateDTO : list) {
            Integer count = map.get(appFaceGateDTO.getFaceGateId());
            appFaceGateDTO.setAuthEmpNum(count == null ? 0 : count);
        }
        return list;
    }

    @Override
    public void add(AppFaceGateAddParam request) {
        logger.info("添加闸机,request:{}", request);
        FaceGateManagerWapper faceGateService = faceGateManagerFactory.getFaceGateService(request.getDeptId(), request.getPlatform());
        faceGateService.add(request);
    }

    @Override
    public void edit(AppFacaGateEditParam request) {
        logger.info("修改闸机,request:{}", request);
        Integer faceGateId = request.getFaceGateId();
        AppFaceGate faceGate = appFaceGateMapper.selectByPrimaryKey(faceGateId);
        if (faceGate == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.EMP_BE_003.getCode());
        }
        faceGate.setName(request.getName());
        faceGate.setDeptId(request.getDeptId());
        faceGate.setDirection(request.getDirection());
        faceGate.setModel(request.getModel());
        faceGate.setAreaId(request.getAreaId());
        faceGate.setAreaName(request.getAreaName());

        Point point = request.getPoint();
        // 增加闸机位置信息
        if (point != null) {
            Double lng = point.getLng();
            Double lat = point.getLat();
            MapLoc loc = mapApiFactory.getMapApi().geocode(lng, lat);
            faceGate.setLat(lat);
            faceGate.setLng(lng);
            faceGate.setAddress(loc.getAddress());
        }
        appFaceGateMapper.updateByPrimaryKeySelective(faceGate);
        // 更新缓存
        AppFaceGateDTO faceGateDTO = new AppFaceGateDTO();
        BeanUtils.copyProperties(faceGate, faceGateDTO);
        faceGateDTO.setFaceGateId(faceGateId);
        faceGateRedisDao.set(faceGate.getDeviceKey(), faceGateDTO);
    }

    @Override
    public void del(Integer faceGateId) {
        logger.info("删除闸机,faceGateId:{}", faceGateId);
        AppFaceGate faceGate = appFaceGateMapper.selectByPrimaryKey(faceGateId);
        if (faceGate == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.EMP_BE_003.getCode());
        }
        // 删除闸机
        FaceGateManagerWapper faceGateService = faceGateManagerFactory.getFaceGateService(faceGateId);
        faceGateService.del(faceGateId);
        // 删除缓存
        faceGateRedisDao.del(faceGate.getDeviceKey());
    }

    @Override
    public PageData<AppFaceGateEmpDTO> empList(Integer faceGateId, String keyword, Integer groupId, Integer type, Integer workTypeId, Integer pageNum, Integer pageSize) {
        logger.info("查看闸机授权人员,faceGateId:{},keyword:{},groupId:{}，type：{},workTypeId:{},pageNum:{},pageSize:{}", faceGateId, keyword, groupId, type, workTypeId, pageNum, pageSize);
        PageHelper.startPage(pageNum, pageSize);
        List<AppFaceGateEmpDTO> list = appFaceGatePersonMapper.selectByFaceGateId(faceGateId, keyword, groupId, workTypeId, type);
        return PageUtil.pageData(PageInfo.of(list));
    }

    @Override
    public PageData<AppFaceGateEmpDTO> failEmpList(Integer faceGateId, String keyword, Integer groupId, Integer pageNum, Integer pageSize) throws BizException {
        logger.info("查看闸机授权失败人员,faceGateId:{},keyword:{},workTypeId:{},pageNum:{},pageSize:{}", faceGateId, keyword, groupId, pageNum, pageSize);
        PageHelper.startPage(pageNum, pageSize);
        List<AppFaceGateEmpDTO> list = appFaceGatePersonMapper.selectByFailFaceGateId(faceGateId, keyword, groupId);
        return PageUtil.pageData(PageInfo.of(list));
    }

    @Override
    public PageData<AppFaceGateEmpDTO> unGrantEmpList(Integer deptId, Integer faceGateId, Integer groupId, String keyword, Integer pageNum, Integer pageSize) {
        logger.info("获取未授权的人员,faceGateId:{},keyword:{},groupId:{},deptId:{},pageNum:{},pageSize:{}", faceGateId, keyword, groupId, deptId, pageNum, pageSize);
        PageHelper.startPage(pageNum, pageSize);
        List<AppFaceGateEmpDTO> list = appFaceGatePersonMapper.selectUnGrantEmp(deptId, faceGateId, groupId, keyword);
        return PageUtil.pageData(PageInfo.of(list));
    }

    @Override
    public void faceGateGrantEmp(AppFaceGateGrantEmpParam request) {
        logger.info("闸机-添加人员授权,request:{}", request);

        Integer faceGateId = request.getFaceGateId();
        // 验证闸机信息
        AppFaceGate faceGate = appFaceGateMapper.selectByPrimaryKey(faceGateId);
        if (faceGate == null) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_003.getCode());
        }
        FaceGateType faceGateType = FaceGateType.parseCode(faceGate.getPlatform());

        if (faceGateType == null || !faceGateType.isOnline()) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_065.getCode());
        }
        String deviceKey = faceGate.getDeviceKey();
        request.setDeviceKey(deviceKey);
        // 是否支持批量同步处理
        if (faceGateType.isSupportBatch()) {
            // 批量同步
            CompletableFuture.runAsync(() -> this.faceGateBatchGrantEmpAuth(request));
            return;
        }

        // 添加人员授权
        List<Integer> empIdList = request.getEmpIdList();
        StringBuilder message = new StringBuilder();
        for (Integer empId : empIdList) {
            // 单个人员闸机授权
            faceGateEmpGrant(empId, message, faceGateId, deviceKey);
        }
        if (message.length() > 0) {
            throw new BizException(ResultEnum.FAILURE.getCode(), message.toString());
        }
    }


    @Override
    public void faceGateRevokeEmp(AppFaceGateGrantEmpParam request) {
        logger.info("闸机-取消人员授权,request:{}", request);
        FaceGateManagerWapper faceGateService = faceGateManagerFactory.getFaceGateService(request.getFaceGateId());
        faceGateService.faceGateRevokeEmp(request);
    }

    @Override
    public String deviceAuthorizationCancel(String deviceKey, String personGuid) {
        logger.info("闸机取消人员授权,deviceKey:{},personGuid：{}", deviceKey, personGuid);
        FaceGateManagerWapper faceGateService = faceGateManagerFactory.getFaceGateService(deviceKey);
        return faceGateService.deviceAuthorizationCancel(deviceKey, personGuid);
    }

    @Override
    public void sync(Integer faceGateId) {
        logger.info("同步闸机授权人员,faceGateId：{}", faceGateId);
        FaceGateManagerWapper faceGateService = faceGateManagerFactory.getFaceGateService(faceGateId);
        faceGateService.sync(faceGateId);
    }

    @Override
    public AppFaceGateDTO getByDeviceKey(String deviceKey) {
        return appFaceGateManager.getByDeviceKey(deviceKey);
    }

    @Override
    public void openDoor(Integer faceGateId) {
        AppFaceGate faceGate = appFaceGateMapper.selectByPrimaryKey(faceGateId);
        if (faceGate == null) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_003.getCode());
        }
        FaceGateManagerWapper faceGateService = faceGateManagerFactory.getFaceGateService(faceGateId);
        faceGateService.openDoor(faceGate.getDeviceKey());
    }

    @Override
    public AppFaceGatePersonDTO getByDeviceKeyAndGuid(String deviceKey, String guid) throws BizException {
        logger.info("根据闸机序列号和人员GUID查找 闸机人员信息 deviceKey:{}，guid：{}", deviceKey, guid);
        AppFaceGatePerson faceGatePerson = appFaceGatePersonMapper.selectByPersonGuidAndDeviceKey(guid, deviceKey);
        if (faceGatePerson == null) {
            return null;
        }
        AppFaceGatePersonDTO appFaceGatePersonDTO = new AppFaceGatePersonDTO();
        BeanUtils.copyProperties(faceGatePerson, appFaceGatePersonDTO);
        return appFaceGatePersonDTO;
    }

    @Override
    public List<AppFaceGatePlatformDTO> getAllFaceGatePlatform() {
        return appFaceGatePlatformMapper.selectAll();
    }

    @Override
    public List<AppFaceGateConfigDTO> getFaceGateList(Integer deptId) {
        return appFaceGateConfigMapper.selectDetailListByDeptId(deptId);
    }

    @Override
    public List<AppFaceGateConfigDTO> getFaceGateAppList(Integer deptId) {
        logger.info("查询可选闸机平台,deptId:{}", deptId);
        return appFaceGateConfigMapper.selectByDeptId(deptId);
    }

    @Override
    public void setFaceGateApp(Integer deptId, List<AppFaceGateConfigDTO> list) {
        // 删除旧数据
        appFaceGateConfigMapper.loginDelByDeptId(deptId);
        // 保存新数据
        for (AppFaceGateConfigDTO faceGateConfigDTO : list) {
            AppFaceGateConfig faceGateConfig = new AppFaceGateConfig();
            faceGateConfig.setPlatform(faceGateConfigDTO.getPlatform());
            faceGateConfig.setDeptId(deptId);
            faceGateConfig.setAppId(faceGateConfigDTO.getAppId());
            faceGateConfig.setAppKey(faceGateConfigDTO.getAppKey());
            faceGateConfig.setAppSecret(faceGateConfigDTO.getAppSecret());
            faceGateConfig.setRemark(faceGateConfigDTO.getRemark());
            appFaceGateConfigMapper.insertSelective(faceGateConfig);
        }
    }

    @Override
    public PageData<AppFaceGateRecordDTO> faceGateRecordList(Integer pageNum, Integer pageSize, Integer deptId, String keyword, Date startTime, Date endTime) {
        logger.info("人员管理-人脸识别记录(分页),pageSize：{},pageSize：{},deptId：{},keyword：{},startTime：{},endTime：{}", pageSize, pageSize, deptId, keyword, startTime, endTime);
        if (endTime == null) {
            endTime = new Date();
        }
        if (startTime == null) {
            startTime = DateUtil.getDateBegin(endTime);
        }

        // 组织机构
        PageHelper.startPage(pageNum, pageSize);
        List<AppFaceGateRecordDTO> list = appFaceGateRecordMapper.selectByDeptIdAndKeyword(deptId, startTime, endTime, keyword);
        PageHelper.clearPage();

        return PageUtil.pageData(PageInfo.of(list));
    }

    @Override
    public OssPathDTO faceGateRecordExport(FaceGateRecordExportParam param) {
        logger.info("人员管理-导出人脸识别记录,param：{}", param);

        // 获取数据
        String keyword = param.getKeyword();
        Date startTime = param.getStartTime();
        Date endTime = param.getEndTime();
        Integer deptId = param.getDeptId();
        if (endTime == null) {
            endTime = new Date();
        }
        if (startTime == null) {
            startTime = DateUtil.getDateBegin(endTime);
        }
        List<AppFaceGateRecordDTO> list = appFaceGateRecordMapper.selectByDeptIdAndKeyword(deptId, startTime, endTime, keyword);

        OssPathDTO ossPathDTO = new OssPathDTO();
        try {
            // 获取模板
            ClassPathResource resource = new ClassPathResource("templates/faceGateRecordTemplate.xls");
            InputStream templateFileInputStream = resource.getInputStream();
            File tempFile = FileUtil.copyTemplateFile(templateFileInputStream);
            String deptName = sysDeptService.getDeptNameById(deptId);
            // 写数据
            FileInputStream fileInputStream = new FileInputStream(tempFile);
            HSSFWorkbook workbook = new HSSFWorkbook(fileInputStream);
            HSSFSheet sheet = workbook.getSheetAt(0);
            HSSFRow row0 = sheet.getRow(0);
            row0.getCell(0).setCellValue(String.format("%s 刷脸记录", deptName));
            // 填充数据
            int rowIdx = 2;
            for (AppFaceGateRecordDTO faceGateRecordDTO : list) {
                HSSFRow row = sheet.createRow(rowIdx);
                Double temperature = faceGateRecordDTO.getTemperature();
                String temperatureStr = "-";
                if (temperature != null) {
                    temperatureStr = String.valueOf(temperature);
                }
                row.createCell(0).setCellValue(faceGateRecordDTO.getEmpName());
                row.createCell(1).setCellValue(StringUtils.isNotEmpty(faceGateRecordDTO.getIdCardNo()) ? faceGateRecordDTO.getIdCardNo() : "-");
                row.createCell(2).setCellValue(StringUtils.isNotEmpty(faceGateRecordDTO.getGroupName()) ? faceGateRecordDTO.getGroupName() : "-");
                row.createCell(3).setCellValue(faceGateRecordDTO.getBirthday() != null ? DateUtil.formatDate(faceGateRecordDTO.getBirthday()) : "-");
                row.createCell(4).setCellValue(faceGateRecordDTO.getGender() != null ? GenderCode.parseByValue(faceGateRecordDTO.getGender()) : "-");
                row.createCell(5).setCellValue(StringUtils.isNotEmpty(faceGateRecordDTO.getPhone()) ? faceGateRecordDTO.getPhone() : "-");
                row.createCell(6).setCellValue(faceGateRecordDTO.getName());
                row.createCell(7).setCellValue(RecMode.parseValue(faceGateRecordDTO.getRecMode()).getDesc());
                row.createCell(8).setCellValue(Direction.parseValue(faceGateRecordDTO.getDirection()).getDesc());
                row.createCell(9).setCellValue(faceGateRecordDTO.getPhotoUrl());
                row.createCell(10).setCellValue(temperatureStr);
                row.createCell(11).setCellValue(DateUtil.formatDate(faceGateRecordDTO.getShowTime(), "yyyy-MM-dd HH:mm:ss"));
                rowIdx++;
            }

            // 保存excel
            FileOutputStream fos = new FileOutputStream(tempFile);
            workbook.write(fos);
            fos.flush();
            fos.close();

            // 上传oss
            String name = msFilePathConfig.getFilePath("emp/tmp", String.format("%s识别记录.xls", deptName));
            FileInputStream inputStream = new FileInputStream(tempFile);
            String upload = fileHandler.upload(name, inputStream, FileExpirationRules.oneDay);
            ossPathDTO.setPath(upload);
        } catch (Exception e) {
            logger.error("识别记录导出异常", e);
        }
        return ossPathDTO;
    }

    @Override
    public PageData<AppFaceGateRecordDTO> strangerList(Integer pageNum, Integer pageSize, Integer deptId, Date startTime, Date endTime) {
        logger.info("人员管理-陌生人识别记录,pageSize：{},pageSize：{},deptId：{},startTime：{},endTime：{}", pageSize, pageSize, deptId, startTime, endTime);
        if (startTime != null) {
            startTime = DateUtil.getDateBegin(startTime);
        }
        if (endTime != null) {
            endTime = DateUtil.getDateEnd(endTime);
        }

        PageHelper.startPage(pageNum, pageSize);
        List<AppFaceGateRecordDTO> list = appFaceGateRecordMapper.selectStrangerList(deptId, startTime, endTime);
        return PageUtil.pageData(PageInfo.of(list));
    }

    @Override
    public void faceGateRecordSync(Integer deptId, Date startTime, Date endTime) throws BizException {
        List<AppFaceGateRecordDTO> recordList = appFaceGateRecordMapper.selectByDeptIdAndTime(deptId, startTime, endTime);
        List<AppFaceGateDTO> faceGateList = appFaceGateMapper.selectByDeptId(deptId, null);
        Double lng = 0D;
        Double lat = 0D;
        if (faceGateList.size() > 0) {
            lng = faceGateList.get(0).getLng();
            lat = faceGateList.get(0).getLat();
        }
        logger.info("手动同步实名制考勤-同步数量,dept:{},startTime:{},endTime:{},size:{}", deptId, startTime, endTime, recordList.size());

        List<EmpThirdSyncWapper> wapperList = empThirdSyncFactory.sync(deptId);
        for (EmpThirdSyncWapper wapper : wapperList) {
            for (AppFaceGateRecordDTO record : recordList) {
                FaceGateRecordParam param = new FaceGateRecordParam();
                param.setDeptId(deptId);
                param.setDeviceKey(record.getDeviceKey());
                param.setEmpId(record.getEmpId());
                param.setPhotoUrl(record.getPhotoUrl());
                param.setShowTime(record.getShowTime());
                param.setDirection(record.getDirection());
                param.setLng(lng);
                param.setLat(lat);
                try {
                    wapper.getSync().syncEmpAttend(param, wapper.getConfig());
                } catch (Exception e) {
                    logger.error("开始同步实名制考勤失败", e.getMessage());
                }
            }
        }
    }

    @Override
    public void faceGateRecordSync(Integer deptId, Integer empId, Date showTime) throws BizException {
        List<AppFaceGateRecordDTO> recordList = appFaceGateRecordMapper.selectByDeptIdAndTime(deptId, showTime, showTime);
        logger.info("手动同步实名制考勤-同步数量,dept:{},empId:{},showTime:{},size:{}", deptId, empId, showTime, recordList.size());
        List<EmpThirdSyncWapper> wapperList = empThirdSyncFactory.sync(deptId);
        if (wapperList.size() == 0) {
            return;
        }
        EmpThirdSyncWapper wapper = wapperList.get(0);

        for (AppFaceGateRecordDTO record : recordList) {
            if (record.getEmpId().equals(empId)) {
                FaceGateRecordParam param = new FaceGateRecordParam();
                param.setDeptId(deptId);
                param.setDeviceKey(record.getDeviceKey());
                param.setEmpId(record.getEmpId());
                param.setPhotoUrl(record.getPhotoUrl());
                param.setShowTime(record.getShowTime());
                param.setDirection(record.getDirection());
                try {
                    wapper.getSync().syncEmpAttend(param, wapper.getConfig());
                } catch (Exception e) {
                    logger.error("手动同步实名制考勤失败", e.getMessage());
                }
            }
        }
    }

    @Override
    public PageData<AppFaceGateDTO> listByEmp(Integer empId, Integer pageNum, Integer pageSize) {
        logger.info("闸机人员列表,empId:{},pageNum:{},pageSize:{}", empId, pageNum, pageSize);
        // 查询人员信息
        AppEmpDTO appEmpDTO = appEmpMapper.selectEmpDTOById(empId);
        if (ObjectUtils.isEmpty(appEmpDTO)) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_001.getCode());
        }
        PageHelper.startPage(pageNum, pageSize);
        // 查询闸机列表
        List<AppFaceGateDTO> faceGateDTOS = appFaceGateMapper.selectByDeptId(appEmpDTO.getDeptId(), null);
        if (ObjectUtils.isEmpty(faceGateDTOS)) {
            return PageUtil.emptyPage();
        }
        PageData<AppFaceGateDTO> pageData = PageUtil.pageData(PageInfo.of(faceGateDTOS));

        // 查询当前用户闸机是否授权
        List<Integer> faceGateIds = faceGateDTOS.stream().map(AppFaceGateDTO::getFaceGateId).collect(Collectors.toList());
        List<AppFaceGatePerson> faceGatePersonList = appFaceGatePersonMapper.selectByEmpIdAndFackGateList(empId, faceGateIds);
        for (AppFaceGateDTO faceGateDTO : faceGateDTOS) {
            int isGrant = 0;
            for (AppFaceGatePerson appFaceGatePerson : faceGatePersonList) {
                if (faceGateDTO.getFaceGateId().equals(appFaceGatePerson.getFaceGateId())) {
                    faceGateDTO.setTaskType(appFaceGatePerson.getTaskType());
                    faceGateDTO.setStateMessage(appFaceGatePerson.getMessage());
                    isGrant = 1;
                }
            }
            faceGateDTO.setIsGrant(isGrant);
        }
        return pageData;
    }

    @Override
    public void faceGateGrantEmpBatch(AppFaceGateGrantEmpBatchParam request) {
        Integer grantType = request.getGrantType();
        Integer empId = request.getEmpId();
        List<Integer> faceGateIdList = request.getFaceGateIdList();
        for (Integer faceGateId : faceGateIdList) {
            if (GrantType.UN_GRANT.getValue().equals(grantType)) {
                // 取消授权闸机
                // 获取闸机服务
                FaceGateManagerWapper faceGateService = faceGateManagerFactory.getFaceGateService(faceGateId);
                AppFaceGateGrantEmpParam faceGateGrantEmpParam = AppFaceGateGrantEmpParam.builder()
                        .faceGateId(faceGateId)
                        .empIdList(Collections.singletonList(empId)).build();
                // 取消授权
                faceGateService.faceGateRevokeEmp(faceGateGrantEmpParam);
            } else if (GrantType.GRANT.getValue().equals(grantType)) {
                // 授权闸机
                // 查询闸机信息
                AppFaceGate appFaceGate = appFaceGateMapper.selectByPrimaryKey(faceGateId);
                if (ObjectUtils.isEmpty(appFaceGate)) {
                    logger.error("授权闸机不存在, faceGateId={}", faceGateId);
                    continue;
                }
                StringBuilder message = new StringBuilder();
                // 闸机授权
                faceGateEmpGrant(empId, message, faceGateId, appFaceGate.getDeviceKey());
                if (message.length() > 0) {
                    throw new BizException(ResultEnum.FAILURE.getCode(), message.toString());
                }
            }
        }
    }

    @Override
    public AppEmpDeviceNumDTO statFaceGate(Integer deptId) {
        return appFaceGateMapper.statFaceGate(Collections.singletonList(deptId));
    }

    private void faceGateBatchGrantEmpAuth(AppFaceGateGrantEmpParam request) throws BizException {
        logger.info("闸机人员批量授权,request:{}", request);
        FaceGateManagerWapper faceGateService = faceGateManagerFactory.getFaceGateService(request.getDeviceKey());
        faceGateService.faceGateBatchGrantEmpAuth(request);
    }

    private String faceGateGrantEmdAdd(FaceGateGrantEmdAddParam request) {
        logger.info("闸机人员注册,request:{}", request);
        FaceGateManagerWapper faceGateService = faceGateManagerFactory.getFaceGateService(request.getDeviceKey());
        return faceGateService.faceGateGrantEmdAdd(request);
    }

    private String faceGateGrantEmdImgAdd(FaceGateGrantEmdImgAddParam request) {
        logger.info("闸机人员照片注册,request:{}", request);
        FaceGateManagerWapper faceGateService = faceGateManagerFactory.getFaceGateService(request.getDeviceKey());
        return faceGateService.faceGateGrantEmdImgAdd(request);
    }

    private String faceGateGrantEmdAuth(FaceGateGrantEmdAuthParam request) {
        logger.info("闸机人员授权,request:{}", request);
        FaceGateManagerWapper faceGateService = faceGateManagerFactory.getFaceGateService(request.getDeviceKey());
        return faceGateService.faceGateGrantEmdAuth(request);
    }

    /**
     * 单个人员授权
     *
     * @param empId      人员ID
     * @param message    授权失败信息
     * @param faceGateId 闸机ID
     * @param deviceKey  闸机序列号
     */
    private void faceGateEmpGrant(Integer empId, StringBuilder message, Integer faceGateId, String deviceKey) {
        AppEmp appPerson = appEmpMapper.selectByPrimaryKey(empId);
        String empName = appPerson == null ? "" : appPerson.getEmpName();
        // 验证照片
        if (appPerson == null || StringUtils.isBlank(appPerson.getAvatar())) {
            message.append(empName).append("请您先上传人员头像，再进行闸机授权");
            logger.info("empId:{}>>>>>该人员不存在或者无图像,不能授权闸机", empId);
            return;
        }
        // 验证empCode
        String empCode = appPerson.getEmpCode();
        if (StringUtils.isBlank(empCode)) {
            empCode = String.valueOf(empId);
            appPerson.setEmpCode(empCode);
            appEmpMapper.updateByPrimaryKeySelective(appPerson);
        }
        // 验证是否重复授权
        AppFaceGatePerson faceGatePerson = appFaceGatePersonMapper.selectByFaceGateIdAndEmpId(faceGateId, empId);
        if (faceGatePerson != null) {
            Integer taskType = faceGatePerson.getTaskType();
            // 未开始 or 执行中
            if (TaskType.EMP_ADD.getValue().equals(taskType) || TaskType.EMP_IMG_ADD.getValue().equals(taskType) || TaskType.EMP_AUTH.getValue().equals(taskType)) {
                message.append(empName).append("该人员正在授权");
                logger.info("empId:{}>>>>>该人员正在执行定时任务", empId);
                return;
            }
            // 执行失败,初始化-重新执行
            else {
                faceGatePerson.setTaskType(TaskType.NOT_START.getValue());
                faceGatePerson.setDeptId(appPerson.getDeptId());
                appFaceGatePersonMapper.updateByPrimaryKeySelective(faceGatePerson);
            }
        } else {
            faceGatePerson = new AppFaceGatePerson();
            faceGatePerson.setDeptId(appPerson.getDeptId());
            faceGatePerson.setDeviceKey(deviceKey);
            faceGatePerson.setFaceGateId(faceGateId);
            faceGatePerson.setEmpId(empId);
            faceGatePerson.setPersonGuid(appPerson.getEmpCode());
            faceGatePerson.setTaskType(TaskType.NOT_START.getValue());
            appFaceGatePersonMapper.insertSelective(faceGatePerson);
        }

        Integer personId = faceGatePerson.getId();

        CompletableFuture.supplyAsync(() -> {
            FaceGateGrantEmdAddParam param = new FaceGateGrantEmdAddParam();
            param.setDeviceKey(deviceKey);
            param.setEmpId(empId);
            param.setPersonId(personId);
            return faceGateGrantEmdAdd(param);
        }).thenApply(personGuid -> {
            if (StringUtils.isNotBlank(personGuid)) {
                FaceGateGrantEmdImgAddParam param = new FaceGateGrantEmdImgAddParam();
                param.setImgUrl(appPerson.getAvatar());
                param.setPersonGuid(personGuid);
                param.setPersonId(personId);
                param.setDeviceKey(deviceKey);
                return faceGateGrantEmdImgAdd(param);
            }
            return null;
        }).thenAccept(personGuid -> {
            if (StringUtils.isNotBlank(personGuid)) {
                FaceGateGrantEmdAuthParam param = new FaceGateGrantEmdAuthParam();
                param.setPersonId(personId);
                param.setPersonGuid(personGuid);
                param.setDeviceKey(deviceKey);
                faceGateGrantEmdAuth(param);
            }
        });
    }
}
