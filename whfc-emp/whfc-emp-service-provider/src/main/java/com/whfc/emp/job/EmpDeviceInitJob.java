package com.whfc.emp.job;

import cn.hutool.core.util.IdUtil;
import com.whfc.XxlJobConfig;
import com.whfc.base.dto.AppDeviceDTO;
import com.whfc.base.service.AppDeviceService;
import com.whfc.emp.dao.AppEmpDataMapper;
import com.whfc.emp.dao.AppEmpDeviceMapper;
import com.whfc.emp.entity.AppEmpData;
import com.whfc.emp.entity.AppEmpDevice;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 人员设备初始化
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/27
 */
@Slf4j
@Component
@ConditionalOnBean(XxlJobConfig.class)
public class EmpDeviceInitJob {

    @DubboReference(interfaceClass = AppDeviceService.class, version = "1.0.0")
    private AppDeviceService appDeviceService;

    @Autowired
    private AppEmpDeviceMapper appEmpDeviceMapper;

    @Autowired
    private AppEmpDataMapper appEmpDataMapper;

    @XxlJob("init-emp-device-data")
    public void initEmpDeviceData() {
        log.info("初始化人员设备数据");
        try {
            // 获取所有未初始化数据
            List<AppEmpDevice> appEmpDevices = appEmpDeviceMapper.selectNoInitData();
            if (CollectionUtils.isEmpty(appEmpDevices)) {
                return;
            }
            // 查询设备信息
            List<Integer> deviceIds = appEmpDevices.stream().map(AppEmpDevice::getDeviceId).collect(Collectors.toList());
            List<AppDeviceDTO> appDevices = appDeviceService.listByIdList(deviceIds);
            Map<Integer, AppDeviceDTO> appDeviceMap = appDevices.stream()
                    .collect(Collectors.toMap(AppDeviceDTO::getId, appDevice -> appDevice));

            // 查询设备人员数据
            List<Integer> empIds = appEmpDevices.stream().map(AppEmpDevice::getEmpId).collect(Collectors.toList());
            List<AppEmpData> appEmpDataList = appEmpDataMapper.selectByEmpIdList(empIds);
            Map<Integer, AppEmpData> appEmpDataMap = appEmpDataList.stream()
                    .collect(Collectors.toMap(AppEmpData::getEmpId, appEmpData -> appEmpData));
            // 设置人员设备信息
            for (AppEmpDevice appEmpDevice : appEmpDevices) {
                appEmpDevice.setGuid(IdUtil.fastSimpleUUID());

                appEmpDevice.setDeviceType(2);
                appEmpDevice.setBindFlag(1);
                AppDeviceDTO appDevice = appDeviceMap.get(appEmpDevice.getDeviceId());
                if (appDevice != null) {
                    appEmpDevice.setPlatform(appDevice.getExt1());
                    appEmpDevice.setSn(appDevice.getCode());
                    appEmpDevice.setColor(appDevice.getColor());
                }
                AppEmpData appEmpData = appEmpDataMap.get(appEmpDevice.getEmpId());
                if (appEmpData != null) {
                    appEmpDevice.setDeptId(appEmpData.getDeptId());
                    appEmpDevice.setNetState(appEmpData.getNetState());
                    appEmpDevice.setBatteryPower(appEmpData.getBatteryPower());
                    appEmpDevice.setTime(appEmpData.getTime());
                }
                appEmpDeviceMapper.updateByPrimaryKeySelective(appEmpDevice);
            }
        } catch (Exception ex) {
            log.error("初始化人员设备数据,出现异常", ex);
        }
    }
}
