package com.whfc.emp.redis.impl;

import com.whfc.emp.redis.IdCardIdentifyRedisDao;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import java.text.MessageFormat;
import java.util.concurrent.TimeUnit;

/**
 * @Description 人员识别次数限制缓存
 * <AUTHOR>
 * @Date 2021-08-06 10:38
 * @Version 1.0
 */
@Repository
public class IdCardIdentifyRedisDaoImpl implements IdCardIdentifyRedisDao {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    private static final String EMP_ID_CARD_IDENTIFY_NUM = "emp-id_card-identify-num::{0}";

    private static final int EXPIRE_MINUTES = 30;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Override
    public void addIdCardIdentify(String sessionId, Integer index) {
        String key = MessageFormat.format(EMP_ID_CARD_IDENTIFY_NUM, sessionId);
        redisTemplate.opsForValue().set(key, index.toString(), EXPIRE_MINUTES, TimeUnit.MINUTES);
    }

    @Override
    public Integer getIdCardIdentify(String sessionId) {
        String key = MessageFormat.format(EMP_ID_CARD_IDENTIFY_NUM, sessionId);
        String index = redisTemplate.opsForValue().get(key);
        if (StringUtils.isEmpty(index)) {
            return 0;
        }
        return Integer.valueOf(index);
    }
}
