package com.whfc.emp.dao;

import com.whfc.emp.entity.AppBroadcastRecord;

public interface AppBroadcastRecordMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppBroadcastRecord record);

    int insertSelective(AppBroadcastRecord record);

    AppBroadcastRecord selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppBroadcastRecord record);

    int updateByPrimaryKey(AppBroadcastRecord record);
}