package com.whfc.emp.third;

import com.whfc.common.enums.SyncPlatform;
import com.whfc.emp.dao.AppSyncMapper;
import com.whfc.emp.entity.AppSync;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description: 人员信息-第三方核验-工厂类
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/6/30 14:53
 */
@Component
public class EmpThirdVerifyFactory {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource(name = "empThirdVerifyWuhu")
    private EmpThirdVerify empThirdVerifyWuhu;

    @Resource(name = "empThirdVerifyAlicloud")
    private EmpThirdVerify empThirdVerifyAlicloud;

    @Autowired
    private AppSyncMapper appSyncMapper;

    /**
     * 获取第三方核验服务实例
     *
     * @param deptId
     * @return
     */
    public EmpThirdVerifyWapper verify(Integer deptId) {
        List<AppSync> syncList = appSyncMapper.selectByDeptId(deptId);
        if (syncList.isEmpty()) {
            return null;
        }
        for (AppSync config : syncList) {
            //芜湖互动系统人证核验
            if (SyncPlatform.WUHU_EMP_VERIFY.name().equals(config.getPlatform())) {
                EmpThirdVerify verify = empThirdVerifyWuhu;
                return new EmpThirdVerifyWapper(verify, config);
            }
            //阿里云人证核验
            else if (SyncPlatform.ALICLOUD_EMP_VERIFY.name().equals(config.getPlatform())) {
                EmpThirdVerify verify = empThirdVerifyAlicloud;
                return new EmpThirdVerifyWapper(verify, config);
            }
        }
        return null;
    }
}
