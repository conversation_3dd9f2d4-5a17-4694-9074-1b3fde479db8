package com.whfc.emp.manager.impl;

import com.whfc.common.enums.I18nErrorCode;
import com.whfc.common.exception.BizException;
import com.whfc.common.result.ResultEnum;
import com.whfc.emp.constant.EmpConstant;
import com.whfc.emp.dao.AppEmpConfigMapper;
import com.whfc.emp.dao.AppEmpMapper;
import com.whfc.emp.dto.AppEmpSettingDTO;
import com.whfc.emp.entity.AppEmp;
import com.whfc.emp.entity.AppEmpConfig;
import com.whfc.emp.enums.AttendType;
import com.whfc.emp.manager.CommonEmpConfigManager;
import com.whfc.emp.redis.EmpPolygonWKTRedisDao;
import com.whfc.emp.redis.RestAreaPolygonWKTRedisDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * @ClasssName CommonEmpConfigManagerImpl
 * @Description 人员配置公共服务
 * <AUTHOR>
 * @Date 2020/12/28 15:57
 * @Version 1.0
 */
@Service
public class CommonEmpConfigManagerImpl implements CommonEmpConfigManager {

    @Autowired
    private AppEmpConfigMapper appEmpConfigMapper;

    @Autowired
    private AppEmpMapper appEmpMapper;

    @Autowired
    private EmpPolygonWKTRedisDao empPolygonWKTRedisDao;

    @Autowired
    private RestAreaPolygonWKTRedisDao restAreaPolygonWKTRedisDao;

    @Override
    public boolean isIdCardVerify(Integer deptId) {
        AppEmpConfig empConfig = appEmpConfigMapper.selectByDeptIdAndCode(deptId, EmpConstant.EMP_IDCARD_VERIFY_CODE);
        if (empConfig != null) {
            return EmpConstant.EMP_IDCARD_VERIFY_YES.equalsIgnoreCase(empConfig.getValue());
        }
        return false;
    }

    @Override
    public AppEmpSettingDTO getSetting(Integer deptId) {
        AppEmpSettingDTO empSettingDTO = new AppEmpSettingDTO();
        List<AppEmpConfig> list = appEmpConfigMapper.selectByDeptId(deptId);
        for (AppEmpConfig empConfig : list) {
            String code = empConfig.getCode();
            String value = empConfig.getValue();
            Integer enableFlag = empConfig.getEnableFlag();
            EmpConstant.setEmpSettingDTOValue(code, empSettingDTO, value, enableFlag);
        }
        return empSettingDTO;
    }

    @Override
    public AppEmpSettingDTO getMinutes(Integer deptId) {
        AppEmpConfig appEmpConfig = appEmpConfigMapper.selectByDeptIdAndCode(deptId, EmpConstant.TIME_OUT);
        Integer minutes = EmpConstant.DEFAULT_TIME_OUT;
        if (appEmpConfig != null) {
            minutes = Integer.parseInt(appEmpConfig.getValue());
        }
        AppEmpSettingDTO data = new AppEmpSettingDTO();
        data.setMinutes(minutes);
        return data;
    }

    @Override
    public AttendType getAttendTypeByEmpId(Integer empId) {
        AppEmp appEmp = appEmpMapper.selectByPrimaryKey(empId);
        if (appEmp == null) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_001.getCode());
        }
        Integer projectId = appEmp.getDeptId();
        AttendType attendType = this.getAttendType(projectId);
        return attendType;
    }

    @Override
    public AttendType getAttendType(Integer deptId) {
        AppEmpConfig empConfig = appEmpConfigMapper.selectByDeptIdAndCode(deptId, EmpConstant.EMP_ATTEND_CODE);
        if (empConfig != null) {
            return AttendType.parseByValue(Integer.valueOf(empConfig.getValue()));
        }
        return AttendType.FACEGATE;
    }

    @Override
    public String getPolygonWKT(Integer deptId) {
        String polygonWKT = empPolygonWKTRedisDao.getPolygonWKT(deptId);
        if (StringUtils.isEmpty(polygonWKT)) {
            AppEmpConfig appEmpConfig = appEmpConfigMapper.selectByDeptIdAndCode(deptId, EmpConstant.FENCE);
            if (appEmpConfig != null) {
                polygonWKT = appEmpConfig.getValue();
                empPolygonWKTRedisDao.setPolygonWKT(deptId, polygonWKT);
            }
        }
        return polygonWKT;
    }

    @Override
    public String getRestAreaPolygonWKT(Integer deptId) {
        String polygonWKT = restAreaPolygonWKTRedisDao.getPolygonWKT(deptId);
        if (StringUtils.isEmpty(polygonWKT)) {
            AppEmpConfig appEmpConfig = appEmpConfigMapper.selectByDeptIdAndCode(deptId, EmpConstant.REST_AREA_FENCE);
            if (appEmpConfig != null) {
                polygonWKT = appEmpConfig.getValue();
                empPolygonWKTRedisDao.setPolygonWKT(deptId, polygonWKT);
            }
        }
        return polygonWKT;
    }
}
