package com.whfc.emp.queue;

import com.alibaba.fastjson.JSON;
import com.whfc.common.constant.QueueConst;
import com.whfc.common.spring.AppContextUtil;
import com.whfc.emp.event.FaceGateDataEvent;
import com.whfc.emp.manager.AppFaceGateManager;
import com.whfc.emp.param.EmpAttendSyncDataParam;
import com.whfc.emp.param.FaceGateRecordParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-08-05 17:33
 */
@Component
@RabbitListener(queuesToDeclare = {@Queue(name = QueueConst.EMP_FACEGATE_ATTEND_DATA)}, concurrency = "1-2")
public class FaceGateAttendSyncQueueMessageListener {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private AppFaceGateManager appFaceGateManager;

    @RabbitHandler
    public void process(String msg) {
        try {
            logger.info("faceGateDataSync|msg|{}", msg);
            EmpAttendSyncDataParam dataParam = JSON.parseObject(msg, EmpAttendSyncDataParam.class);
            appFaceGateManager.handleRec(dataParam);

            //事件推送
            FaceGateRecordParam param = new FaceGateRecordParam();
            BeanUtils.copyProperties(dataParam, param);
            param.setPhotoUrl(dataParam.getPicture());
            FaceGateDataEvent event = new FaceGateDataEvent(param);
            AppContextUtil.context().publishEvent(event);

        } catch (Exception ex) {
            logger.error("faceGateDataSync|msg,消息处理失败", ex);
        }
    }


}
