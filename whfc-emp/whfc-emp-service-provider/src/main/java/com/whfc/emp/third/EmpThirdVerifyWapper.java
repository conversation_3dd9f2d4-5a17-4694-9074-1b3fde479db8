package com.whfc.emp.third;

import com.whfc.emp.entity.AppSync;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/6/30 15:24
 */
public class EmpThirdVerifyWapper {

    private EmpThirdVerify verify;

    private AppSync config;

    public EmpThirdVerifyWapper(EmpThirdVerify verify, AppSync config) {
        this.verify = verify;
        this.config = config;
    }

    public EmpThirdVerify getVerify() {
        return verify;
    }

    public void setVerify(EmpThirdVerify verify) {
        this.verify = verify;
    }

    public AppSync getConfig() {
        return config;
    }

    public void setConfig(AppSync config) {
        this.config = config;
    }
}
