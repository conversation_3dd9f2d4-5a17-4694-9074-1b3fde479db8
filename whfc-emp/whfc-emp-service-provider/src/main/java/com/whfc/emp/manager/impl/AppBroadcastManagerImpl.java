package com.whfc.emp.manager.impl;

import com.whfc.common.enums.DevicePlatform;
import com.whfc.emp.dao.AppBroadcastRecordEmpMapper;
import com.whfc.emp.dao.AppEmpDeviceMapper;
import com.whfc.emp.entity.AppBroadcastRecordEmp;
import com.whfc.emp.entity.AppEmpDevice;
import com.whfc.emp.enums.AppBroadcastSendState;
import com.whfc.emp.manager.AppBroadcastManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @ClasssName AppBroadcastManagerImpl
 * @Description 广播服务
 * <AUTHOR>
 * @Date 2021/1/4 16:01
 * @Version 1.0
 */
@Service
public class AppBroadcastManagerImpl implements AppBroadcastManager {

    @Autowired
    private AppBroadcastRecordEmpMapper appBroadcastRecordEmpMapper;

    @Autowired
    private AppEmpDeviceMapper appEmpDeviceMapper;

    @Override
    public void updateBroadcastState(Integer id, String code, Integer state) {
        if (id != null) {
            appBroadcastRecordEmpMapper.updateState(id, state);
        } else {
            AppEmpDevice appEmpDevice = appEmpDeviceMapper.selectByPlatformAndSn(DevicePlatform.cncit.name(), code);
            List<AppBroadcastRecordEmp> recordEmpList = appBroadcastRecordEmpMapper.selectUndoneByDeviceId(appEmpDevice.getId());
            for (int i = 0; i < recordEmpList.size(); i++) {
                AppBroadcastRecordEmp recordEmp = recordEmpList.get(i);
                if (i == 0) {
                    //同一个设备，修改最后一条的状态，其它默认为失效
                    recordEmp.setState(state);
                } else {
                    recordEmp.setState(AppBroadcastSendState.EXPIRED.getValue());
                }
                appBroadcastRecordEmpMapper.updateState(recordEmp.getId(), recordEmp.getState());
            }
        }
    }
}
