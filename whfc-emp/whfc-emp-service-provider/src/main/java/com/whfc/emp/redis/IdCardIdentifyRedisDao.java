package com.whfc.emp.redis;

/**
 * @Description 人员识别次数限制缓存
 * <AUTHOR>
 * @Date 2021-08-06 10:34
 * @Version 1.0
 */
public interface IdCardIdentifyRedisDao {

    /**
     * 保存人员识别次数限制缓存
     *
     * @param sessionId
     * @param index
     */
    void addIdCardIdentify(String sessionId, Integer index);

    /**
     * 获取人员识别次数限制缓存次数
     *
     * @param sessionId
     * @return
     */
    Integer getIdCardIdentify(String sessionId);
}
