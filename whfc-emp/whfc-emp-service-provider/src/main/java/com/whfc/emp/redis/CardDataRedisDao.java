package com.whfc.emp.redis;


import com.whfc.emp.dto.AppDeviceCardLogDTO;
import com.whfc.emp.entity.AppDeviceCardLog;

import java.util.Date;
import java.util.List;

/**
 * @Description: 人员安全帽数据 缓存
 * @author: xug<PERSON>
 * @version: 1.0
 * @date: 2019/11/25 18:51
 */
public interface CardDataRedisDao {

    /**
     * 添加日志数据
     *
     * @param empId
     * @param date
     * @param logList
     */
    void addCardDataLog(Integer empId, Date date, List<AppDeviceCardLogDTO> logList);

    /**
     * 查询日志数据
     *
     * @param empId
     * @param date
     * @return
     */
    List<AppDeviceCardLog> getCardDataLog(Integer empId, Date date);

    /**
     * 查询日志数据
     *
     * @param empId
     * @param date
     * @param startTime
     * @param endTime
     * @return
     */
    List<AppDeviceCardLog> getCardDataLog(Integer empId, Date date, Date startTime, Date endTime);

    /**
     * 判断数据缓存是否已存在
     *
     * @param empId
     * @param date
     * @return
     */
    boolean exists(Integer empId, Date date);

    /**
     * 删除缓存
     *
     * @param empId
     * @param date
     */
    void delete(Integer empId, Date date);
}
