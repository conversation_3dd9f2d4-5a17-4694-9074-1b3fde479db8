package com.whfc.emp.queue;

import com.whfc.common.constant.QueueConst;
import com.whfc.common.third.indoor.uwb.UwbRealPosition;
import com.whfc.common.util.JSONUtil;
import com.whfc.emp.indoor.IndoorPositionMgr;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/1/4 11:05
 */
@Component
@RabbitListener(queuesToDeclare = {@Queue(name = QueueConst.EMP_INDOOR_UWB)}, concurrency = "1-2")
public class IndoorUwbMessageListener {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private IndoorPositionMgr indoorPositionMgr;

    @RabbitHandler
    public void process(String msg) {
        try {
            logger.info("indoor|uwb|msg|{}", msg);
            UwbRealPosition data = JSONUtil.parseObject(msg, UwbRealPosition.class);
            if (data != null) {
                indoorPositionMgr.addUwbData(data);
            }
        } catch (Exception ex) {
            logger.error("indoor|uwb|msg,消息处理失败", ex);
        }
    }
}
