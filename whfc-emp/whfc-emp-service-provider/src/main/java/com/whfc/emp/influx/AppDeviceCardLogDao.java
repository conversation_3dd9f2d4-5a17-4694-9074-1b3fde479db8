package com.whfc.emp.influx;

import com.whfc.common.result.PageData;
import com.whfc.emp.dto.AppDeviceCardLogDTO;
import com.whfc.emp.entity.AppDeviceCardLog;

import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2020-11-03 16:57
 */
public interface AppDeviceCardLogDao {

    /**
     * 插入数据
     *
     * @param record
     */
    void insert(AppDeviceCardLog record);

    /**
     * 批量插入数据
     *
     * @param records
     */
    void batchInsert(List<AppDeviceCardLog> records);

    /**
     * 查询人员原始数据
     *
     * @param empId
     * @param startTime
     * @param endTime
     * @return
     */
    List<AppDeviceCardLogDTO> selectHelmetDataLogListByEmpId(Integer empId, Date startTime, Date endTime);

    /**
     * 查询人员原始数据
     *
     * @param empId     人员ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param columns   列
     * @return
     */
    List<AppDeviceCardLogDTO> selectHelmetDataLogListByEmpId(Integer empId, Date startTime, Date endTime, String[] columns);

    /**
     * 查询安全帽原始数据
     *
     * @param deviceId
     * @param startTime
     * @param endTime
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageData<AppDeviceCardLogDTO> selectHelmetDataLogListByDeviceId(Integer deviceId, Date startTime, Date endTime, Integer pageNum, Integer pageSize);

    /**
     * 查询安全帽原始数据
     *
     * @param deviceId
     * @param startTime
     * @param endTime
     * @param pageNum
     * @param pageSize
     * @param columns
     * @return
     */
    PageData<AppDeviceCardLogDTO> selectHelmetDataLogListByDeviceId(Integer deviceId, Date startTime, Date endTime, Integer pageNum, Integer pageSize, String[] columns);

    /**
     * 查询人员安全统计数据
     *
     * @param empIds
     * @param startTime
     * @param endTime
     * @return
     */
    List<AppDeviceCardLogDTO> selectHelmetDTOByEmpIds(List<Integer> empIds, Date startTime, Date endTime);

    /**
     * 查询人员健康数据
     * @param empId
     * @param startTime
     * @param endTime
     * @return
     */
    AppDeviceCardLogDTO selectHealthByEmpId(Integer empId, Date startTime, Date endTime);
}
