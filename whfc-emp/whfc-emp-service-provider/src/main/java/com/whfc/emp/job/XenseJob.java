package com.whfc.emp.job;

import com.whfc.XxlJobConfig;
import com.whfc.common.constant.QueueConst;
import com.whfc.common.face.xense.XenseFaceApi;
import com.whfc.common.face.xense.entity.*;
import com.whfc.common.util.CollectionUtil;
import com.whfc.common.util.JSONUtil;
import com.whfc.emp.dao.*;
import com.whfc.emp.entity.*;
import com.whfc.emp.enums.*;
import com.whfc.emp.param.EmpInfoSyncDataParam;
import com.whfc.fuum.dto.AppCorpDTO;
import com.whfc.fuum.service.AppCorpService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Xense 定时任务
 */
@Component
@ConditionalOnBean(XxlJobConfig.class)
public class XenseJob {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    private static final Map<String, AppEmpGroup> corp_group_map = new HashMap<>();

    private static final String emp_name = "未知";

    private static final String visitor = "訪客";

    @DubboReference(interfaceClass = AppCorpService.class, version = "1.0.0")
    private AppCorpService appCorpService;

    @Autowired
    private AmqpTemplate amqpTemplate;

    @Autowired
    private AppFaceGateConfigMapper appFaceGateConfigMapper;

    @Autowired
    private AppEmpMapper appEmpMapper;

    @Autowired
    private AppEmpGroupMapper appEmpGroupMapper;

    @Autowired
    private AppEmpDataMapper appEmpDataMapper;

    @Autowired
    private AppEmpDayMapper appEmpDayMapper;

    @Autowired
    private AppEmpAttendRecordMapper appEmpAttendRecordMapper;

    /**
     * 刷新考勤数据
     */
    @XxlJob("xense-attend")
    public void xenseAttendData() {
        try {
            XxlJobHelper.log("开始获取xense人脸机数据.");
            Date today = new Date();

            //查询所有配置xense闸机的项目配置
            List<AppFaceGateConfig> list = appFaceGateConfigMapper.selectByPlatform(FaceGateType.XENSE.getCode());
            for (AppFaceGateConfig config : list) {
                Integer deptId = config.getDeptId();
                String appKey = config.getAppKey();
                String appSecret = config.getAppSecret();
                XenseFaceApi api = new XenseFaceApi(appKey, appSecret);

                //同步人员信息
                this.syncWorkerProfile(api, deptId);

                TimeUnit.MINUTES.sleep(5);

                //同步考勤数据
                this.syncDailyCount(api, deptId, today);
            }


        } catch (Exception ex) {
            logger.error("", ex);
            XxlJobHelper.log("获取xense人脸机数据失败 error:{}", ex);
            XxlJobHelper.handleFail("获取xense人脸机数据失败 error:" + ex.getLocalizedMessage());
        }
    }

    /**
     * 同步人员信息
     *
     * @param api
     * @param deptId
     */
    private void syncWorkerProfile(XenseFaceApi api, Integer deptId) {
        int pageNum = 1;
        int pageSize = 100;
        int pages = 0;
        List<WorkerInfo> workerInfos = null;
        do {
            WorkerProfile workerProfile = api.workerProfile(pageNum, pageSize);

            if (workerInfos == null) {
                int total = workerProfile.getTotalResult();
                pages = total / pageSize + ((total % pageSize) > 0 ? 1 : 0);
                workerInfos = new ArrayList<>(total);
            }
            workerInfos.addAll(workerProfile.getData());
            pageNum++;
        } while (pageNum <= pages);

        logger.info("xense,人员数量:{},{}", workerInfos.size(), JSONUtil.toString(workerInfos));

        for (WorkerInfo workerInfo : workerInfos) {
            List<Subcontractor> empSub = workerInfo.getEmpSub();
            Optional<Subcontractor> subcontractor = empSub.stream().findFirst();
            String empName = workerInfo.getNameChi();
            String workTypeName = workerInfo.getTradeName();
            Integer corpId = null;
            String corpName = "";
            if (subcontractor.isPresent()) {
                corpName = subcontractor.get().getSubContractorNameChi();
            }

            //过滤访客
            if (visitor.equals(corpName)) {
                continue;
            }

            //合作单位
            if (StringUtils.isNotBlank(corpName)) {
                AppCorpDTO corpInfo = this.getCorpInfo(deptId, corpName);
                if (corpInfo != null) {
                    corpId = corpInfo.getCorpId();
                }
            }

            //班组
            Integer groupId = null;
            String groupName = null;
            AppEmpGroup group = this.getGroupInfo(deptId, corpId, corpName);
            if (group != null) {
                groupId = group.getId();
                groupName = group.getGroupName();
            }

            //推送消息
            EmpInfoSyncDataParam dataParam = new EmpInfoSyncDataParam();
            dataParam.setDeptId(deptId);
            dataParam.setPlatform(FaceGateType.XENSE.getCode());
            dataParam.setSyncType(FaceGateSyncOp.EMP_ADD_OR_UPDATE.getValue());
            dataParam.setEmpCode(workerInfo.getEmployeeId());
            dataParam.setEmpName(empName);
            dataParam.setCorpId(corpId);
            dataParam.setCorpName(corpName);
            dataParam.setGroupId(groupId);
            dataParam.setGroupName(groupName);
            dataParam.setWorkTypeName(workTypeName);

            String jsonStr = JSONUtil.toString(dataParam);
            amqpTemplate.convertAndSend(QueueConst.EMP_FACEGATE_EMP_INFO, jsonStr);
        }
    }

    /**
     * 同步考勤数据
     *
     * @param api
     * @param deptId
     * @param date
     */
    private void syncDailyCount(XenseFaceApi api, Integer deptId, Date date) {
        DailyResult dailyResult = api.dailyCount(date);
        if (dailyResult != null) {
            List<DailyInOut> inTotalDetail = dailyResult.getInTotalDetail();
            List<DailyInOut> outTotalDetail = dailyResult.getOutTotalDetail();
            Map<String, Date> inMap = CollectionUtil.list2map(inTotalDetail, DailyInOut::getEmployeeId, DailyInOut::getTime);
            Map<String, Date> outMap = CollectionUtil.list2map(outTotalDetail, DailyInOut::getEmployeeId, DailyInOut::getTime);
            Set<String> inEmployeeIds = inMap.keySet();
            Set<String> outEmployeeIds = outMap.keySet();
            Set<String> employeeIds = new HashSet<>(inEmployeeIds.size() + outEmployeeIds.size());
            employeeIds.addAll(inEmployeeIds);
            employeeIds.addAll(outEmployeeIds);

            logger.info("xense,考勤数据:{},{},{}", employeeIds.size(), inTotalDetail.size(), outTotalDetail.size());

            //考勤数据
            for (String employeeId : employeeIds) {
                Date inTime = inMap.get(employeeId);
                Date outTime = outMap.get(employeeId);
                if (inTime != null && outTime != null && inTime.after(outTime)) {
                    outTime = null;
                }

                //查询人员
                AppEmp appEmp = appEmpMapper.selectByDeptIdAndEmpCode(deptId, employeeId);
                if (appEmp == null) {
                    continue;
                }
                //更新实时出勤状态
                Integer workTimes = 0;
                AttendState attendState = AttendState.ABSENCE;
                if (inTime != null) {
                    attendState = AttendState.ATTEND;
                    appEmpDataMapper.updateEmpAttendState(appEmp.getId(), AttendState.ATTEND.getValue());
                    if (outTime != null) {
                        workTimes = (int) ((outTime.getTime() - inTime.getTime()) / (1000));
                    }
                }
                //更新日考勤统计
                AppEmpDay empDay = appEmpDayMapper.selectByEmpIdAndDate(appEmp.getId(), date);
                if (empDay == null) {
                    empDay = new AppEmpDay();
                    empDay.setDeptId(deptId);
                    empDay.setEmpId(appEmp.getId());
                    empDay.setWorkRoleId(appEmp.getWorkRoleId());
                    empDay.setDate(date);
                    empDay.setAttendState(attendState.getValue());
                    empDay.setStartTime(inTime);
                    empDay.setEndTime(outTime);
                    empDay.setWorkTimes(workTimes);
                    appEmpDayMapper.insertSelective(empDay);
                } else {
                    empDay.setWorkRoleId(appEmp.getWorkRoleId());
                    empDay.setAttendState(attendState.getValue());
                    empDay.setStartTime(inTime);
                    empDay.setEndTime(outTime);
                    empDay.setWorkTimes(workTimes);
                    appEmpDayMapper.updateByPrimaryKeySelective(empDay);
                }

                //更新人员打卡记录
                if (inTime != null) {
                    int inCount = appEmpAttendRecordMapper.countByEmpIdAndTime(appEmp.getId(), inTime);
                    if (inCount == 0) {
                        AppEmpAttendRecord record = new AppEmpAttendRecord();
                        record.setDeptId(deptId);
                        record.setEmpId(appEmp.getId());
                        record.setTime(inTime);
                        record.setType(AttendType.FACEGATE.getValue());
                        record.setDirection(Direction.IN.getValue());
                        appEmpAttendRecordMapper.insertSelective(record);
                    }
                }
                if (outTime != null) {
                    int outCount = appEmpAttendRecordMapper.countByEmpIdAndTime(appEmp.getId(), outTime);
                    if (outCount == 0) {
                        AppEmpAttendRecord record = new AppEmpAttendRecord();
                        record.setDeptId(deptId);
                        record.setEmpId(appEmp.getId());
                        record.setTime(inTime);
                        record.setType(AttendType.FACEGATE.getValue());
                        record.setDirection(Direction.OUT.getValue());
                        appEmpAttendRecordMapper.insertSelective(record);
                    }
                }

                //更新人脸识别记录
                if (inTime != null) {

                }
                if (outTime != null) {

                }
            }
        }
    }

    /**
     * 获取合作单位
     *
     * @param deptId
     * @param corpName
     * @return
     */
    private AppCorpDTO getCorpInfo(Integer deptId, String corpName) {
        AppCorpDTO corpDTO = appCorpService.getCorpByDeptIdAndName(deptId, corpName);
        return corpDTO;
    }

    /**
     * 获取班组
     *
     * @param deptId
     * @param corpId
     * @param corpName
     * @return
     */
    private AppEmpGroup getGroupInfo(Integer deptId, Integer corpId, String corpName) {
        AppEmpGroup group;
        if (corp_group_map.containsKey(corpName)) {
            group = corp_group_map.get(corpName);
        } else {
            group = appEmpGroupMapper.selectByDeptIdAndName(deptId, corpName);
            if (group == null) {
                group = new AppEmpGroup();
                group.setDeptId(deptId);
                group.setCorpId(corpId);
                group.setCorpName(corpName);
                group.setGroupName(corpName);
                appEmpGroupMapper.insertSelective(group);
            }
        }
        return group;
    }
}
