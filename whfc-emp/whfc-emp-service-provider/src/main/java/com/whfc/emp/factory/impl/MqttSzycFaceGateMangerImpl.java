package com.whfc.emp.factory.impl;

import com.whfc.common.enums.I18nErrorCode;
import com.whfc.common.exception.BizException;
import com.whfc.common.face.szycq.*;
import com.whfc.common.file.FileHandler;
import com.whfc.common.geometry.Point;
import com.whfc.common.result.ResultEnum;
import com.whfc.common.util.Base64Util;
import com.whfc.common.util.ImageUtil;
import com.whfc.common.util.JSONUtil;
import com.whfc.emp.dao.AppEmpMapper;
import com.whfc.emp.dao.AppFaceGateMapper;
import com.whfc.emp.dao.AppFaceGatePersonMapper;
import com.whfc.emp.entity.AppEmp;
import com.whfc.emp.entity.AppFaceGate;
import com.whfc.emp.entity.AppFaceGateConfig;
import com.whfc.emp.entity.AppFaceGatePerson;
import com.whfc.emp.enums.TaskType;
import com.whfc.emp.factory.FaceGateManager;
import com.whfc.emp.mqtt.MqttConfig;
import com.whfc.emp.param.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.integration.core.MessageProducer;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * MQTT(深圳玉川)
 *
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/1/5 16:38
 */
@Service("mqttSzycFaceGateManagerImpl")
public class MqttSzycFaceGateMangerImpl implements FaceGateManager {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Value("${mqtt.enabled}")
    private Boolean mqttEnable;

    @Autowired
    private AppFaceGateMapper appFaceGateMapper;

    @Autowired
    private AppFaceGatePersonMapper appFaceGatePersonMapper;

    @Autowired
    private AppEmpMapper appEmpMapper;

    @Autowired
    private FileHandler fileHandler;

    @Autowired(required = false)
    private MqttConfig.MqttMessageSender mqttMessageSender;

    @Autowired(required = false)
    private MessageProducer messageProducer;

    @Override
    public void add(AppFaceGateAddParam request, AppFaceGateConfig config) {
        String deviceKey = request.getDeviceKey();
        String name = request.getName();
        Integer deptId = request.getDeptId();
        Point point = request.getPoint();

        AppFaceGate faceGate = new AppFaceGate();
        faceGate.setDeviceKey(deviceKey);
        faceGate.setDeptId(deptId);
        faceGate.setName(name);
        faceGate.setDirection(request.getDirection());
        faceGate.setPlatform(request.getPlatform());
        faceGate.setModel(request.getModel());
        // 增加闸机位置信息
        if (point != null) {
            Double lat = point.getLat();
            Double lng = point.getLng();
            faceGate.setLng(lng);
            faceGate.setLat(lat);
        }
        appFaceGateMapper.insertSelective(faceGate);
    }

    @Override
    public void del(Integer faceGateId, AppFaceGateConfig config) {
        Integer count = appFaceGatePersonMapper.countByFaceGateId(faceGateId);
        if (count > 0) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_031.getCode());
        }
        appFaceGateMapper.deleteLogicById(faceGateId);
    }

    @Override
    public String faceGateGrantEmdAdd(FaceGateGrantEmdAddParam request, AppFaceGateConfig config) {
        if (mqttEnable == null || !mqttEnable) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.EMP_BE_027.getCode());
        }
        String deviceKey = request.getDeviceKey();
        Integer personId = request.getPersonId();
        Integer empId = request.getEmpId();

        AppEmp appEmp = appEmpMapper.selectByPrimaryKey(empId);
        //验证人员头像
        String empCode = appEmp.getEmpCode();
        String avatar = appEmp.getAvatar();

        AddPerson addPerson = new AddPerson();
        addPerson.setName(appEmp.getEmpName());
        addPerson.setPerson_uuid(empCode);
        addPerson.setGender(appEmp.getGender());
        //addPerson.setImage_url(avatar);
        addPerson.setImage_base64(this.getImageBase64(avatar));

        AddPersonMsg msg = new AddPersonMsg();
        msg.setConfirmation_topic(SzycqConst.getAddPersonAckTopic());
        msg.setMessage_id(personId);
        msg.setMessage_uuid("add_" + personId);
        msg.setData(Arrays.asList(addPerson));

        // 发送批量同步人员的MQ消息
        String topic = SzycqConst.getAddPersonTopic(deviceKey);
        String payload = JSONUtil.toString(msg);
        mqttMessageSender.sendToMqtt(topic, 0, payload);

        return null;
    }

    @Override
    public String faceGateGrantEmdImgAdd(FaceGateGrantEmdImgAddParam request, AppFaceGateConfig config) {
        return null;
    }

    @Override
    public String faceGateGrantEmdAuth(FaceGateGrantEmdAuthParam request, AppFaceGateConfig config) {
        return null;
    }

    @Override
    public void faceGateBatchGrantEmpAuth(AppFaceGateGrantEmpParam request, AppFaceGateConfig config) {

    }

    @Override
    public void faceGateRevokeEmp(AppFaceGateGrantEmpParam request, AppFaceGateConfig config) {
        if (mqttEnable == null || !mqttEnable) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.EMP_BE_027.getCode());
        }
        List<Integer> empIdList = request.getEmpIdList();
        if (empIdList == null || empIdList.isEmpty()) {
            return;
        }
        Integer faceGateId = request.getFaceGateId();
        AppFaceGate appFaceGate = appFaceGateMapper.selectByPrimaryKey(faceGateId);
        if (appFaceGate == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.EMP_BE_003.getCode());
        }
        String deviceKey = appFaceGate.getDeviceKey();
        List<String> customIds = new ArrayList<>();
        for (Integer empId : empIdList) {
            AppFaceGatePerson appFaceGatePerson = appFaceGatePersonMapper.selectByFaceGateIdAndEmpId(faceGateId, empId);
            if (appFaceGatePerson != null) {
                customIds.add(appFaceGatePerson.getPersonGuid());
                appFaceGatePerson.setTaskType(TaskType.EMP_UN_AUTH.getValue());
                appFaceGatePersonMapper.updateByPrimaryKeySelective(appFaceGatePerson);
            }
        }
        // 发送批量同步人员的MQ消息

        DelPerson delPerson = new DelPerson();
        delPerson.setPerson_uuids(customIds);

        DelPersonMsg msg = new DelPersonMsg();
        msg.setConfirmation_topic(SzycqConst.getDelPersonAckTopic());
        msg.setMessage_id(faceGateId);
        msg.setMessage_uuid("del_" + faceGateId);
        msg.setData(delPerson);

        String topic = SzycqConst.getDelPersonTopic(deviceKey);
        String payload = JSONUtil.toString(msg);
        mqttMessageSender.sendToMqtt(topic, 0, payload);
    }

    @Override
    public String deviceAuthorizationPerson(String deviceKey, String name, String imgUrl, AppFaceGateConfig config) {
        return null;
    }

    @Override
    public String deviceAuthorizationCancel(String deviceKey, String personGuid, AppFaceGateConfig config) {
        return null;
    }

    @Override
    public void openDoor(String deviceKey, AppFaceGateConfig config) {

    }

    @Override
    public void sync(Integer faceGateId, AppFaceGateConfig config) {

    }

    @Override
    public String getToken(AppFaceGateConfig config) {
        return null;
    }

    /**
     * 获取图片base64(照片压缩)
     *
     * @param url
     * @return
     */
    private String getImageBase64(String url) {
        logger.info("获取图片base64,{}", url);
        url = fileHandler.getDownloadUrl(url);
        logger.info("获取图片base64,{}", url);
        byte[] imageData = Base64Util.getUrlImageData(url);
        //logger.debug("获取图片base64,{},压缩前:{}", url, imageData.length);
        byte[] compressed = ImageUtil.compressPicForScale(imageData, 60);
        //logger.debug("获取图片base64,{},压缩后:{}", url, compressed.length);
        return Base64Util.encodeToString(compressed);
    }
}
