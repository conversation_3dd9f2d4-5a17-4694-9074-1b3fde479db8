package com.whfc.emp.dao;

import com.whfc.emp.dto.AppFenceDTO;
import com.whfc.emp.entity.AppFence;
import com.whfc.emp.param.AppFenceListParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AppFenceMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppFence record);

    int insertSelective(AppFence record);

    AppFence selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppFence record);

    int updateByPrimaryKey(AppFence record);

    /**
     * 查询电子围栏
     *
     * @param request
     * @return
     */
    List<AppFenceDTO> selectByParam(AppFenceListParam request);

    /**
     * 插入
     *
     * @param appFence
     * @return
     */
    void insertSelectiveByParam(AppFence appFence);

    /**
     * 更新
     *
     * @param appFence
     * @return
     */
    void updateSelectiveByParam(AppFence appFence);

    /**
     * 人员管理-删除电子围栏
     *
     * @param id
     */
    void deleteLogicById(Integer id);


    /**
     * 查询包含指定gps的电子围栏ID
     *
     * @param deptId
     * @param lng
     * @param lat
     * @return
     */
    List<AppFenceDTO> selectFenceIdListContainsGps(@Param("deptId") Integer deptId,
                                              @Param("empId") Integer empId,
                                              @Param("lng") Double lng,
                                              @Param("lat") Double lat);

    /**
     * 查找电子围栏
     *
     * @param deptId
     * @param fenceId
     * @return
     */
    List<AppFenceDTO> selectByFenceId(@Param("deptId") Integer deptId,
                                      @Param("fenceId") Integer fenceId);
}