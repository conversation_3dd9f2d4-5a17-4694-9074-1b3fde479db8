package com.whfc.emp.manager.warn;

import com.whfc.emp.enums.AppEmpWarnRuleType;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Description: 人员报警管理器-工厂类
 * @author: xug<PERSON>
 * @version: 1.0
 * @date: 2023/10/10 15:58
 */
@Component
public class AppEmpWarnMgrFactory {

    @Resource(name = "appEmpWarnMgrFence")
    private AppEmpWarnMgrFence appEmpWarnMgrFence;

    @Resource(name = "appEmpWarnMgrHardwareDrop")
    private AppEmpWarnMgrHardwareDrop appEmpWarnMgrHardwareDrop;

    @Resource(name = "appEmpWarnMgrHardwareDoff")
    private AppEmpWarnMgrHardwareDoff appEmpWarnMgrHardwareDoff;

    @Resource(name = "appEmpWarnMgrHardwareSos")
    private AppEmpWarnMgrHardwareSos appEmpWarnMgrHardwareSos;

    @Resource(name = "appEmpWarnMgrHardwareCrash")
    private AppEmpWarnMgrHardwareCrash appEmpWarnMgrHardwareCrash;

    @Resource(name = "appEmpWarnMgrHardwareStill")
    private AppEmpWarnMgrHardwareStill appEmpWarnMgrHardwareStill;

    @Resource(name = "appEmpWarnMgrLowPower")
    private AppEmpWarnMgrLowPower appEmpWarnMgrLowPower;

    @Resource(name = "appEmpWarnMgrHeartRate")
    private AppEmpWarnMgrHeartRate appEmpWarnMgrHeartRate;

    @Resource(name = "appEmpWarnMgrTemp")
    private AppEmpWarnMgrTemp appEmpWarnMgrTemp;

    /**
     * 获取人员报警管理器
     *
     * @param ruleType 报警规则
     * @return
     */
    public AppEmpWarnMgr getWarnMgr(Integer ruleType) {
        AppEmpWarnRuleType type = AppEmpWarnRuleType.parseByValue(ruleType);
        switch (type) {
            case FENCE:
                return appEmpWarnMgrFence;
            case DROP:
                return appEmpWarnMgrHardwareDrop;
            case DOFF:
                return appEmpWarnMgrHardwareDoff;
            case SOS:
                return appEmpWarnMgrHardwareSos;
            case CRASH:
                return appEmpWarnMgrHardwareCrash;
            case STILL:
                return appEmpWarnMgrHardwareStill;
            case BATTERY:
                return appEmpWarnMgrLowPower;
            case HEART_RATE:
                return appEmpWarnMgrHeartRate;
            case TEMP:
                return appEmpWarnMgrTemp;
            default:
                return null;
        }
    }
}
