package com.whfc.emp.dao;

import com.whfc.emp.dto.AppEmpAttendGroupDTO;
import com.whfc.emp.dto.AppEmpBoardRecordAnalysisDTO;
import com.whfc.emp.dto.AppFaceGateRecordDTO;
import com.whfc.emp.dto.stat.FacegateRecEmpStat;
import com.whfc.emp.entity.AppFaceGateRecord;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.Date;
import java.util.List;

@Repository
public interface AppFaceGateRecordMapper {
    int deleteByPrimaryKey(Integer id);

    int insertSelective(AppFaceGateRecord record);

    AppFaceGateRecord selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppFaceGateRecord record);

    /**
     * 查询识别记录
     *
     * @param deptId
     * @param startTime
     * @param endTime
     * @param keyword
     * @return
     */
    List<AppFaceGateRecordDTO> selectByDeptIdAndKeyword(@Param("deptId") Integer deptId,
                                                        @Param("startTime") Date startTime,
                                                        @Param("endTime") Date endTime,
                                                        @Param("keyword") String keyword);

    /**
     * 查询识别记录
     *
     * @param deptId
     * @param startTime
     * @param endTime
     * @param faceGateIds
     * @return
     */
    List<AppFaceGateRecordDTO> selectByDeptIdAndFaceGate(@Param("deptId") Integer deptId,
                                                         @Param("startTime") Date startTime,
                                                         @Param("endTime") Date endTime,
                                                         @Param("faceGateIds") Collection<Integer> faceGateIds);

    /**
     * 查询项目识别记录
     *
     * @param deptId
     * @param startTime
     * @param endTime
     * @return
     */
    List<AppFaceGateRecordDTO> selectByDeptIdAndTime(@Param("deptId") Integer deptId,
                                                     @Param("startTime") Date startTime,
                                                     @Param("endTime") Date endTime);


    /**
     * 查找陌生人识别记录
     *
     * @param deptId
     * @param startTime
     * @param endTime
     * @return
     */
    List<AppFaceGateRecordDTO> selectStrangerList(@Param("deptId") Integer deptId,
                                                  @Param("startTime") Date startTime,
                                                  @Param("endTime") Date endTime);


    /********脏数据清理**********/


    /**
     * 查询人员识别记录重复统计
     *
     * @param startTime
     * @param endTime
     * @return
     */
    List<FacegateRecEmpStat> selectEmpRecCount(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询人员某时刻的识别记录
     *
     * @param empId
     * @param showTime
     * @return
     */
    List<AppFaceGateRecord> selectRecRecordByEmpIdAndTime(@Param("empId") Integer empId, @Param("showTime") Date showTime);

    /**
     * 批量删除
     *
     * @param idList
     * @return
     */
    int batchDelete(@Param("idList") List<Integer> idList);

    /**
     * 人员进出统计
     *
     * @param deptId
     * @param startDate
     * @param endDate
     * @return
     */
    AppEmpBoardRecordAnalysisDTO selectRecordAnalysis(@Param("deptId") Integer deptId, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 人员最新刷卡记录（进闸机出闸机各一条）
     *
     * @param deptId
     * @param direction
     * @param startDate
     * @param endDate
     * @return
     */
    AppFaceGateRecordDTO selectLatestRecord(@Param("deptId") Integer deptId, @Param("direction") Integer direction, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 人员最新刷卡记录
     *
     * @param deptId
     * @param direction
     * @param startDate
     * @param endDate
     * @return
     */
    List<AppFaceGateRecordDTO> selectRecordList(@Param("deptId") Integer deptId, @Param("direction") Integer direction, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 工区出勤统计
     *
     * @param deptId
     * @param startTime
     * @param endTime
     * @return
     */
    List<AppEmpAttendGroupDTO> selectAreaAttendData(@Param("deptId") Integer deptId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);
}