package com.whfc.emp.queue;

import com.alibaba.fastjson.JSONObject;
import com.whfc.common.constant.QueueConst;
import com.whfc.common.third.map.MapApi;
import com.whfc.common.third.map.MapApiFactory;
import com.whfc.common.third.map.MapLoc;
import com.whfc.common.util.Gps;
import com.whfc.emp.entity.AppDeviceCardLog;
import com.whfc.emp.manager.AppEmpDataManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 智能手表数据处理
 */
@Component
@RabbitListener(queuesToDeclare = {@Queue(name = QueueConst.EMP_WATCH_DATA)}, concurrency = "1-2")
public class WatchDataQueueMessageListener {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private AppEmpDataManager appEmpDataManager;

    @Autowired
    private MapApiFactory mapApiFactory;

    @RabbitHandler
    public void process(String msg) {
        try {
            logger.info("watch|data|{}", msg);
            JSONObject json = JSONObject.parseObject(msg);
            String deviceCode = json.getString("deviceCode");
            String platform = json.getString("platform");
            Date time = json.getDate("time");
            Double lngWgs84 = json.getDouble("lngWgs84");
            Double latWgs84 = json.getDouble("latWgs84");
            Double lng = json.getDouble("lng");
            Double lat = json.getDouble("lat");
            Double speed = json.getDouble("speed");
            Integer batteryPower = json.getInteger("batteryPower");
            Double bodyTemp = json.getDouble("bodyTemp");
            Integer heartRate = json.getInteger("heartRate");
            Integer diastolicPressure = json.getInteger("diastolicPressure");
            Integer systolicPressure = json.getInteger("systolicPressure");
            Integer bloodOxygen = json.getInteger("bloodOxygen");
            Double bloodSugar = json.getDouble("bloodSugar");
            Integer alarmSos = json.getInteger("alarmSos");
            Integer alarmDrop = json.getInteger("alarmDrop");
            Integer alarmDoff = json.getInteger("alarmDoff");
            Integer alarmStill = json.getInteger("alarmStill");
            String location = null;
            //坐标系转换
            if (lngWgs84 != null && latWgs84 != null && lng == null && lat == null) {
                MapApi mapApi = mapApiFactory.getMapApi();
                Gps gcj02Gps = mapApi.translateToGcj02(lngWgs84, latWgs84);
                lng = gcj02Gps.getLng();
                lat = gcj02Gps.getLat();
                MapLoc loc = mapApi.geocode(lng, lat);
                location = loc.getAddress();
            }

            AppDeviceCardLog cardLog = new AppDeviceCardLog();
            cardLog.setPlatform(platform);
            cardLog.setDeviceCode(deviceCode);
            cardLog.setTime(time);
            cardLog.setLngWgs84(lngWgs84 != null ? lngWgs84 : 0D);
            cardLog.setLatWgs84(latWgs84 != null ? latWgs84 : 0D);
            cardLog.setLng(lng != null ? lng : 0D);
            cardLog.setLat(lat != null ? lat : 0D);
            cardLog.setLocation(location);
            cardLog.setSpeed(speed);
            cardLog.setBatteryPower(batteryPower);
            cardLog.setBodyTemp(bodyTemp);
            cardLog.setHeartRate(heartRate);
            cardLog.setDiastolicPressure(diastolicPressure);
            cardLog.setSystolicPressure(systolicPressure);
            cardLog.setBloodOxygen(bloodOxygen);
            cardLog.setBloodSugar(bloodSugar);
            cardLog.setAlarmSos(alarmSos);
            cardLog.setAlarmDrop(alarmDrop);
            cardLog.setAlarmDoff(alarmDoff);
            cardLog.setAlarmStill(alarmStill);

            appEmpDataManager.addEmpDeviceData(cardLog);
        } catch (Exception ex) {
            logger.error("watch|data,消息处理失败", ex);
        }
    }
}
