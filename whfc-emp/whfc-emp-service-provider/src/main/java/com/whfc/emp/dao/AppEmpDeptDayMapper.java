package com.whfc.emp.dao;

import com.whfc.emp.entity.AppEmpDeptDay;
import com.whfc.emp.param.EmpAttendPlan;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface AppEmpDeptDayMapper {

    /**
     * 插入 or 更新
     *
     * @param record
     * @return
     */
    int insertOrUpdate(AppEmpDeptDay record);

    /**
     * 查询项目某一天的计划出勤
     *
     * @param deptId
     * @param date
     * @return
     */
    EmpAttendPlan selectByDeptIdAndDate(@Param("deptId") Integer deptId, @Param("date") Date date);

    /**
     * 查询项目一段时间你的计划出勤
     *
     * @param deptId
     * @param startDate
     * @param endDate
     * @return
     */
    List<EmpAttendPlan> selectByDeptIdAndTimeRange(@Param("deptId") Integer deptId, @Param("startDate") Date startDate, @Param("endDate") Date endDate);
}