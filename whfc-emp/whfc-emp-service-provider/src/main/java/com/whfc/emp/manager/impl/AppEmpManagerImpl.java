package com.whfc.emp.manager.impl;

import com.whfc.emp.dao.AppEmpMapper;
import com.whfc.emp.dto.AppEmpDTO;
import com.whfc.emp.entity.AppEmp;
import com.whfc.emp.manager.AppEmpManager;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-03-16 14:20
 */
@Service
public class AppEmpManagerImpl implements AppEmpManager {

    @Autowired
    private AppEmpMapper appEmpMapper;

    @Override
    public AppEmpDTO selectEmpInfo(Integer deptId, String empCode) {
        AppEmp appEmp = appEmpMapper.selectByDeptIdAndEmpCode(deptId, empCode);
        if (appEmp== null){
            return null;
        }
        AppEmpDTO appEmpDTO = new AppEmpDTO();
        BeanUtils.copyProperties(appEmp,appEmpDTO);
        appEmpDTO.setEmpId(appEmp.getId());
        return appEmpDTO;
    }
}
