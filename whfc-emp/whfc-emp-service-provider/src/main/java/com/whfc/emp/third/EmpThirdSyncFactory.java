package com.whfc.emp.third;

import com.whfc.common.enums.SyncPlatform;
import com.whfc.emp.dao.AppSyncMapper;
import com.whfc.emp.entity.AppSync;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @Description: 人员信息-第三方同步-工厂类
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/6/30 14:50
 */
@Component
public class EmpThirdSyncFactory {

    @Resource(name = "empThirdSyncWuhu")
    private EmpThirdSync empThirdSyncWuhu;

    @Resource(name = "empThirdSyncHunan")
    private EmpThirdSync empThirdSyncHunan;

    @Resource(name = "empThirdSyncLyh")
    private EmpThirdSync empThirdSyncLyh;

    @Resource(name = "empThirdSyncSca")
    private EmpThirdSync empThirdSyncSca;

    @Resource(name = "empThirdSyncYjbh")
    private EmpThirdSync empThirdSyncYjbh;


    @Autowired
    private AppSyncMapper appSyncMapper;

    /**
     * 获取第三方同步服务实例
     *
     * @param deptId
     * @return
     */
    public List<EmpThirdSyncWapper> sync(Integer deptId) {
        List<AppSync> configList = appSyncMapper.selectByDeptId(deptId);
        if (configList.isEmpty()) {
            return Collections.emptyList();
        }
        List<EmpThirdSyncWapper> list = new ArrayList<>(configList.size());
        for (AppSync config : configList) {
            //芜湖劳务实名制同步
            if (SyncPlatform.WUHU_EMP.name().equals(config.getPlatform())) {
                list.add(new EmpThirdSyncWapper(empThirdSyncWuhu, config));
            }
            //湖南省劳务实名制同步
            else if (SyncPlatform.HUNANEMP.name().equals(config.getPlatform())) {
                list.add(new EmpThirdSyncWapper(empThirdSyncHunan, config));
            }
            //电视投屏
            else if (SyncPlatform.SCA.name().equals(config.getPlatform())) {
                list.add(new EmpThirdSyncWapper(empThirdSyncSca, config));
            }
            //劳研惠实名制同步
            else if (SyncPlatform.LDYJH.name().equals(config.getPlatform())) {
                list.add(new EmpThirdSyncWapper(empThirdSyncLyh, config));
            }
            //引江补汉
            else if (SyncPlatform.YJBH.name().equals(config.getPlatform())) {
                list.add(new EmpThirdSyncWapper(empThirdSyncYjbh, config));
            }
        }
        return list;
    }
}
