package com.whfc.emp.third.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.whfc.common.constant.QueueConst;
import com.whfc.common.enums.Gender;
import com.whfc.common.enums.I18nErrorCode;
import com.whfc.common.enums.PostState;
import com.whfc.common.exception.BizException;
import com.whfc.common.file.FileHandler;
import com.whfc.common.result.ResultEnum;
import com.whfc.common.third.yjbh.owner.YjbhApi;
import com.whfc.common.util.*;
import com.whfc.emp.dao.*;
import com.whfc.emp.dto.AppIndoorPositionStationDTO;
import com.whfc.emp.dto.AppPayrollEmpDTO;
import com.whfc.emp.dto.TrainEmpDTO;
import com.whfc.emp.dto.indoor.AppIndoorUwbTTL;
import com.whfc.emp.entity.*;
import com.whfc.emp.enums.Direction;
import com.whfc.emp.param.FaceGateRecordParam;
import com.whfc.emp.third.EmpThirdSync;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Random;

import static com.whfc.common.third.yjbh.owner.OwnerConst.*;

/**
 * @Description: 人员信息-第三方同步-引江补汉
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/6/30 14:44
 */
@Service
public class EmpThirdSyncYjbh extends EmpThirdSyncBase implements EmpThirdSync {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 随机数
     */
    private Random random = new Random();

    /**
     * 图片大小
     */
    private static final long IMAGE_SIZE = 45;

    /**
     * 气体定位标签SN
     */
    private static final String GAS_SN = "3221";

    /**
     * 气体检测设备SN
     */
    private static final String GAS_DEVICE_SN = "GAS_56D8C78E24D011041";

    private static final String SYNC = "sync";

    private static final int TTL1 = 1 * 60 * 1000;
    private static final int TTL2 = 2 * 60 * 1000;
    private static final int TTL3 = 1 * 60 * 1000;
    private static final int HOLE_DISTANCE1 = 15;
    private static final int HOLE_DISTANCE2 = 30;
    private static final int HOLE_DISTANCE3 = 10;


    @Autowired
    private AppFaceGateMapper appFaceGateMapper;

    @Autowired
    private AppEmpMapper appEmpMapper;

    @Autowired
    private AppTrainEmpMapper appTrainEmpMapper;

    @Autowired
    private AppPayrollEmpMapper appPayrollEmpMapper;

    @Autowired
    private AppIndoorPositionStationMapper appIndoorPositionStationMapper;

    @Autowired
    private AmqpTemplate amqpTemplate;

    @Autowired
    private FileHandler fileHandler;

    @Override
    public void syncEmp(AppEmp emp, AppSync config) {

        if (StringUtils.isEmpty(emp.getIdCardNo())) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.EMP_BE_004.getCode());
        }
        if (StringUtils.isEmpty(emp.getPhone())) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.SYS_BE_001.getCode());
        }

        try {
            String nation = StringUtils.isNotEmpty(emp.getNation()) ? emp.getNation() : "汉族";
            nation = nation.endsWith("族") ? nation : nation + "族";

            String url = this.getEndPointUrl(emp.getAvatar());
            String photoBase64 = this.getImageBase64(url);

            JSONObject item = new JSONObject();
            item.put("orgId", orgId);
            item.put("staffType", manager.equals(emp.getWorkRoleName()) ? manager : labor);
            item.put("staffName", emp.getEmpName());
            item.put("phone", emp.getPhone());
            item.put("idCardNo", StringUtils.upperCase(emp.getIdCardNo()));
            item.put("idExpireDate", emp.getIdCardEndDate() != null ? DateUtil.formatDate(emp.getIdCardEndDate()) : null);
            item.put("workerType", null);
            item.put("workStatus", PostState.ENTER.getValue().equals(emp.getPostState()) ? "已进场" : "已退场");
            item.put("nation", nation);
            item.put("sex", Gender.parseValue(emp.getGender()));
            item.put("comeIn", PostState.ENTER.getValue().equals(emp.getPostState()) && emp.getEnterTime() != null ? DateUtil.formatDate(emp.getEnterTime()) : null);
            item.put("comeOut", PostState.OUTER.getValue().equals(emp.getPostState()) && emp.getOuterTime() != null ? DateUtil.formatDate(emp.getOuterTime()) : null);
            item.put("laborSubCom", emp.getCorpName());
            item.put("specialWorker", null);
            item.put("emergencyName", null);
            item.put("emergencyPhone", null);
            item.put("accessLevel", null);
            item.put("photoBase64", photoBase64);

            JSONArray staff = new JSONArray();
            staff.add(item);

            JSONObject properties = new JSONObject();
            properties.put("staff", staff);

            JSONObject value = new JSONObject();
            value.put("reportTs", System.currentTimeMillis());
            value.put("profile", new JSONObject());
            value.put("properties", properties);
            value.put("events", new JSONObject());
            value.put("services", new JSONObject());

            JSONArray values = new JSONArray();
            values.add(value);

            JSONObject json = new JSONObject();
            json.put("SN", sn);
            json.put("deviceType", device_type_emp);
            json.put("dataType", data_type_emp);
            json.put("bidCode", bidCode);
            json.put("workAreaCode", null);
            json.put("workSurface", null);
            json.put("deviceName", deviceName);
            json.put("managementDept", null);
            json.put("values", values);

            String data = json.toJSONString();
            logger.info("同步YJBH人员信息数据:{}", data);

            //同步数据
            String host = config.getExt1();
            String appId = config.getAppKey();
            String appSecret = config.getAppSecret();
            YjbhApi api = new YjbhApi(host, appId, appSecret);
            api.pushData(data);
        } catch (Exception ex) {
            logger.error("同步YJBH人员信息数据失败", ex);
            throw new BizException(ResultEnum.API_ERROR.getCode(), I18nErrorCode.EMP_BE_051.getCode());
        }
    }

    @Override
    public void syncEmp(List<AppEmp> empList, AppSync config) {

        try {
            JSONArray staff = new JSONArray();
            for (AppEmp emp : empList) {
                if (StringUtils.isEmpty(emp.getIdCardNo())) {
                    logger.info("身份证为空:{}", emp.getEmpName());
                    continue;
                }
                if (StringUtils.isEmpty(emp.getPhone())) {
                    logger.info("手机号为空:{}", emp.getEmpName());
                    continue;
                }
                String nation = StringUtils.isNotEmpty(emp.getNation()) ? emp.getNation() : "汉族";
                nation = nation.endsWith("族") ? nation : nation + "族";

                String url = this.getEndPointUrl(emp.getAvatar());
                String photoBase64 = this.getImageBase64(url);

                JSONObject item = new JSONObject();
                item.put("orgId", orgId);
                item.put("staffType", manager.equals(emp.getWorkRoleName()) ? manager : labor);
                item.put("staffName", emp.getEmpName());
                item.put("phone", emp.getPhone());
                item.put("idCardNo", StringUtils.upperCase(emp.getIdCardNo()));
                item.put("idExpireDate", emp.getIdCardEndDate() != null ? DateUtil.formatDate(emp.getIdCardEndDate()) : null);
                item.put("workerType", null);
                item.put("workStatus", PostState.ENTER.getValue().equals(emp.getPostState()) ? "已进场" : "已退场");
                item.put("nation", nation);
                item.put("sex", Gender.parseValue(emp.getGender()));
                item.put("comeIn", PostState.ENTER.getValue().equals(emp.getPostState()) && emp.getEnterTime() != null ? DateUtil.formatDate(emp.getEnterTime()) : null);
                item.put("comeOut", PostState.OUTER.getValue().equals(emp.getPostState()) && emp.getOuterTime() != null ? DateUtil.formatDate(emp.getOuterTime()) : null);
                item.put("laborSubCom", emp.getCorpName());
                item.put("specialWorker", null);
                item.put("emergencyName", null);
                item.put("emergencyPhone", null);
                item.put("accessLevel", null);
                item.put("photoBase64", photoBase64);
                staff.add(item);
            }

            JSONObject properties = new JSONObject();
            properties.put("staff", staff);

            JSONObject value = new JSONObject();
            value.put("reportTs", System.currentTimeMillis());
            value.put("profile", new JSONObject());
            value.put("properties", properties);
            value.put("events", new JSONObject());
            value.put("services", new JSONObject());

            JSONArray values = new JSONArray();
            values.add(value);

            JSONObject json = new JSONObject();
            json.put("SN", sn);
            json.put("deviceType", device_type_emp);
            json.put("dataType", data_type_emp);
            json.put("bidCode", bidCode);
            json.put("workAreaCode", null);
            json.put("workSurface", null);
            json.put("deviceName", deviceName);
            json.put("managementDept", null);
            json.put("values", values);

            String data = json.toJSONString();
            logger.info("同步YJBH人员信息数据:{}", data);

            //同步数据
            String host = config.getExt1();
            String appId = config.getAppKey();
            String appSecret = config.getAppSecret();
            YjbhApi api = new YjbhApi(host, appId, appSecret);
            api.pushData(data);
        } catch (Exception ex) {
            logger.error("同步YJBH人员信息数据失败", ex);
            throw new BizException(ResultEnum.API_ERROR.getCode(), I18nErrorCode.EMP_BE_051.getCode());
        }
    }

    @Override
    public void syncEmpTrain(AppTrain train, AppSync config) {

        try {
            Integer trainId = train.getId();
            String trainIdStr = String.valueOf(train.getId());
            String number = "TR" + DateUtil.formatDate(train.getDate(), "yyyyMMdd") + StringUtils.leftPad(trainIdStr, 6, "0");
            Date startTime = DateUtil.addMinutes(train.getDate(), 540 + random.nextInt(60));
            Date endTime = DateUtil.addHours(startTime, MathUtil.toInteger(String.valueOf(train.getDuration())));

            List<TrainEmpDTO> empList = appTrainEmpMapper.selectByTrainId(trainId);
            JSONArray exam = new JSONArray();
            for (TrainEmpDTO emp : empList) {
                JSONObject examItem = new JSONObject();
                examItem.put("name", emp.getEmpName());
                examItem.put("grade", emp.getScore() != null ? MathUtil.doule2int(emp.getScore()) : 90);
                examItem.put("idcard", StringUtils.upperCase(emp.getIdCardNo()));
                examItem.put("isPass", emp.getPassFlag());
                exam.add(examItem);
            }

            JSONObject events = new JSONObject();
            events.put("number", number);
            events.put("type", "three");
            events.put("startTime", DateUtil.formatDateTime(startTime));
            events.put("endTime", DateUtil.formatDateTime(endTime));
            events.put("organization", "中国水利水电第六工程局");
            events.put("dept", "项目部");
            events.put("title", train.getName());
            events.put("teacher", train.getTrainer());
            events.put("location", train.getAddress());
            events.put("content", train.getContent());
            events.put("exam", exam);

            JSONObject profile = new JSONObject();
            profile.put("appType", "education");
            profile.put("modelId", "2078");
            profile.put("poiCode", "w23090601");
            profile.put("name", "安全教育培训");

            JSONObject value = new JSONObject();
            value.put("reportTs", System.currentTimeMillis());
            value.put("profile", profile);
            value.put("properties", new JSONObject());
            value.put("events", events);
            value.put("services", new JSONObject());

            JSONArray values = new JSONArray();
            values.add(value);

            JSONObject json = new JSONObject();
            json.put("SN", "ED03024717001");
            json.put("deviceType", device_type_not);
            json.put("dataType", data_type_not);
            json.put("bidCode", bidCode);
            json.put("workAreaCode", null);
            json.put("deviceName", "安全教育培训");
            json.put("values", values);

            String data = json.toJSONString();
            logger.info("同步YJBH培训数据:{}", data);

            //同步数据
            String host = config.getExt1();
            String appId = config.getAppKey();
            String appSecret = config.getAppSecret();
            YjbhApi api = new YjbhApi(host, appId, appSecret);
            api.pushData(data);
        } catch (Exception ex) {
            logger.error("同步YJBH培训数据失败", ex);
            throw new BizException(ResultEnum.API_ERROR.getCode(), I18nErrorCode.EMP_BE_052.getCode());
        }
    }

    @Override
    public void syncEmpAttend(FaceGateRecordParam param, AppSync config) {

        String datetime = DateUtil.formatDateTime(param.getShowTime());
        Integer deptId = param.getDeptId();
        String personGuid = param.getPersonGuid();
        String idCardNo = param.getIdCardNo();
        Integer empId = param.getEmpId();
        String deviceKey = param.getDeviceKey();
        String direction = Direction.IN.getValue().equals(param.getDirection()) ? "02" : "01";

        try {

            AppFaceGate faceGate = appFaceGateMapper.selectByDeviceKey(deviceKey);
            if (faceGate == null || !SYNC.equals(faceGate.getTag())) {
                return;
            }
            AppEmp emp = null;
            //根据人员ID查询
            if (empId != null) {
                emp = appEmpMapper.selectByPrimaryKey(empId);
            }
            //根据身份证查询
            else if (StringUtils.isNotBlank(idCardNo)) {
                emp = appEmpMapper.selectByDeptIdAndIdCardNo(deptId, idCardNo);
            }
            //根据personGuid查询
            else if (StringUtils.isNotBlank(personGuid)) {
                emp = appEmpMapper.selectByDeptIdAndEmpCode(deptId, personGuid);
            }
            if (emp == null) {
                return;
            }

            logger.info("同步YJBH考勤记录,{},{},{}", deptId, empId, datetime);
            //构建数据
            JSONObject pass = new JSONObject();
            pass.put("eventType", 1);
            pass.put("eventTs", param.getShowTime().getTime());
            pass.put("describe", "");
            pass.put("idCardNumber", emp.getIdCardNo());
            pass.put("name", emp.getEmpName());
            pass.put("passTime", datetime);
            pass.put("passDirection", direction);

            JSONObject events = new JSONObject();
            events.put("pass", pass);

            JSONObject profile = new JSONObject();
            profile.put("appType", "access_control");
            profile.put("modelId", "2053");
            profile.put("poiCode", "w0713001");
            profile.put("name", faceGate.getName());
            profile.put("model", "");
            profile.put("manufacture", "");
            profile.put("owner", owner);
            profile.put("makeDate", "");
            profile.put("validYear", "");
            profile.put("state", "01");
            profile.put("installPosition", "");
            profile.put("level", "01");

            JSONObject value = new JSONObject();
            value.put("reportTs", System.currentTimeMillis());
            value.put("profile", profile);
            value.put("properties", new JSONObject());
            value.put("events", events);
            value.put("services", new JSONObject());

            JSONArray values = new JSONArray();
            values.add(value);

            JSONObject json = new JSONObject();
            json.put("SN", deviceKey);
            json.put("deviceType", device_type_face);
            json.put("dataType", data_type_face);
            json.put("bidCode", bidCode);
            json.put("workAreaCode", null);
            json.put("deviceName", faceGate.getName());
            json.put("values", values);

            //同步数据
            String data = json.toJSONString();
            logger.info("同步YJBH考勤记录:{}", data);

            //同步数据
            String host = config.getExt1();
            String appId = config.getAppKey();
            String appSecret = config.getAppSecret();
            YjbhApi api = new YjbhApi(host, appId, appSecret);
            api.pushData(data);


            if (faceGate.getAreaId() != null) {
                //进洞人员-主洞推送uwb消息,提升标签佩戴率
                if (Direction.IN.getValue().equals(faceGate.getDirection())) {
                    logger.info("推送YJBH隧洞标签数据-进,{},{}", faceGate.getName(), emp.getEmpName());
                    AppIndoorUwbTTL ttlDTO = new AppIndoorUwbTTL();
                    ttlDTO.setDeptId(deptId);
                    ttlDTO.setEmpId(emp.getId());

                    //1分钟,15米
                    ttlDTO.setTtl(TTL1);
                    ttlDTO.setHoleDistance(HOLE_DISTANCE1);
                    amqpTemplate.convertAndSend(QueueConst.UWB_TTL_EXCHANGE, QueueConst.UWB_TTL_QUEUE, JSONUtil.toString(ttlDTO), message -> {
                        message.getMessageProperties().setExpiration(String.valueOf(TTL1));
                        return message;
                    });
                    //2分钟,30米
                    ttlDTO.setTtl(TTL2);
                    ttlDTO.setHoleDistance(HOLE_DISTANCE2);
                    amqpTemplate.convertAndSend(QueueConst.UWB_TTL_EXCHANGE, QueueConst.UWB_TTL_QUEUE, JSONUtil.toString(ttlDTO), message -> {
                        message.getMessageProperties().setExpiration(String.valueOf(TTL2));
                        return message;
                    });
                }
                //出洞人员-主洞推送uwb消息,提升标签佩戴率
                else if (Direction.OUT.getValue().equals(faceGate.getDirection())) {
                    logger.info("推送YJBH隧洞标签数据-出,{},{}", faceGate.getName(), emp.getEmpName());
                    AppIndoorUwbTTL ttlDTO = new AppIndoorUwbTTL();
                    ttlDTO.setDeptId(deptId);
                    ttlDTO.setEmpId(emp.getId());

                    //1分钟,15米
                    ttlDTO.setTtl(TTL3);
                    ttlDTO.setHoleDistance(random.nextInt(HOLE_DISTANCE3));
                    amqpTemplate.convertAndSend(QueueConst.UWB_TTL_EXCHANGE, QueueConst.UWB_TTL_QUEUE, JSONUtil.toString(ttlDTO), message -> {
                        message.getMessageProperties().setExpiration(String.valueOf(TTL3));
                        return message;
                    });
                }
            }

        } catch (Exception ex) {
            logger.error("同步YJBH考勤记录失败，error:{}", ex);
        }
    }

    @Override
    public void syncEmpIndoorPosition(AppIndoorPositionTag tag, AppSync config) {
        try {
            Integer deptId = tag.getDeptId();
            Integer empId = tag.getEmpId();
            //查询人员
            AppEmp emp = appEmpMapper.selectByPrimaryKey(empId);
            if (emp == null) {
                return;
            }

            //决定使用哪个基站
            List<AppIndoorPositionStationDTO> stationList = appIndoorPositionStationMapper.selectByDeptId(deptId);
            if (stationList.size() == 0) {
                return;
            }
            AppIndoorPositionStationDTO station = stationList.get(0);

            Double stationX = tag.getStationX();
            Double stationY = tag.getStationY();
            Double stationZ = tag.getStationZ();

            Double x = tag.getX();
            Double y = tag.getY();
            Double z = tag.getZ();

            Integer stationDistance = tag.getDistance();
            Integer holeDistance = tag.getDistance1();

            JSONObject properties = new JSONObject();
            properties.put("ringCode", tag.getGuid());
            properties.put("icCode", "");
            properties.put("heartRate", null);
            properties.put("electric", tag.getBatteryPower());
            properties.put("time", DateUtil.formatDateTime(tag.getTime()));
            properties.put("sos", 0);
            properties.put("type", manager.equals(emp.getWorkRoleName()) ? type_manager : type_labor);
            properties.put("stationX", stationX);
            properties.put("stationY", stationY);
            properties.put("stationZ", stationZ);
            properties.put("humanX", x);
            properties.put("humanY", y);
            properties.put("humanZ", z);
            properties.put("stationDistance", stationDistance);
            properties.put("holeDistance", holeDistance);
            properties.put("idCardNumber", GAS_SN.equals(tag.getGuid()) ? GAS_DEVICE_SN : StringUtils.upperCase(emp.getIdCardNo()));
            properties.put("name", emp.getEmpName());
            properties.put("locateMode", "GPS");

            JSONObject profile = new JSONObject();
            profile.put("appType", "life");
            profile.put("modelId", "200017");
            profile.put("poiCode", "w0907005");
            profile.put("deviceType", "2001000040");

            JSONObject value = new JSONObject();
            value.put("reportTs", System.currentTimeMillis());
            value.put("profile", profile);
            value.put("properties", properties);
            value.put("events", new JSONObject());
            value.put("services", new JSONObject());

            JSONArray values = new JSONArray();
            values.add(value);

            JSONObject json = new JSONObject();
            json.put("SN", station.getGuid());
            json.put("deviceType", device_type_uwb);
            json.put("dataType", data_type_uwb);
            json.put("bidCode", bidCode);
            json.put("workAreaCode", station.getExt1());
            json.put("workSurface", station.getExt2());
            json.put("tunnCode", station.getExt3());
            json.put("deviceName", station.getName());
            json.put("values", values);

            //同步数据
            String data = json.toJSONString();
            logger.info("同步YJBH-UWB数据:{}", data);

            //同步数据
            String host = config.getExt1();
            String appId = config.getAppKey();
            String appSecret = config.getAppSecret();
            YjbhApi api = new YjbhApi(host, appId, appSecret);
            api.pushData(data);
        } catch (Exception ex) {
            logger.error("同步YJBH人员uwb定位数据失败", ex);
            //throw new BizException(ResultEnum.API_ERROR.getCode(), "同步YJBH人员uwb定位数据失败!");
        }
    }

    @Override
    public void syncEmpPayroll(AppPayroll payroll, AppSync config) {
        try {
            Integer payrollId = payroll.getId();
            List<AppPayrollEmpDTO> empList = appPayrollEmpMapper.selectByPayrollId(payrollId);

            JSONArray staff = new JSONArray();
            String month = String.format("%04d-%02d-01", payroll.getYear(), payroll.getMonth());
            for (AppPayrollEmpDTO emp : empList) {
                JSONObject staffItem = new JSONObject();
                staffItem.put("idCardNo", emp.getIdCardNo());
                staffItem.put("name", emp.getEmpId());
                staffItem.put("month", month);
                staffItem.put("workDay", emp.getRealAttendDays());
                staffItem.put("overWorkDay", 0);
                staffItem.put("wages", emp.getSalaryReal());
                staffItem.put("giveOutStatus", "WAGES_GIVE_OUT_YES");
                staffItem.put("giveOutDate", emp.getDate());
                staffItem.put("isTpgrant", "N");
                staff.add(staffItem);
            }

            JSONObject properties = new JSONObject();
            properties.put("staff", staff);

            JSONObject value = new JSONObject();
            value.put("reportTs", System.currentTimeMillis());
            value.put("properties", properties);
            value.put("events", new JSONObject());
            value.put("services", new JSONObject());

            JSONArray values = new JSONArray();
            values.add(value);

            JSONObject json = new JSONObject();
            json.put("SN", "RY01024718003");
            json.put("deviceType", device_type_payroll);
            json.put("dataType", data_type_payroll);
            json.put("bidCode", bidCode);
            json.put("workAreaCode", null);
            json.put("deviceName", deviceName);
            json.put("values", values);

            String data = json.toJSONString();
            logger.info("同步YJBH工资数据:{}", data);

            //同步数据
            String host = config.getExt1();
            String appId = config.getAppKey();
            String appSecret = config.getAppSecret();
            YjbhApi api = new YjbhApi(host, appId, appSecret);
            api.pushData(data);
        } catch (Exception ex) {
            logger.error("同步YJBH工资数据失败", ex);
            throw new BizException(ResultEnum.API_ERROR.getCode(), I18nErrorCode.EMP_BE_052.getCode());
        }
    }

    @Override
    public void syncEmpCert(AppEmpCert cert, AppSync config) {
        try {
            String url = this.getEndPointUrl(cert.getCertFile());
            String photoBase64 = this.getImageBase64(url);

            String workType = work_type_map.get(cert.getCertTypeName());
            String operationItem = operation_item_map.get(cert.getOperationItemName());

            AppEmp emp = appEmpMapper.selectByPrimaryKey(cert.getEmpId());

            JSONArray staff = new JSONArray();
            JSONObject staffItem = new JSONObject();
            staffItem.put("idCardNo", emp.getIdCardNo());
            staffItem.put("validDate", DateUtil.formatDate(cert.getCertStartDate()));
            staffItem.put("invalidDate", DateUtil.formatDate(cert.getCertExpireDate()));
            staffItem.put("lisenceName", cert.getCertName());
            staffItem.put("licenseNo", cert.getCertCode());
            staffItem.put("issuingAuthority", cert.getCertGrantOrg());
            staffItem.put("issuingDate", DateUtil.formatDate(cert.getCertStartDate()));
            staffItem.put("workType", workType);
            staffItem.put("operationItem", operationItem);
            staffItem.put("url", url);
            staffItem.put("photoBase64", photoBase64);
            staffItem.put("fileSuffix", FileUtil.getSuffixWithoutDot(url));
            staff.add(staffItem);

            JSONObject properties = new JSONObject();
            properties.put("staff", staff);

            JSONObject value = new JSONObject();
            value.put("reportTs", System.currentTimeMillis());
            value.put("properties", properties);
            value.put("events", new JSONObject());
            value.put("services", new JSONObject());

            JSONArray values = new JSONArray();
            values.add(value);

            JSONObject json = new JSONObject();
            json.put("SN", "TZ01024719003");
            json.put("deviceType", device_type_cert);
            json.put("dataType", data_type_cert);
            json.put("bidCode", bidCode);
            json.put("workAreaCode", null);
            json.put("deviceName", deviceName);
            json.put("values", values);

            String data = json.toJSONString();
            logger.info("同步YJBH证件数据:{}", data);

            //同步数据
            String host = config.getExt1();
            String appId = config.getAppKey();
            String appSecret = config.getAppSecret();
            YjbhApi api = new YjbhApi(host, appId, appSecret);
            api.pushData(data);
        } catch (Exception ex) {
            logger.error("同步YJBH证件数据失败", ex);
            throw new BizException(ResultEnum.API_ERROR.getCode(), I18nErrorCode.EMP_BE_052.getCode());
        }
    }

    /**
     * 获取图片base64(照片压缩)
     *
     * @param url
     * @return
     */
    private String getImageBase64(String url) {
        if (StringUtils.isEmpty(url)) {
            return "";
        }
        logger.info("获取图片base64,{}", url);
        byte[] imageData = Base64Util.getUrlImageData(url);
        logger.info("获取图片base64,{},压缩前:{}", url, imageData.length);
        byte[] compressed = ImageUtil.compressPicForScale(imageData, IMAGE_SIZE);
        logger.info("获取图片base64,{},压缩后:{}", url, compressed.length);
        return Base64Util.encodeToString(compressed);
    }

    /**
     * 获取EndPoint地址
     *
     * @param url
     * @return
     */
    private String getEndPointUrl(String url) {
        if (StringUtils.isEmpty(url)) {
            return "";
        }
        if (url.contains("file.whfciot.com")) {
            return url;
        }
        return fileHandler.getDownloadUrl(url);
    }
}


