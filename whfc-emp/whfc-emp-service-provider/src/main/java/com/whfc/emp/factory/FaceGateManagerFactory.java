package com.whfc.emp.factory;

import com.whfc.emp.dao.AppFaceGateConfigMapper;
import com.whfc.emp.dao.AppFaceGateMapper;
import com.whfc.emp.entity.AppFaceGate;
import com.whfc.emp.entity.AppFaceGateConfig;
import com.whfc.emp.enums.FaceGateType;
import com.whfc.emp.factory.impl.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description
 * @date 2020-06-20
 */
@Component
public class FaceGateManagerFactory {

    /**
     * 宇泛沃平台
     */
    @Resource(name = "woFaceGateManagerImpl")
    private WoFaceGateManagerImpl woFaceGateManager;

    /**
     * 其他离线平台
     */
    @Resource(name = "faceGateManagerImpl")
    private FaceGateManagerImpl faceGateManagerImpl;

    /**
     * 海清视讯MQTT
     */
    @Resource(name = "mqttHqsxFaceGateManagerImpl")
    private MqttHqsxFaceGateManagerImpl mqttHqsxFaceGateManager;

    /**
     * 海清视讯MQTT 住建局版本
     */
    @Resource(name = "mqttHqsxJjzFaceGateManagerImpl")
    private MqttHqsxJjzFaceGateManagerImpl mqttHqsxJjzFaceGateManager;

    /**
     * 深圳玉川Q系列设备协议(mqtt协议)
     */
    @Resource(name = "mqttSzycFaceGateManagerImpl")
    private MqttSzycFaceGateMangerImpl mqttSzycFaceGateManger;

    /**
     * 睿瞳人脸识别设备协议(mqtt协议)
     */
    @Resource(name = "mqttRtFaceGateManagerImpl")
    private MqttRtFaceGateMangerImpl mqttRtFaceGateManger;

    /**
     * 深圳快优易设备协议(websocket)
     */
    @Resource(name = "kyyFaceGateManagerImpl")
    private KyyFaceGateManagerImpl kyyFaceGateManager;

    @Autowired
    private AppFaceGateConfigMapper appFaceGateConfigMapper;

    @Autowired
    private AppFaceGateMapper faceGateMapper;

    /**
     * 根据闸机ID查询闸机服务
     *
     * @param faceGateId 闸机ID
     * @return
     */
    public FaceGateManagerWapper getFaceGateService(Integer faceGateId) {
        AppFaceGate faceGate = faceGateMapper.selectByPrimaryKey(faceGateId);
        Integer deptId = faceGate.getDeptId();
        String platform = faceGate.getPlatform();
        return getFaceGateService(deptId, platform);
    }

    /**
     * 根据闸机deviceKey查询闸机服务
     *
     * @param deviceKey
     * @return
     */
    public FaceGateManagerWapper getFaceGateService(String deviceKey) {
        AppFaceGate faceGate = faceGateMapper.selectByDeviceKey(deviceKey);
        Integer deptId = faceGate.getDeptId();
        String platform = faceGate.getPlatform();
        return getFaceGateService(deptId, platform);
    }

    /**
     * 根据项目ID和平台查询闸机服务器
     *
     * @param deptId
     * @param platform
     * @return
     */
    public FaceGateManagerWapper getFaceGateService(Integer deptId, String platform) {
        AppFaceGateConfig appFaceGateConfig = appFaceGateConfigMapper.selectByDeptIdAndPlatform(deptId, platform);
        FaceGateType faceGateType = FaceGateType.parseCode(platform);
        FaceGateManager faceGateManager = getFaceGateManager(faceGateType.getValue());
        return new FaceGateManagerWapper(faceGateManager, appFaceGateConfig);
    }

    /**
     * 根据配置ID查询闸机服务
     *
     * @param configId
     * @return
     */
    public FaceGateManagerWapper getFaceGateServiceById(Integer configId) {
        AppFaceGateConfig appFaceGateConfig = appFaceGateConfigMapper.selectByPrimaryKey(configId);
        FaceGateType faceGateType = FaceGateType.parseCode(appFaceGateConfig.getPlatform());
        FaceGateManager faceGateManager = this.getFaceGateManager(faceGateType.getValue());
        return new FaceGateManagerWapper(faceGateManager, appFaceGateConfig);
    }

    /**
     * 获取闸机服务
     *
     * @param faceGateType
     * @return
     */
    private FaceGateManager getFaceGateManager(Integer faceGateType) {
        switch (faceGateType) {
            case 3:
                return woFaceGateManager;
            case 9:
                return mqttHqsxJjzFaceGateManager;
            case 10:
                return mqttHqsxFaceGateManager;
            case 12:
                return kyyFaceGateManager;
            case 13:
                return mqttSzycFaceGateManger;
            case 16:
                return mqttRtFaceGateManger;
            default:
                return faceGateManagerImpl;
        }
    }
}
