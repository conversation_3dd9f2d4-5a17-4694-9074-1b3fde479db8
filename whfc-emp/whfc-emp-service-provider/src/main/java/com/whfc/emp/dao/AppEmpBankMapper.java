package com.whfc.emp.dao;

import com.whfc.emp.dto.AppEmpBankDTO;
import com.whfc.emp.entity.AppEmpBank;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AppEmpBankMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppEmpBank record);

    int insertSelective(AppEmpBank record);

    AppEmpBank selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppEmpBank record);

    int updateByPrimaryKey(AppEmpBank record);

    /**
     * 根据人员id查找正在使用的银行卡号
     *
     * @param empId
     * @return
     */
    String selectBankNumberByEmpId(@Param("empId") Integer empId);

    /**
     * 根据人员id逻辑删除
     *
     * @param empId
     */
    void deleteLogicByEmpId(Integer empId);

    /**
     * 根据人员id查找
     *
     * @param empId
     * @return
     */
    List<AppEmpBankDTO> selectBankDTOList(@Param("empId") Integer empId);

    /**
     * 根据银行卡号查找
     *
     * @param bankNumber
     * @return
     */
    AppEmpBank selectByBankNumber(@Param("bankNumber")String bankNumber);

    /**
     * 逻辑删除
     *
     * @param bankId
     */
    void deleteLogicById(@Param("bankId")Integer bankId);

    /**
     * 根据人员id查找正在使用的银行卡号
     *
     * @param empId
     * @return
     */
    List<AppEmpBank> selectByEmpId(@Param("empId") Integer empId);
}