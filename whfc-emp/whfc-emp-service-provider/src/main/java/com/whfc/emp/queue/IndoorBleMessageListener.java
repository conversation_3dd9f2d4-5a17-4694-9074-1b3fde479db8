package com.whfc.emp.queue;

import com.whfc.common.constant.QueueConst;
import com.whfc.common.third.indoor.ble.BleScan;
import com.whfc.common.third.indoor.ble.BleTag;
import com.whfc.common.util.JSONUtil;
import com.whfc.emp.indoor.IndoorPositionMgr;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/1/4 11:05
 */
@Component
@RabbitListener(queuesToDeclare = {@Queue(name = QueueConst.EMP_INDOOR_BLE)}, concurrency = "1-2")
public class IndoorBleMessageListener {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private IndoorPositionMgr indoorPositionMgr;

    @RabbitHandler
    public void process(String msg) {
        try {
            logger.info("indoor|ble|msg|{}", msg);
            BleScan scan = JSONUtil.parseObject(msg, BleScan.class);
            if (scan != null) {
                String imei = scan.getImei();
                List<BleTag> tagList = scan.getTagList();
                for (BleTag bleTag : tagList) {
                    String mac = bleTag.getMac();
                    indoorPositionMgr.addBleScan(imei, mac);
                }
            }
        } catch (Exception ex) {
            logger.error("indoor|ble|msg,消息处理失败", ex);
        }
    }
}
