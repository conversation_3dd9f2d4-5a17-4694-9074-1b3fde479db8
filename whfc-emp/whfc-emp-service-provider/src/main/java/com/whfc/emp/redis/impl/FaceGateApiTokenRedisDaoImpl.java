package com.whfc.emp.redis.impl;

import com.whfc.emp.redis.FaceGateApiTokenRedisDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import java.util.concurrent.TimeUnit;


@Repository
public class FaceGateApiTokenRedisDaoImpl implements FaceGateApiTokenRedisDao {

    public static final String key = "face-gate-api:%s:token";

    public static final long timeout = 1;

    @Autowired
    private StringRedisTemplate redisTemplate;


    @Override
    public void setAccessToken(String appId, String accessToken) {
        String str = String.format(key,appId);
        redisTemplate.opsForValue().set(str, accessToken, timeout, TimeUnit.DAYS);
    }

    @Override
    public String getAccessToken(String appId) {
        String str = String.format(key,appId);
        return redisTemplate.opsForValue().get(str);
    }
}
