package com.whfc.emp.dao;

import com.whfc.emp.entity.AppFaceGateFace;

public interface AppFaceGateFaceMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppFaceGateFace record);

    int insertSelective(AppFaceGateFace record);

    AppFaceGateFace selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppFaceGateFace record);

    int updateByPrimaryKey(AppFaceGateFace record);
}