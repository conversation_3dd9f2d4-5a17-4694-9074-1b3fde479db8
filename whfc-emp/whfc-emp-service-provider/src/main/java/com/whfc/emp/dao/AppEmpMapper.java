package com.whfc.emp.dao;

import com.whfc.emp.dto.*;
import com.whfc.emp.entity.AppEmp;
import com.whfc.emp.param.AppEmpListParam;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;

public interface AppEmpMapper {
    int deleteByPrimaryKey(Integer id);

    int insertSelective(AppEmp record);

    AppEmp selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppEmp record);

    /**
     * 查询项目人员列表
     *
     * @param deptId
     * @return
     */
    List<AppEmp> selectDeptEmpList(@Param("deptId") Integer deptId);

    /**
     * 查询项目人员名单(排除指定人员)
     *
     * @param deptId
     * @param empIdList
     * @return
     */
    List<AppEmpDTO> selectEmpListExclude(@Param("deptId") Integer deptId, @Param("empIdList") List<Integer> empIdList);

    /**
     * 查询项目人员名单(排除指定人员)
     *
     * @param deptId
     * @param empIdList
     * @param groupId
     * @param keyword
     * @return
     */
    List<AppEmpDTO> selectEmpListExcludes(@Param("deptId") Integer deptId,
                                          @Param("empIdList") List<Integer> empIdList,
                                          @Param("groupId") Integer groupId,
                                          @Param("keyword") String keyword);

    /**
     * 根据班组ID或合作单位查找绑定了安全帽的人员
     *
     * @param corpId
     * @param groupId
     * @param deptId
     * @return 人员列表
     */
    List<AppEmp> selectBindEmpByCorpIdOrGroupId(@Param("corpId") Integer corpId, @Param("groupId") Integer groupId, @Param("deptId") Integer deptId);

    /**
     * 根据项目id和人员姓名查找
     *
     * @param projectId
     * @param empName
     * @return
     */
    List<AppEmp> selectByProjectIdAndEmpName(@Param("projectId") Integer projectId, @Param("empName") String empName);

    /**
     * 获取人员列表
     *
     * @param request
     * @return
     */
    List<AppEmpDTO> selectByParam(AppEmpListParam request);

    /**
     * 查询所有人员
     *
     * @param deptId
     * @param keyword
     * @return
     */
    List<AppEmpDTO> selectByDeptId(@Param("deptId") Integer deptId, @Param("keyword") String keyword);

    /**
     * 更新人员二维码
     *
     * @param empId
     * @param qr
     */
    void updateQrByEmpId(@Param("empId") Integer empId, @Param("qr") String qr);

    /**
     * 逻辑删除
     *
     * @param empId
     */
    void delLogicByEmpId(@Param("empId") Integer empId);

    /**
     * 更新岗位状态
     *
     * @param empId
     * @param postState
     * @param date
     */
    void updatePostState(@Param("empId") Integer empId,
                         @Param("postState") Integer postState,
                         @Param("date") Date date);


    /**
     * 批量更新岗位状态
     *
     * @param empIds
     * @param postState
     * @param date
     */
    void updatePostStateByEmpIds(@Param("empIds") List<Integer> empIds,
                                 @Param("postState") Integer postState,
                                 @Param("date") Date date);


    /**
     * 获取人员信息
     *
     * @param empId 人员ID
     * @return AppEmpDTO
     */
    AppEmpDTO selectEmpDTOById(@Param("empId") Integer empId);

    /**
     * 人员分析-获取所有在岗人员数据
     *
     * @param deptId
     * @return
     */
    List<AppEmpDTO> selectAnaEmpByDeptId(@Param("deptId") Integer deptId);

    /**
     * 查询最近进场人员
     *
     * @param deptId
     * @return
     */
    List<AppAnaEmpDTO> selectRecentEnterEmp(@Param("deptId") Integer deptId);

    /**
     * 查找工种
     *
     * @param deptId
     * @return
     */
    List<AppWorkTypeDTO> selectWorkTypeList(@Param("deptId") Integer deptId);

    /**
     * 查找首页人员列表h
     *
     * @param deptId
     * @param keyword
     * @param attendState
     * @param localeState
     * @param groupId
     * @param empId
     * @return
     */
    List<AppEmpDTO> selectIndexEmpList(@Param("deptId") Integer deptId,
                                       @Param("keyword") String keyword,
                                       @Param("attendState") Integer attendState,
                                       @Param("localeState") Integer localeState,
                                       @Param("groupId") Integer groupId,
                                       @Param("empId") Integer empId);

    /**
     * 查找绑定硬件人员列表
     *
     * @param deptId
     * @param keyword
     * @return
     */
    List<AppEmpDTO> selectHelmetEmpList(@Param("deptId") Integer deptId,
                                        @Param("bindFlag") Integer bindFlag,
                                        @Param("keyword") String keyword);

    /**
     * 更新关键岗位人员认证
     *
     * @param empId
     * @param keyPositionAuth
     */
    void updateKeyPositionAuth(@Param("empId") Integer empId,
                               @Param("keyPositionAuth") Integer keyPositionAuth);

    /**
     * 根据身份证跟新人员信息
     *
     * @param idcardNo
     * @param phone
     * @param nation
     * @param org
     * @param address
     */
    void updateEmpInfoByIdCardNo(@Param("idcardNo") String idcardNo,
                                 @Param("phone") String phone,
                                 @Param("nation") String nation,
                                 @Param("org") String org,
                                 @Param("address") String address);

    /**
     * 逻辑删除 根据项目ID与人员身份证
     *
     * @param deptId
     * @param idCardNo
     */
    void delLogicByDeptIdAndIdCardNo(@Param("deptId") Integer deptId, @Param("idCardNo") String idCardNo);

    /**
     * 用项目 身份证号查找人员
     *
     * @param deptId
     * @param idCardNo
     * @return
     */
    AppEmp selectByDeptIdAndIdCardNo(@Param("deptId") Integer deptId, @Param("idCardNo") String idCardNo);

    /**
     * 逻辑删除 根据项目ID与人员empCode
     *
     * @param deptId
     * @param empCode
     */
    void delLogicByDeptIdAndEmpCode(@Param("deptId") Integer deptId, @Param("empCode") String empCode);

    /**
     * 根据人员姓名和人员编码查找人员
     *
     * @param deptId  项目id
     * @param empCode 人员编码
     * @return 人员
     */
    AppEmp selectByDeptIdAndEmpCode(@Param("deptId") Integer deptId, @Param("empCode") String empCode);

    /**
     * 根据手机号查询人员信息
     *
     * @param deptId
     * @param phone
     * @return
     */
    AppEmp selectByDeptIdAndPhone(@Param("deptId") Integer deptId, @Param("phone") String phone);


    /**
     * 三位模型-人员数据
     *
     * @param deptId
     * @return
     */
    List<GisEmpDTO> selectGisEmpData(@Param("deptId") Integer deptId);

    /**
     * 查询机构内的在岗人员ID列表
     *
     * @param deptId
     * @return
     */
    List<AppEmpDTO> selectEmpListByDeptId(@Param("deptId") Integer deptId);

    /**
     * @param deptId
     * @param keyword
     * @return
     */
    List<AppEmpDTO> selectEmpByDeptIdAndKeyword(@Param("deptId") Integer deptId, @Param("keyword") String keyword);

    /**
     * 查询开放平台人员列表
     *
     * @param deptId 组织机构ID
     * @return 人员列表
     */
    List<AppEmpDTO> selectOpenApiEmpList(@Param("deptId") Integer deptId);

    /**
     * 项目的在岗人数
     *
     * @param deptIds
     * @return
     */
    Integer selectPostEmpNum(@Param("deptIds") List<Integer> deptIds);

    /**
     * 项目的场内人数
     *
     * @param deptId
     * @return 人数
     */
    Integer countLocaleNum(@Param("deptId") Integer deptId);

    /**
     * 项目的在岗人数
     *
     * @param deptId
     * @return
     */
    Integer countByDeptId(@Param("deptId") Integer deptId);

    /**
     * 根据工种统计人数
     *
     * @param deptId
     * @param workTypeId
     * @return
     */
    int countByWorkTypeId(@Param("deptId") Integer deptId, @Param("workTypeId") Integer workTypeId);

    /**
     * 根据人员类型统计
     *
     * @param deptId
     * @param workRoleId
     * @return
     */
    int countByWorkRole(@Param("deptId") Integer deptId, @Param("workRoleId") Integer workRoleId);

    /**
     * 根据人员类型统计
     *
     * @param deptId
     * @return
     */
    List<AppEmpWorkRoleNumDTO> countByWorkRoleId(@Param("deptId") Integer deptId);

    /**
     * 根据分组统计人数
     *
     * @param deptId
     * @param groupId
     * @return
     */
    int countByGroup(@Param("deptId") Integer deptId, @Param("groupId") Integer groupId);

    /**
     * 查询人员基本信息统计
     *
     * @param deptId
     * @return
     */
    AppEmpAnaNumDTO selectEmpNumByDeptId(@Param("deptId") Integer deptId);

    /**
     * 查询人员生日
     *
     * @param deptId
     * @return
     */
    List<EmpAge> selectBirthdayByDeptId(@Param("deptId") Integer deptId);

    /**
     * 查询企业年龄统计
     *
     * @param deptIds
     * @return
     */
    List<EmpAge> selectEnterpriseAgeStat(@Param("deptIds") Collection<Integer> deptIds);

    /**
     * 查询企业合作单位统计
     *
     * @param deptIds
     * @return
     */
    List<AppEmpAttendGroupDTO> selectEnterpriseCorpStat(@Param("deptIds") Collection<Integer> deptIds);

    /**
     * 查询企业工种统计
     *
     * @param deptIds
     * @return
     */
    List<AppEmpAttendGroupDTO> selectEnterpriseWorkStat(@Param("deptIds") Collection<Integer> deptIds);

    /**
     * 查询企业特殊工种统计
     *
     * @param deptIds
     * @return
     */
    List<AppEmpAttendGroupDTO> selectEnterpriseSpecWorkStat(@Param("deptIds") Collection<Integer> deptIds);

    /**
     * 查询班组统计
     *
     * @param deptId
     * @return
     */
    List<AppEmpAttendGroupDTO> selectGroupNumByDeptId(@Param("deptId") Integer deptId);

    /**
     * 查询合作单位统计
     *
     * @param deptId
     * @return
     */
    List<AppEmpAttendGroupDTO> selectCorpNumByDeptId(@Param("deptId") Integer deptId);

    /**
     * 查询工种统计
     *
     * @param deptId
     * @return
     */
    List<AppEmpAttendGroupDTO> selectWorkNumByDeptId(@Param("deptId") Integer deptId);

    /**
     * 人员统计
     *
     * @param request
     * @return
     */
    AppEmpNumDTO selectEmpNumByParam(AppEmpListParam request);

    /**
     * 更新人员入场培训状态
     *
     * @param empId          人员ID
     * @param enterTrainFlag 入场培训状态
     */
    void updateEmpEnterTrainFlag(@Param("empId") Integer empId, @Param("enterTrainFlag") Integer enterTrainFlag);

    /**
     * 更新人员核验状态
     *
     * @param empId
     * @param verifyState
     */
    void updateEmpVerifyState(@Param("empId") Integer empId, @Param("verifyState") Integer verifyState);

    /**
     * 更新人员同步状态
     *
     * @param empId
     * @param syncFlag
     */
    void updateEmpSyncFlag(@Param("empId") Integer empId, @Param("syncFlag") Integer syncFlag);

    /**
     * 批量保存人员
     *
     * @param empList 人员列表
     */
    void batchInsertEmp(@Param("empList") List<AppEmp> empList);

    /**
     * 查询已删除人员列表
     *
     * @return
     */
    List<AppEmpDTO> selectDelEmpList();

    /**
     * 查询已离职人员
     *
     * @return
     */
    List<AppEmpDTO> selectOuterEmpList();

    /**
     * 更新工种id
     *
     * @param workTypeId
     * @param workTypeName
     * @param deptId
     */
    void updateWorkTypeId(@Param("deptId") Integer deptId, @Param("workTypeId") Integer workTypeId, @Param("workTypeName") String workTypeName);

    /**
     * 更新工种名称
     *
     * @param workTypeId   工种ID
     * @param workTypeName 工种名称
     */
    void updateWorkTypeName(@Param("deptId") Integer deptId, @Param("workTypeId") Integer workTypeId, @Param("workTypeName") String workTypeName);

    /**
     * 查找工种异常的人员
     *
     * @return 人员
     */
    List<AppEmp> selectOddWorkTypeEmp();

    /**
     * 验证组织机构下面是否有该人员
     *
     * @param deptIdList 组织机构ID列表
     * @param empId      人员ID
     * @return 设备ID
     */
    Integer selectByDeptIdsAndEmpId(@Param("deptIdList") List<Integer> deptIdList, @Param("empId") Integer empId);

    /**
     * 验证人员名称是否重复
     *
     * @param deptId
     * @param name
     * @return
     */
    int checkEmpName(@Param("deptId") Integer deptId, @Param("name") String name);
}