package com.whfc.emp.queue;

import com.alibaba.fastjson.JSON;
import com.whfc.common.constant.QueueConst;
import com.whfc.emp.dto.train.*;
import com.whfc.emp.enums.TrainBoxMsgType;
import com.whfc.emp.manager.TrainBoxManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 人员培训箱
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-08-26 15:24
 */
@Component
@RabbitListener(queuesToDeclare = {@Queue(QueueConst.EMP_TRAIN_BOX_DATA)}, concurrency = "1-2")
public class TrainBoxQueueMessageListener {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private TrainBoxManager trainBoxManager;

    @RabbitHandler
    public void handle(String msg) {
        logger.info("添加培训箱数据,{}", msg);
        try {
            TrainBoxDTO trainBoxDTO = JSON.parseObject(msg, TrainBoxDTO.class);
            if (trainBoxDTO == null) {
                return;
            }
            handleTrainBoxData(msg);
        } catch (Exception ex) {
            logger.error("", ex);
        }
    }

    /**
     * 处理培训箱数据
     *
     * @param msg 培训箱数据
     */
    private void handleTrainBoxData(String msg) {
        TrainBoxDTO trainBoxDTO = JSON.parseObject(msg, TrainBoxDTO.class);
        if (trainBoxDTO == null) {
            return;
        }
        Integer deptId = trainBoxDTO.getDeptId();
        try {
            TrainBoxMsgType type = TrainBoxMsgType.parseValue(trainBoxDTO.getType());
            if (type == null) {
                logger.warn("消息类型错误");
                return;
            }
            String data = trainBoxDTO.getData();
            switch (type) {
                case TRAIN_EMP_INFO:
                    // 人员信息
                    List<TrainEmpInfoDTO> empInfoList = JSON.parseArray(data, TrainEmpInfoDTO.class);
                    break;
                case TRAIN_DEPT_INFO:
                    // 部门信息
                    List<TrainDeptInfoDTO> deptInfoList = JSON.parseArray(data, TrainDeptInfoDTO.class);
                    break;
                case TRAIN_RECORD:
                    // 培训记录
                    List<TrainRecordDTO> trainRecordList = JSON.parseArray(data, TrainRecordDTO.class);
                    trainRecordList.forEach(record -> record.setDeptId(deptId));
                    trainBoxManager.addTrainRecord(trainRecordList);
                    // 处理错误数据
                    trainBoxManager.handleFailedData();
                    break;
                case TRAIN_RECORD_EMP:
                    // 培训人员
                    List<TrainRecordEmpDTO> trainRecordEmpList = JSON.parseArray(data, TrainRecordEmpDTO.class);
                    trainRecordEmpList.forEach(record -> record.setDeptId(deptId));
                    trainBoxManager.addTrainEmp(trainRecordEmpList);
                    break;
                case TRAIN_RECORD_PAPER:
                    // 培训试卷
                    List<TrainPaperDTO> trainPaperList = JSON.parseArray(data, TrainPaperDTO.class);
                    trainPaperList.forEach(record -> record.setDeptId(deptId));
                    trainBoxManager.addTrainPaper(trainPaperList);
                    break;
                case TRAIN_RECORD_INFO_EMP:
                    // 人员培训信息
                    List<TrainRecordInfoEmpDTO> trainRecordInfoEmpList = JSON.parseArray(data, TrainRecordInfoEmpDTO.class);
                    break;
                case TRAIN_RECORD_FILE:
                    // 人员培训文件
                    List<TrainRecordFileDTO> trainRecordFileList = JSON.parseArray(data, TrainRecordFileDTO.class);
                    trainRecordFileList.forEach(record -> record.setDeptId(deptId));
                    trainBoxManager.addTrainFile(trainRecordFileList);
                    break;
                case TRAIN_VIOLATION_INFO:
                    // 违章信息
                    List<TrainViolationInfoDTO> trainViolationInfoList = JSON.parseArray(data, TrainViolationInfoDTO.class);
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            logger.error("培训箱数据处理失败.", e);
        }
    }

}
