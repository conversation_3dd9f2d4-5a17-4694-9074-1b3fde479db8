package com.whfc.emp.dao;

import com.whfc.emp.entity.AppEmpConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AppEmpConfigMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppEmpConfig record);

    int insertSelective(AppEmpConfig record);

    AppEmpConfig selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppEmpConfig record);

    int updateByPrimaryKey(AppEmpConfig record);

    /**
     * 使用组织机构id和code查询人员配置
     *
     * @param deptId
     * @param code
     * @return
     */
    AppEmpConfig selectByDeptIdAndCode(@Param("deptId") Integer deptId, @Param("code") String code);

    /**
     * 使用组织机构id 查询人员配置列表
     *
     * @param deptId
     * @return
     */
    List<AppEmpConfig> selectByDeptId(@Param("deptId") Integer deptId);

    /**
     * 添加数据
     *
     * @param list
     */
    void insertOrUpdate(@Param("list") List<AppEmpConfig> list);

    /**
     * 删除配置
     *
     * @param deptId 组织机构ID
     * @param code   编码
     */
    void delByDeptIdAndCode(@Param("deptId") Integer deptId, @Param("code") String code);

    /**
     * 根据编码和值查找组织机构
     *
     * @param code  编码
     * @param value 值
     * @return 组织机构ID
     */
    Integer selectDeptIdByCodeAndValue(@Param("code") String code, @Param("value") String value);
}