package com.whfc.emp.manager.warn;

import com.alibaba.fastjson2.JSON;
import com.whfc.emp.dto.EmpWarnCheckDTO;
import com.whfc.emp.dto.EmpWarnRuleDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 心率报警
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/7
 */
@Component(value = "appEmpWarnMgrHeartRate")
public class AppEmpWarnMgrHeartRate extends AppEmpWarnMgrBase implements AppEmpWarnMgr {


    private static final String MAX_PARAM_NAME = "maxValue";
    private static final String MIN_PARAM_NAME = "minValue";

    @Override
    public boolean checkValue(EmpWarnCheckDTO checkDTO, EmpWarnRuleDTO ruleDTO) {
        Integer heartRate = checkDTO.getHeartRate();
        if (heartRate == null) {
            return false;
        }
        String ruleParam = ruleDTO.getRuleParam();
        if (StringUtils.isBlank(ruleParam)) {
            return false;
        }
        // 校验心率是否超过最高值
        Integer maxValue = JSON.parseObject(ruleParam).getInteger(MAX_PARAM_NAME);
        if (maxValue != null && maxValue < heartRate) {
            checkDTO.setTriggerKey(heartRate + "");
            return true;
        }
        // 校验心率是否低于最低值
        Integer minValue = JSON.parseObject(ruleParam).getInteger(MIN_PARAM_NAME);
        if (minValue != null && minValue > heartRate) {
            checkDTO.setTriggerKey(heartRate + "");
            return true;
        }
        return false;
    }

    @Override
    public boolean checkFrequency(EmpWarnCheckDTO checkDTO, EmpWarnRuleDTO ruleDTO) {
        return true;
    }
}
