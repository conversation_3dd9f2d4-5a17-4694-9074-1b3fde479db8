package com.whfc.emp.dao;

import com.whfc.emp.dto.AppEmpAttendGroupDTO;
import com.whfc.emp.dto.AppGroupDTO;
import com.whfc.emp.dto.MapEmpGroupDTO;
import com.whfc.emp.dto.WxEmpGroupDTO;
import com.whfc.emp.dto.train.AppBoardEmpGroupCountDTO;
import com.whfc.emp.entity.AppEmpGroup;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-09-03 16:01
 */

public interface AppEmpGroupMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppEmpGroup record);

    int insertSelective(AppEmpGroup record);

    AppEmpGroup selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppEmpGroup record);

    int updateByPrimaryKey(AppEmpGroup record);

    /**
     * 通过projectId查找列表
     *
     * @param projectId
     * @param corpId
     * @param keyword
     * @return
     */
    List<AppGroupDTO> selectByProjectId(@Param("projectId") Integer projectId,
                                        @Param("corpId") Integer corpId,
                                        @Param("keyword") String keyword);

    /**
     * 根据合作单位id查找
     *
     * @param corpId
     * @return
     */
    List<AppGroupDTO> selectByCorpId(@Param("corpId") Integer corpId);

    /**
     * 根据组织机构id查找
     *
     * @param deptId
     * @param keyword
     * @return
     */
    List<AppGroupDTO> selectByDeptId(@Param("deptId") Integer deptId,
                                     @Param("keyword") String keyword);

    /**
     * 查询合作单位ID列表
     *
     * @param deptIds
     * @return
     */
    List<Integer> selectCorpIdList(@Param("deptIds") List<Integer> deptIds);

    /**
     * 逻辑删除
     *
     * @param groupId
     */
    void deleteLogicById(@Param("groupId") Integer groupId);

    /**
     * 根据组织机构id查找
     *
     * @param deptId
     * @return
     */
    List<WxEmpGroupDTO> selectGroupNameByDeptId(Integer deptId);

    /**
     * 使用班组编码和租子机构id查询班组信息
     *
     * @param groupCode
     * @param deptId
     * @return
     */
    AppEmpGroup selectGroupCodeAndDeptId(@Param("groupCode") String groupCode, @Param("deptId") Integer deptId);

    /**
     * 查询班组出勤统计
     *
     * @param deptId
     * @param date
     * @return
     */
    List<AppEmpAttendGroupDTO> selectGroupAttendNum(@Param("deptId") Integer deptId, @Param("date") Date date);

    /**
     * 查询班组出勤统计
     *
     * @param deptId
     * @param date
     * @return
     */
    List<AppEmpAttendGroupDTO> selectCorpAttendNum(@Param("deptId") Integer deptId, @Param("date") Date date);

    /**
     * 查看带监控的班组
     *
     * @param deptId      组织机构ID
     * @param groupId     班组ID
     * @param fvsDeviceId 视频监控ID
     * @return 带监控的班组
     */
    List<MapEmpGroupDTO> selectFvsGroup(@Param("deptId") Integer deptId,
                                        @Param("groupId") Integer groupId,
                                        @Param("fvsDeviceId") Integer fvsDeviceId);

    /**
     * 根据合作单位统计
     *
     * @param deptId 组织机构ID
     * @return 合作单位统计
     */
    List<AppBoardEmpGroupCountDTO> countByCorp(@Param("deptId") Integer deptId);

    /**
     * 根据组织机构和名称查找班组
     *
     * @param deptId
     * @param groupName
     * @return
     */
    AppEmpGroup selectByDeptIdAndName(@Param("deptId") Integer deptId, @Param("groupName") String groupName);

    /**
     * 根据合作单位ID已经班组名称查找班组
     *
     * @param corpId    合作单位ID
     * @param groupName 班组名称
     * @return 班组
     */
    AppEmpGroup selectByCorpIdAndName(@Param("corpId") Integer corpId, @Param("groupName") String groupName);
}