package com.whfc.emp.dao;

import com.whfc.emp.dto.AppEmpAttendRecordDTO;
import com.whfc.emp.dto.AppEmpInputDataDTO;
import com.whfc.emp.dto.BoardEmpAttendRecordDTO;
import com.whfc.emp.dto.stat.EmpAttendStat;
import com.whfc.emp.entity.AppEmpAttendRecord;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface AppEmpAttendRecordMapper {
    int deleteByPrimaryKey(Integer id);

    int insertSelective(AppEmpAttendRecord record);

    AppEmpAttendRecord selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppEmpAttendRecord record);

    /**
     * 打卡记录列表
     *
     * @param deptId
     * @param startTime
     * @param endTime
     * @param keyword
     * @param type
     * @return
     */
    List<AppEmpInputDataDTO> selectByDeptIdAndDate(@Param("deptId") Integer deptId,
                                                   @Param("corpId") Integer corpId,
                                                   @Param("groupId") Integer groupId,
                                                   @Param("workTypeId") Integer workTypeId,
                                                   @Param("startTime") Date startTime,
                                                   @Param("endTime") Date endTime,
                                                   @Param("keyword") String keyword,
                                                   @Param("type") Integer type);


    /**
     * 查询打卡记录
     *
     * @param empId
     * @param startTime
     * @param endTime
     * @param direction
     * @param attendType
     * @return
     */
    List<AppEmpAttendRecordDTO> selectByEmpIdAndDate(@Param("empId") Integer empId,
                                                     @Param("startTime") Date startTime,
                                                     @Param("endTime") Date endTime,
                                                     @Param("direction") Integer direction,
                                                     @Param("attendType") Integer attendType);


    /**
     * 查询项目所有的进出记录
     *
     * @param deptId
     * @param startTime
     * @param endTime
     * @param attendType
     * @return
     */
    List<AppEmpAttendRecordDTO> selectAttendRecordByDeptIdAndTime(@Param("deptId") Integer deptId,
                                                                  @Param("startTime") Date startTime,
                                                                  @Param("endTime") Date endTime,
                                                                  @Param("attendType") Integer attendType);


    /**
     * 查找人员的最新一条数据
     *
     * @param empId
     * @param attendType
     * @param date
     * @return
     */
    AppEmpAttendRecord selectLastAttendRecord(@Param("empId") Integer empId,
                                              @Param("attendType") Integer attendType,
                                              @Param("date") Date date);

    /**
     * 查询大屏人员考勤列表
     *
     * @param deptId
     * @param startTime
     * @param endTime
     * @return
     */
    List<BoardEmpAttendRecordDTO> selectByDeptIdAndTime(@Param("deptId") Integer deptId,
                                                        @Param("startTime") Date startTime,
                                                        @Param("endTime") Date endTime);


    /********脏数据清理**********/

    /**
     * 查询人员考勤记录重复统计
     *
     * @param startTime
     * @param endTime
     * @return
     */
    List<EmpAttendStat> selectEmpAttendRecCount(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询人员某时刻的考勤记录
     *
     * @param empId
     * @param time
     * @return
     */
    List<AppEmpAttendRecord> selectAttendRecordByEmpIdAndTime(@Param("empId") Integer empId, @Param("time") Date time);

    /**
     * 根据人员ID和时间统计
     *
     * @param empId
     * @param time
     * @return
     */
    int countByEmpIdAndTime(@Param("empId") Integer empId, @Param("time") Date time);

    /**
     * 批量删除
     *
     * @param idList
     * @return
     */
    int batchDelete(@Param("idList") List<Integer> idList);

    /**
     * 逻辑删除
     *
     * @param id
     */
    void deleteLogicById(@Param("id") Integer id);
}