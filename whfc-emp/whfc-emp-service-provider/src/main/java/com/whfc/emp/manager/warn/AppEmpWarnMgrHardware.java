package com.whfc.emp.manager.warn;

import com.whfc.emp.dao.AppEmpWarnMapper;
import com.whfc.emp.dto.EmpWarnCheckDTO;
import com.whfc.emp.dto.EmpWarnRuleDTO;
import com.whfc.emp.entity.AppEmpWarn;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description: 人员报警管理器-安全帽硬件报警
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/10/10 15:53
 */
@Component
public abstract class AppEmpWarnMgrHardware extends AppEmpWarnMgrBase implements AppEmpWarnMgr {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 硬件报警标记
     */
    private static final Integer ALARM_YES = 1;

    @Autowired
    private AppEmpWarnMapper appEmpWarnMapper;

    @Override
    public boolean checkValue(EmpWarnCheckDTO checkDTO, EmpWarnRuleDTO ruleDTO) {
        Integer alarmValue = this.getAlarmValue(checkDTO);
        return ALARM_YES.equals(alarmValue);
    }

    @Override
    public Integer saveWarn(EmpWarnCheckDTO checkDTO, EmpWarnRuleDTO ruleDTO) {
        AppEmpWarn record = new AppEmpWarn();
        record.setDeptId(checkDTO.getDeptId());
        record.setTriggerObjectId(String.valueOf(checkDTO.getEmpId()));
        record.setTriggerTime(checkDTO.getTriggerTime());
        record.setRuleId(ruleDTO.getRuleId());
        record.setRuleType(ruleDTO.getRuleType());
        appEmpWarnMapper.insertSelective(record);
        return record.getId();
    }

    /**
     * 获取报警标记
     *
     * @param checkDTO
     * @return
     */
    public abstract Integer getAlarmValue(EmpWarnCheckDTO checkDTO);
}
