package com.whfc.emp.third;

import com.whfc.emp.entity.AppSync;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/6/30 15:24
 */
public class EmpThirdSyncWapper {

    private EmpThirdSync sync;

    private AppSync config;

    public EmpThirdSyncWapper(EmpThirdSync sync, AppSync config) {
        this.sync = sync;
        this.config = config;
    }

    public EmpThirdSync getSync() {
        return sync;
    }

    public void setSync(EmpThirdSync sync) {
        this.sync = sync;
    }

    public AppSync getConfig() {
        return config;
    }

    public void setConfig(AppSync config) {
        this.config = config;
    }
}
