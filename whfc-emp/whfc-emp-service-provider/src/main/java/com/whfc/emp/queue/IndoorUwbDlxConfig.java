package com.whfc.emp.queue;

import com.whfc.common.constant.QueueConst;
import org.springframework.amqp.core.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class IndoorUwbDlxConfig {

    /**
     * TTL交换机
     *
     * @return
     */
    @Bean
    public Exchange ttlExchange() {
        return ExchangeBuilder
                .topicExchange(QueueConst.UWB_TTL_EXCHANGE)
                .durable(true)
                .build();
    }

    /**
     * TTL迟队列
     *
     * @return
     */
    @Bean
    public Queue ttlQueue() {
        return QueueBuilder
                .durable(QueueConst.UWB_TTL_QUEUE)
                .withArgument("x-dead-letter-exchange", QueueConst.UWB_DLX_EXCHANGE)
                .withArgument("x-dead-letter-routing-key", QueueConst.UWB_DLX_QUEUE)
                .build();
    }

    /**
     * TTL绑定
     *
     * @param ttlQueue    用户队列名
     * @param ttlExchange 用户交换机名
     * @return
     */
    @Bean
    public Binding ttlBinding(Queue ttlQueue, Exchange ttlExchange) {
        return BindingBuilder
                .bind(ttlQueue)
                .to(ttlExchange)
                .with("*")
                .noargs();
    }

    @Bean
    Exchange dlxExchange() {
        return ExchangeBuilder
                .topicExchange(QueueConst.UWB_DLX_EXCHANGE)
                .durable(true)
                .build();
    }

    @Bean
    Queue dlxQueue() {
        return QueueBuilder
                .durable(QueueConst.UWB_DLX_QUEUE)
                .build();
    }

    @Bean
    Binding dlxBinding(Queue dlxQueue, Exchange dlxExchange) {
        return BindingBuilder
                .bind(dlxQueue)
                .to(dlxExchange)
                .with("*")
                .noargs();
    }
}
