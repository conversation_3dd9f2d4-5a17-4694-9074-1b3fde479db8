package com.whfc.emp.mqtt;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
public class MqttMessageProcessorFactory {

    @Resource(name = "mqttMessageFaceStdProcessor")
    private MqttMessageProcessor mqttMessageFaceStdProcessor;

    @Resource(name = "mqttMessageFaceSzycProcessor")
    private MqttMessageProcessor mqttMessageFaceSzycProcessor;

    @Resource(name = "mqttMessageFaceRtProcessor")
    private MqttMessageProcessor mqttMessageFaceRtProcessor;

    @Resource(name = "mqttMessageFtkProcessor")
    private MqttMessageProcessor mqttMessageFtkProcessor;

    /**
     * 获取所有消息处理类
     *
     * @return
     */
    public List<MqttMessageProcessor> getAllMqttMessageProcessors() {
        List<MqttMessageProcessor> processorList = new ArrayList<>();
        processorList.add(mqttMessageFaceStdProcessor);
        processorList.add(mqttMessageFaceSzycProcessor);
        processorList.add(mqttMessageFaceRtProcessor);
        processorList.add(mqttMessageFtkProcessor);
        return processorList;
    }

    /**
     * 根据topic获取对应的消息处理类
     *
     * @param topic
     * @return
     */
    public MqttMessageProcessor getMqttMessageProcessor(String topic) {
        if (StringUtils.isNotBlank(topic)) {
            List<MqttMessageProcessor> processorList = getAllMqttMessageProcessors();
            for (MqttMessageProcessor processor : processorList) {
                if (topic.startsWith(processor.getTopicPrefix())) {
                    return processor;
                }
            }
        }
        return null;
    }

    /**
     * 获取所有订阅的topic
     *
     * @return
     */
    public List<String> getAllSubTopicList() {
        List<String> topicList = new ArrayList<>();
        List<MqttMessageProcessor> processorList = getAllMqttMessageProcessors();
        for (MqttMessageProcessor processor : processorList) {
            topicList.addAll(processor.getSubTopicList());
        }
        return topicList;
    }
}
