package com.whfc.emp.event;

import com.whfc.emp.param.FaceGateRecordParam;
import com.whfc.emp.third.EmpThirdSyncFactory;
import com.whfc.emp.third.EmpThirdSyncWapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 人脸识别-数据同步
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023-06-30 18:08
 */
@Component
public class FaceGateDataEventSyncListener implements ApplicationListener<FaceGateDataEvent> {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private EmpThirdSyncFactory empThirdSyncFactory;

    @Override
    public void onApplicationEvent(FaceGateDataEvent faceGateDataEvent) {
        FaceGateRecordParam param = faceGateDataEvent.getParam();
        if (param == null || param.getPersonGuid().equals("-1")) {
            return;
        }
        try {
            List<EmpThirdSyncWapper> wapperList = empThirdSyncFactory.sync(param.getDeptId());
            for (EmpThirdSyncWapper wapper : wapperList) {
                wapper.getSync().syncEmpAttend(param, wapper.getConfig());
            }
        } catch (Exception e) {
            logger.error("第三方人脸数据同步异常", e);
        }
    }
}
