package com.whfc.emp.dao;

import com.whfc.emp.dto.AppEmpAnaWeekDataDTO;
import com.whfc.emp.dto.AppEmpDTO;
import com.whfc.emp.entity.AppEmpEnterRecord;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;

public interface AppEmpEnterRecordMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppEmpEnterRecord record);

    int insertSelective(AppEmpEnterRecord record);

    AppEmpEnterRecord selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppEmpEnterRecord record);

    int updateByPrimaryKey(AppEmpEnterRecord record);

    /**
     * 统计企业-一段时间内的进退场人数
     *
     * @param deptIds
     * @param startTime
     * @param endTime
     * @return
     */
    List<AppEmpAnaWeekDataDTO> selectEnterpriseEnterRecordStat(@Param("deptIds") Collection<Integer> deptIds,
                                                               @Param("startTime") Date startTime,
                                                               @Param("endTime") Date endTime);

    /**
     * 统计项目一段时间内每天的进退场人数
     *
     * @param deptId
     * @param startTime
     * @param endTime
     * @return
     */
    List<AppEmpAnaWeekDataDTO> selectDayStatStat(@Param("deptId") Integer deptId,
                                                 @Param("startTime") Date startTime,
                                                 @Param("endTime") Date endTime);

    /**
     * 统计项目一段时间内总的进退场人数
     *
     * @param deptId
     * @param startTime
     * @param endTime
     * @return
     */
    AppEmpAnaWeekDataDTO selectTotalStat(@Param("deptId") Integer deptId,
                                         @Param("startTime") Date startTime,
                                         @Param("endTime") Date endTime);

    /**
     * 人员日志
     *
     * @param deptId
     * @param date
     * @return
     */
    List<AppEmpDTO> selectEmpLog(@Param("deptId") Integer deptId, @Param("date") Date date);
}