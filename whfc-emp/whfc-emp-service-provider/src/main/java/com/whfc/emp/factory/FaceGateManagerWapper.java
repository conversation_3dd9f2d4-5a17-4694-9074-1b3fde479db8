package com.whfc.emp.factory;

import com.whfc.emp.entity.AppFaceGateConfig;
import com.whfc.emp.param.*;

/**
 * @Description: 闸机服务和配置
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/7/18 10:06
 */
public class FaceGateManagerWapper {

    /**
     * 闸机平台服务
     */
    private FaceGateManager manager;

    /**
     * 闸机配置
     */
    private AppFaceGateConfig config;

    public FaceGateManagerWapper(FaceGateManager manager, AppFaceGateConfig config) {
        this.manager = manager;
        this.config = config;
    }

    public FaceGateManager getManager() {
        return manager;
    }

    public void setManager(FaceGateManager manager) {
        this.manager = manager;
    }

    public AppFaceGateConfig getConfig() {
        return config;
    }

    public void setConfig(AppFaceGateConfig config) {
        this.config = config;
    }

    /**
     * 闸机-添加
     *
     * @param request
     */
    public void add(AppFaceGateAddParam request) {
        manager.add(request, config);
    }

    /**
     * 闸机-删除
     */
    public void del(Integer faceGateId) {
        manager.del(faceGateId, config);
    }

    /**
     * 闸机-人员注册
     *
     * @param request
     */
    public String faceGateGrantEmdAdd(FaceGateGrantEmdAddParam request) {
        return manager.faceGateGrantEmdAdd(request, config);
    }

    /**
     * 闸机-人员照片注册
     *
     * @param request
     */
    public String faceGateGrantEmdImgAdd(FaceGateGrantEmdImgAddParam request) {
        return manager.faceGateGrantEmdImgAdd(request, config);
    }

    /**
     * 闸机-人员授权
     *
     * @param request
     */
    public String faceGateGrantEmdAuth(FaceGateGrantEmdAuthParam request) {
        return manager.faceGateGrantEmdAuth(request, config);
    }

    /**
     * 闸机-人员批量授权(批量)
     *
     * @param request
     */
    public void faceGateBatchGrantEmpAuth(AppFaceGateGrantEmpParam request) {
        manager.faceGateBatchGrantEmpAuth(request, config);
    }

    /**
     * 闸机-人员取消授权(批量)
     */
    public void faceGateRevokeEmp(AppFaceGateGrantEmpParam request) {
        manager.faceGateRevokeEmp(request, config);
    }

    /**
     * 闸机-人员授权(访客)
     *
     * @param deviceKey
     * @param name
     * @param imgUrl
     * @return
     */
    public String deviceAuthorizationPerson(String deviceKey, String name, String imgUrl) {
        return manager.deviceAuthorizationPerson(deviceKey, name, imgUrl, config);
    }

    /**
     * 闸机-人员取消授权(访客)
     *
     * @param deviceKey
     * @param personGuid
     */
    public String deviceAuthorizationCancel(String deviceKey, String personGuid) {
        return manager.deviceAuthorizationCancel(deviceKey, personGuid, config);
    }

    /**
     * 闸机-开门
     *
     * @param deviceKey
     */
    public void openDoor(String deviceKey) {
        manager.openDoor(deviceKey, config);
    }

    /**
     * 同步闸机授权人员
     *
     * @param faceGateId
     */
    public void sync(Integer faceGateId) {
        manager.sync(faceGateId, config);
    }

    /**
     * 获取token
     *
     * @return
     */
    public String getToken() {
        return manager.getToken(config);
    }
}
