package com.whfc.emp.queue;

import com.alibaba.fastjson.JSON;
import com.whfc.common.constant.QueueConst;
import com.whfc.emp.manager.AppFaceGateManager;
import com.whfc.emp.param.EmpInfoSyncDataParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-08-05 17:33
 */
@Component
@RabbitListener(queuesToDeclare = {@Queue(name = QueueConst.EMP_FACEGATE_EMP_INFO)}, concurrency = "1-2")
public class FaceGateEmpInfoSyncQueueMessageListener {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private AppFaceGateManager appFaceGateManager;

    @RabbitHandler
    public void process(String msg) {
        try {
            logger.debug("faceGateEmpInfoSync|msg|{}", msg);
            EmpInfoSyncDataParam dataParam = JSON.parseObject(msg, EmpInfoSyncDataParam.class);
            logger.info("faceGateEmpInfoSync|msg|{}", dataParam);
            appFaceGateManager.handleEmpInfo(dataParam);
        } catch (Exception ex) {
            logger.error("faceGateEmpInfoSync|msg,消息处理失败", ex);
        }
    }


}
