package com.whfc.emp.job;

import com.whfc.XxlJobConfig;
import com.whfc.emp.manager.TrainBoxManager;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Component;

/**
 * 人员培训定时任务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-08-30 15:15
 */
@Component
@ConditionalOnBean(XxlJobConfig.class)
public class EmpTrainBoxJob {


    @Autowired
    private TrainBoxManager trainBoxManager;

    /**
     * 更新培训箱错误记录，每2小时同步一次
     */
    @XxlJob("handleFailedData")
    public void handleFailedData() {
        XxlJobHelper.log("开始更新培训箱错误记录");
        trainBoxManager.handleFailedData();
    }


}
