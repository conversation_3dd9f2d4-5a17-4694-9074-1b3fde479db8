package com.whfc.emp.dao;

import com.whfc.emp.dto.AppEmpConfessDTO;
import com.whfc.emp.entity.AppEmpConfess;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-09-02 15:59
 */
@Repository
public interface AppEmpConfessMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppEmpConfess record);

    int insertSelective(AppEmpConfess record);

    AppEmpConfess selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppEmpConfess record);

    int updateByPrimaryKey(AppEmpConfess record);

    /**
     * 根据人员ID查找安全交底
     *
     * @param empId 人员ID
     * @return 安全交底
     */
    List<AppEmpConfessDTO> selectByEmpId(@Param("empId") Integer empId);

    /**
     * 删除安全交底
     *
     * @param confessId 安全交底ID
     */
    void logicDel(@Param("confessId") Integer confessId);

    /**
     * 更新签名图片
     *
     * @param confessId 安全交底ID
     * @param imgUrl 图片地址
     */
    void updateSignImg(@Param("confessId") Integer confessId, @Param("imgUrl") String imgUrl);
}