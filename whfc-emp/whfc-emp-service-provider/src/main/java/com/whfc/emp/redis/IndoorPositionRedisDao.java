package com.whfc.emp.redis;

import com.whfc.emp.entity.AppIndoorPositionMap;
import com.whfc.emp.entity.AppIndoorPositionStation;
import com.whfc.emp.entity.AppIndoorPositionTag;

/**
 * 室内定位缓存接口
 */
public interface IndoorPositionRedisDao {

    /**
     * 设置地图缓存
     *
     * @param guid
     * @param map
     */
    void setMap(String guid, AppIndoorPositionMap map);

    /**
     * 查询地图缓存
     *
     * @param guid
     * @return
     */
    AppIndoorPositionMap getMap(String guid);

    /**
     * 删除地图缓存
     *
     * @param guid
     */
    void delMap(String guid);

    /**
     * 设置基站缓存
     *
     * @param guid
     * @param station
     */
    void setStation(String guid, AppIndoorPositionStation station);

    /**
     * 查询基站缓存
     *
     * @param guid
     * @return
     */
    AppIndoorPositionStation getStation(String guid);

    /**
     * 删除基站缓存
     *
     * @param guid
     */
    void delStation(String guid);

    /**
     * 设置标签缓存
     *
     * @param deptId
     * @param guid
     * @param tag
     */
    void setTag(Integer deptId, String guid, AppIndoorPositionTag tag);

    /**
     * 获取标签缓存
     *
     * @param deptId
     * @param guid
     * @return
     */
    AppIndoorPositionTag getTag(Integer deptId, String guid);

    /**
     * 删除标签缓存
     *
     * @param deptId
     * @param guid
     */
    void delTag(Integer deptId, String guid);
}
