package com.whfc.emp.service.impl;


import cn.hutool.core.util.NumberUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.whfc.common.entity.DefaultProperties;
import com.whfc.common.enums.*;
import com.whfc.common.exception.BizException;
import com.whfc.common.file.FileHandler;
import com.whfc.common.file.FilePathConfig;
import com.whfc.common.file.properties.FileExpirationRules;
import com.whfc.common.geometry.Point;
import com.whfc.common.idcard.AdvancedInfo;
import com.whfc.common.idcard.IdentifyDTO;
import com.whfc.common.idcard.IdentifyUtil;
import com.whfc.common.qrcode.QRCodeUtil;
import com.whfc.common.result.PageData;
import com.whfc.common.result.ResultEnum;
import com.whfc.common.util.*;
import com.whfc.emp.dao.*;
import com.whfc.emp.dto.*;
import com.whfc.emp.entity.*;
import com.whfc.emp.enums.*;
import com.whfc.emp.enums.NetState;
import com.whfc.emp.manager.AppEmpDataManager;
import com.whfc.emp.manager.CommonEmpConfigManager;
import com.whfc.emp.param.*;
import com.whfc.emp.redis.IdCardIdentifyRedisDao;
import com.whfc.emp.service.AppEmpService;
import com.whfc.emp.third.EmpThirdSyncFactory;
import com.whfc.emp.third.EmpThirdSyncWapper;
import com.whfc.emp.third.EmpThirdVerifyFactory;
import com.whfc.emp.third.EmpThirdVerifyWapper;
import com.whfc.entity.dto.OssPathDTO;
import com.whfc.fuum.dto.AppCorpDTO;
import com.whfc.fuum.dto.AppProjectDTO;
import com.whfc.fuum.dto.SysDictDTO;
import com.whfc.fuum.service.AppCorpService;
import com.whfc.fuum.service.AppProjectService;
import com.whfc.fuum.service.SysDeptService;
import com.whfc.fuum.service.SysDictService;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClasssName AppEmpServiceImpl
 * @Description 人员管理
 * <AUTHOR>
 * @Date 2020/12/28 10:08
 * @Version 1.0
 */
@DubboService(interfaceClass = AppEmpService.class, version = "1.0.0", timeout = 60 * 1000)
public class AppEmpServiceImpl implements AppEmpService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @DubboReference(interfaceClass = AppProjectService.class, version = "1.0.0")
    private AppProjectService appProjectService;

    @DubboReference(interfaceClass = SysDeptService.class, version = "1.0.0")
    private SysDeptService sysDeptService;

    @DubboReference(interfaceClass = AppCorpService.class, version = "1.0.0")
    private AppCorpService appCorpService;

    @DubboReference(interfaceClass = SysDictService.class, version = "1.0.0")
    private SysDictService sysDictService;

    @Autowired
    private AppEmpMapper appEmpMapper;

    @Autowired
    private AppEmpGroupMapper appEmpGroupMapper;

    @Autowired
    private AppEmpWorkTypeMapper appEmpWorkTypeMapper;

    @Autowired
    private AppEmpDataMapper appEmpDataMapper;

    @Autowired
    private AppEmpEnterRecordMapper appEmpEnterRecordMapper;

    @Autowired
    private AppEmpBankMapper appEmpBankMapper;

    @Autowired
    private AppEmpBlackListMapper appEmpBlackListMapper;

    @Autowired
    private AppEmpDeviceMapper appEmpDeviceMapper;

    @Autowired
    private CommonEmpConfigManager commonEmpConfigManager;

    @Autowired
    private AppEmpDataManager appEmpDataManager;

    @Autowired
    private AppEmpAttendRecordMapper appEmpAttendRecordMapper;

    @Autowired
    private AppEmpDayMapper appEmpDayMapper;

    @Autowired
    private AppEmpApplyMapper appEmpApplyMapper;

    @Autowired
    private DefaultProperties defaultProperties;

    @Autowired
    private IdCardIdentifyRedisDao idCardIdentifyRedisDao;

    @Autowired
    private FilePathConfig filePathConfig;

    @Autowired
    private FileHandler fileHandler;

    @Autowired
    private EmpThirdVerifyFactory empThirdVerifyFactory;

    @Autowired
    private EmpThirdSyncFactory empThirdSyncFactory;

    @Autowired
    private AppFaceGatePersonMapper appFaceGatePersonMapper;

    @Override
    public AppEmpNumDTO empNum(AppEmpListParam request) throws BizException {
        return appEmpMapper.selectEmpNumByParam(request);
    }

    @Override
    public PageData<AppEmpDTO> list(AppEmpListParam request) {
        logger.info("查询人员列表,request：{}", request);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        List<AppEmpDTO> list = appEmpMapper.selectByParam(request);
        PageHelper.clearPage();
        return PageUtil.pageData(PageInfo.of(list));
    }

    @Override
    public List<AppEmpDTO> list(Integer deptId, String keyword) {
        logger.info("人员列表（不分页）,deptId：{}，keyword:{}", deptId, keyword);
        return appEmpMapper.selectByDeptId(deptId, keyword);
    }

    @Override
    public List<AppEmpDTO> list(Integer deptId) throws BizException {
        return appEmpMapper.selectEmpListByDeptId(deptId);
    }

    @Override
    public OssPathDTO export(AppEmpListExportParam param) {
        logger.info("导出人员列表：param：{}", param);
        String deptName = sysDeptService.getDeptName(param.getDeptId());
        OssPathDTO ossPathDTO = new OssPathDTO();
        try {
            AppEmpListParam request = new AppEmpListParam();
            BeanUtils.copyProperties(param, request);
            List<AppEmpDTO> list = appEmpMapper.selectByParam(request);
            for (AppEmpDTO emp : list) {
                emp.setAge(emp.getBirthday() != null ? DateUtil.getAge(emp.getBirthday()) : null);
                emp.setGenderName(Gender.parseValue(emp.getGender()));
                emp.setPostStateName(PostState.parseValue(emp.getPostState()).getDesc());
                emp.setOuterTime(PostState.ENTER.getValue().equals(emp.getPostState()) ? null : emp.getOuterTime());
            }

            // 写数据
            File destFile = File.createTempFile(RandomUtil.getRandomFileName(), ".xls");
            ClassPathResource resource = new ClassPathResource("templates/empListTemplate.xls");
            ExcelWriter excelWriter = EasyExcel.write(destFile).withTemplate(resource.getInputStream()).build();
            WriteSheet writeSheet = EasyExcel.writerSheet().build();

            Map<String, Object> map = new HashMap<>(3);
            map.put("deptName", deptName);
            excelWriter.fill(map, writeSheet);

            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            excelWriter.fill(list, fillConfig, writeSheet);
            excelWriter.finish();

            // 上传oss
            String filepath = filePathConfig.getFilePath(String.format("emp/temp/%s人员列表.xls", deptName));
            String upload = fileHandler.upload(filepath, new FileInputStream(destFile), FileExpirationRules.DAYS_1);
            ossPathDTO.setPath(upload);
        } catch (Exception e) {
            logger.error("导出人员列表失败", e);
        }
        return ossPathDTO;
    }

    @Override
    public AppEmpDTO detail(Integer empId) {
        logger.info("查看人员详情,empId:{}", empId);
        AppEmp appEmp = appEmpMapper.selectByPrimaryKey(empId);
        if (appEmp == null) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_001.getCode());
        }
        AppEmpDTO appEmpDTO = new AppEmpDTO();
        BeanUtils.copyProperties(appEmp, appEmpDTO);
        Integer workTypeId = appEmp.getWorkTypeId();
        // 获取工种
        AppEmpWorkType appEmpWorkType = appEmpWorkTypeMapper.selectByPrimaryKey(workTypeId);
        if (appEmpWorkType != null) {
            appEmpDTO.setWorkTypeName(appEmpWorkType.getName());
        }
        // 获取人员班组
        AppEmpGroup appEmpGroup = appEmpGroupMapper.selectByPrimaryKey(appEmp.getGroupId());
        if (appEmpGroup != null) {
            appEmpDTO.setGroupName(appEmpGroup.getGroupName());
        }
        // 设置三级教育状态
        appEmpDTO.setPassFlag(appEmp.getEnterTrainFlag());
        appEmpDTO.setEmpId(empId);
        return appEmpDTO;
    }

    @Override
    public AppEmpDTO getEmpInfo(Integer empId) {
        logger.info("获取人员信息,empId:{}", empId);
        AppEmpDTO empDTO = appEmpMapper.selectEmpDTOById(empId);
        // 计算年龄
        Date birthday = empDTO.getBirthday();
        if (birthday != null) {
            int age = DateUtil.getAge(birthday);
            empDTO.setAge(age);
        }
        return empDTO;
    }

    @Override
    public OssPathDTO getEmpQr(Integer empId) {
        logger.info("生成人员二维码,empId：{}", empId);
        AppEmpDTO empDTO = getEmpInfo(empId);
        if (empDTO == null) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_001.getCode());
        }
        String qr = empDTO.getQr();
        OssPathDTO ossPathDTO = new OssPathDTO();
        if (!StringUtils.isBlank(qr)) {
            ossPathDTO.setPath(qr);
            return ossPathDTO;
        }
        try {
            ClassPathResource resource = new ClassPathResource("log.jpg");
            String empName = empDTO.getEmpName();
            String fileName = "fcwl" + empId;
            File tempFile = File.createTempFile(fileName, ".jpg");
            String empInfoUrl = defaultProperties.getHost() + "/ms/pages/emp.html?empId=" + empId;
            QRCodeUtil.encode(empInfoUrl, empName, resource.getInputStream(), tempFile.getAbsolutePath());

            // 上传oss
            FileInputStream inputStream = new FileInputStream(tempFile);
            Map<String, String> headers = new HashMap<>();
            headers.put("cache-control", "no-cache");
            String path = filePathConfig.getFilePath("emp/qr", fileName, "jpg");
            String upload = fileHandler.upload(path, inputStream, headers);
            // 更新人员二维码信息
            appEmpMapper.updateQrByEmpId(empId, upload);
            ossPathDTO.setPath(upload);
        } catch (Exception e) {
            logger.info("生成人员二维码失败，", e);
        }

        return ossPathDTO;

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(AppEmpAddParam request) {
        logger.info("人员管理-添加人员,request:{}", request);
        Date birthday = request.getBirthday();
        String idCardNo = request.getIdCardNo();
        Integer projectId = request.getProjectId();

        // 提取出生日期
        if (StringUtils.isNotEmpty(idCardNo)) {
            birthday = IDCardUtil.getBirth(idCardNo);
            request.setBirthday(birthday);
        }

        // 年龄验证
        if (birthday != null) {
            int age = DateUtil.getAge(birthday);
            Integer gender = request.getGender();
            this.checkAge(projectId, age, request.getGender());
        }

        // 添加人员
        addEmp(request);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(AppEmpEditParam request) {
        logger.info("人员管理-编辑人员，request:{}", request);
        Integer projectId = request.getProjectId();
        Integer deptId = request.getProjectId();
        Integer empId = request.getEmpId();
        String idCardNo = request.getIdCardNo();
        String empCode = request.getEmpCode();
        String phone = request.getPhone();
        Integer groupId = request.getGroupId();

        // 根据班组id查找合作单位
        AppEmpGroup appEmpGroup = appEmpGroupMapper.selectByPrimaryKey(groupId);
        if (appEmpGroup == null) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.SYS_BE_002.getCode());
        }
        String groupName = appEmpGroup.getGroupName();

        // 提取出生日期
        if (!StringUtils.isBlank(idCardNo)) {
            Date birthday = IDCardUtil.getBirth(idCardNo);
            request.setBirthday(birthday);
        }

        // 验证人员编号
        if (StringUtils.isNotEmpty(empCode)) {
            AppEmp emp = appEmpMapper.selectByDeptIdAndEmpCode(deptId, empCode);
            if (emp != null && !emp.getId().equals(empId)) {
                throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_006.getCode());
            }
        }

        // 验证人员证件号
        if (StringUtils.isNotEmpty(idCardNo)) {
            AppEmp emp = appEmpMapper.selectByDeptIdAndIdCardNo(deptId, idCardNo);
            if (emp != null && !emp.getId().equals(empId)) {
                throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_007.getCode());
            }
        }

        // 验收人员手机号
        if (StringUtils.isNotEmpty(phone)) {
            AppEmp emp = appEmpMapper.selectByDeptIdAndPhone(deptId, phone);
            if (emp != null && !emp.getId().equals(empId)) {
                throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_007.getCode());
            }
        }

        // 更新人员数据
        AppEmp appEmp = new AppEmp();
        BeanUtils.copyProperties(request, appEmp);
        appEmp.setId(empId);
        appEmp.setDeptId(deptId);
        appEmp.setGroupId(groupId);
        appEmp.setGroupName(groupName);
        appEmp.setCorpId(appEmpGroup.getCorpId());
        appEmp.setCorpName(appEmpGroup.getCorpName());
        appEmp.setEname(PinyinUtil.toPinyin(request.getEmpName()));
        appEmp.setQr(null);
        appEmp.setKeyPositionFlag(request.getKeyPositionFlag());
        appEmp.setKeyPositionAuth(request.getKeyPositionAuth());
        appEmpMapper.updateByPrimaryKeySelective(appEmp);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void del(Integer empId) {
        logger.info("人员管理-删除人员,empId:{}", empId);
        AppEmp appEmp = appEmpMapper.selectByPrimaryKey(empId);
        if (appEmp != null && PostState.ENTER.getValue().equals(appEmp.getPostState())) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_016.getCode());
        }

        appEmpMapper.delLogicByEmpId(empId);

        // 删除银行卡信息
        appEmpBankMapper.deleteLogicByEmpId(empId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enter(AppEmpEnterParam request) {
        logger.info("人员管理-人员进场：request：{}", request);
        Integer empId = request.getEmpId();
        AppEmp appEmp = appEmpMapper.selectByPrimaryKey(empId);
        if (appEmp == null) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_001.getCode());
        }
        this.checkBlackList(appEmp);

        appEmpMapper.updatePostState(empId, PostState.ENTER.getValue(), request.getDate());

        // 向人员进出场记录表中插入数据
        AppEmpEnterRecord empEnterRecord = new AppEmpEnterRecord();
        empEnterRecord.setCorpId(appEmp.getCorpId());
        empEnterRecord.setDate(request.getDate());
        empEnterRecord.setDeptId(appEmp.getDeptId());
        empEnterRecord.setEmpId(empId);
        empEnterRecord.setType(PostState.ENTER.getValue());
        appEmpEnterRecordMapper.insertSelective(empEnterRecord);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void outer(AppEmpOuterParam request) {
        logger.info("人员管理-人员退场:request:{}", request);
        Integer empId = request.getEmpId();

        Integer count = appEmpDeviceMapper.countByEmpId(empId);
        if (count > 0) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_017.getCode());
        }
        Integer count1 = appFaceGatePersonMapper.countByEmpId(empId);
        if (count1 > 0) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_017.getCode());
        }

        appEmpMapper.updatePostState(empId, PostState.OUTER.getValue(), request.getDate());

        // 向人员进出场记录表中插入数据
        AppEmp appEmp = appEmpMapper.selectByPrimaryKey(empId);
        AppEmpEnterRecord empEnterRecord = new AppEmpEnterRecord();
        empEnterRecord.setCorpId(appEmp.getCorpId());
        empEnterRecord.setDate(request.getDate());
        empEnterRecord.setDeptId(appEmp.getDeptId());
        empEnterRecord.setType(PostState.OUTER.getValue());
        empEnterRecord.setEmpId(empId);
        appEmpEnterRecordMapper.insertSelective(empEnterRecord);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void outer(List<Integer> empIds, Date date) throws BizException {
        logger.info("人员管理,批量离职,{},{}", empIds, date);

        // 批量修改敢为状态
        appEmpMapper.updatePostStateByEmpIds(empIds, PostState.OUTER.getValue(), date);

        // 插入进出场离职记录
        for (Integer empId : empIds) {
            AppEmp appEmp = appEmpMapper.selectByPrimaryKey(empId);
            AppEmpEnterRecord empEnterRecord = new AppEmpEnterRecord();
            empEnterRecord.setCorpId(appEmp.getCorpId());
            empEnterRecord.setDate(date);
            empEnterRecord.setDeptId(appEmp.getDeptId());
            empEnterRecord.setType(PostState.OUTER.getValue());
            empEnterRecord.setEmpId(empId);
            appEmpEnterRecordMapper.insertSelective(empEnterRecord);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bind(String userName, Integer userId, AppEmpDeviceBindParam request) {
        logger.info("人员管理-人员绑定安全帽：userName：{}，userId：{}，request：{}", userId, userId, request);
        // todo  小程序绑定解绑暂时取消
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unbind(String userName, Integer userId, AppEmpDeviceUnbindParam request) {
        logger.info("人员管理-人员解除绑定安全帽：userName：{}，userId：{}，request：{}", userId, userId, request);
        // todo  小程序绑定解绑暂时取消
    }

    @Override
    public List<AppEmpGpsDTO> getGpsList(Integer empId, Date date) {
        logger.info("查询人员轨迹,empId:{},date:{}", empId, date);
        Date startTime = DateUtil.getDateBegin(date);
        Date endTime = DateUtil.getDateEnd(date);
        List<AppDeviceCardLog> logList = appEmpDataManager.getHelmetDataLogByEmpId(empId, date, startTime, endTime);
        return logList.stream().filter(log -> PositionUtil.isValid(log.getLng(), log.getLat())).map(this::toGps).collect(Collectors.toList());
    }


    @Override
    public List<WxEmpGroupDTO> getEmpList(Integer deptId, String keyword) throws BizException {
        // 查询组织机构下全部班组
        List<WxEmpGroupDTO> groupDTOS = appEmpGroupMapper.selectGroupNameByDeptId(deptId);
        groupDTOS.add(new WxEmpGroupDTO(0, "未分组"));
        // 根据条件查询全部人员
        List<AppEmpDTO> empDTOList = appEmpMapper.selectEmpByDeptIdAndKeyword(deptId, keyword);
        // 业务数据处理
        groupDTOS.forEach(wxEmpGroupDTO -> {
            Integer groupId = wxEmpGroupDTO.getGroupId();
            Integer onlineNum = 0;
            Integer enterNum = 0;
            Integer attendNum = 0;
            Integer localeNum = 0;
            Integer outLocaleNum = 0;
            Integer absentNum = 0;
            Integer bindNum = 0;
            Integer unbindNum = 0;

            List<AppEmpDTO> list = new ArrayList<>();
            for (AppEmpDTO appEmpDTO : empDTOList) {

                if (groupId.equals(appEmpDTO.getGroupId())) {
                    // 人员图标处理
                    HelmetIconDTO helmetIcon = appEmpDataManager.getHelmetIcon(appEmpDTO.getColor());
                    BeanUtils.copyProperties(helmetIcon, appEmpDTO);
                    // 在线人数
                    if (NetState.ONLINE.getValue().equals(appEmpDTO.getNetState())) {
                        onlineNum++;
                    }
                    // 在岗人数
                    enterNum++;
                    // 出勤人数
                    if (AttendState.ATTEND.getValue().equals(appEmpDTO.getAttendState())) {
                        attendNum++;
                        // 在场人数
                        if (LocaleState.IN.getValue().equals(appEmpDTO.getLocaleState())) {
                            localeNum++;
                        }
                        // 离场人数
                        if (LocaleState.OUT.getValue().equals(appEmpDTO.getLocaleState())) {
                            outLocaleNum++;
                        }
                    }
                    // 缺勤人数
                    if (AttendState.ABSENCE.getValue().equals(appEmpDTO.getAttendState())) {
                        absentNum++;
                    }
                    // 绑定人数
                    if (BindFlag.BIND.getValue().equals(appEmpDTO.getBindFlag())) {
                        bindNum++;
                    }
                    // 未绑定人数
                    if (BindFlag.UNBIND.getValue().equals(appEmpDTO.getBindFlag())) {
                        unbindNum++;
                    }

                    list.add(appEmpDTO);
                }
            }

            wxEmpGroupDTO.setOnlineNum(onlineNum);
            wxEmpGroupDTO.setEnterNum(enterNum);
            wxEmpGroupDTO.setAttendNum(attendNum);
            wxEmpGroupDTO.setLocaleNum(localeNum);
            wxEmpGroupDTO.setOutLocaleNum(outLocaleNum);
            wxEmpGroupDTO.setAbsentNum(absentNum);
            wxEmpGroupDTO.setBindNum(bindNum);
            wxEmpGroupDTO.setUnbindNum(unbindNum);
            wxEmpGroupDTO.setDataList(list);
        });

        return groupDTOS;
    }

    @Override
    public WxMapEmpDTO getMapList(Integer deptId) throws BizException {

        // 查询组织机构下全部班组
        List<WxEmpGroupDTO> groupDTOS = appEmpGroupMapper.selectGroupNameByDeptId(deptId);
        groupDTOS.add(new WxEmpGroupDTO(0, "未分组"));
        // 根据条件查询全部人员
        List<AppEmpDTO> empDTOList = appEmpMapper.selectEmpByDeptIdAndKeyword(deptId, null);

        // 安全帽离线或关机时长
        AppEmpSettingDTO data = commonEmpConfigManager.getMinutes(deptId);
        Date lastTime = DateUtil.addMinutes(new Date(), -data.getMinutes());

        // 坐标有效区域
        String polygonWKT = commonEmpConfigManager.getPolygonWKT(deptId);

        // 业务数据处理
        groupDTOS.forEach(wxEmpGroupDTO -> {
            Integer groupId = wxEmpGroupDTO.getGroupId();
            Integer onlineNum = 0;

            List<AppEmpDTO> list = new ArrayList<>();
            for (AppEmpDTO empDTO : empDTOList) {
                Double lat = empDTO.getLat();
                Double lng = empDTO.getLng();
                Date gpsTime = empDTO.getTime();

                if (gpsTime != null && gpsTime.after(lastTime) && groupId.equals(empDTO.getGroupId()) && PositionUtil.contains(FenceType.POLYGON.value(), polygonWKT, null, lat, lng)) {
                    // 人员图标处理
                    HelmetIconDTO helmetIcon = appEmpDataManager.getHelmetIcon(empDTO.getColor());
                    BeanUtils.copyProperties(helmetIcon, empDTO);
                    // 在线人数
                    onlineNum++;
                    list.add(empDTO);
                }
            }

            wxEmpGroupDTO.setDataList(list);
            wxEmpGroupDTO.setOnlineNum(onlineNum);
        });

        WxMapEmpDTO wxMapEmpDTO = new WxMapEmpDTO();
        // 查找项目坐标
        AppProjectDTO projectDTO = appProjectService.getByDeptId(deptId);
        if (projectDTO != null) {
            Point Point = new Point();
            Point.setLat(projectDTO.getLat());
            Point.setLng(projectDTO.getLng());
            wxMapEmpDTO.setPoint(Point);
        }

        groupDTOS.sort((x, y) -> y.getOnlineNum() - x.getOnlineNum());
        wxMapEmpDTO.setList(groupDTOS);
        return wxMapEmpDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bindAndAddEmp(Integer userId, String userName, DeviceBindEmp request) throws BizException {
        logger.info("人员和硬件解绑,userId:{},userName:{},request:{}", userId, userName, request);
        // todo 小程序绑定解绑暂时取消
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unbindEmpDevice(Integer userId, String userName, Integer empId) {
        logger.info("人员和硬件解绑,userId:{},userName:{},empId:{}", userId, userName, empId);
        // todo 小程序绑定解绑暂时取消
    }

    @Override
    public DeviceBindCheckDTO bindCheck(String sn) {
        // todo 小程序绑定解绑暂时取消
        return new DeviceBindCheckDTO();
    }

    @Override
    public WxEmpWorkHourDTO getEmpWorkHour(Integer empId, Date date) {
        double workTimes = 0D;
        List<WxEmpLocaleStateDTO> localeStateArr = new ArrayList<>();
        Integer attendState = 0;
        Integer localeState = 0;
        AppEmpDay empDay = appEmpDayMapper.selectByEmpIdAndDate(empId, date);
        if (empDay != null) {
            workTimes = empDay.getWorkTimes();
            attendState = empDay.getAttendState();
        }
        // 查找考勤方式
        AttendType attendType = commonEmpConfigManager.getAttendTypeByEmpId(empId);
        Date startTime = DateUtil.getDateBegin(date);
        Date endTime = DateUtil.getDateEnd(date);
        List<AppEmpAttendRecordDTO> attendRecordDTOS = appEmpAttendRecordMapper.selectByEmpIdAndDate(empId, startTime, endTime, null, attendType.getValue());
        for (AppEmpAttendRecordDTO empAttendRecordDTO : attendRecordDTOS) {
            Date time = empAttendRecordDTO.getTime();
            Integer state = empAttendRecordDTO.getState();
            String name = empAttendRecordDTO.getName();
            WxEmpLocaleStateDTO empLocaleStateDTO = new WxEmpLocaleStateDTO();
            empLocaleStateDTO.setLocaleState(state);
            empLocaleStateDTO.setTime(time);
            empLocaleStateDTO.setName(name);
            localeStateArr.add(empLocaleStateDTO);
        }

        AppEmpData appEmpData = appEmpDataMapper.selectByEmpId(empId);
        if (appEmpData != null) {
            localeState = appEmpData.getLocaleState();
        }

        WxEmpWorkHourDTO workHourDTO = new WxEmpWorkHourDTO();
        workHourDTO.setEmpId(empId);
        workHourDTO.setDate(date);
        workHourDTO.setWorkTimes(workTimes);
        workHourDTO.setLocaleStateArr(localeStateArr);
        workHourDTO.setAttendState(attendState);
        workHourDTO.setLocaleState(localeState);
        return workHourDTO;
    }

    @Override
    public AppEmpGpsDTO getLatestGps(Integer empId) {
        AppEmpGpsDTO appEmpGpsDTO = new AppEmpGpsDTO();
        AppEmpData appEmpData = appEmpDataMapper.selectByEmpId(empId);
        if (appEmpData != null) {
            appEmpGpsDTO.setLat(appEmpData.getLat());
            appEmpGpsDTO.setLng(appEmpData.getLng());
        }
        return appEmpGpsDTO;
    }

    @Override
    public WxEmpDataCenterDTO getEmpDataCenter(Integer deptId) {
        WxEmpDataCenterDTO wxEmpDataCenterDTO = new WxEmpDataCenterDTO();
        // 获取在岗人数
        int enterEmpNum = appEmpMapper.selectPostEmpNum(Collections.singletonList(deptId));
        wxEmpDataCenterDTO.setEnterEmpNum(enterEmpNum);
        // 获取过去一周的数据
        int day = 7;
        Date now = DateUtil.getDate(new Date());
        Date endDate = DateUtil.addDays(now, -1);
        Date startDate = DateUtil.addDays(now, -day);
        List<WxEmpCurveMapDayDTO> list = appEmpDayMapper.selectWxEmpCurveMapDayDTOListInDept(deptId, startDate, endDate);
        List<WxEmpDataCenterDayDTO> dayList = new ArrayList<>();
        double totalAttendTimes = 0.0D;
        for (WxEmpCurveMapDayDTO wxEmpCurveMapDayDTO : list) {
            WxEmpDataCenterDayDTO centerDayDTO = new WxEmpDataCenterDayDTO();
            Integer totalEmpNum = wxEmpCurveMapDayDTO.getTotalEmpNum();
            Integer attendEmpNum = wxEmpCurveMapDayDTO.getAttendEmpNum();
            Double workTimes = wxEmpCurveMapDayDTO.getWorkTimes();
            centerDayDTO.setDate(wxEmpCurveMapDayDTO.getDate());
            centerDayDTO.setAttendEmpNum(attendEmpNum);
            centerDayDTO.setAbsentEmpNum(totalEmpNum - attendEmpNum);
            double avgAttendTimes = 0.0D;
            if (totalEmpNum > 0 && workTimes > 0) {
                avgAttendTimes = MathUtil.round((workTimes / totalEmpNum), 2);
            }
            totalAttendTimes += avgAttendTimes;
            centerDayDTO.setAvgAttendTimes(avgAttendTimes);
            dayList.add(centerDayDTO);
        }
        wxEmpDataCenterDTO.setDayList(dayList);
        double avgTotalWorkTimes = 0.0D;
        if (totalAttendTimes > 0) {
            avgTotalWorkTimes = MathUtil.round((totalAttendTimes / day), 2);
        }
        wxEmpDataCenterDTO.setAvgDayWorkTimes(avgTotalWorkTimes);
        return wxEmpDataCenterDTO;
    }


    @Override
    public Integer countByWorkRole(Integer deptId, AppWorkRole appWorkRole) throws BizException {
        return appEmpMapper.countByWorkRole(deptId, appWorkRole.getValue());
    }

    @Override
    public Integer countLocaleEmpNum(Integer deptId) throws BizException {
        return appEmpMapper.countLocaleNum(deptId);
    }


    @Override
    public PageData<AppEmpApplyDTO> getApplyList(Integer deptId, Integer checkResult, Date startTime, Date endTime, Integer pageSize, Integer pageNum) throws BizException {
        logger.info("人员审批列表，depId:{},checkResult:{},startTime:{},endTime:{},pageSize:{},pageNum:{}", deptId, checkResult, startTime, endTime, pageSize, pageNum);
        PageHelper.startPage(pageNum, pageSize);
        if (endTime != null) {
            endTime = DateUtil.getDateEnd(endTime);
        }
        List<AppEmpApplyDTO> list = appEmpApplyMapper.selectByDeptId(deptId, checkResult, startTime, endTime);
        for (AppEmpApplyDTO dto : list) {
            Date birthday = dto.getBirthday();
            if (birthday != null) {
                Integer age = DateUtil.getAge(birthday);
                dto.setAge(age);
            }
        }
        return PageUtil.pageData(PageInfo.of(list));
    }

    @Override
    public AppEmpApplyNumDTO getApplyNum(Integer deptId, Date startTime, Date endTime) {
        if (endTime != null) {
            endTime = DateUtil.getDateEnd(endTime);
        }
        return appEmpApplyMapper.selectNumBy(deptId, startTime, endTime);
    }

    @Override
    public AppEmpApplyDTO getApplyDetail(Integer applyEmpId) throws BizException {
        logger.info("人员审批详情，applyEmpId:{}", applyEmpId);
        AppEmpApply appEmpApply = appEmpApplyMapper.selectByPrimaryKey(applyEmpId);
        AppEmpApplyDTO dto = new AppEmpApplyDTO();
        BeanUtils.copyProperties(appEmpApply, dto);
        Date birthday = appEmpApply.getBirthday();
        if (birthday != null) {
            Integer age = DateUtil.getAge(birthday);
            dto.setAge(age);
        }
        return dto;
    }

    @Override
    public void applyDel(Integer applyEmpId) throws BizException {
        logger.info("人员审批删除,applyEmpId:{}", applyEmpId);
//        AppEmpApply appEmpApply = appEmpApplyMapper.selectByPrimaryKey(applyEmpId);
//        if (VisitorState.success.getValue().equals(appEmpApply.getCheckResult())) {
//            throw new BizException(ResultEnum.FAILURE.getCode(), "已审核通过无法删除");
//        }
        appEmpApplyMapper.del(applyEmpId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void checkApply(AppEnpApplyCheckParam param) throws BizException {
        logger.info("人员审批，param:{}", param);
        Integer checkResult = param.getCheckResult();
        Integer empId = param.getEmpId();
        AppEmpApply appEmpApply = appEmpApplyMapper.selectByPrimaryKey(empId);
        if (!VisitorState.pending.getValue().equals(appEmpApply.getCheckResult())) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_013.getCode());
        }

        // 验证年龄
        Integer deptId = appEmpApply.getDeptId();
        Date birthday = appEmpApply.getBirthday();
        Integer gender = appEmpApply.getGender();
        int age = DateUtil.getAge(birthday);
        this.checkAge(deptId, age, gender);

        BeanUtils.copyProperties(param, appEmpApply);
        appEmpApply.setCheckName(param.getCheckName());
        appEmpApply.setCheckTime(new Date());
        appEmpApplyMapper.updateByPrimaryKeySelective(appEmpApply);
        if (VisitorState.success.getValue().equals(checkResult)) {
            AppEmpAddParam appEmpAddParam = new AppEmpAddParam();
            BeanUtils.copyProperties(appEmpApply, appEmpAddParam);
            appEmpAddParam.setDeptId(appEmpApply.getDeptId());
            appEmpAddParam.setProjectId(appEmpApply.getDeptId());
            addEmp(appEmpAddParam);
        }
    }

    @Override
    public void empApplyAdd(AppEmpAddParam param) throws BizException {
        logger.info("人员申请，param:{}", param);
        AppEmp appEmp = appEmpMapper.selectByDeptIdAndIdCardNo(param.getDeptId(), param.getIdCardNo());
        if (appEmp != null) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_047.getCode());
        }
        AppEmpApply appEmpApply = appEmpApplyMapper.selectByDeptIdAndIdCardNo(param.getDeptId(), param.getIdCardNo());
        if (appEmpApply != null) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_047.getCode());
        }
        appEmpApply = new AppEmpApply();
        BeanUtils.copyProperties(param, appEmpApply);
        appEmpApplyMapper.insertSelective(appEmpApply);
    }

    @Override
    public AppEmpIdentifyDTO idCardIdentify(String imgUrl, String sessionId) throws BizException {
        logger.info("人员身份证识别，idCardImgUrl:{}", imgUrl);
        Integer index = idCardIdentifyRedisDao.getIdCardIdentify(sessionId);
        if (index > 6) {
            logger.info("人员身份证识别,当前用户超过限制次数,{}", imgUrl);
            fileHandler.delete(imgUrl);
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_053.getCode());
        }
        AppEmpIdentifyDTO appEmpIdentifyDTO = new AppEmpIdentifyDTO();
        IdentifyDTO identifyDTO = IdentifyUtil.identify(imgUrl, null);
        if (identifyDTO != null) {
            AdvancedInfo advancedInfo = identifyDTO.getAdvancedInfo();
            BeanUtils.copyProperties(identifyDTO, appEmpIdentifyDTO);
            if (advancedInfo != null) {
//                String warnInfos = advancedInfo.getWarnInfos();
//                if (StringUtils.isNotEmpty(warnInfos)) {
//                    throw new BizException(ResultEnum.FAILURE.getCode(), warnInfos);
//                }
                String base64Str = advancedInfo.getPortrait();
                if (StringUtils.isNotEmpty(base64Str)) {
                    String path = filePathConfig.getFilePath("emp/image", RandomUtil.getRandomFileName(), "png");
                    // base64上传
                    byte[] imageByte = ImageUtil.base64ToImage(base64Str);
                    ByteArrayInputStream inputStream = new ByteArrayInputStream(imageByte);
                    String upload = fileHandler.upload(path, inputStream);
                    appEmpIdentifyDTO.setAdvancedInfo(upload);
                }
            }
            index++;
            idCardIdentifyRedisDao.addIdCardIdentify(sessionId, index);
            return appEmpIdentifyDTO;
        }
        fileHandler.delete(imgUrl);
        throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_054.getCode());
    }

    @Override
    public List<AppEmpDTO> listOpenApiEmp(Integer deptId) throws BizException {
        return appEmpMapper.selectOpenApiEmpList(deptId);
    }


    @Override
    @Async
    public void importEmp(Integer deptId, List<AppEmpImportParam> importParamList) throws BizException {
        if (importParamList == null || importParamList.isEmpty()) {
            return;
        }
        logger.info("导入人员, deptId:{}, size:{}", deptId, importParamList.size());
        for (int i = 0; i < importParamList.size(); i++) {
            AppEmpImportParam appEmpImportParam = importParamList.get(i);
            try {
                AppEmp appEmp = new AppEmp();
                appEmp.setDeptId(deptId);
                String empName = appEmpImportParam.getEmpName();
                if (StringUtils.isBlank(empName)) {
                    logger.info("姓名为空, rowNum:{}", i);
                    continue;
                }
                appEmp.setEmpName(empName);
                appEmp.setEname(PinyinUtil.toPinyin(empName));
                // 处理性别
                String sex = appEmpImportParam.getGender();
                int gender = "男".equals(sex) ? 1 : "女".equals(sex) ? 2 : 0;
                appEmp.setGender(gender);
                // 处理民族
                String nation = appEmpImportParam.getNation();
                if (StringUtils.isNotBlank(nation)) {
                    nation = nation.replace("族", "");
                }
                appEmp.setNation(nation);
                appEmp.setAddress(appEmpImportParam.getAddress());

                // 证件类型
                String cardTypeStr = appEmpImportParam.getCardType();
                Integer cardTypeValue = CardType.JMSFZ.getValue();
                CardType cardType = CardType.parseDesc(cardTypeStr);
                if (cardType != null) {
                    cardTypeValue = cardType.getValue();
                }
                appEmp.setCardType(cardTypeValue);

                // 验证证件号
                String idCardNo = appEmpImportParam.getIdCardNo();
                if (StringUtils.isNotEmpty(idCardNo)) {
                    AppEmp emp = appEmpMapper.selectByDeptIdAndIdCardNo(deptId, idCardNo);
                    if (emp != null) {
                        logger.info("当前项目人员证件号已存在,deptId:{}, idCardNo:{}", deptId, idCardNo);
                        // 证件号已存在 不保存证件号
                    } else {
                        appEmp.setIdCardNo(idCardNo);
                        Date birthday = IDCardUtil.getBirth(idCardNo);
                        appEmp.setBirthday(birthday);
                    }
                }
                // 验证手机号
                String phone = appEmpImportParam.getPhone();
                if (StringUtils.isNotBlank(phone)) {
                    AppEmp emp = appEmpMapper.selectByDeptIdAndPhone(deptId, phone);
                    if (emp != null) {
                        logger.info("当前人员手机号已存在,deptId:{}, phone:{}", deptId, phone);
                        // 手机号已存在 不保存手机号
                    } else {
                        appEmp.setPhone(phone);
                    }
                }
                // 处理劳务分包单位
                String corpName = appEmpImportParam.getCorpName();
                Integer corpId = null;
                if (StringUtils.isNotBlank(corpName)) {
                    AppCorpDTO corp = appCorpService.getCorpByDeptIdAndName(deptId, corpName);
                    if (corp == null) {
                        logger.info("当前合作单位不存在,deptId:{}, corpName:{}", deptId, corpName);
                        // 合作单位不存在 不保存合作单位
                    } else {
                        corpId = corp.getCorpId();
                    }
                }
                appEmp.setCorpId(corpId);
                appEmp.setCorpName(corpName);
                // 处理班组
                String groupName = appEmpImportParam.getGroupName();
                if (StringUtils.isNotBlank(groupName)) {
                    AppEmpGroup group = appEmpGroupMapper.selectByCorpIdAndName(corpId, groupName);
                    if (group == null) {
                        logger.info("当前班组不存在,deptId:{}, groupName:{}", deptId, groupName);
                        // 班组不存在 不保存班组
                    } else {
                        appEmp.setGroupId(group.getId());
                        appEmp.setGroupName(groupName);
                    }
                }
                // 处理工种
                String workTypeName = StringUtils.isBlank(appEmpImportParam.getWorkTypeName()) ? "其他" : appEmpImportParam.getWorkTypeName();
                AppEmpWorkType workType = appEmpWorkTypeMapper.selectByDeptIdAndName(deptId, workTypeName);
                if (workType == null) {
                    logger.info("当前工种不存在,deptId:{}, workTypeName:{}", deptId, workTypeName);
                    // 工种不存在 不保存工种
                } else {
                    appEmp.setWorkTypeId(workType.getId());
                    appEmp.setWorkTypeName(workTypeName);
                }

                // 处理工人类型
                String workRoleName = appEmpImportParam.getWorkRoleName();
                if (StringUtils.isNotBlank(workRoleName)) {
                    SysDictDTO sysDictDTO = sysDictService.getByCodeAndName(deptId, "work_role", workRoleName);
                    if (sysDictDTO != null) {
                        if (NumberUtil.isNumber(sysDictDTO.getCode())) {
                            appEmp.setWorkRoleId(Integer.valueOf(sysDictDTO.getCode()));
                        }
                        appEmp.setWorkRoleName(sysDictDTO.getName());
                    } else {
                        logger.info("当前工人类型不存在,deptId:{}, workRoleName:{}", deptId, workRoleName);
                    }
                }

                // 处理进场时间
                Date enterDate = cn.hutool.core.date.DateUtil.date();
                String enterTime = appEmpImportParam.getEnterTime();
                if (StringUtils.isNotBlank(enterTime)) {
                    try {
                        enterDate = cn.hutool.core.date.DateUtil.parseDate(enterTime);
                    } catch (Exception e) {
                        logger.error("日期格式化错误, enterTime:" + enterTime);
                    }
                }
                appEmp.setEnterTime(enterDate);
                appEmpMapper.insertSelective(appEmp);


                Integer empId = appEmp.getId();

                // 初始化人员数据
                AppEmpData empData = new AppEmpData();
                empData.setEmpId(empId);
                empData.setNetState(NetState.OFFLINE.getValue());
                empData.setLocaleState(LocaleState.OUT.getValue());
                empData.setAttendState(AttendState.ABSENCE.getValue());
                appEmpDataMapper.insertSelective(empData);

                // 增加人员进场记录
                AppEmpEnterRecord empEnterRecord = new AppEmpEnterRecord();
                empEnterRecord.setDeptId(deptId);
                empEnterRecord.setEmpId(empId);
                empEnterRecord.setCorpId(corpId);
                empEnterRecord.setDate(enterDate);
                empEnterRecord.setType(PostState.ENTER.getValue());
                appEmpEnterRecordMapper.insertSelective(empEnterRecord);
            } catch (Exception e) {
                logger.warn("导入人员失败", e);
            }
        }
    }

    @Override
    public boolean validDeptEmpId(Integer deptId, Integer empId) throws BizException {
        if (deptId == null || empId == null) {
            return false;
        }
        // 查找组织机构ID及其子孙组织机构ID
        List<Integer> deptIdList = sysDeptService.getDescendantDeptIdList(deptId);
        empId = appEmpMapper.selectByDeptIdsAndEmpId(deptIdList, empId);
        return empId != null;
    }

    @Override
    public void empThirdVerify(Integer empId) throws BizException {
        logger.info("人员信息-第三方核验,empId:{}", empId);
        AppEmp emp = appEmpMapper.selectByPrimaryKey(empId);
        Integer deptId = emp.getDeptId();
        EmpThirdVerifyWapper wapper = empThirdVerifyFactory.verify(deptId);
        if (wapper != null) {
            try {
                wapper.getVerify().verifyEmp(emp, wapper.getConfig());
                appEmpMapper.updateEmpVerifyState(empId, VerifyState.SUCCESS.getValue());
            } catch (Exception ex) {
                appEmpMapper.updateEmpVerifyState(empId, VerifyState.FAIL.getValue());
                throw ex;
            }
        }
    }

    @Override
    public void empThirdSync(Integer empId) throws BizException {
        logger.info("人员信息-第三方同步,empId:{}", empId);
        AppEmp emp = appEmpMapper.selectByPrimaryKey(empId);
        Integer deptId = emp.getDeptId();
        List<EmpThirdSyncWapper> wapperList = empThirdSyncFactory.sync(deptId);
        for (EmpThirdSyncWapper wapper : wapperList) {
            try {
                wapper.getSync().syncEmp(emp, wapper.getConfig());
                appEmpMapper.updateEmpSyncFlag(empId, SyncFlag.SUCCESS.getValue());
            } catch (Exception ex) {
                appEmpMapper.updateEmpSyncFlag(empId, SyncFlag.FAIL.getValue());
                throw ex;
            }
        }
    }

    @Override
    public void empThirdSyncBatch(Integer deptId) throws BizException {
        logger.info("人员信息-第三方同步(批量),deptId:{}", deptId);
        List<EmpThirdSyncWapper> wapperList = empThirdSyncFactory.sync(deptId);
        if (wapperList.size() == 0) {
            return;
        }
        EmpThirdSyncWapper wapper = wapperList.get(0);
        List<AppEmp> empList = appEmpMapper.selectDeptEmpList(deptId);
        logger.info("同步人员信息-总数:{}", empList.size());
        if (empList.size() == 0) {
            return;
        }

        try {
            wapper.getSync().syncEmp(empList, wapper.getConfig());
            // appEmpMapper.updateEmpSyncFlag(empId, SyncFlag.SUCCESS.getValue());
            // logger.info("同步人员信息-成功,empId={}", empId);
        } catch (Exception ex) {
            // appEmpMapper.updateEmpSyncFlag(empId, SyncFlag.FAIL.getValue());
            // logger.error("同步人员信息-失败,empId={}", empId, ex);
        }
    }

    @Override
    public void importEmpDay(Integer deptId, List<AppEmpDayImportDTO> list) throws BizException {
        for (AppEmpDayImportDTO dto : list) {
            String empCode = dto.getEmpCode();
            AppEmp emp = appEmpMapper.selectByDeptIdAndEmpCode(deptId, empCode);
            if (emp != null) {
                Integer empId = emp.getId();
                Date date = DateUtil.parseDate(dto.getDate(), "yyyy-MM-dd");
                AppEmpDay empDay = appEmpDayMapper.selectByEmpIdAndDate(empId, date);
                if (empDay == null) {
                    empDay = new AppEmpDay();
                    empDay.setDeptId(deptId);
                    empDay.setEmpId(empId);
                    empDay.setDate(date);
                    empDay.setWorkRoleId(emp.getWorkRoleId());
                    empDay.setAttendType(dto.getAttendType());
                    empDay.setAttendState(dto.getAttendState());
                    empDay.setStartTime(StringUtils.isBlank(dto.getStartTime()) ? null : DateUtil.parseDateTime(dto.getStartTime()));
                    empDay.setEndTime(StringUtils.isBlank(dto.getEndTime()) ? null : DateUtil.parseDateTime(dto.getEndTime()));
                    appEmpDayMapper.insertSelective(empDay);
                } else {
                    empDay.setAttendType(dto.getAttendType());
                    empDay.setAttendState(dto.getAttendState());
                    empDay.setStartTime(StringUtils.isBlank(dto.getStartTime()) ? null : DateUtil.parseDateTime(dto.getStartTime()));
                    empDay.setEndTime(StringUtils.isBlank(dto.getEndTime()) ? null : DateUtil.parseDateTime(dto.getEndTime()));
                    appEmpDayMapper.updateByPrimaryKeySelective(empDay);
                }
            }
        }
    }

    @Override
    public Boolean checkEmpName(Integer deptId, String name) {
        return appEmpMapper.checkEmpName(deptId, name) != 0;
    }

    /*****************************************  private   *****************************************/

    /**
     * 添加人员
     *
     * @param request
     * @return
     */
    private Integer addEmp(AppEmpAddParam request) {
        logger.info("人员管理-添加人员:request:{}", request);

        Integer deptId = request.getProjectId();
        Integer groupId = request.getGroupId();
        String empCode = request.getEmpCode();
        String idCardNo = request.getIdCardNo();
        String phone = request.getPhone();
        Date birthday = request.getBirthday();

        // 验证班组
        AppEmpGroup appEmpGroup = appEmpGroupMapper.selectByPrimaryKey(groupId);
        if (appEmpGroup == null) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_002.getCode());
        }
        String groupName = appEmpGroup.getGroupName();
        Integer corpId = appEmpGroup.getCorpId();
        String corpName = appEmpGroup.getCorpName();

        // 验证工号
        if (StringUtils.isNotEmpty(empCode)) {
            AppEmp emp = appEmpMapper.selectByDeptIdAndEmpCode(deptId, empCode);
            if (emp != null) {
                throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_006.getCode());
            }
        }

        // 验证证件号
        if (StringUtils.isNotEmpty(idCardNo)) {
            AppEmp emp = appEmpMapper.selectByDeptIdAndIdCardNo(deptId, idCardNo);
            if (emp != null) {
                throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_007.getCode());
            }
            if (birthday == null) {
                birthday = IDCardUtil.getBirth(idCardNo);
                request.setBirthday(birthday);
            }
        }

        // 验证手机号
        if (StringUtils.isNotEmpty(phone)) {
            AppEmp emp = appEmpMapper.selectByDeptIdAndPhone(deptId, phone);
            if (emp != null) {
                throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_008.getCode());
            }
        }

        // 新增人员信息
        AppEmp appEmp = new AppEmp();
        BeanUtils.copyProperties(request, appEmp);
        appEmp.setDeptId(deptId);
        appEmp.setGroupId(groupId);
        appEmp.setGroupName(groupName);
        appEmp.setCorpId(corpId);
        appEmp.setCorpName(corpName);
        appEmp.setEname(PinyinUtil.toPinyin(request.getEmpName()));
        appEmp.setKeyPositionFlag(request.getKeyPositionFlag());
        appEmp.setKeyPositionAuth(request.getKeyPositionAuth());
        appEmp.setBindFlag(BindFlag.UNBIND.getValue());
        appEmp.setDelFlag(DelFlag.UNDELETE.getValue());
        appEmpMapper.insertSelective(appEmp);
        Integer empId = appEmp.getId();

        // 初始化人员数据
        AppEmpData empData = new AppEmpData();
        empData.setEmpId(empId);
        empData.setNetState(NetState.OFFLINE.getValue());
        empData.setLocaleState(LocaleState.OUT.getValue());
        empData.setAttendState(AttendState.ABSENCE.getValue());
        appEmpDataMapper.insertSelective(empData);

        // 增加人员进场记录
        AppEmpEnterRecord empEnterRecord = new AppEmpEnterRecord();
        empEnterRecord.setDeptId(deptId);
        empEnterRecord.setEmpId(empId);
        empEnterRecord.setCorpId(corpId);
        empEnterRecord.setDate(request.getEnterTime());
        empEnterRecord.setType(PostState.ENTER.getValue());
        appEmpEnterRecordMapper.insertSelective(empEnterRecord);
        return empId;
    }

    /**
     * 人员年龄检测
     *
     * @param deptId 项目ID
     * @param age    年龄
     * @param gender 性别
     */
    private void checkAge(Integer deptId, Integer age, Integer gender) {
        AppEmpSettingDTO setting = commonEmpConfigManager.getSetting(deptId);
        Integer childLabourAgeFlag = setting.getChildLabourAgeFlag();
        if (EnableState.ENABLED.getValue().equals(childLabourAgeFlag)) {
            if (age < setting.getChildLabourAge()) {
                throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_055.getCode());
            }
        }
        if (Gender.man.getValue().equals(gender)) {
            Integer maleRetireAgeFlag = setting.getMaleRetireAgeFlag();
            if (EnableState.ENABLED.getValue().equals(maleRetireAgeFlag)) {
                if (age > setting.getMaleRetireAge()) {
                    throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_056.getCode());
                }
            }
        }
        if (Gender.woman.getValue().equals(gender)) {
            Integer femaleRetireAgeFlag = setting.getFemaleRetireAgeFlag();
            if (EnableState.ENABLED.getValue().equals(femaleRetireAgeFlag)) {
                if (age > setting.getFemaleRetireAge()) {
                    throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_056.getCode());
                }
            }
        }
    }

    /**
     * 检测人员是否在黑名单中
     *
     * @param appEmp
     */
    private void checkBlackList(AppEmp appEmp) {
        Integer deptId = appEmp.getDeptId();
        Integer empId = appEmp.getId();
        Integer projectId = appEmp.getDeptId();
        AppEmpSettingDTO setting = commonEmpConfigManager.getSetting(projectId);
        Integer empBlackListFlag = setting.getEmpBlackListFlag();
        if (EnableState.ENABLED.getValue().equals(empBlackListFlag)) {
            Integer cnt = appEmpBlackListMapper.countByEmpIdAndDeptId(empId, deptId);
            if (cnt > 0) {
                throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_057.getCode());
            }
        }
    }

    /**
     * 数据转换
     *
     * @param log
     * @return
     */
    private AppEmpGpsDTO toGps(AppDeviceCardLog log) {
        AppEmpGpsDTO gpsDTO = new AppEmpGpsDTO();
        gpsDTO.setTime(log.getTime());
        gpsDTO.setLng(log.getLng());
        gpsDTO.setLat(log.getLat());
        gpsDTO.setLocation(log.getLocation());
        return gpsDTO;
    }
}
