package com.whfc.emp.dao;

import com.whfc.emp.dto.AppFaceGateDTO;
import com.whfc.emp.dto.AppFaceGateEmpDTO;
import com.whfc.emp.entity.AppFaceGatePerson;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface AppFaceGatePersonMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppFaceGatePerson record);

    int insertSelective(AppFaceGatePerson record);

    AppFaceGatePerson selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppFaceGatePerson record);

    int updateByPrimaryKey(AppFaceGatePerson record);

    /**
     * 验证闸机下是否有人员未销权
     *
     * @param faceGateId
     * @return
     */
    Integer countByFaceGateId(@Param("faceGateId") Integer faceGateId);

    /**
     * 根据empId 和faceGateId 查看是否授权
     *
     * @param faceGateId
     * @param empId
     * @param
     * @return
     */
    AppFaceGatePerson selectByFaceGateIdAndEmpId(@Param("faceGateId") Integer faceGateId, @Param("empId") Integer empId);

    /**
     * 查找人员persinGuid
     *
     * @param faceGateId
     * @param empId
     * @return
     */
    List<String> selectPersonGuidByFaceGateIdAndEmpId(@Param("faceGateId") Integer faceGateId, @Param("empId") Integer empId);

    /**
     * 查询全部授权记录
     *
     * @param faceGateId
     * @return
     */
    List<AppFaceGatePerson> selectPersonByFaceGateId(@Param("faceGateId") Integer faceGateId);

    /**
     * 查询授权记录
     *
     * @param empIdList
     * @return
     */
    List<AppFaceGatePerson> selectByEmpIdList(@Param("empIdList") List<Integer> empIdList);

    /**
     * 根据闸机id查找人员guid
     *
     * @param faceGateId
     * @return
     */
    List<String> selectPersonGuidByFaceGateId(@Param("faceGateId") Integer faceGateId);

    /**
     * 查询人员授权
     *
     * @param personGuid
     * @param deviceKey
     * @return
     */
    AppFaceGatePerson selectByPersonGuidAndDeviceKey(@Param("personGuid") String personGuid, @Param("deviceKey") String deviceKey);

    /**
     * 逻辑删除
     *
     * @param deviceKey
     * @param personGuidList
     */
    void delBYDeviceKeyAndParsonGuid(@Param("deviceKey") String deviceKey, @Param("personGuidList") List<String> personGuidList);

    /**
     * 逻辑删除
     *
     * @param deviceKey
     * @param empId
     */
    void delByDeviceKeyAndEmpId(@Param("deviceKey") String deviceKey, @Param("empId") Integer empId);

    /**
     * 查看闸机授权人员
     *
     * @param faceGateId
     * @param keyword
     * @param groupId
     * @param workTypeId
     * @param type
     * @return
     */
    List<AppFaceGateEmpDTO> selectByFaceGateId(@Param("faceGateId") Integer faceGateId,
                                               @Param("keyword") String keyword,
                                               @Param("groupId") Integer groupId,
                                               @Param("workTypeId") Integer workTypeId,
                                               @Param("type") Integer type);

    /**
     * 查找未授权的人员
     *
     * @param deptId
     * @param faceGateId
     * @param groupId
     * @param keyword
     * @return
     */
    List<AppFaceGateEmpDTO> selectUnGrantEmp(@Param("deptId") Integer deptId,
                                             @Param("faceGateId") Integer faceGateId,
                                             @Param("groupId") Integer groupId,
                                             @Param("keyword") String keyword);


    /**
     * 查询需要授权的人员(当前在线的闸机)
     *
     * @return 人员信息
     */
    List<AppFaceGateEmpDTO> selectNeedGrantEmp();

    /**
     * 查询需要取消授权的人员(当前在线的闸机)
     *
     * @return 人员信息
     */
    List<AppFaceGateEmpDTO> selectNeedUnGrantEmp();

    /**
     * 查询是否正在执行定时任务
     *
     * @param faceGateId
     * @param empId
     * @return
     */
    List<AppFaceGatePerson> selectByFaceGateIdAndEmpIdAndTask(@Param("faceGateId") Integer faceGateId, @Param("empId") Integer empId);

    /**
     * 查看闸机授权失败人员
     *
     * @param faceGateId
     * @param keyword
     * @param groupId
     * @return
     */
    List<AppFaceGateEmpDTO> selectByFailFaceGateId(@Param("faceGateId") Integer faceGateId, @Param("keyword") String keyword, @Param("groupId") Integer groupId);

    /**
     * 更新授权信息
     *
     * @param id
     * @param taskType
     * @param personGuid
     * @return
     */
    int updateGrantInfo(@Param("id") Integer id, @Param("taskType") Integer taskType, @Param("personGuid") String personGuid);

    /**
     * 更新授权任务任务状态
     *
     * @param id
     * @param taskType
     * @param message
     * @return
     */
    int updateTaskType(@Param("id") Integer id, @Param("taskType") Integer taskType, @Param("message") String message);

    /**
     * 查询人员授权的闸机列表
     *
     * @param empId       人员
     * @param faceGateIds 闸机列表
     * @return 授权记录
     */
    List<AppFaceGatePerson> selectByEmpIdAndFackGateList(@Param("empId") Integer empId,
                                                         @Param("faceGateIds") List<Integer> faceGateIds);

    /**
     * 查询闸机授权人数
     *
     * @param deptId
     * @return
     */
    List<AppFaceGateDTO> selectCountByFaceGateId(@Param("deptId") Integer deptId);

    /**
     * 查询人员授权情况
     *
     * @param empId
     * @return
     */
    Integer countByEmpId(@Param("empId") Integer empId);
}