package com.whfc.emp.dao;

import com.whfc.emp.dto.AppEmpHealthReportDTO;
import com.whfc.emp.entity.AppEmpHealthReport;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-09-02 10:53
 */
@Repository
public interface AppEmpHealthReportMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppEmpHealthReport record);

    int insertSelective(AppEmpHealthReport record);

    AppEmpHealthReport selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppEmpHealthReport record);

    int updateByPrimaryKey(AppEmpHealthReport record);

    /**
     * 根据人员ID和类型查找防疫信息
     *
     * @param empId 人员ID
     * @param type  类型
     * @return 防疫信息
     */
    List<AppEmpHealthReportDTO> selectByEmpIdAndType(@Param("empId") Integer empId, @Param("type") String type);

    /**
     * 删除防疫信息
     *
     * @param healthInfoId 防疫信息ID
     */
    void logDel(@Param("healthInfoId") Integer healthInfoId);

}