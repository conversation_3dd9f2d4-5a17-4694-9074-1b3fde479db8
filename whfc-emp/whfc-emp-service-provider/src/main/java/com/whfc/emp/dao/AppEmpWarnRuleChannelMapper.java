package com.whfc.emp.dao;

import com.whfc.emp.entity.AppEmpWarnRuleChannel;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AppEmpWarnRuleChannelMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppEmpWarnRuleChannel record);

    int insertSelective(AppEmpWarnRuleChannel record);

    AppEmpWarnRuleChannel selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppEmpWarnRuleChannel record);

    int updateByPrimaryKey(AppEmpWarnRuleChannel record);

    /**
     * 使用报警规则id查询报警渠道
     *
     * @param ruleId
     * @return
     */
    List<Integer> selectByRuleId(@Param("ruleId") Integer ruleId);

    /**
     * 软删除
     *
     * @param ruleId
     */
    void deleteByRuleId(@Param("ruleId") Integer ruleId);

    /**
     * 批量新增
     *
     * @param ruleId         规则ID
     * @param msgChannelList 消息渠道
     */
    void batchInsert(@Param("ruleId") Integer ruleId, @Param("msgChannelList") List<Integer> msgChannelList);
}