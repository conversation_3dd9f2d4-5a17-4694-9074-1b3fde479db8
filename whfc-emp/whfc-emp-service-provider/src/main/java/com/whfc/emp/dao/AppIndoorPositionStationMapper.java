package com.whfc.emp.dao;

import com.whfc.emp.dto.AppIndoorPositionStationDTO;
import com.whfc.emp.entity.AppIndoorPositionStation;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AppIndoorPositionStationMapper {
    int deleteByPrimaryKey(Integer id);

    int insertSelective(AppIndoorPositionStation record);

    AppIndoorPositionStation selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppIndoorPositionStation record);

    List<AppIndoorPositionStationDTO> selectByDeptId(@Param("deptId") Integer deptId);

    List<AppIndoorPositionStationDTO> selectByMapId(@Param("mapId") Integer mapId);

    AppIndoorPositionStation selectByGuid(@Param("guid") String guid);

    AppIndoorPositionStation selectByCode(@Param("code") String code);

    int logicDeleteById(@Param("id") Integer id);

    int clearByMapId(@Param("mapId") Integer mapId);
}