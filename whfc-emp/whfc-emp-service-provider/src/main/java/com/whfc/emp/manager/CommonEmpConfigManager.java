package com.whfc.emp.manager;

import com.whfc.emp.dto.AppEmpSettingDTO;
import com.whfc.emp.enums.AttendType;

/**
 * @ClasssName CommonEmpConfigManager
 * @Description 人员配置公共服务
 * <AUTHOR>
 * @Date 2020/12/28 15:41
 * @Version 1.0
 */
public interface CommonEmpConfigManager {
    /**
     * 项目是否需要开启人脸识别
     *
     * @param deptId
     * @return
     */
    boolean isIdCardVerify(Integer deptId);

    /**
     * 查询项目的人员配置
     *
     * @param deptId
     * @return
     */
    AppEmpSettingDTO getSetting(Integer deptId);

    /**
     * 查询地图显示设备离线时间
     *
     * @param deptId
     * @return
     */
    AppEmpSettingDTO getMinutes(Integer deptId);

    /**
     * 获取某个人的考勤方式
     *
     * @param empId
     * @return
     */
    AttendType getAttendTypeByEmpId(Integer empId);

    /**
     * 获取考勤方式
     *
     * @param deptId
     * @return
     */
    AttendType getAttendType(Integer deptId);

    /**
     * 获取项目人员电子围栏
     *
     * @param deptId
     * @return
     */
    String getPolygonWKT(Integer deptId);

    /**
     * 获取休息区电子围栏(不报警)
     *
     * @param deptId
     * @return
     */
    String getRestAreaPolygonWKT(Integer deptId);
}
