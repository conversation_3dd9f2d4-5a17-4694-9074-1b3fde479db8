package com.whfc.emp.manager.warn;

import com.alibaba.fastjson.JSONObject;
import com.whfc.base.service.WarnConfigService;
import com.whfc.common.constant.AppMsgObjType;
import com.whfc.common.constant.QueueConst;
import com.whfc.common.enums.AppWarnModuleType;
import com.whfc.common.enums.DeviceType;
import com.whfc.common.enums.MsgType;
import com.whfc.common.util.DateUtil;
import com.whfc.emp.dao.AppEmpWarnMapper;
import com.whfc.emp.dao.AppEmpWarnRuleChannelMapper;
import com.whfc.emp.dao.AppEmpWarnRuleTimeMapper;
import com.whfc.emp.dao.AppEmpWarnRuleUserMapper;
import com.whfc.emp.dto.AppWarnTimeDTO;
import com.whfc.emp.dto.EmpWarnCheckDTO;
import com.whfc.emp.dto.EmpWarnRuleDTO;
import com.whfc.emp.entity.AppEmpWarn;
import com.whfc.emp.enums.AppEmpWarnRuleType;
import com.whfc.entity.dto.msg.AppMsgToUserDTO;
import com.whfc.entity.dto.msg.PushMsgDTO;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * @Description: 人员报警管理器-抽象父类
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/10/10 15:50
 */
@Component
public abstract class AppEmpWarnMgrBase implements AppEmpWarnMgr {


    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 报警内容模版
     */
    private static final String WARN_CONTENT = "%s在%s,%s,请您及时处理";

    @Autowired
    private AppEmpWarnMapper appEmpWarnMapper;

    @Autowired
    private AppEmpWarnRuleChannelMapper appEmpWarnRuleChannelMapper;

    @Autowired
    private AppEmpWarnRuleUserMapper appEmpWarnRuleUserMapper;

    @Autowired
    private AppEmpWarnRuleTimeMapper appEmpWarnRuleTimeMapper;

    @Autowired
    private AmqpTemplate amqpTemplate;

    @DubboReference(interfaceClass = WarnConfigService.class, version = "1.0.0")
    private WarnConfigService warnConfigService;

    @Override
    public void checkWarn(EmpWarnCheckDTO checkDTO, EmpWarnRuleDTO ruleDTO) {
        logger.info("检测报警规则,empId:{},ruleType:{}", checkDTO.getEmpId(), ruleDTO.getRuleType());

        // 检测是否触发报警值
        boolean isMeetRuleValue = this.checkValue(checkDTO, ruleDTO);
        if (!isMeetRuleValue) {
            logger.info("不满足报警条件,empId:{},ruleType:{}", checkDTO.getEmpId(), ruleDTO.getRuleType());
            return;
        }

        // 检测是否在报警时间段内
        boolean isMeetTime = this.checkTime(checkDTO, ruleDTO);
        if (!isMeetTime) {
            logger.info("不满足报警时间,empId:{},ruleType:{}", checkDTO.getEmpId(), ruleDTO.getRuleType());
            return;
        }

        // 报警频率验证
        boolean isMeetAlarmFrequency = this.checkFrequency(checkDTO, ruleDTO);
        if (!isMeetAlarmFrequency) {
            logger.info("不满足报警频率,empId:{},ruleType:{}", checkDTO.getEmpId(), ruleDTO.getRuleType());
            return;
        }

        logger.info("触发报警规则,empId:{},ruleType:{}", checkDTO.getEmpId(), ruleDTO.getRuleType());

        // 保存报警记录
        Integer warnId = this.saveWarn(checkDTO, ruleDTO);

        // 推送报警消息
        this.generateMessage(checkDTO, ruleDTO, warnId);
    }

    /**
     * 报警时间验证
     *
     * @param checkDTO
     * @param ruleDTO
     * @return
     */
    public boolean checkTime(EmpWarnCheckDTO checkDTO, EmpWarnRuleDTO ruleDTO) {
        // 增加时间浮动
        Date time = DateUtil.getTime(checkDTO.getTriggerTime());
        Integer ruleId = ruleDTO.getRuleId();
        boolean flag = false;
        List<AppWarnTimeDTO> timeList = appEmpWarnRuleTimeMapper.selectByRuleId(ruleId);
        for (AppWarnTimeDTO timeDTO : timeList) {
            Date startTime = timeDTO.getStartTime();
            Date endTime = timeDTO.getEndTime();
            if (endTime.after(time) && startTime.before(time)) {
                flag = true;
                break;
            }
        }
        if (timeList.isEmpty()) {
            flag = true;
        }
        return flag;
    }

    /**
     * 报警频率验证
     *
     * @param checkDTO
     * @param ruleDTO
     * @return
     */
    public boolean checkFrequency(EmpWarnCheckDTO checkDTO, EmpWarnRuleDTO ruleDTO) {
        Integer deptId = checkDTO.getDeptId();
        Integer ruleType = ruleDTO.getRuleType();
        String objectId = String.valueOf(checkDTO.getEmpId());
        Date triggerTime = checkDTO.getTriggerTime();
        return warnConfigService.checkAlarmFrequency(deptId, AppWarnModuleType.EMP.value(), ruleType, objectId, triggerTime);
    }

    /**
     * 检测是否触发报警值
     *
     * @param checkDTO
     * @param ruleDTO
     * @return
     */
    public abstract boolean checkValue(EmpWarnCheckDTO checkDTO, EmpWarnRuleDTO ruleDTO);

    /**
     * 保存报警记录
     *
     * @param checkDTO
     * @param ruleDTO
     * @return
     */
    public Integer saveWarn(EmpWarnCheckDTO checkDTO, EmpWarnRuleDTO ruleDTO) {
        AppEmpWarn record = new AppEmpWarn();
        record.setDeptId(checkDTO.getDeptId());
        record.setTriggerObjectId(String.valueOf(checkDTO.getEmpId()));
        record.setTriggerTime(checkDTO.getTriggerTime());
        record.setTriggerKey(checkDTO.getTriggerKey());
        record.setRuleId(ruleDTO.getRuleId());
        record.setRuleType(ruleDTO.getRuleType());
        record.setLat(checkDTO.getLng());
        record.setLng(checkDTO.getLat());
        appEmpWarnMapper.insertSelective(record);
        return record.getId();
    }

    /**
     * 推送报警消息
     *
     * @param checkDTO
     * @param ruleDTO
     * @param warnId
     */
    public void generateMessage(EmpWarnCheckDTO checkDTO, EmpWarnRuleDTO ruleDTO, Integer warnId) {
        Integer moduleType = AppWarnModuleType.EMP.value();
        Integer deptId = checkDTO.getDeptId();
        Date time = checkDTO.getTriggerTime();
        Integer ruleId = ruleDTO.getRuleId();

        // 查找报警通知人
        List<AppMsgToUserDTO> userList = appEmpWarnRuleUserMapper.selectByRuleId(ruleId);

        // 查找报警通知方式
        List<Integer> channelList = appEmpWarnRuleChannelMapper.selectByRuleId(ruleId);

        // 推送报警消息
        AppEmpWarnRuleType ruleType = AppEmpWarnRuleType.parseByValue(ruleDTO.getRuleType());
        String title = ruleType.tittle();
        String content = getMessageContent(checkDTO, ruleType);
        String objectId = String.valueOf(warnId);

        // 设置报警类型
        Integer deviceType = checkDTO.getDeviceType();
        String warnObjType = null;
        if (DeviceType.helmet.getValue().equals(deviceType)) {
            warnObjType = AppMsgObjType.EMP_HELMET;
        } else if (DeviceType.smart_bracelet.getValue().equals(deviceType)) {
            warnObjType = AppMsgObjType.EMP_BRACELET;
        }

        PushMsgDTO pushMsgDTO = new PushMsgDTO();
        pushMsgDTO.setDeptId(deptId);
        pushMsgDTO.setTime(time);
        pushMsgDTO.setTitle(title);
        pushMsgDTO.setContent(content);
        pushMsgDTO.setModuleType(moduleType);
        pushMsgDTO.setMsgType(MsgType.ALARM.getValue());
        pushMsgDTO.setMsgObjectType(warnObjType);
        pushMsgDTO.setMsgObjectId(objectId);
        pushMsgDTO.setMsgChannelList(channelList);
        pushMsgDTO.setToUserList(userList);

        amqpTemplate.convertAndSend(QueueConst.PUSH_MSG_EXCHANGE, "", JSONObject.toJSONString(pushMsgDTO));
    }

    private String getMessageContent(EmpWarnCheckDTO checkDTO, AppEmpWarnRuleType ruleType) {
        String detail = ruleType.detail();
        String timeStr = DateUtil.formatDateTime(checkDTO.getTriggerTime());
        return String.format(WARN_CONTENT, checkDTO.getWorkTypeName() + " " + checkDTO.getEmpName(), timeStr, detail);
    }
}
