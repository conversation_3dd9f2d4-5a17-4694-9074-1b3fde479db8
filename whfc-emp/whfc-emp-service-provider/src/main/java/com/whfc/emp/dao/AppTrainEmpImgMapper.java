package com.whfc.emp.dao;

import com.whfc.emp.entity.AppTrainEmpImg;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/11 11:34
 */
@Repository
public interface AppTrainEmpImgMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppTrainEmpImg record);

    int insertSelective(AppTrainEmpImg record);

    AppTrainEmpImg selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppTrainEmpImg record);

    int updateByPrimaryKey(AppTrainEmpImg record);

    /**
     * 查找成绩图片
     *
     * @param trainEmpId 培训人员ID
     * @return 成绩图片
     */
    List<String> selectImgUrlList(@Param("trainEmpId") Integer trainEmpId);

    /**
     * 逻辑删除成绩图片
     *
     * @param trainEmpId 培训人员ID
     */
    void logicDel(@Param("trainEmpId") Integer trainEmpId);
}