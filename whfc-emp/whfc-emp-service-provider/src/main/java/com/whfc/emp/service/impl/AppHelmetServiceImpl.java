package com.whfc.emp.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.whfc.base.dto.AppDeviceDTO;
import com.whfc.base.service.AppDeviceService;
import com.whfc.common.constant.QueueConst;
import com.whfc.common.enums.DevicePlatform;
import com.whfc.common.enums.DeviceType;
import com.whfc.common.enums.I18nErrorCode;
import com.whfc.common.exception.BizException;
import com.whfc.common.result.PageData;
import com.whfc.common.result.ResultEnum;
import com.whfc.common.third.baibutonget.BaibutongetApi;
import com.whfc.common.third.baibutonget.entity.Token;
import com.whfc.common.util.CollectionUtil;
import com.whfc.common.util.PageUtil;
import com.whfc.emp.dao.*;
import com.whfc.emp.dto.*;
import com.whfc.emp.dto.helmet.AppHelmetFileDTO;
import com.whfc.emp.entity.*;
import com.whfc.emp.enums.AppBroadcastSendState;
import com.whfc.emp.enums.AppBroadcastType;
import com.whfc.emp.manager.AppEmpDataManager;
import com.whfc.emp.param.AppEmpBroadcastParam;
import com.whfc.emp.service.AppHelmetService;
import com.whfc.emp.influx.AppDeviceCardLogDao;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description 安全帽相关接口
 * <AUTHOR>
 * @Date 2021/1/4 14:11
 * @Version 1.0
 */
@DubboService(interfaceClass = AppHelmetService.class, version = "1.0.0", timeout = 60 * 1000)
public class AppHelmetServiceImpl implements AppHelmetService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @DubboReference(interfaceClass = AppDeviceService.class, version = "1.0.0")
    private AppDeviceService appDeviceService;

    @Autowired
    private AppEmpMapper appEmpMapper;

    @Autowired
    private AppEmpGroupMapper appEmpGroupMapper;

    @Autowired
    private AppEmpDataManager appEmpDataManager;

    @Autowired
    private AppBroadcastRecordMapper appBroadcastRecordMapper;

    @Autowired
    private AppBroadcastRecordEmpMapper appBroadcastRecordEmpMapper;

    @Autowired
    private AmqpTemplate amqpTemplate;

    @Autowired
    private AppHelmetConfigMapper appHelmetConfigMapper;

    @Autowired
    private AppHelmetFileMapper appHelmetFileMapper;

    @Autowired
    private AppDeviceCardLogDao appDeviceCardLogDao;

    @Autowired
    private AppEmpDeviceMapper appEmpDeviceMapper;

    @Override
    public PageData<AppDeviceCardLogDTO> getDeviceDataLog(String sn, Date startTime, Date endTime, Integer pageNum, Integer pageSize) {
        logger.info("查询安全帽硬件数据,sn:{},startTime:{},endTime:{},pageNum：{}，pageSize：{}", sn, startTime, endTime, pageNum, pageSize);
        AppDeviceDTO device = appDeviceService.getDeviceBySn(sn);
        if (device == null) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), I18nErrorCode.MACH_BE_001.getCode());
        }
        return appDeviceCardLogDao.selectHelmetDataLogListByDeviceId(device.getId(), startTime, endTime, pageNum, pageSize);
    }


    @Override
    public List<AppEmpGroupEmpDTO> helmetEmpList(Integer deptId, Integer bindFlag, String keyword) throws BizException {
        logger.info("查询人员硬件列表，deptId：{}", deptId);
        // 查找人员列表
        List<AppEmpDTO> empList = appEmpMapper.selectHelmetEmpList(deptId, bindFlag, keyword);
        // 转换数据
        Map<Integer, List<AppEmpDTO>> empMap = empList.stream().collect(Collectors.groupingBy(AppEmpDTO::getGroupId));

        List<AppEmpGroupEmpDTO> list = new ArrayList<>();

        for (Map.Entry<Integer, List<AppEmpDTO>> entry : empMap.entrySet()) {
            AppEmpGroupEmpDTO appEmpGroupEmp = new AppEmpGroupEmpDTO();
            appEmpGroupEmp.setGroupId(entry.getKey());
            List<AppEmpDTO> emps = entry.getValue();
            String groupName = "";
            for (AppEmpDTO emp : emps) {
                // 设置班组名称
                if (StringUtils.isEmpty(groupName)) {
                    groupName = emp.getGroupName();
                }
                // 设置安全帽颜色
                HelmetIconDTO helmetIcon = appEmpDataManager.getHelmetIcon(emp.getColor());
                BeanUtils.copyProperties(helmetIcon, emp);
            }
            appEmpGroupEmp.setGroupName(groupName);
            appEmpGroupEmp.setTotal(emps.size());
            appEmpGroupEmp.setEmpList(emps);
            list.add(appEmpGroupEmp);
        }
        return list;
    }

    @Override
    public Map<String, Object> getHelmetParam(Integer deptId, String platform) throws BizException {

        Map<String, Object> param = new HashMap<>();

        // 百步通
        if (DevicePlatform.baibutonget.name().equalsIgnoreCase(platform)) {

            AppHelmetConfig config = appHelmetConfigMapper.selectByDeptIdAndPlatform(deptId, platform);
            if (config != null) {
                try {
                    BaibutongetApi baibutongetApi = new BaibutongetApi(config.getHost(), config.getUser(), config.getPass(), config.getExt1());
                    String pkey = baibutongetApi.getPkey();
                    Token token = baibutongetApi.getToken(pkey);
                    param.put("host", config.getHost());
                    param.put("wss", config.getExt2());
                    param.put("token", token != null ? token.getToken() : "");
                    param.put("user", config.getUser());
                } catch (Exception ex) {
                    logger.error("", ex);
                }
            }
        }
        return param;
    }

    @Override
    public PageData<AppHelmetFileDTO> getHelmetFileList(Integer deptId, Integer empId, Integer pageNum, Integer pageSize) throws BizException {

        PageHelper.startPage(pageNum, pageSize);
        List<AppHelmetFileDTO> list = appHelmetFileMapper.selectFileList(deptId, empId);
        PageHelper.clearPage();

        return PageUtil.pageData(PageInfo.of(list));
    }

    @Override
    public PageData<AppBroadcastRecordEmpDTO> getBroadcastList(Integer empId, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<AppBroadcastRecordEmpDTO> list = appBroadcastRecordEmpMapper.selectBroadcastRecordList(empId);
        return PageUtil.pageData(PageInfo.of(list));
    }

    @Override
    public void broadcast(AppEmpBroadcastParam request) {
        logger.info("发送人员广播:request:{}", request);
        String content = request.getContent();
        if (content.length() > 100) {
            throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_067.getCode());
        }
        Integer empId = request.getEmpId();
        Integer corpId = request.getCorpId();
        Integer groupId = request.getGroupId();
        Integer deptId = request.getDeptId();
        AppBroadcastRecord record = new AppBroadcastRecord();
        record.setContent(content);
        record.setDeptId(deptId);
        record.setSendUserId(request.getSendUserId());
        record.setSendUserName(request.getSendUserName());
        // 取出人员
        if (empId == null) {
            record.setType(AppBroadcastType.DEPT.getValue());
        } else {
            record.setType(AppBroadcastType.EMP.getValue());
        }

        // 保存记录
        appBroadcastRecordMapper.insertSelective(record);
        // 保存广播人员
        if (empId == null) {
            // 查找出组织机构下面的人员
            List<AppEmp> list = appEmpMapper.selectBindEmpByCorpIdOrGroupId(corpId, groupId, deptId);
            saveRecordEmpAndSendBroadcast(record.getId(), content, list);
        } else {
            // 查找出人员信息
            AppEmp emp = appEmpMapper.selectByPrimaryKey(empId);
            if (emp == null) {
                throw new BizException(ResultEnum.FAILURE.getCode(), I18nErrorCode.EMP_BE_001.getCode());
            }
            saveRecordEmpAndSendBroadcast(record.getId(), content, Collections.singletonList(emp));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delBroadcast(Integer id) {
        appBroadcastRecordEmpMapper.logicDelId(id);
    }

    /**
     * 保存接收广播的人员
     *
     * @param recordId 广播记录ID
     * @param empList  人员信息
     */
    private void saveRecordEmpAndSendBroadcast(Integer recordId, String content, List<AppEmp> empList) {
        for (AppEmp emp : empList) {
            Integer empId = emp.getId();
            AppEmpDevice appEmpDevice = appEmpDeviceMapper.selectByEmpIdAndType(empId, DeviceType.helmet.getValue());
            if (appEmpDevice == null) {
                // 未绑定人员跳过
                continue;
            }
            AppBroadcastRecordEmp recordEmp = new AppBroadcastRecordEmp();
            recordEmp.setEmpId(empId);
            recordEmp.setRecordId(recordId);
            recordEmp.setSendTime(new Date());
            recordEmp.setDeviceId(appEmpDevice.getId());
            recordEmp.setState(AppBroadcastSendState.SEND.getValue());
            appBroadcastRecordEmpMapper.insertSelective(recordEmp);
            AppBroadcastRecordEmpDTO recordEmpDTO = new AppBroadcastRecordEmpDTO();
            BeanUtils.copyProperties(recordEmp, recordEmpDTO);
            recordEmpDTO.setContent(content);
            try {
                recordEmpDTO.setCode(appEmpDevice.getSn());
                amqpTemplate.convertAndSend(QueueConst.EMP_HELMET_BOARDCAST, JSON.toJSONString(recordEmpDTO));
            } catch (Exception e) {
                logger.error("发送广播失败", e);
                // 修改广播为未发送状态
                appBroadcastRecordEmpMapper.updateState(recordEmpDTO.getId(), AppBroadcastSendState.UNSENT.getValue());
            }
        }

    }

}
