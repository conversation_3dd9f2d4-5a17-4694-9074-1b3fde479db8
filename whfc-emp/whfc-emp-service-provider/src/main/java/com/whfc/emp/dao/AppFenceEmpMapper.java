package com.whfc.emp.dao;

import com.whfc.emp.dto.AppFenceDTO;
import com.whfc.emp.dto.AppFenceEmpDTO;
import com.whfc.emp.entity.AppFenceEmp;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AppFenceEmpMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppFenceEmp record);

    int insertSelective(AppFenceEmp record);

    AppFenceEmp selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppFenceEmp record);

    int updateByPrimaryKey(AppFenceEmp record);

    /**
     * 查找电子围栏考勤人员
     *
     * @param fenceId
     * @param keyword
     * @return
     */
    List<AppFenceEmpDTO> selectByFenceId(@Param("fenceId") Integer fenceId,
                                         @Param("groupId") Integer groupId,
                                         @Param("workTypeId") Integer workTypeId,
                                         @Param("keyword") String keyword);

    /**
     * 添加或修改
     * @param fenceEmpList
     */
    void insertOrUpdate(List<AppFenceEmp> fenceEmpList);

    /**
     * 删除
     * @param fenceId
     * @param empId
     */
    void deleteLogic(@Param("fenceId") Integer fenceId,
                     @Param("empId") Integer empId);

    /**
     * 根据人员id查找电子围栏列表
     *
     * @param empId
     * @return
     */
    List<AppFenceDTO> selectByEmpId(Integer empId);

    /**
     * 查找未授权的人员
     *
     * @param deptId
     * @param fenceId
     * @param groupId
     * @param keyword
     * @return
     */
    List<AppFenceEmpDTO> selectUnGrantEmp(@Param("deptId") Integer deptId,
                                          @Param("fenceId") Integer fenceId,
                                          @Param("groupId") Integer groupId,
                                          @Param("keyword") String keyword);
}