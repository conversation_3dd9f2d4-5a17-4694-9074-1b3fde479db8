package com.whfc.emp.dao;

import com.whfc.emp.dto.AppFaceGatePlatformDTO;
import com.whfc.emp.entity.AppFaceGatePlatform;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-08-17 11:31
 */
@Repository
public interface AppFaceGatePlatformMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppFaceGatePlatform record);

    int insertSelective(AppFaceGatePlatform record);

    AppFaceGatePlatform selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppFaceGatePlatform record);

    int updateByPrimaryKey(AppFaceGatePlatform record);

    /**
     * 查看所有闸机平台
     *
     * @return 所有闸机平台
     */
    List<AppFaceGatePlatformDTO> selectAll();
}