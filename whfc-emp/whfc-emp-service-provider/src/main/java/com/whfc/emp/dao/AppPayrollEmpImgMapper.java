package com.whfc.emp.dao;

import com.whfc.emp.entity.AppPayrollEmpImg;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/11 11:34
 */
@Repository
public interface AppPayrollEmpImgMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppPayrollEmpImg record);

    int insertSelective(AppPayrollEmpImg record);

    AppPayrollEmpImg selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppPayrollEmpImg record);

    int updateByPrimaryKey(AppPayrollEmpImg record);

    /**
     * 查找人员工资图片
     *
     * @param payrollEmpId 人员工资ID
     * @return 图片
     */
    List<String> selectImgUrlList(@Param("payrollEmpId") Integer payrollEmpId);

    /**
     * 逻辑删除工资图片
     * @param payrollEmpId  人员工资ID
     */
    void logicDel(@Param("payrollEmpId") Integer payrollEmpId);

}