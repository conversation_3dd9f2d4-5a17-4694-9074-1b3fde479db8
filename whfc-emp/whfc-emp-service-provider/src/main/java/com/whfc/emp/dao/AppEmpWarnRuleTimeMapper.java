package com.whfc.emp.dao;

import com.whfc.emp.dto.AppWarnTimeDTO;
import com.whfc.emp.entity.AppEmpWarnRuleTime;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AppEmpWarnRuleTimeMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AppEmpWarnRuleTime record);

    int insertSelective(AppEmpWarnRuleTime record);

    AppEmpWarnRuleTime selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AppEmpWarnRuleTime record);

    int updateByPrimaryKey(AppEmpWarnRuleTime record);

    /**
     * 使用规则id查询报警时间
     * @param ruleId
     * @return
     */
    List<AppWarnTimeDTO> selectByRuleId(Integer ruleId);

    /**
     * 软删除
     * @param ruleId
     */
    void deleteByRuleId(@Param("ruleId") Integer ruleId);

    /**
     * 批量新增
     * @param timeList
     */
    void batchInsert(@Param("list")List<AppWarnTimeDTO> timeList);
}