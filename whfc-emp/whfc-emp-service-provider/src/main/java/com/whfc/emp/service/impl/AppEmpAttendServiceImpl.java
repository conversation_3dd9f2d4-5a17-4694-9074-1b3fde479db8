package com.whfc.emp.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.whfc.common.enums.LocaleState;
import com.whfc.common.exception.BizException;
import com.whfc.common.file.FileHandler;
import com.whfc.common.file.FilePathConfig;
import com.whfc.common.file.properties.FileExpirationRules;
import com.whfc.common.result.PageData;
import com.whfc.common.util.*;
import com.whfc.emp.dao.AppEmpAttendRecordMapper;
import com.whfc.emp.dao.AppEmpDayMapper;
import com.whfc.emp.dao.AppEmpDeptDayMapper;
import com.whfc.emp.dao.AppEmpMapper;
import com.whfc.emp.dto.*;
import com.whfc.emp.entity.AppEmpDeptDay;
import com.whfc.emp.enums.AttendState;
import com.whfc.emp.enums.AttendType;
import com.whfc.emp.manager.CommonEmpConfigManager;
import com.whfc.emp.param.EmpAttendPlan;
import com.whfc.emp.param.EmpDayReportExportParam;
import com.whfc.emp.param.EmpMonthReportExportParam;
import com.whfc.emp.service.AppEmpAttendService;
import com.whfc.entity.dto.OssPathDTO;
import com.whfc.fuum.service.SysDeptService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Description 人员考勤service
 * <AUTHOR>
 * @Date 2020/12/22 15:22
 * @Version 1.0
 */
@DubboService(interfaceClass = AppEmpAttendService.class, version = "1.0.0", timeout = 60 * 1000)
public class AppEmpAttendServiceImpl implements AppEmpAttendService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @DubboReference(interfaceClass = SysDeptService.class, version = "1.0.0")
    private SysDeptService sysDeptService;

    @Autowired
    private AppEmpMapper appEmpMapper;

    @Autowired
    private AppEmpDayMapper appEmpDayMapper;

    @Autowired
    private AppEmpDeptDayMapper appEmpDeptDayMapper;

    @Autowired
    private CommonEmpConfigManager commonEmpConfigManager;

    @Autowired
    private AppEmpAttendRecordMapper appEmpAttendRecordMapper;

    @Autowired
    private FilePathConfig msFilePathConfig;

    @Autowired
    private FileHandler fileHandler;


    @Override
    public PageData<AttendDayEmpDTO> attendDayList(Integer pageNum, Integer pageSize, Date date, Integer deptId, Integer corpId, Integer groupId, String keyword, Integer attendState) {
        logger.info("人员管理-日考勤记录列表,pageNum:{},pageSize:{},date:{},deptId:{},corpId:{},groupId:{},keyword:{},empId:{},attendState:{}", pageNum, pageSize, date, deptId, corpId, groupId, keyword, attendState);
        PageHelper.startPage(pageNum, pageSize);
        List<AttendDayEmpDTO> empList = appEmpDayMapper.selectAttendDayEmp(deptId, date, corpId, groupId, keyword, attendState);
        PageHelper.clearPage();
        String deptName = sysDeptService.getDeptName(deptId);
        for (AttendDayEmpDTO attendDayEmpDTO : empList) {
            attendDayEmpDTO.setDate(date);
            attendDayEmpDTO.setDeptName(deptName);
        }
        return PageUtil.pageData(PageInfo.of(empList));
    }

    @Override
    public AppEmpAttendDayNumDTO attendDayNum(Date date, Integer deptId, Integer corpId, Integer groupId, String keyword, Integer empId) throws BizException {
        logger.info("人员管理-日考勤记录统计,date:{},deptId:{},corpId:{},groupId:{},keyword:{},empId:{}", date, deptId, corpId, groupId, keyword, empId);
        return appEmpDayMapper.selectAttendDayEmpNum(deptId, corpId, groupId, date, keyword, empId);
    }

    @Override
    public PageData<AppEmpAttendMonthDTO> attendMonthList(Integer pageNum, Integer pageSize, Date time, Integer deptId, Integer corpId, Integer groupId, String keyword) {
        logger.info("人员管理-月考勤记录列表,pageNum:{},pageSize:{},time:{},deptId:{},corpId:{},groupId:{},keyword:{}", pageNum, pageSize, time, deptId, corpId, groupId, keyword);
        Date monthBegin = DateUtil.getMonthBegin(time);
        Date monthEnd = DateUtil.getMonthEnd(time);
        PageHelper.startPage(pageNum, pageSize);
        List<AppEmpAttendMonthDTO> list = appEmpDayMapper.selectAttendMonthEmp(deptId, corpId, groupId, monthBegin, monthEnd, keyword);
        PageHelper.clearPage();
        String deptName = sysDeptService.getDeptName(deptId);
        joinAttendMont(monthBegin, monthEnd, list, deptName);
        return PageUtil.pageData(PageInfo.of(list));
    }

    @Override
    public OssPathDTO exportMonthReport(EmpMonthReportExportParam request) {
        logger.info("导出人员月考勤记录,request:{}", request);
        // 获取数据
        Integer deptId = request.getDeptId();
        Integer corpId = request.getCorpId();
        Integer groupId = request.getGroupId();
        Date month = request.getMonth();
        String keyword = request.getKeyword();
        Date monthBegin = DateUtil.getMonthBegin(month);
        Date monthEnd = DateUtil.getMonthEnd(month);
        List<AppEmpAttendMonthDTO> list = appEmpDayMapper.selectAttendMonthEmp(deptId, corpId, groupId, monthBegin, monthEnd, keyword);
        String deptName = sysDeptService.getDeptName(deptId);
        joinAttendMont(monthBegin, monthEnd, list, deptName);
        OssPathDTO ossPathDTO = new OssPathDTO();
        try {
            // 获取模板
            ClassPathResource resource = new ClassPathResource("templates/empMonthReportTemplate.xls");
            InputStream templateFileInputStream = resource.getInputStream();
            File tempFile = FileUtil.copyTemplateFile(templateFileInputStream);

            // 写数据
            FileInputStream fileInputStream = new FileInputStream(tempFile);
            HSSFWorkbook workbook = new HSSFWorkbook(fileInputStream);
            HSSFSheet sheet = workbook.getSheetAt(0);

            // 日期列表
            List<Date> dateList = DateUtil.getDateListBetween(monthBegin, monthEnd);

            // 表格抬头
            HSSFRow row0 = sheet.getRow(0);
            HSSFCell cell0 = row0.createCell(0);
            cell0.setCellValue(DateUtil.formatDate(month, "yyyy年M月人员考勤记录"));
            // 合并单元格
            CellRangeAddress cra = new CellRangeAddress(0, 0, 0, 3 + dateList.size());
            sheet.addMergedRegion(cra);
            CellRangeAddress cra1 = new CellRangeAddress(1, 1, 4, 3 + dateList.size());
            sheet.addMergedRegion(cra1);
            // 居中
            HSSFCellStyle titleStyle = workbook.createCellStyle();
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            cell0.setCellStyle(titleStyle);

            // 填充表头
            HSSFRow row = sheet.getRow(2);
            for (int i = 0; i < dateList.size(); i++) {
                Date date = dateList.get(i);
                int colIdx = 4 + i;
                HSSFCell cell = row.createCell(colIdx);
                cell.setCellValue(DateUtil.formatDate(date, "d"));
            }

            // 填充数据
            int rowIdx = 3;
            for (AppEmpAttendMonthDTO empAttendMonthDTO : list) {
                HSSFRow row1 = sheet.createRow(rowIdx);

                row1.createCell(0).setCellValue(empAttendMonthDTO.getEmpName());
                row1.createCell(1).setCellValue(empAttendMonthDTO.getEmpPhone());
                row1.createCell(2).setCellValue(empAttendMonthDTO.getGroupName());
                row1.createCell(3).setCellValue(empAttendMonthDTO.getAttendDays());
                List<AppEmpDayWorkTimesDTO> dateList1 = empAttendMonthDTO.getDateList();

                for (int i = 0; i < dateList1.size(); i++) {
                    int colIdx = i + 4;
                    // 开机时长
                    row1.createCell(colIdx).setCellValue(dateList1.get(i).getWorkTimes());
                }
                rowIdx++;
            }

            // 保存excel
            FileOutputStream fos = new FileOutputStream(tempFile);
            workbook.write(fos);
            fos.flush();
            fos.close();

            // 上传oss
            String name = msFilePathConfig.getFilePath("emp/tmp", String.format("%s%s人员考勤记录.xls", DateUtil.formatDate(month, "yyyy年MM月"), deptName));
            FileInputStream inputStream = new FileInputStream(tempFile);
            String upload = fileHandler.upload(name, inputStream, FileExpirationRules.oneDay);
            ossPathDTO.setPath(upload);
        } catch (Exception e) {
            logger.error("导出人员月考勤记录失败", e);
        }
        return ossPathDTO;
    }

    @Override
    public OssPathDTO exportDayReport(EmpDayReportExportParam param) throws BizException {
        Integer deptId = param.getDeptId();
        Date date = param.getDate();
        Integer corpId = param.getCorpId();
        Integer groupId = param.getGroupId();
        Integer attendState = param.getAttendState();
        String keyword = param.getKeyword();
        String deptName = sysDeptService.getDeptName(deptId);
        List<AttendDayEmpDTO> empList = appEmpDayMapper.selectAttendDayEmp(deptId, date, corpId, groupId, keyword, attendState);
        OssPathDTO ossPathDTO = new OssPathDTO();
        try {
            String dateStr = DateUtil.formatDate(date, "yyyy年MM月dd日");

            // 写数据
            File destFile = File.createTempFile(RandomUtil.getRandomFileName(), ".xls");
            ClassPathResource resource = new ClassPathResource("templates/attendDay.xls");
            ExcelWriter excelWriter = EasyExcel.write(destFile).withTemplate(resource.getInputStream()).build();
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            Map<String, Object> map = new HashMap<>(2);
            map.put("deptName", deptName);
            map.put("date", dateStr);
            excelWriter.fill(map, writeSheet);

            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            excelWriter.fill(empList, fillConfig, writeSheet);
            excelWriter.finish();

            // 上传oss
            String filepath = msFilePathConfig.getFilePath(String.format("emp/tmp/%s%s日考勤记录.xls", deptName, dateStr));
            String upload = fileHandler.upload(filepath, new FileInputStream(destFile), FileExpirationRules.DAYS_1);
            ossPathDTO.setPath(upload);
        } catch (Exception e) {
            logger.error("导出日考勤记录失败", e);
        }
        logger.info("导出文件:{}", ossPathDTO.getPath());
        return ossPathDTO;
    }

    @Override
    public PageData<EmpDataDTO> empDayList(Integer deptId, Date startDate, Date endDate, Integer pageNum, Integer pageSize) {
        logger.info("人员日报,pageNum:{},pageSize:{},deptId:{},startDate:{},endDate:{}", pageNum, pageSize, deptId, startDate, endDate);
        PageHelper.startPage(pageNum, pageSize);
        List<EmpDataDTO> list = appEmpDayMapper.selectEmpNumTotalPerDay(deptId, startDate, endDate);
        PageHelper.clearPage();

        // 计划出勤人数
        List<EmpAttendPlan> attendPlans = appEmpDeptDayMapper.selectByDeptIdAndTimeRange(deptId, startDate, endDate);
        Map<Date, Integer> attendPlanMap = new HashMap<>(attendPlans.size());
        for (EmpAttendPlan plan : attendPlans) {
            attendPlanMap.put(plan.getDate(), plan.getPlanAttendNum());
        }

        // 统计工人类型出勤情况
        List<EmpDataDTO> workRoleEmpDataList = appEmpDayMapper.selectEmpNumTotalPerDayByWorkRole(deptId, startDate, endDate);
        Map<Date, List<AppEmpWorkRoleNumDTO>> workRoleEmpDataMap = new HashMap<>(workRoleEmpDataList.size());
        for (EmpDataDTO empDataDTO : workRoleEmpDataList) {
            Date date = empDataDTO.getDate();
            int workRoleId = empDataDTO.getWorkRoleId();
            String workRoleName = empDataDTO.getWorkRoleName();
            int empNum = empDataDTO.getEmpTotal();
            int attendNum = empDataDTO.getAttendNum();

            workRoleEmpDataMap.computeIfAbsent(date, k -> new ArrayList<>())
                    .add(new AppEmpWorkRoleNumDTO(workRoleId, workRoleName, empNum, attendNum, 0));
        }

        // 累加相同日期和工作角色ID的 num 和 attendNum
        for (List<AppEmpWorkRoleNumDTO> workRoleNumDTOList : workRoleEmpDataMap.values()) {
            Map<Integer, AppEmpWorkRoleNumDTO> workRoleMap = new HashMap<>();
            for (AppEmpWorkRoleNumDTO dto : workRoleNumDTOList) {
                workRoleMap.computeIfAbsent(dto.getWorkRoleId(), k -> new AppEmpWorkRoleNumDTO(dto.getWorkRoleId(), dto.getWorkRoleName(), 0, 0, 0))
                        .accumulate(dto.getNum(), dto.getAttendNum(), dto.getLocaleNum());
            }
            workRoleNumDTOList.clear();
            workRoleNumDTOList.addAll(workRoleMap.values());
        }


        for (EmpDataDTO empData : list) {
            Date date = empData.getDate();
            Integer empTotal = empData.getEmpTotal();
            Integer attendNum = empData.getAttendNum();
            Integer planAttendNum = attendPlanMap.getOrDefault(date, empTotal);
            Double rate = planAttendNum > 0 ? MathUtil.divide(attendNum * 100, planAttendNum, 2) : 0;
            empData.setAbsentNum(empTotal - attendNum);
            empData.setPlanAttendNum(planAttendNum);
            empData.setRate(rate);
            empData.setWorkRoleNumList(workRoleEmpDataMap.getOrDefault(date, Collections.emptyList()));
        }
        return PageUtil.pageData(PageInfo.of(list));
    }

    @Override
    public void setDataPlan(EmpAttendPlan plan) throws BizException {
        AppEmpDeptDay record = new AppEmpDeptDay();
        record.setDeptId(plan.getDeptId());
        record.setDate(plan.getDate());
        record.setPlanAttendNum(plan.getPlanAttendNum());
        appEmpDeptDayMapper.insertOrUpdate(record);
    }

    @Override
    public PageData<AppEmpAttendRecordDTO> attendRecord(Integer empId, Date date, Integer pageNum, Integer pageSize, Integer direction) {
        logger.info("打卡记录,empId:{},date:{},pageNum:{},pageSize:{},direction:{}", empId, date, pageNum, pageSize, direction);
//        AttendType attendType = commonEmpConfigManager.getAttendTypeByEmpId(empId);
        PageHelper.startPage(pageNum, pageSize);
        Date startTime = DateUtil.getDateBegin(date);
        Date endTime = DateUtil.getDateEnd(date);
        AttendType attendType = commonEmpConfigManager.getAttendTypeByEmpId(empId);
        List<AppEmpAttendRecordDTO> list = appEmpAttendRecordMapper.selectByEmpIdAndDate(empId, startTime, endTime, direction, attendType.getValue());
        return PageUtil.pageData(PageInfo.of(list));
    }

    @Override
    public List<AppEmpAttendRecordDTO> attendRecord(Integer empId, Date date, Integer direction) {
        logger.info("打卡记录,empId:{},date:{},direction:{}", empId, date, direction);
        Date startTime = DateUtil.getDateBegin(date);
        Date endTime = DateUtil.getDateEnd(date);
        AttendType attendType = commonEmpConfigManager.getAttendTypeByEmpId(empId);
        return appEmpAttendRecordMapper.selectByEmpIdAndDate(empId, startTime, endTime, direction, attendType.getValue());
    }

    @Override
    public List<AppEmpAttendRecordDTO> attendRecord(Integer deptId, Date startTime, Date endTime, AttendType attendType) throws BizException {
        return appEmpAttendRecordMapper.selectAttendRecordByDeptIdAndTime(deptId, startTime, endTime, attendType.getValue());
    }


    @Override
    public AppEmpAttendStatDTO attendGroup(Integer deptId, String keyword, Integer localeState, Integer attendState) {
        List<AppEmpDTO> empList = appEmpMapper.selectEmpByDeptIdAndKeyword(deptId, keyword);
        AppEmpAttendStatDTO attendStatDTO = new AppEmpAttendStatDTO();
        int totalNum = empList.size();
        attendStatDTO.setTotalNum(totalNum);
        int attendNum = 0;
        int localeNum = 0;
        int outLocaleNum = 0;
        int absentNum = 0;

        Map<Integer, String> groupMap = new HashMap<>();
        for (AppEmpDTO appEmpDTO : empList) {
            if (AttendState.ATTEND.getValue().equals(appEmpDTO.getAttendState())) {
                // 出勤人数
                attendNum++;
                if (LocaleState.IN.getValue().equals(appEmpDTO.getLocaleState())) {
                    // 在场人数
                    localeNum++;
                } else if (LocaleState.OUT.getValue().equals(appEmpDTO.getLocaleState())) {
                    // 离场人数
                    outLocaleNum++;
                }
            } else if (AttendState.ABSENCE.getValue().equals(appEmpDTO.getAttendState())) {
                // 缺勤人数
                absentNum++;
            }

            // 设置班组信息
            groupMap.put(appEmpDTO.getGroupId(), appEmpDTO.getGroupName());
        }
        attendStatDTO.setAttendNum(attendNum);
        attendStatDTO.setLocaleNum(localeNum);
        attendStatDTO.setOutLocaleNum(outLocaleNum);
        attendStatDTO.setAbsentNum(absentNum);
        // 出勤率
        double attendRate = 0.0;
        if (totalNum > 0) {
            attendRate = NumberUtil.div(attendNum, totalNum, 4);
        }
        attendStatDTO.setAttendRate(attendRate);
        // 过滤条件
        Stream<AppEmpDTO> stream = empList.stream();
        if (localeState != null) {
            stream = stream.filter(e -> localeState.equals(e.getLocaleState()));
        }
        if (attendState != null) {
            stream = stream.filter(e -> attendState.equals(e.getAttendState()));
        }

        // 分组
        Map<Integer, List<AppEmpDTO>> map = stream.collect(Collectors.groupingBy(AppEmpDTO::getGroupId));

        // 设置数据
        List<AppEmpAttendGroupDTO> groupList = new ArrayList<>();
        for (Map.Entry<Integer, List<AppEmpDTO>> entry : map.entrySet()) {
            AppEmpAttendGroupDTO groupDTO = new AppEmpAttendGroupDTO();
            Integer groupId = entry.getKey();
            groupDTO.setId(groupId);
            groupDTO.setName(groupMap.get(groupId));
            groupDTO.setNum(entry.getValue().size());
            groupDTO.setEmpList(entry.getValue());
            groupList.add(groupDTO);
        }
        attendStatDTO.setGroupAttend(groupList);
        return attendStatDTO;
    }

    private void joinAttendMont(Date monthBegin, Date monthEnd, List<AppEmpAttendMonthDTO> list, String deptName) {
        List<Date> dayListOfMonth = DateUtil.getDayListOfMonth(monthBegin);
        for (AppEmpAttendMonthDTO appEmpAttendMonthDTO : list) {
            appEmpAttendMonthDTO.setDeptName(deptName);
            List<AppEmpDayWorkTimesDTO> dateList = new ArrayList<>(dayListOfMonth.size());
            for (Date date : dayListOfMonth) {
                AppEmpDayWorkTimesDTO appEmpDayWorkTimesDTO = new AppEmpDayWorkTimesDTO();
                appEmpDayWorkTimesDTO.setDate(date);
                appEmpDayWorkTimesDTO.setWorkTimes(0.0);
                dateList.add(appEmpDayWorkTimesDTO);
            }
            // 出勤天数
            Integer deptId1 = appEmpAttendMonthDTO.getDeptId();
            Integer empId = appEmpAttendMonthDTO.getEmpId();
            Integer attendDays = appEmpDayMapper.selectAttendDaysByEmpId(deptId1, empId, monthBegin, monthEnd);
            appEmpAttendMonthDTO.setAttendDays(attendDays);
            // 每日工时
            List<AppEmpDayWorkTimesDTO> dateList2 = appEmpDayMapper.selectWorkTimesByEmpId(empId, monthBegin, monthEnd);
            for (AppEmpDayWorkTimesDTO appEmpDayWorkTimesDTO : dateList) {
                for (AppEmpDayWorkTimesDTO appEmpDayWorkTimesDTO2 : dateList2) {
                    if (appEmpDayWorkTimesDTO.getDate().getTime() == appEmpDayWorkTimesDTO2.getDate().getTime()) {
                        appEmpDayWorkTimesDTO.setWorkTimes(appEmpDayWorkTimesDTO2.getWorkTimes());
                    }
                }
            }
            appEmpAttendMonthDTO.setDateList(dateList);
        }
    }
}
