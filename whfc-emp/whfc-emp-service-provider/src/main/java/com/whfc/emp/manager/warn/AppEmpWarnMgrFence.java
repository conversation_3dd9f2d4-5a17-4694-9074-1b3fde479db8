package com.whfc.emp.manager.warn;

import com.whfc.common.enums.FenceType;
import com.whfc.common.geometry.GeometryUtil;
import com.whfc.common.util.MathUtil;
import com.whfc.common.util.PositionUtil;
import com.whfc.emp.dao.AppEmpWarnRuleObjectMapper;
import com.whfc.emp.dto.AppFenceDTO;
import com.whfc.emp.dto.EmpWarnCheckDTO;
import com.whfc.emp.dto.EmpWarnRuleDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description: 人员报警管理器-电子围栏报警
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/10/10 15:54
 */
@Component(value = "appEmpWarnMgrFence")
public class AppEmpWarnMgrFence extends AppEmpWarnMgrBase implements AppEmpWarnMgr {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private AppEmpWarnRuleObjectMapper appEmpWarnRuleObjectMapper;


    @Override
    public boolean checkValue(EmpWarnCheckDTO checkDTO, EmpWarnRuleDTO ruleDTO) {
        Integer empId = checkDTO.getEmpId();
        Double lat = checkDTO.getLat();
        Double lng = checkDTO.getLng();

        if (PositionUtil.isValid(lng, lat)) {
            AppFenceDTO fenceDTO = appEmpWarnRuleObjectMapper.selectByEmpId(empId);
            if (fenceDTO == null) {
                return false;
            }
            Integer fenceType = fenceDTO.getType();
            if (FenceType.CIRCLE.value().equals(fenceType)) {
                Double radius = MathUtil.round(fenceDTO.getRadius() / 1000, 5);
                return !GeometryUtil.isInCircle(fenceDTO.getCenter(), radius, lat, lng);
            }
            if (FenceType.POLYGON.value().equals(fenceType)) {
                return !GeometryUtil.isInPolygon(fenceDTO.getPolygon(), lat, lng);
            }
        }
        return false;
    }
}
