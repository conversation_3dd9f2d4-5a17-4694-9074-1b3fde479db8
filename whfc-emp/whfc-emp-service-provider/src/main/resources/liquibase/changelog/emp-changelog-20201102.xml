<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd">
    <property name="autoIncrement" value="true" dbms="mysql"/>
    <changeSet id="1" author="hw">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `app_emp_work_type`
            ADD COLUMN `work_code` varchar(64) NULL COMMENT '工种编码' AFTER `name`
        </sql>
    </changeSet>
    <changeSet id="2" author="hw">
        <comment>新增表</comment>
        <sql>
            CREATE TABLE `app_face_gate_app`  (
            `id` int NOT NULL AUTO_INCREMENT,
            `platform` varchar(64) NOT NULL COMMENT '闸机平台',
            `dept_id` int NOT NULL COMMENT '组织机构id',
            `app_id` varchar(64)   COMMENT '应用ID',
            `app_key` varchar(64)   COMMENT '应用key',
            `app_secret` varchar(128)  COMMENT '应用秘钥',
            `remark` varchar(128) COMMENT '备注',
            `del_flag` int NOT NULL DEFAULT 0 COMMENT '删除标记:0-未删除 1-已删除',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE
            ) COMMENT = '闸机平台应用';
        </sql>
    </changeSet>
    <changeSet id="3" author="sunguodong">
        <comment>增加字段</comment>
        <sql>
            ALTER TABLE `app_emp_data`
            ADD COLUMN `gps_time` datetime NULL COMMENT '坐标时间' after `lat`;
        </sql>
    </changeSet>
    <changeSet id="4" author="hw">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `app_face_gate_person`
            ADD COLUMN `task_type` int NOT NULL DEFAULT 0 COMMENT '定时任务状态，0-未开始执行，1-人员注册完成，2-人员照片注册完成，3-人员授权完成' AFTER `type`
        </sql>
    </changeSet>

    <changeSet id="5" author="sunguodong">
        <comment>修改字段长度</comment>
        <sql>
            ALTER TABLE `app_emp_config`
            MODIFY COLUMN `value` varchar(320) NULL AFTER `code`;
        </sql>
    </changeSet>
    <changeSet id="6" author="hw">
        <comment>修改字段长度</comment>
        <sql>
            ALTER TABLE `app_emp_reward_punishment`
            MODIFY COLUMN `description` varchar(200) NULL DEFAULT NULL COMMENT '描述' AFTER `item`
        </sql>
    </changeSet>
    <changeSet id="7" author="hw">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `app_face_gate_person`
            ADD COLUMN `message` varchar(64) NULL COMMENT '添加考勤人员返回信息' AFTER `task_type`
        </sql>
    </changeSet>
</databaseChangeLog>
