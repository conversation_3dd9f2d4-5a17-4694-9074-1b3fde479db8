<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd">
    <property name="autoIncrement" value="true" dbms="mysql"/>

    <changeSet id="1" author="sunguodong">
        <comment>迁移表结构</comment>
        <sql>

            CREATE TABLE `app_broadcast_record` (
            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
            `dept_id` int(11) NOT NULL COMMENT '组织机构ID',
            `content` varchar(100) NOT NULL COMMENT '广播内容',
            `type` int(11) NOT NULL COMMENT '广播类型 1-人员广播，2-组织机构广播',
            `send_user_id` int(11) NOT NULL COMMENT '发送广播的用户ID',
            `send_user_name` varchar(20) NOT NULL COMMENT '发送广播的用户姓名',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
            PRIMARY KEY (`id`) USING BTREE
            ) COMMENT = '广播记录表';

            CREATE TABLE `app_broadcast_record_emp` (
            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
            `record_id` int(11) NOT NULL COMMENT '广播记录ID',
            `emp_id` int(11) NOT NULL COMMENT '接收广播的人员ID',
            `device_id` int(11) NOT NULL COMMENT '接收广播的设备ID',
            `send_time` datetime(0) NOT NULL COMMENT '发送时间',
            `retry_cnt` int(11) NOT NULL DEFAULT 0 COMMENT '重试次数',
            `state` int(11) NOT NULL DEFAULT 0 COMMENT '发送状态 0-未发送，1-已发送，2-已确认 ，3-已失效',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
            PRIMARY KEY (`id`) USING BTREE
            ) COMMENT = '广播记录人员表';

            CREATE TABLE `app_device_card_data` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `device_id` int(11) NOT NULL COMMENT '硬件ID(工牌)',
            `time` datetime(0) NOT NULL COMMENT '时间',
            `lng_wgs84` double NOT NULL,
            `lat_wgs84` double NOT NULL,
            `lng` double NOT NULL,
            `lat` double NOT NULL,
            `speed` double NULL DEFAULT NULL COMMENT '速度',
            `rotation` double NULL DEFAULT NULL COMMENT '角度',
            `version` varchar(20) NULL DEFAULT NULL COMMENT '版本号',
            `geo_num` int(11) NULL DEFAULT NULL COMMENT '卫星数量',
            `level_factor` double NULL DEFAULT NULL COMMENT '水平因子',
            `rssi` int(11) NULL DEFAULT NULL COMMENT '信号强度',
            `alarm_value` int(11) NULL DEFAULT NULL COMMENT '报警值',
            `alarm_report` int(11) NULL DEFAULT 0 COMMENT '报到提醒(0-无效 1-有效)',
            `alarm_sos` int(11) NULL DEFAULT 0 COMMENT 'sos提醒(0-无效 1-有效)',
            `alarm_drop` int(11) NULL DEFAULT 0 COMMENT '跌落报警(0-无效 1-有效)',
            `alarm_doff` int(11) NULL DEFAULT 0 COMMENT '脱帽报警(0-无效 1-有效)',
            `alarm_still` int(11) NULL DEFAULT 0 COMMENT '长时间静止报警(0-无效 1-有效)',
            `card_state_value` int(11) NULL DEFAULT NULL COMMENT '状态值',
            `pos_type` int(11) NULL DEFAULT NULL COMMENT '定位类型(0:基站 1-卫星)',
            `pos_state` int(11) NULL DEFAULT NULL COMMENT '定位状态(0-无效,1-有效)',
            `pos_mode` int(11) NULL DEFAULT NULL COMMENT '定位方式(0-GPS 1-北斗 2-GPS北斗双模)',
            `batter_volt` double NULL DEFAULT NULL COMMENT '电压',
            `battery_power` int(11) NULL DEFAULT NULL COMMENT '电量',
            `acceleration_x` double NULL DEFAULT NULL COMMENT '加速度x',
            `acceleration_y` double NULL DEFAULT NULL COMMENT '加速度y',
            `acceleration_z` double NULL DEFAULT NULL COMMENT '加速度z',
            `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
            `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
            PRIMARY KEY (`id`) USING BTREE,
            UNIQUE INDEX `uk_deviceId`(`device_id`) USING BTREE
            ) COMMENT = '人员-工牌-数据';

            CREATE TABLE `app_device_card_log` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `emp_id` int(11) NOT NULL COMMENT '劳务ID',
            `device_id` int(11) NOT NULL COMMENT '硬件ID(工牌)',
            `time` datetime(0) NOT NULL COMMENT '时间',
            `lng_wgs84` double NOT NULL,
            `lat_wgs84` double NOT NULL,
            `lng` double NOT NULL,
            `lat` double NOT NULL,
            `location` varchar(64) NULL DEFAULT NULL COMMENT '坐标地址',
            `speed` double NULL DEFAULT NULL COMMENT '速度',
            `rotation` double NULL DEFAULT NULL COMMENT '角度',
            `version` varchar(20) NULL DEFAULT NULL COMMENT '版本号',
            `geo_num` int(11) NULL DEFAULT NULL COMMENT '卫星数量',
            `level_factor` double NULL DEFAULT NULL COMMENT '水平因子',
            `rssi` int(11) NULL DEFAULT NULL COMMENT '信号强度',
            `alarm_value` int(11) NULL DEFAULT NULL COMMENT '报警值',
            `alarm_report` int(11) NULL DEFAULT 0 COMMENT '报到提醒(0-无效 1-有效)',
            `alarm_sos` int(11) NULL DEFAULT 0 COMMENT 'sos提醒(0-无效 1-有效)',
            `alarm_drop` int(11) NULL DEFAULT 0 COMMENT '跌落报警(0-无效 1-有效)',
            `alarm_doff` int(11) NULL DEFAULT 0 COMMENT '脱帽报警(0-无效 1-有效)',
            `alarm_still` int(11) NULL DEFAULT 0 COMMENT '长时间静止报警(0-无效 1-有效)',
            `alarm_crash` int(11) NOT NULL DEFAULT 0 COMMENT '碰撞报警 0-无效 1-有效',
            `card_state_value` int(11) NULL DEFAULT NULL COMMENT '状态值',
            `pos_type` int(11) NULL DEFAULT NULL COMMENT '定位类型(0:基站 1-卫星)',
            `pos_state` int(11) NULL DEFAULT NULL COMMENT '定位状态(0-无效,1-有效)',
            `pos_mode` int(11) NULL DEFAULT NULL COMMENT '定位方式(0-GPS 1-北斗 2-GPS北斗双模)',
            `batter_volt` double NULL DEFAULT NULL COMMENT '电压',
            `battery_power` int(11) NULL DEFAULT NULL COMMENT '电量',
            `acceleration_x` double NULL DEFAULT NULL COMMENT '加速度x',
            `acceleration_y` double NULL DEFAULT NULL COMMENT '加速度y',
            `acceleration_z` double NULL DEFAULT NULL COMMENT '加速度z',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
            `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
            `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
            PRIMARY KEY (`id`) USING BTREE,
            INDEX `idx_time_empId_deviceId`(`time`, `emp_id`, `device_id`) USING BTREE
            ) COMMENT ='硬件-工牌数据日志';

            CREATE TABLE `app_emp` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `project_id` int(11) NULL DEFAULT NULL COMMENT '项目ID',
            `dept_id` int(11) NOT NULL COMMENT '部门ID',
            `dept_name` varchar(32) NULL DEFAULT NULL COMMENT '机构名称',
            `corp_id` int(11) NULL DEFAULT NULL COMMENT '合作单位ID',
            `corp_name` varchar(32) NULL DEFAULT NULL COMMENT '合作单位名称',
            `group_id` int(11) NULL DEFAULT NULL COMMENT '班组id',
            `group_name` varchar(32) NULL DEFAULT NULL COMMENT '班组名称',
            `emp_name` varchar(20) NULL DEFAULT NULL COMMENT '人员姓名',
            `ename` varchar(20) NULL DEFAULT NULL COMMENT '姓名拼音',
            `phone` varchar(20) NULL DEFAULT NULL COMMENT '电话',
            `gender` int(11) NULL DEFAULT 1 COMMENT '性别（1-男 2-女）',
            `birthday` date NULL DEFAULT NULL COMMENT '生日',
            `id_card_no` varchar(20) NULL DEFAULT NULL COMMENT '身份证号',
            `head_img` varchar(255) NULL DEFAULT NULL COMMENT '身份证头像',
            `avatar` varchar(255) NULL DEFAULT NULL COMMENT '用户头像',
            `card_type` int(11) NOT NULL DEFAULT 1 COMMENT '证件类型 (1-居民身份证 2-澳门永久居民证 3-澳门非永久居民证 4-澳门外地雇员身份识别证
            5-澳门特别逗留证)',
            `work_card_no` varchar(16) NULL DEFAULT NULL COMMENT
            '工作证编号',
            `work_card_end_date` date NULL DEFAULT NULL COMMENT '工作证有效期',
            `id_card_front` varchar(255) NULL DEFAULT NULL COMMENT
            '身份证正面',
            `id_card_back` varchar(255) NULL DEFAULT NULL COMMENT
            '身份证反面',
            `id_card_grant_org` varchar(32) NULL DEFAULT NULL COMMENT
            '发证机关',
            `id_card_start_date` date NULL DEFAULT NULL COMMENT '证件有效期开始时间',
            `id_card_end_date` date NULL DEFAULT NULL COMMENT '证件有效期结束时间',
            `nation` varchar(10) NULL DEFAULT NULL COMMENT '名族',
            `province` varchar(32) NULL DEFAULT NULL COMMENT '省',
            `city` varchar(32) NULL DEFAULT NULL COMMENT '市',
            `area` varchar(32) NULL DEFAULT NULL COMMENT '区',
            `address` varchar(64) NULL DEFAULT NULL COMMENT '地址',
            `education` int(11) NULL DEFAULT NULL COMMENT '学历',
            `degree` int(11) NULL DEFAULT NULL COMMENT '学位',
            `emp_code` varchar(64) NULL DEFAULT NULL COMMENT '人员编号',
            `work_role_id` int(11) NULL DEFAULT NULL COMMENT '工人类型ID',
            `work_role_name` varchar(20) NULL DEFAULT NULL COMMENT
            '工人类型名称',
            `work_type_id` int(11) NULL DEFAULT NULL COMMENT '工种ID',
            `work_type_name` varchar(20) NULL DEFAULT NULL COMMENT
            '工种名称',
            `leader_flag` int(11) NULL DEFAULT 0 COMMENT '班组长标记(1-是 0-否)',
            `post_state` int(11) NULL DEFAULT 1 COMMENT '岗位状态(1-在岗 2-退场)',
            `bind_flag` int(11) NOT NULL DEFAULT 0 COMMENT '绑定状态（0-未绑定 1-绑定）',
            `device_id` int(11) NULL DEFAULT NULL COMMENT '绑定的硬件id',
            `sn` varchar(50) NULL DEFAULT NULL COMMENT '绑定的硬件sn码',
            `qr` varchar(255) NULL DEFAULT NULL COMMENT '二维码地址',
            `enter_time` date NULL DEFAULT NULL COMMENT '入场时间',
            `outer_time` date NULL DEFAULT NULL COMMENT '退场时间',
            `key_position_flag` int(11) NULL DEFAULT 0 COMMENT '关键岗位标记 0-否 1-是',
            `key_position_auth` int(11) NULL DEFAULT 0 COMMENT '关键岗位信息认证 0-否 1-是',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT
            '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE
            ) COMMENT = '人员信息-对应机构';

            CREATE TABLE `app_emp_attend_record` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `emp_id` int(11) NOT NULL,
            `dept_id` int(11) NOT NULL,
            `time` datetime(0) NOT NULL COMMENT '时间',
            `type` int(11) NOT NULL COMMENT '方式 1-电子围栏 2-闸机 3-后台 4-扫码 ',
            `direction` int(11) NOT NULL COMMENT '方向 0-出 1-进',
            `name` varchar(32) NULL DEFAULT NULL COMMENT '电子围栏或者闸机名称',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT
            '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE,
            INDEX `idx_deptId_time`(`dept_id`, `time`) USING BTREE,
            INDEX `idx_empId_time`(`emp_id`, `time`) USING BTREE
            ) COMMENT ='人员打卡记录';

            CREATE TABLE `app_emp_bank` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `emp_id` int(11) NOT NULL COMMENT '人员ID',
            `bank_code` varchar(16) NOT NULL COMMENT '银行代码
            见枚举BankCode',
            `bank_name` varchar(32) NOT NULL COMMENT '银行名称',
            `bank_number` varchar(32) NOT NULL COMMENT '银行卡号',
            `enable_flag` int(11) NOT NULL DEFAULT 1 COMMENT '启用标记(0-未启用 1-已启用)',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT
            '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE
            ) COMMENT ='人员-银行账户';

            CREATE TABLE `app_emp_black_list` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `emp_id` int(11) NOT NULL,
            `emp_name` varchar(10) NOT NULL COMMENT '姓名',
            `id_card_no` varchar(20) NULL DEFAULT NULL COMMENT '身份证号码',
            `dept_id` int(11) NOT NULL COMMENT '组织机构ID',
            `dept_name` varchar(16) NULL DEFAULT NULL COMMENT '组织机构名称',
            `time` datetime(0) NOT NULL COMMENT '操作时间',
            `user_id` int(11) NULL DEFAULT NULL COMMENT '操作人',
            `user_name` varchar(20) NULL DEFAULT NULL COMMENT '操作人姓名',
            `reason` varchar(20) NULL DEFAULT NULL COMMENT '加入黑名单原因',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT
            '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE
            ) COMMENT ='人员黑名单';

            CREATE TABLE `app_emp_cert` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `emp_id` int(11) NOT NULL COMMENT '人员ID',
            `cert_type_id` int(11) NULL DEFAULT NULL COMMENT '证书类型ID',
            `cert_type_name` varchar(32) NULL DEFAULT NULL COMMENT
            '证书类型名称',
            `cert_name` varchar(32) NULL DEFAULT NULL COMMENT '证书名称',
            `cert_code` varchar(32) NULL DEFAULT NULL COMMENT '证书编号',
            `level` varchar(16) NULL DEFAULT NULL COMMENT '技能等级',
            `cert_start_date` date NULL DEFAULT NULL COMMENT '发证日期',
            `cert_expire_date` date NULL DEFAULT NULL COMMENT '证件有效期',
            `cert_grant_org` varchar(32) NULL DEFAULT NULL COMMENT
            '发证机关',
            `cert_file` varchar(255) NULL DEFAULT NULL COMMENT '证书附件',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT
            '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE
            )COMMENT = '人员-证书';

            CREATE TABLE `app_emp_cert_attach` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `cert_id` int(11) NOT NULL COMMENT '人员证书id',
            `name` varchar(64) NULL DEFAULT NULL COMMENT '名称',
            `url` varchar(128) NOT NULL COMMENT '附件地址',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记 0-未删除 1-已删除',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
            PRIMARY KEY (`id`) USING BTREE
            ) COMMENT = '人员资格证书';

            CREATE TABLE `app_emp_config` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `dept_id` int(11) NOT NULL COMMENT '项目id',
            `name` varchar(50) NULL DEFAULT NULL COMMENT '名称',
            `code` varchar(50) NOT NULL COMMENT '编码',
            `value` varchar(320) NULL DEFAULT NULL,
            `enable_flag` int(11) NOT NULL DEFAULT 1 COMMENT '启用标记（0-禁用 1-启用）',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记 0-未删除 1-已删除',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
            PRIMARY KEY (`id`) USING BTREE,
            UNIQUE INDEX `idx_deptId_code`(`dept_id`, `code`) USING BTREE
            ) COMMENT ='人员模块的配置表';

            CREATE TABLE `app_emp_contract` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `emp_id` int(11) NOT NULL,
            `contract_no` varchar(50) NOT NULL COMMENT '合同编号',
            `contact_type` int(11) NULL DEFAULT NULL COMMENT '合同期限类型(0-固定期限 1-固定工作量)',
            `start_date` date NULL DEFAULT NULL COMMENT '开始日期',
            `end_date` date NULL DEFAULT NULL COMMENT '截止日期',
            `state` int(11) NULL DEFAULT NULL COMMENT '合同状态 0-未签订 1-已签订',
            `pay_type` int(11) NULL DEFAULT NULL COMMENT '工资方式 1-按小时 2-按天 3-按月 ',
            `salary` double NULL DEFAULT NULL COMMENT '工资',
            `contract_file` varchar(255) NULL DEFAULT NULL COMMENT
            '合同附件',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT
            '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE
            ) COMMENT = '人员合同';

            CREATE TABLE `app_emp_contract_attach` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `contract_id` int(11) NOT NULL COMMENT '合同id',
            `name` varchar(32) NULL DEFAULT NULL COMMENT '名称',
            `url` varchar(255) NOT NULL COMMENT '附件地址',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记 0-未删除 1-已删除',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
            PRIMARY KEY (`id`) USING BTREE
            ) COMMENT = '合同附件';

            CREATE TABLE `app_emp_data` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `emp_id` int(11) NOT NULL COMMENT '人员ID',
            `device_id` int(11) NULL DEFAULT NULL COMMENT '硬件ID',
            `net_state` int(11) NOT NULL DEFAULT 0 COMMENT '网络状态(0-离线 1-在线)',
            `helmet_state` int(11) NULL DEFAULT NULL COMMENT '安全帽状态(1-正常佩戴 2-未佩戴 3-跌倒 4-坠落 5-休眠 6-计算错误)',
            `locale_state` int(11) NULL DEFAULT 0 COMMENT '现场状态(0-不在现场 1-在现场)',
            `attend_state` int(11) NULL DEFAULT 0 COMMENT '考勤状态(0-缺勤 1-出勤)',
            `time` datetime(0) NULL DEFAULT NULL COMMENT '最近通信时间',
            `lng_flag` varchar(1) NULL DEFAULT NULL COMMENT
            'E-东经/W-西经',
            `lat_flag` varchar(1) NULL DEFAULT NULL COMMENT
            'N-北纬/S-南纬',
            `lng` double NULL DEFAULT NULL COMMENT '经度',
            `lat` double NULL DEFAULT NULL COMMENT '纬度',
            `gps_time` datetime(0) NULL DEFAULT NULL COMMENT '坐标时间',
            `lng_wgs84` double NULL DEFAULT NULL,
            `lat_wgs84` double NULL DEFAULT NULL,
            `battery_state` int(11) NULL DEFAULT NULL COMMENT '电池状态(1-在充电 2-未充电)',
            `battery_power` int(11) NULL DEFAULT NULL COMMENT '电池电量(百分比)',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT
            '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE,
            UNIQUE INDEX `uk_emp_id`(`emp_id`) USING BTREE
            ) COMMENT ='人员硬件-最新数据';

            CREATE TABLE `app_emp_day` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `dept_id` int(11) NOT NULL COMMENT '组织机构ID',
            `emp_id` int(11) NOT NULL COMMENT '人员ID',
            `date` date NOT NULL COMMENT '日期',
            `warn_cnt` int(11) NOT NULL DEFAULT 0 COMMENT '报警次数',
            `fall_cnt` int(11) NOT NULL DEFAULT 0 COMMENT '摔倒次数',
            `drop_cnt` int(11) NOT NULL DEFAULT 0 COMMENT '跌落次数',
            `doff_cnt` int(11) NOT NULL DEFAULT 0 COMMENT '脱帽次数',
            `attend_state` int(11) NOT NULL DEFAULT 0 COMMENT '出勤状态(0-缺勤 1-出勤)',
            `start_time` datetime(0) NULL DEFAULT NULL COMMENT '上班时间',
            `end_time` datetime(0) NULL DEFAULT NULL COMMENT '下班时间',
            `work_times` int(11) NULL DEFAULT 0 COMMENT '工作时长(秒)',
            `helmet_start_time` datetime(0) NULL DEFAULT NULL COMMENT '安全帽第一次在电子围栏内时间',
            `helmet_end_time` datetime(0) NULL DEFAULT NULL COMMENT '安全帽最后一次在电子围栏内时间',
            `helmet_times` int(11) NULL DEFAULT 0 COMMENT '安全帽出勤时长(s)',
            `face_gate_start_time` datetime(0) NULL DEFAULT NULL COMMENT '闸机第一次进时间',
            `face_gate_end_time` datetime(0) NULL DEFAULT NULL COMMENT '闸机最后一次出时间',
            `face_gate_times` int(11) NULL DEFAULT 0 COMMENT '闸机出勤时长(s)',
            `attend_type` int(11) NULL DEFAULT NULL COMMENT '考勤方式',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT
            '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE,
            UNIQUE INDEX `uk_empId_date`(`emp_id`, `date`) USING BTREE
            ) COMMENT = '人员硬件-每日统计';

            CREATE TABLE `app_emp_device` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `emp_id` int(11) NOT NULL COMMENT '人员ID',
            `device_id` int(11) NOT NULL COMMENT '设备ID',
            `bind_time` datetime(0) NOT NULL,
            `bind_user` varchar(25) NULL DEFAULT NULL,
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT
            '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE,
            UNIQUE INDEX `uk_empId_deviceId`(`emp_id`, `device_id`) USING BTREE
            ) COMMENT ='人员-硬件绑定';

            CREATE TABLE `app_emp_device_log` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `emp_id` int(11) NOT NULL,
            `device_id` int(11) NOT NULL,
            `time` datetime(0) NOT NULL COMMENT '操作时间',
            `type` int(11) NOT NULL COMMENT '类型(1-绑定 2-解绑)',
            `user_id` int(11) NULL DEFAULT NULL COMMENT '操作人',
            `user_name` varchar(20) NULL DEFAULT NULL COMMENT '操作人姓名',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT
            '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE
            ) COMMENT ='人员-硬件绑定日志表';

            CREATE TABLE `app_emp_enter_record` (
            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
            `emp_id` int(11) NOT NULL,
            `dept_id` int(11) NOT NULL,
            `corp_id` int(11) NULL DEFAULT NULL,
            `type` int(11) NOT NULL COMMENT '类型（1-进场 2-出场）',
            `date` date NOT NULL COMMENT '进出场日期',
            `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
            `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
            PRIMARY KEY (`id`) USING BTREE
            ) COMMENT = '人员进出场记录表';

            CREATE TABLE `app_emp_group` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `dept_id` int(11) NOT NULL COMMENT '组织机构id',
            `corp_id` int(11) NOT NULL COMMENT '合作单位id',
            `corp_code` varchar(32) NULL DEFAULT NULL COMMENT '合作单位编码',
            `corp_name` varchar(32) NOT NULL COMMENT '合作单位名称',
            `group_name` varchar(32) NOT NULL COMMENT '班组名称',
            `group_code` varchar(16) NULL DEFAULT NULL COMMENT '班组编码',
            `responsible_person_name` varchar(16) NULL DEFAULT NULL
            COMMENT '责任人姓名',
            `responsible_person_phone` varchar(16) NULL DEFAULT NULL
            COMMENT '责任人电话',
            `responsible_idcard_type` int(11) NOT NULL DEFAULT 1 COMMENT '证件类型\n (1-居民身份证 2-澳门永久居民证 3-澳门非永久居民证
            4-澳门外地雇员身份识别证\n 5-澳门特别逗留证)',
            `responsible_idcard_number` varchar(32) NULL DEFAULT NULL
            COMMENT '责任人证件号码',
            `remark` varchar(16) NULL DEFAULT NULL COMMENT '备注',
            `entry_time` date NULL DEFAULT NULL COMMENT '进场日期',
            `exit_time` date NULL DEFAULT NULL COMMENT '退场日期',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT
            '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE
            ) COMMENT = '班组信息';

            CREATE TABLE `app_emp_reward_punishment` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `emp_id` int(11) NOT NULL COMMENT '人员ID',
            `emp_name` varchar(16) NOT NULL COMMENT '人员姓名',
            `type` int(11) NOT NULL COMMENT '类型 1-奖励 2-惩罚',
            `item` int(11) NOT NULL COMMENT '类目 1-火电气 2-气瓶 3-特种作业证 4-悬空作业 5-高处作业 6-翻斗车 7-安全帽 8-安全带 9-国家级 10-省级 11-市级',
            `description` varchar(200) NULL DEFAULT NULL COMMENT '描述',
            `date` date NOT NULL COMMENT '发生日期',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT
            '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE
            ) COMMENT ='人员-惩罚记录';

            CREATE TABLE `app_emp_warn` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `dept_id` int(11) NOT NULL COMMENT '项目ID',
            `rule_id` int(11) NOT NULL COMMENT '规则ID',
            `rule_type` int(11) NOT NULL COMMENT '规则类型',
            `trigger_time` datetime(0) NOT NULL COMMENT '触发时间',
            `trigger_object_id` varchar(32) NOT NULL COMMENT
            '触发报警的人员id',
            `lat` double NULL DEFAULT NULL COMMENT '经度',
            `lng` double NULL DEFAULT NULL COMMENT '纬度',
            `state` int(11) NOT NULL DEFAULT 0 COMMENT '报警记录状态(0-未处理 1-已处理)',
            `handle_time` datetime(0) NULL DEFAULT NULL COMMENT '处理时间',
            `handle_result` varchar(100) NULL DEFAULT NULL COMMENT
            '处理结果',
            `handle_remark` varchar(100) NULL DEFAULT NULL COMMENT
            '处理结果备注',
            `handle_user_id` int(11) NULL DEFAULT NULL COMMENT '处理人',
            `handle_user_name` varchar(32) NULL DEFAULT NULL COMMENT
            '处理人姓名',
            `handle_user_phone` varchar(20) NULL DEFAULT NULL COMMENT
            '处理人手机号',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除,1-已删除)',
            `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
            `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
            PRIMARY KEY (`id`) USING BTREE
            ) COMMENT ='人员报警';

            CREATE TABLE `app_emp_warn_rule` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(32) NOT NULL COMMENT '报警规则名称',
            `dept_id` int(11) NOT NULL COMMENT '项目ID',
            `rule_type` int(11) NOT NULL COMMENT '报警规则类型: 101-跌落报警 102-摔倒报警, 103-脱帽报警 ,104-SOS报警',
            `enable_flag` int(11) NOT NULL DEFAULT 1 COMMENT '启用标记(0-未启用 1-已启用)',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
            `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
            PRIMARY KEY (`id`) USING BTREE
            ) COMMENT ='人员报警规则';

            CREATE TABLE `app_emp_warn_rule_channel` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `rule_id` int(11) NOT NULL COMMENT '人员报警规则id',
            `msg_channel` int(11) NOT NULL COMMENT '接收方式 1-小程序 2-公众号 3-后台 4-短信',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记 0-未删除 1-已删除',
            `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
            `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
            PRIMARY KEY (`id`) USING BTREE
            )COMMENT = '人员报警接收方式';

            CREATE TABLE `app_emp_warn_rule_object` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `rule_id` int(11) NOT NULL COMMENT '报警规则id',
            `object_id` varchar(32) NOT NULL COMMENT '报警对象id',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记（0-否 1-是）',
            `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
            `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
            PRIMARY KEY (`id`) USING BTREE
            )COMMENT = '人员报警对象';

            CREATE TABLE `app_emp_warn_rule_time` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `rule_id` int(11) NOT NULL COMMENT '报警规则ID',
            `start_time` time(0) NOT NULL COMMENT '开始时间',
            `end_time` time(0) NOT NULL COMMENT '结束时间',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
            `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
            PRIMARY KEY (`id`) USING BTREE
            ) COMMENT ='人员报警时间规则';

            CREATE TABLE `app_emp_warn_rule_user` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `rule_id` int(11) NOT NULL COMMENT '设备报警规则id',
            `to_user_id` int(11) NULL DEFAULT NULL COMMENT '接收人(后台用户ID)',
            `to_user_name` varchar(32) NULL DEFAULT NULL COMMENT
            '接收人姓名',
            `to_user_phone` varchar(20) NULL DEFAULT NULL COMMENT
            '接收人手机号',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
            `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
            PRIMARY KEY (`id`) USING BTREE
            ) COMMENT = '人员报警接收人';

            CREATE TABLE `app_emp_work_type` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `dept_id` int(11) NOT NULL COMMENT '项目ID',
            `name` varchar(16) NOT NULL COMMENT '工种名称',
            `work_code` varchar(64) NULL DEFAULT NULL COMMENT '工种编码',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记,0-未删除 1-已删除',
            `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
            `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
            PRIMARY KEY (`id`) USING BTREE
            )COMMENT = '人员工种表';

            CREATE TABLE `app_face_gate` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `dept_id` int(11) NOT NULL COMMENT '组织机构ID',
            `device_key` varchar(32) NOT NULL COMMENT '设备序列号',
            `name` varchar(32) NOT NULL COMMENT '闸机名称',
            `lat` double NULL DEFAULT NULL,
            `lng` double NULL DEFAULT NULL,
            `address` varchar(64) NULL DEFAULT NULL COMMENT '坐标地址',
            `direction` int(11) NOT NULL COMMENT '方向(0-出 1-进)',
            `tag` varchar(32) NULL DEFAULT NULL COMMENT
            '设备标签,用于标记设备聚类',
            `scene_guid` varchar(32) NULL DEFAULT NULL,
            `user_guid` varchar(32) NULL DEFAULT NULL,
            `state` int(11) NULL DEFAULT NULL COMMENT '设备状态，1：未绑定，2：绑定中，3：解绑中，4：未同步，5：同步中，6：已同步，7：已禁用，8：禁用中，9：启用中',
            `status` int(11) NULL DEFAULT NULL COMMENT '设备网络状态，1：在线，2：离线',
            `version_no` varchar(32) NULL DEFAULT NULL COMMENT
            '设备应用版本号',
            `system_version_no` varchar(32) NULL DEFAULT NULL COMMENT
            '设备系统版本号',
            `app_id` varchar(32) NULL DEFAULT NULL COMMENT '应用ID',
            `reg_num` int(11) NULL DEFAULT NULL COMMENT '设备总识别次数',
            `need_upgrade_app` int(11) NULL DEFAULT NULL COMMENT '是否需要APP升级',
            `need_upgrade_system` int(11) NULL DEFAULT NULL COMMENT '是否需要系统升级',
            `need_upgrade` int(11) NULL DEFAULT NULL COMMENT '是否需要升级（包括app和系统升级）',
            `expired` int(11) NULL DEFAULT NULL COMMENT '设备是否禁用',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT
            '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE
            )COMMENT ='机构-人脸识别闸机';

            CREATE TABLE `app_face_gate_app` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `platform` varchar(64) NOT NULL COMMENT '闸机平台',
            `dept_id` int(11) NOT NULL COMMENT '组织机构id',
            `app_id` varchar(64) NULL DEFAULT NULL COMMENT '应用ID',
            `app_key` varchar(64) NULL DEFAULT NULL COMMENT '应用key',
            `app_secret` varchar(128) NULL DEFAULT NULL COMMENT '应用秘钥',
            `remark` varchar(128) NULL DEFAULT NULL COMMENT '备注',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记:0-未删除 1-已删除',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT
            '修改时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE
            ) COMMENT ='闸机平台应用';

            CREATE TABLE `app_face_gate_face` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `face_gate_id` int(11) NOT NULL COMMENT '闸机iD',
            `device_key` varchar(32) NOT NULL COMMENT '闸机序列号',
            `emp_id` int(11) NOT NULL COMMENT '人员ID',
            `person_guid` varchar(32) NOT NULL COMMENT '人员guid',
            `type` int(11) NULL DEFAULT NULL COMMENT '1：普通 RGB 照片，默认；2：红外照片， 特定设备型号使用',
            `face_guid` varchar(32) NULL DEFAULT NULL COMMENT '人脸guid',
            `face_url` varchar(255) NULL DEFAULT NULL COMMENT
            '人脸图片url',
            `valid_level` int(11) NULL DEFAULT NULL COMMENT '校验等级',
            `state` int(11) NULL DEFAULT NULL COMMENT '照片注册状态(1:上传中 2:注册中 3:注册成功 4:注册失败)',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT
            '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE
            ) COMMENT ='机构-人脸识别闸机-人脸';

            CREATE TABLE `app_face_gate_person` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `face_gate_id` int(11) NOT NULL COMMENT '闸机ID',
            `device_key` varchar(32) NOT NULL COMMENT '闸机序列号',
            `emp_id` int(11) NOT NULL COMMENT '人员ID',
            `person_guid` varchar(32) NULL DEFAULT NULL COMMENT
            '人员guid',
            `type` int(11) NULL DEFAULT NULL COMMENT '人员类型（由客户自行定义，支持正整数，0没有意义）',
            `task_type` int(11) NOT NULL DEFAULT 0 COMMENT '定时任务状态，0-未开始执行，1-人员注册完成，2-人员照片注册完成，3-人员授权完成',
            `message` varchar(64) NULL DEFAULT NULL COMMENT
            '添加考勤人员返回信息',
            `tag` varchar(32) NULL DEFAULT NULL COMMENT '人员标记（加密）',
            `pass_start_time` time(0) NULL DEFAULT NULL COMMENT '每日允许进入的时间段',
            `pass_end_time` time(0) NULL DEFAULT NULL COMMENT '每日允许进入的时间段',
            `face_permission` int(11) NULL DEFAULT NULL COMMENT '刷脸权限(1-关 2-开)',
            `card_permission` int(11) NULL DEFAULT NULL COMMENT '刷卡权限(1-关 2-开)',
            `card_face_permission` int(11) NULL DEFAULT NULL COMMENT '人卡合一权限(1-关 2-开)',
            `idcard_face_permission` int(11) NULL DEFAULT NULL COMMENT '人证比对权限(1-关 2-开)',
            `effective_time` datetime(0) NULL DEFAULT NULL COMMENT '有效期(过期删除人员)',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT
            '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE
            )COMMENT ='机构-人脸识别闸机-人员授权';

            CREATE TABLE `app_face_gate_record` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `face_gate_id` int(11) NOT NULL COMMENT '闸机ID',
            `device_key` varchar(32) NOT NULL,
            `person_guid` varchar(32) NOT NULL,
            `emp_id` int(11) NULL DEFAULT NULL,
            `emp_name` varchar(20) NULL DEFAULT NULL,
            `temperature` double NULL DEFAULT NULL,
            `photo_url` varchar(255) NOT NULL,
            `show_time` datetime(0) NULL DEFAULT NULL,
            `rec_mode` int(11) NULL DEFAULT NULL COMMENT '识别模式，1:刷脸，2:刷卡，3:双重认证， 4:人证比对 5-指纹',
            `type` int(11) NULL DEFAULT NULL COMMENT '识别出的人员类型，0：时间段内，1：时间段外，2：陌生人',
            `data` varchar(255) NULL DEFAULT NULL COMMENT '人脸附属信息',
            `id_card_info` varchar(512) NULL DEFAULT NULL COMMENT
            '人证比对详细',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT
            '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE,
            INDEX `showTime`(`show_time`) USING BTREE
            ) COMMENT = '机构-人脸识别闸机-识别结果';

            CREATE TABLE `app_fence` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `dept_id` int(11) NOT NULL COMMENT '机构ID',
            `rule_id` int(11) NULL DEFAULT NULL COMMENT '报警规则id（废弃）',
            `module_type` int(11) NOT NULL DEFAULT 1 COMMENT '业务模块（1-项目的电子围栏 2-设备报警区域）（废弃）',
            `type` int(11) NOT NULL DEFAULT 1 COMMENT '电子围栏类型(1-多边形 2-圆形)',
            `name` varchar(32) NOT NULL COMMENT '考勤区域名称',
            `polygon` polygon NULL COMMENT '多边形坐标',
            `center` point NULL COMMENT '中心点坐标',
            `radius` double NULL DEFAULT NULL COMMENT '半径(米)',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT
            '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE
            ) COMMENT = '电子围栏' ;

            CREATE TABLE `app_fence_emp` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `fence_id` int(11) NOT NULL COMMENT '人员电子围栏id(表app_fence 中id)',
            `emp_id` int(11) NOT NULL COMMENT '人员id',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记 0-未删除 1-已删除',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
            PRIMARY KEY (`id`) USING BTREE,
            UNIQUE INDEX `uk_fenceId_empId`(`fence_id`, `emp_id`) USING BTREE
            ) COMMENT ='电子围栏人员表';

            CREATE TABLE `app_payroll` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `dept_id` int(11) NOT NULL COMMENT '组织机构ID',
            `corp_id` int(11) NULL DEFAULT NULL COMMENT '合作单位ID',
            `corp_name` varchar(32) NULL DEFAULT NULL COMMENT '合作单位名称',
            `group_id` int(11) NULL DEFAULT NULL COMMENT '班组id',
            `group_name` varchar(32) NULL DEFAULT NULL COMMENT '班组名称',
            `year` int(11) NULL DEFAULT NULL,
            `month` int(11) NULL DEFAULT NULL,
            `state` int(11) NOT NULL DEFAULT 0 COMMENT '状态（0-未完成 1-已完成）',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT
            '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE
            ) COMMENT = '人员工资发放记录';

            CREATE TABLE `app_payroll_attach` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `payroll_id` int(11) NOT NULL COMMENT '发放记录ID',
            `name` varchar(64) NULL DEFAULT NULL COMMENT '附件名称',
            `url` varchar(255) NULL DEFAULT NULL COMMENT '附件地址',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT
            '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE
            ) COMMENT ='人员工资发放记录-附件';

            CREATE TABLE `app_payroll_emp` (
            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
            `payroll_id` int(11) NOT NULL COMMENT '工资发放记录id',
            `emp_id` int(11) NOT NULL COMMENT '人员id',
            `attend_days` int(11) NOT NULL COMMENT '出勤天数',
            `real_attend_days` int(11) NULL DEFAULT NULL COMMENT '实际出勤天数',
            `salary_total` double NOT NULL DEFAULT 0 COMMENT '薪水总额（元）',
            `salary_borrow` double NOT NULL DEFAULT 0 COMMENT '借款（元）',
            `salary_should` double NOT NULL DEFAULT 0 COMMENT '应发工资（元）',
            `salary_real` double NOT NULL DEFAULT 0 COMMENT '实发工资（元）',
            `date` date NULL DEFAULT NULL COMMENT '发放日期',
            `remark` varchar(50) NULL DEFAULT NULL COMMENT '备注',
            `del_flag` int(11) NOT NULL DEFAULT 0,
            `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
            `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
            PRIMARY KEY (`id`) USING BTREE
            ) COMMENT ='人员工资发放记录';

            CREATE TABLE `app_sync_detail` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `sync_id` int(11) NOT NULL COMMENT '表app_sync中的id',
            `module` int(11) NOT NULL COMMENT '模块 1-设备,2-人员,3-物资,4-环境,5-智能监控,6-特种设备,7-车辆,8-质量,9-安全,100-其它',
            `type` int(11) NULL DEFAULT NULL COMMENT '同步模块 1-合作单位 2-班组 3-人员 4-进退场 5-打卡记录 6-工资',
            `time` datetime(0) NOT NULL COMMENT '同步时间',
            `user_id` int(11) NULL DEFAULT NULL COMMENT '同步人id',
            `user_name` varchar(16) NULL DEFAULT NULL COMMENT '同步人姓名',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT
            '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE,
            UNIQUE INDEX `uk_syncId_type`(`sync_id`, `type`) USING BTREE
            )COMMENT = '数据同步详情表';

            CREATE TABLE `app_sync_record` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `object_id` int(11) NOT NULL COMMENT '同步的id',
            `platform` int(11) NOT NULL COMMENT '同步平台 1-湖南省劳务实名制 2-湖南省智慧工地 3-雄安建设',
            `type` int(11) NOT NULL COMMENT '同步模块 1-合作单位 2-班组 3-人员 4-进退场 5-打卡记录 6-工资',
            `sync_flag` int(11) NULL DEFAULT 0 COMMENT '同步标记 0-未同步 1-已同步',
            `sync_type` int(11) NULL DEFAULT 1 COMMENT '需要同步的类型 1-新增 2-修改',
            `sync_time` datetime(0) NULL DEFAULT NULL COMMENT '同步时间',
            `sync_user_id` int(11) NULL DEFAULT NULL COMMENT '同步人id',
            `sync_user_name` varchar(16) NULL DEFAULT NULL COMMENT
            '同步人姓名',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
            PRIMARY KEY (`id`) USING BTREE
            ) COMMENT ='同步记录表' ;

            CREATE TABLE `app_train` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `dept_id` int(11) NOT NULL COMMENT '机构ID',
            `train_type` int(11) NULL DEFAULT NULL COMMENT '培训类型',
            `name` varchar(32) NULL DEFAULT NULL COMMENT '培训名称',
            `date` date NULL DEFAULT NULL COMMENT '开始日期',
            `duration` double NULL DEFAULT NULL COMMENT '培训时长(小时)',
            `organizer` varchar(32) NULL DEFAULT NULL COMMENT '组织者',
            `trainer` varchar(32) NULL DEFAULT NULL COMMENT '培训人',
            `address` varchar(255) NULL DEFAULT NULL COMMENT '培训地点',
            `content` varchar(255) NULL DEFAULT NULL COMMENT '内容',
            `state` int(11) NOT NULL DEFAULT 0 COMMENT '状态（0-未完成 1-完成）',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记（0-未删除 1-已删除）',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT
            '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE
            ) COMMENT ='组织机构-培训记录';

            CREATE TABLE `app_train_emp` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `train_id` int(11) NOT NULL COMMENT '培训ID',
            `emp_id` int(11) NOT NULL COMMENT '人员ID',
            `emp_name` varchar(32) NULL DEFAULT NULL COMMENT
            '人员姓名(冗余)',
            `pass_flag` int(11) NULL DEFAULT 1 COMMENT '通过标记(1-通过 0-未通过)',
            `score` double NULL DEFAULT NULL COMMENT '分数',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT
            '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE
            ) COMMENT ='组织机构-培训人员';

        </sql>
    </changeSet>

    <changeSet id="2" author="hw">
        <comment>添加表</comment>
        <sql>
            CREATE TABLE `app_face_gate_visitor` (
            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
            `name` varchar(32) NOT NULL COMMENT '姓名',
            `phone` varchar(11) NOT NULL COMMENT '手机号',
            `face_gate_id` int(11) NOT NULL COMMENT '闸机id',
            `state` int(11) NOT NULL DEFAULT 0 COMMENT '审批状态(0-待审批，1-已通过，2-已拒绝)',
            `check_result` int(11) NULL DEFAULT NULL COMMENT '审批结果(1-已通过，2-已拒绝)',
            `check_name` varchar(32) DEFAULT NULL COMMENT '审批人',
            `check_time` datetime(0) NULL DEFAULT NULL COMMENT '审批时间',
            `remark` varchar(200) NULL DEFAULT NULL COMMENT '备注',
            `del_flag` int(11) NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT
            '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE
            ) COMMENT = '闸机访客审批' ;
        </sql>
    </changeSet>

    <changeSet id="3" author="hw">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `app_face_gate_person`
            MODIFY COLUMN `message` varchar(64) NULL COMMENT '添加考勤人员返回信息' AFTER `task_type`
        </sql>
    </changeSet>

    <changeSet id="4" author="hw">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `app_emp`
            ADD COLUMN `color` varchar(10) NULL COMMENT '绑定的硬件颜色' AFTER `sn`
        </sql>
    </changeSet>

    <changeSet id="5" author="hw">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `app_emp_day`
            ADD COLUMN `work_role_id` int NULL COMMENT '工人类型ID' AFTER `emp_id`
        </sql>
    </changeSet>

    <changeSet id="10" author="hw">
        <comment>添加字段</comment>
        <sql>
            ALTER TABLE `app_face_gate_visitor`
            MODIFY COLUMN `del_flag` int NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)' AFTER `remark`
        </sql>
    </changeSet>

    <changeSet id="11" author="sunguodong">
        <comment>增加表</comment>
        <sql>
            CREATE TABLE `app_emp_warn_rule_fence` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `dept_id` int(11) NOT NULL COMMENT '项目id',
            `rule_id` int(11) NOT NULL COMMENT '报警规则id',
            `type` int(11) NOT NULL COMMENT '电子围栏类型(1-多边形 2-圆形)',
            `polygon` polygon DEFAULT NULL COMMENT '多边形坐标',
            `center` point DEFAULT NULL COMMENT '中心点坐标',
            `radius` double DEFAULT NULL COMMENT '半径(米)',
            `del_flag` int(11) NOT NULL DEFAULT '0' COMMENT '删除标记 0-未删除 1-已删除',
            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='报警规则电子围栏';
        </sql>
    </changeSet>

    <changeSet id="12" author="sunguodong">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `app_emp_warn_rule`
            ADD COLUMN `warn_type` int(11) NOT NULL DEFAULT 1 COMMENT '报警类型 1-硬件报警 2-系统报警' AFTER `rule_type`
        </sql>
    </changeSet>

    <changeSet id="13" author="hw">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `app_face_gate_visitor`
            ADD COLUMN `picture_url` varchar(32) NULL COMMENT '照片地址' AFTER `name`,
            ADD COLUMN `visitors_type` int NULL COMMENT '访客类型（1-普通访客，2-临时工）' AFTER `face_gate_id`,
            ADD COLUMN `start_time` datetime(0) NULL COMMENT '开始时间' AFTER `visitors_type`,
            ADD COLUMN `end_time` datetime(0) NULL COMMENT '结束时间' AFTER `start_time`,
            ADD COLUMN `open_id` varchar(64) NULL COMMENT '微信唯一id' AFTER `id`
        </sql>
    </changeSet>

    <changeSet id="14" author="hw">
        <comment>新增表</comment>
        <sql>
            CREATE TABLE `app_train_attachment`  (
            `id` int NOT NULL COMMENT '主键',
            `train_id` int NOT NULL COMMENT '培训id',
            `type` int NULL DEFAULT NULL COMMENT '附件类型：1-培训照片；2-附件',
            `url` varchar(64) NULL DEFAULT NULL COMMENT '附件地址',
            `del_flag` int NOT NULL DEFAULT 0 COMMENT '删除标记（0-未删除  1-已删除）',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE
            ) COMMENT = '人员培训附件';
        </sql>
    </changeSet>

    <changeSet id="15" author="hw">
        <comment>修改字段长度</comment>
        <sql>
            ALTER TABLE `app_train_attachment`
            MODIFY COLUMN `url` varchar(256) NULL DEFAULT NULL COMMENT '附件地址' AFTER `type`,
            MODIFY COLUMN `id` int NOT NULL AUTO_INCREMENT COMMENT '主键' FIRST
        </sql>
    </changeSet>

    <changeSet id="16" author="hw">
        <comment>修改字段长度</comment>
        <sql>
            ALTER TABLE `app_face_gate_visitor`
            MODIFY COLUMN `picture_url` varchar(256) NULL COMMENT '照片地址' AFTER `name`
        </sql>
    </changeSet>

    <changeSet id="17" author="sunguodong">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `app_emp_attend_record`
            ADD COLUMN `photo` varchar(256)  NULL DEFAULT NULL  AFTER `emp_id`,
            ADD COLUMN `lat` double  NULL DEFAULT NULL  AFTER `photo`,
            ADD COLUMN `lng` double  NULL DEFAULT NULL  AFTER `lat`,
            ADD COLUMN `location` varchar(64)  NULL DEFAULT NULL  AFTER `lng`;
        </sql>
    </changeSet>

    <changeSet id="18" author="hw">
        <comment>新增字段</comment>
        <sql>
            CREATE TABLE `app_emp_apply`  (
            `id` int NOT NULL AUTO_INCREMENT,
            `dept_id` int NOT NULL COMMENT '部门ID',
            `group_id` int NULL DEFAULT NULL COMMENT '班组id',
            `group_name` varchar(64) NULL DEFAULT NULL COMMENT '班组名称',
            `corp_id` int NULL DEFAULT NULL COMMENT '合作单位ID',
            `corp_name` varchar(64) NULL DEFAULT NULL COMMENT '合作单位名称',
            `emp_name` varchar(20)  NULL DEFAULT NULL COMMENT '人员姓名',
            `phone` varchar(20)  NULL DEFAULT NULL COMMENT '电话',
            `gender` int NULL DEFAULT 1 COMMENT '性别（1-男 2-女）',
            `birthday` date NULL DEFAULT NULL COMMENT '生日',
            `id_card_no` varchar(20)  NULL DEFAULT NULL COMMENT '身份证号',
            `head_img` varchar(255)  NULL DEFAULT NULL COMMENT '身份证头像',
            `avatar` varchar(255)  NULL DEFAULT NULL COMMENT '用户头像',
            `card_type` int NOT NULL DEFAULT 1 COMMENT '证件类型 (1-居民身份证 2-澳门永久居民证 3-澳门非永久居民证 4-澳门外地雇员身份识别证 5-澳门特别逗留证)',
            `id_card_front` varchar(255)  NULL DEFAULT NULL COMMENT '身份证正面',
            `id_card_back` varchar(255)  NULL DEFAULT NULL COMMENT '身份证反面',
            `id_card_grant_org` varchar(32)  NULL DEFAULT NULL COMMENT '发证机关',
            `id_card_start_date` date NULL DEFAULT NULL COMMENT '证件有效期开始时间',
            `id_card_end_date` date NULL DEFAULT NULL COMMENT '证件有效期结束时间',
            `nation` varchar(10)  NULL DEFAULT NULL COMMENT '名族',
            `province` varchar(32)  NULL DEFAULT NULL COMMENT '省',
            `city` varchar(32)  NULL DEFAULT NULL COMMENT '市',
            `area` varchar(32)  NULL DEFAULT NULL COMMENT '区',
            `address` varchar(64)  NULL DEFAULT NULL COMMENT '地址',
            `work_role_id` int NULL DEFAULT NULL COMMENT '工人类型ID',
            `work_role_name` varchar(20)  NULL DEFAULT NULL COMMENT '工人类型名称',
            `work_type_id` int NULL DEFAULT NULL COMMENT '工种ID',
            `work_type_name` varchar(64) NULL DEFAULT NULL COMMENT '工种名称',
            `leader_flag` int NULL DEFAULT 0 COMMENT '班组长标记(1-是 0-否)',
            `enter_time` date NULL DEFAULT null COMMENT '入场时间',
            `check_result` int NULL DEFAULT 0 COMMENT '审批状态(0-待审批，1-已通过，2-已拒绝)',
            `check_name` varchar(32)  NULL DEFAULT NULL COMMENT '审批人',
            `check_time` datetime(0) NULL DEFAULT NULL COMMENT '审批时间',
            `del_flag` int NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE
            )COMMENT = '人员信息扫码录入';
        </sql>
    </changeSet>

    <changeSet id="19" author="qinzexing">
        <comment>新建表</comment>
        <sql>
            CREATE TABLE `app_face_gate_platform`  (
               `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
               `platform` varchar(20) NOT NULL COMMENT '平台编码',
               `name` varchar(30) NOT NULL COMMENT '平台名称',
               `factory` varchar(50) NULL COMMENT '厂家信息',
               `remark` varchar(100) NULL COMMENT '备注',
               `state` int(11) NOT NULL COMMENT '闸机状态  0-离线闸机  1-在线闸机',
               `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记（0-未删除 1-已删除）',
               `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
               `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
               PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '平台闸机-品牌库';

            INSERT INTO `app_face_gate_platform` (`platform`, `name`, `state`) VALUES ('wheatsunshine','杭州新爱(平台)', 1);
            INSERT INTO `app_face_gate_platform` (`platform`, `name`, `state`) VALUES ('wotu','宇泛智能(沃土平台)', 1);
            INSERT INTO `app_face_gate_platform` (`platform`, `name`, `state`) VALUES ('wo','宇泛智能(沃平台)', 1);
            INSERT INTO `app_face_gate_platform` (`platform`, `name`, `state`) VALUES ('yfoffline','宇泛智能(离线)', 0);
            INSERT INTO `app_face_gate_platform` (`platform`, `name`, `state`) VALUES ('zk','中控智慧(离线)', 0);
            INSERT INTO `app_face_gate_platform` (`platform`, `name`, `state`) VALUES ('fcr','深圳玉川(离线)', 0);
            INSERT INTO `app_face_gate_platform` (`platform`, `name`, `state`) VALUES ('rec','云起智能(离线)', 0);
            INSERT INTO `app_face_gate_platform` (`platform`, `name`, `state`) VALUES ('debao','德宝智能(离线)', 0);
            INSERT INTO `app_face_gate_platform` (`platform`, `name`, `state`) VALUES ('offline','其他品牌(离线)', 0);
        </sql>
    </changeSet>

    <changeSet id="20" author="qinzexing">
        <comment>表调整</comment>
        <sql>
            ALTER TABLE app_face_gate_app RENAME app_face_gate_config;

            ALTER TABLE `app_face_gate_config`
                MODIFY COLUMN `dept_id` int NOT NULL COMMENT '组织机构id' AFTER `id`,
                ADD COLUMN `params` text NULL COMMENT '其它参数' AFTER `app_secret`,
                COMMENT = '项目闸机-参数配置';

            ALTER TABLE `app_face_gate`
                ADD COLUMN `platform` varchar(64) NOT NULL COMMENT '平台编码' AFTER `device_key`,
                MODIFY COLUMN `scene_guid` varchar(32) NULL COMMENT '（废弃）' AFTER `status`,
                MODIFY COLUMN `user_guid` varchar(32) NULL COMMENT '（废弃）' AFTER `scene_guid`,
                MODIFY COLUMN `version_no` varchar(32) NULL COMMENT '（废弃）设备应用版本号' AFTER `user_guid`,
                MODIFY COLUMN `system_version_no` varchar(32) NULL COMMENT '（废弃）设备系统版本号' AFTER `version_no`,
                MODIFY COLUMN `app_id` varchar(32) NULL COMMENT '（废弃）应用ID' AFTER `system_version_no`,
                MODIFY COLUMN `reg_num` int NULL COMMENT '（废弃）设备总识别次数' AFTER `app_id`,
                MODIFY COLUMN `need_upgrade_app` int NULL COMMENT '（废弃）是否需要APP升级' AFTER `reg_num`,
                MODIFY COLUMN `need_upgrade_system` int NULL COMMENT '（废弃）是否需要系统升级' AFTER `need_upgrade_app`,
                MODIFY COLUMN `need_upgrade` int NULL COMMENT '（废弃）是否需要升级（包括app和系统升级）' AFTER `need_upgrade_system`,
                MODIFY COLUMN `expired` int NULL COMMENT '（废弃）设备是否禁用' AFTER `need_upgrade`,
                COMMENT = '项目闸机';


            ALTER TABLE `app_face_gate_face`
                ADD COLUMN `dept_id` int NOT NULL COMMENT '组织机构ID' AFTER `id`,
                COMMENT = '项目闸机-授权人员照片';


            ALTER TABLE `app_face_gate_person`
                ADD COLUMN `dept_id` int NOT NULL COMMENT '组织机构ID' AFTER `id`,
                MODIFY COLUMN `type` int NULL COMMENT '（废弃）人员类型（由客户自行定义，支持正整数，0没有意义）' AFTER `message`,
                MODIFY COLUMN `tag` varchar(32) NULL COMMENT '（废弃）人员标记（加密）' AFTER `type`,
                MODIFY COLUMN `pass_start_time` time NULL COMMENT '（废弃）每日允许进入的时间段' AFTER `tag`,
                MODIFY COLUMN `pass_end_time` time NULL COMMENT '（废弃）每日允许进入的时间段' AFTER `pass_start_time`,
                MODIFY COLUMN `face_permission` int NULL COMMENT '（废弃）刷脸权限(1-关 2-开)' AFTER `pass_end_time`,
                MODIFY COLUMN `card_permission` int NULL COMMENT '（废弃）刷卡权限(1-关 2-开)' AFTER `face_permission`,
                MODIFY COLUMN `card_face_permission` int NULL COMMENT '（废弃）人卡合一权限(1-关 2-开)' AFTER `card_permission`,
                MODIFY COLUMN `idcard_face_permission` int NULL COMMENT '（废弃）人证比对权限(1-关 2-开)' AFTER `card_face_permission`,
                MODIFY COLUMN `effective_time` datetime NULL COMMENT '（废弃）有效期(过期删除人员)' AFTER `idcard_face_permission`,
                COMMENT = '项目闸机-授权人员';


            ALTER TABLE `app_face_gate_record`
                ADD COLUMN `dept_id` int NOT NULL COMMENT '组织机构ID' AFTER `id`,
                MODIFY COLUMN `device_key` varchar(32) NOT NULL COMMENT '闸机序列号' AFTER `face_gate_id`,
                MODIFY COLUMN `person_guid` varchar(32) NOT NULL COMMENT '人员编号' AFTER `device_key`,
                MODIFY COLUMN `emp_id` int NULL COMMENT '人员ID' AFTER `person_guid`,
                MODIFY COLUMN `emp_name` varchar(20) NULL COMMENT '人员姓名' AFTER `emp_id`,
                MODIFY COLUMN `temperature` double NULL COMMENT '温度' AFTER `emp_name`,
                MODIFY COLUMN `photo_url` varchar(255) NOT NULL COMMENT '照片' AFTER `temperature`,
                MODIFY COLUMN `show_time` datetime NULL COMMENT '识别时间' AFTER `photo_url`,
                MODIFY COLUMN `data` varchar(255) NULL COMMENT '（废弃）人脸附属信息' AFTER `type`,
                MODIFY COLUMN `id_card_info` varchar(512) NULL COMMENT '（废弃）人证比对详细' AFTER `data`,
                COMMENT = '项目闸机-识别记录';

        </sql>
    </changeSet>

    <changeSet id="21" author="qinzexing">
        <comment>新建表</comment>
        <sql>
            CREATE TABLE `app_train_paper`  (
                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                `train_id` int(11) NOT NULL COMMENT '培训记录ID',
                `exam_no` varchar(32) NULL COMMENT '试卷编号',
                `group_no` int(11) NULL COMMENT '批次号',
                `course_id` varchar(32) NULL COMMENT '课程id',
                `order` int(11) NULL COMMENT '顺序号',
                `qsn_code` varchar(32) NULL COMMENT '题库编码',
                `qsn_file_name` varchar(32) NULL COMMENT '题库文件名称',
                `qsn_answer` varchar(50) NULL COMMENT '题库答案',
                `qsn_category` int(11) NULL COMMENT '题库类型（1.文字题、2.多媒体题、3.图片题）',
                `qsn_kind` int(11) NULL COMMENT '试题类型（(1:单选 2:多选 3:判断）',
                `qsn_important` int(11) NULL COMMENT '重要程度 (0:容易 1:一般 2:困难)',
                `source` int(11) NULL COMMENT '来源 0：系统；1：用户',
                `version` int(11) NULL COMMENT '版本',
                `upload_time` datetime NULL COMMENT '上传时间',
                `content_id` int(11) NULL COMMENT '试题id',
                `qsn_content` text NULL COMMENT '试题内容',
                `desc` varchar(255) NULL COMMENT '说明',
                `analysis` text NULL COMMENT '试题解析',
                `del_flag` int NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
                `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '培训试卷';
        </sql>

        <sql>
            CREATE TABLE `app_train_fail_data`  (
               `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
               `type` int NOT NULL COMMENT '培训数据类型',
               `data` text NOT NULL COMMENT '数据(json)',
               `error_msg` text NULL COMMENT '错误信息',
               `del_flag` int NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
               `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
               `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
               PRIMARY KEY (`id`) USING BTREE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '培训同步失败数据';
        </sql>
    </changeSet>

    <changeSet id="22" author="qinzexing">
        <comment>增加字段</comment>
        <sql>
            ALTER TABLE `app_train`
            ADD COLUMN `guid` varchar(32) NULL COMMENT '培训记录guid' AFTER `id`,
            ADD COLUMN `source` varchar(10) NOT NULL DEFAULT 'ms' COMMENT '来源（ms-后台  mp-小程序 train_box-培训箱 other-其它）' AFTER `state`;

            ALTER TABLE `app_train_emp`
            MODIFY COLUMN `emp_id` int(11) NULL COMMENT '人员ID' AFTER `train_id`;
        </sql>
    </changeSet>

    <changeSet id="23" author="qinzexing">
        <comment>修改字段长度</comment>
        <sql>
            ALTER TABLE `app_train`
                MODIFY COLUMN `guid` varchar(64) NULL COMMENT '培训记录guid' AFTER `id`;
        </sql>
    </changeSet>

    <changeSet id="24" author="qinzexing">
        <comment>新增表</comment>
        <sql>
            CREATE TABLE `app_emp_health_report`  (
             `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
             `emp_id` int(11) NOT NULL COMMENT '人员ID',
             `type` varchar(20) NOT NULL COMMENT 'healthCode-健康码  journeyCard-行程卡 testRecord-核算记录  vaccination-疫苗接种',
             `date` date NULL COMMENT '日期',
             `test_result` int(11) NULL COMMENT '检测状态  1-阴性  2-阳性',
             `code_state` varchar(10) NULL COMMENT '健康码状态 green-绿码  red-红码  yellow-黄码  orange-橙码',
             `journey` varchar(255) NULL COMMENT '行程',
             `exp_date` date NULL COMMENT '失效日期',
             `first_time` datetime NULL COMMENT '第一次接种时间',
             `second_time` datetime NULL COMMENT '第二次接种时间',
             `del_flag` int NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
             `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
             `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
             PRIMARY KEY (`id`) USING BTREE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '人员防疫信息';

            CREATE TABLE `app_emp_health_report_img`  (
             `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
             `health_report_id` int NOT NULL COMMENT '防疫信息ID',
             `img_url` varchar(255) NOT NULL COMMENT '图片地址',
             `del_flag` int NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
             `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
             `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
             PRIMARY KEY (`id`) USING BTREE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '人员防疫信息图片';
        </sql>
    </changeSet>

    <changeSet id="25" author="qinzexing">
        <comment>新建表</comment>
        <sql>
            CREATE TABLE `app_emp_confess`  (
             `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
             `emp_id` int NOT NULL COMMENT '人员ID',
             `project_name` varchar(30) NOT NULL COMMENT '工程名称',
             `date` date NOT NULL COMMENT '交底时间',
             `corp_id` int NULL COMMENT '施工单位ID',
             `corp_name` varchar(30) NULL COMMENT '施工单位',
             `project_part` varchar(50) NULL COMMENT '分项工程',
             `principal_id` int NULL COMMENT '负责人ID',
             `principal_name` varchar(30) NULL COMMENT '负责人名称',
             `confidant_id` int NULL COMMENT '交底人ID',
             `confidant_name` varchar(30) NULL COMMENT '交底人名称',
             `be_confidant_id` int NULL COMMENT '被交底人ID',
             `be_confidant_name` varchar(30) NULL COMMENT '被交底人名称',
             `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记（0-未删除 1-已删除）',
             `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
             `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
             PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '人员安全交底';
        </sql>
    </changeSet>

    <changeSet id="26" author="xuguocheng">
        <comment>设置默认值</comment>
        <sql>
            ALTER TABLE `app_emp_day`
            MODIFY COLUMN `work_role_id`  int(11) NULL DEFAULT 2 COMMENT '工人类型ID' AFTER `emp_id`;
        </sql>
    </changeSet>

    <changeSet id="27" author="qinzexing">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `app_train_emp`
            ADD COLUMN `train_period` int(11) NULL COMMENT '培训学时' AFTER `score`,
            ADD COLUMN `total_score` int(11) NULL COMMENT '总分' AFTER `train_period`,
            ADD COLUMN `pass_score` int(11) NULL COMMENT '及格分' AFTER `total_score`,
            ADD COLUMN `exam_no` varchar(32) NULL COMMENT '试卷编号' AFTER `pass_flag`,
            ADD COLUMN `exam_count` int(11) NULL COMMENT '考试次数' AFTER `exam_no`,
            ADD COLUMN `sign_img_url` varchar(255) NULL COMMENT '签名图片' AFTER `exam_count`;
        </sql>
    </changeSet>

    <changeSet id="28" author="qinzexing">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `app_emp_group`
             ADD COLUMN `fvs_device_id` int(11) NULL COMMENT '监控设备ID' AFTER `exit_time`,
             ADD COLUMN `fvs_device_name` varchar(32) NULL COMMENT '监控设备名称' AFTER `fvs_device_id`,
             ADD COLUMN `lng` double NULL COMMENT '经度' AFTER `fvs_device_name`,
             ADD COLUMN `lat` double NULL COMMENT '纬度' AFTER `lng`,
             ADD COLUMN `address` varchar(255) NULL COMMENT '位置信息' AFTER `lat`;
        </sql>
    </changeSet>

    <changeSet id="29" author="qinzexing">
        <comment></comment>
        <sql>
            ALTER TABLE `app_emp_group`
            DROP COLUMN `lng`,
            DROP COLUMN `lat`,
            DROP COLUMN `address`;
        </sql>
    </changeSet>

    <changeSet id="30" author="qinzexing">
        <comment>修改字段长度</comment>
        <sql>
            ALTER TABLE `app_train_emp`
                MODIFY COLUMN `exam_no` varchar(64) NULL COMMENT '试卷编号' AFTER `pass_flag`;

            ALTER TABLE `app_train_paper`
                MODIFY COLUMN `exam_no` varchar(64) NULL COMMENT '试卷编号' AFTER `train_id`,
                MODIFY COLUMN `course_id` varchar(64) NULL COMMENT '课程id' AFTER `group_no`,
                MODIFY COLUMN `qsn_code` varchar(64) NULL COMMENT '题库编码' AFTER `order`,
                MODIFY COLUMN `qsn_file_name` varchar(50) NULL COMMENT '题库文件名称' AFTER `qsn_code`,
                MODIFY COLUMN `qsn_answer` varchar(255) NULL COMMENT '题库答案' AFTER `qsn_file_name`;
        </sql>
    </changeSet>

    <changeSet id="31" author="qinzexing">
        <comment>修改字段长度</comment>
        <sql>
            ALTER TABLE `app_train`
            MODIFY COLUMN `name` varchar(100) NULL COMMENT '培训名称' AFTER `train_type`,
            MODIFY COLUMN `content` text NULL COMMENT '内容' AFTER `address`;
        </sql>
    </changeSet>

    <changeSet id="32" author="qinzexing">
        <comment>修改字段长度</comment>
        <sql>
            ALTER TABLE `app_emp`
                ADD COLUMN `enter_train_flag` int(11) NOT NULL DEFAULT 0 COMMENT '是否参加入场三级教育（0-未参加  1-已参加）' AFTER `key_position_auth`;
        </sql>
    </changeSet>

    <changeSet id="33" author="qinzexing">
        <comment>新增表</comment>
        <sql>
            CREATE TABLE `app_emp_risk`  (
             `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
             `dept_id` int(11) NOT NULL COMMENT '组织机构ID',
             `name` varchar(50) NOT NULL COMMENT '风险告知书名称',
             `file_url` varchar(255) NOT NULL COMMENT '风险告知书地址',
             `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记（0-未删除 1-已删除）',
             `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
             `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
             PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '人员风险告知书';


            CREATE TABLE `app_emp_risk_emp`  (
             `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
             `risk_id` int(11) NOT NULL COMMENT '风险告知书ID',
             `emp_id` int(11) NOT NULL COMMENT '人员ID',
             `sign_time` datetime NULL COMMENT '签名时间',
             `sign_img_url` varchar(255) NULL COMMENT '签名图片',
             `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记（0-未删除 1-已删除）',
             `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
             `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
             PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '风险告知书关联人员';
        </sql>
    </changeSet>

    <changeSet id="34" author="qinzexing">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `app_emp_confess`
                ADD COLUMN `sign_img_url` varchar(255) NULL COMMENT '签名图片' AFTER `be_confidant_name`;
        </sql>
    </changeSet>

    <changeSet id="35" author="qinzexing">
        <comment>新建表</comment>
        <sql>
            CREATE TABLE `app_emp_confess_img`  (
             `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
             `confess_id` int NOT NULL COMMENT '安全交底ID',
             `img_url` varchar(255) NOT NULL COMMENT '图片地址',
             `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记（0-未删除 1-已删除）',
             `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
             `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
             PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '安全交底图片';


            CREATE TABLE `app_train_emp_img`  (
             `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
             `train_emp_id` int NOT NULL COMMENT '培训人员关联表ID',
             `img_url` varchar(255) NOT NULL COMMENT '图片地址',
             `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记（0-未删除 1-已删除）',
             `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
             `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
             PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '培训人员图片';


            CREATE TABLE `app_payroll_emp_img`  (
             `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
             `payroll_emp_id` int NOT NULL COMMENT '工资人员关联表ID',
             `img_url` varchar(255) NOT NULL COMMENT '图片地址',
             `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记（0-未删除 1-已删除）',
             `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
             `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
             PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '人员工资图片';
        </sql>
    </changeSet>

    <changeSet id="36" author="hw">
        <comment>添加表字段</comment>
        <sql>
            ALTER TABLE `app_payroll`
                ADD COLUMN `clearing_form` int NULL DEFAULT 1 COMMENT '结算方式；1-工时，2-工程量' AFTER `corp_name`;
            ALTER TABLE `app_payroll_emp`
                ADD COLUMN `unit_price` double NULL COMMENT '单价' AFTER `real_attend_days`;
            ALTER TABLE `app_payroll_emp`
                ADD COLUMN `salary_deduct` double NULL COMMENT '扣款' AFTER `salary_borrow`;
            ALTER TABLE `app_payroll_emp`
                ADD COLUMN `work_amount` int NULL COMMENT '工程量' AFTER `unit_price`;
        </sql>
    </changeSet>

    <changeSet id="37" author="hw">
        <comment>添加表字段</comment>
        <sql>
            ALTER TABLE `app_train`
                ADD COLUMN `organizer_id` int NULL COMMENT '组织者Id' AFTER `duration`,
                ADD COLUMN `trainer_id` int NULL COMMENT '培训人Id' AFTER `organizer`;
        </sql>
    </changeSet>

    <changeSet id="38" author="xuguocheng">
        <comment>新增索引</comment>
        <sql>
            ALTER TABLE `app_emp_day`
            ADD INDEX `nk_deptId_date` (`dept_id`, `date`) USING BTREE ;
        </sql>
    </changeSet>

    <changeSet id="39" author="xuguocheng">
        <comment>整理字段顺序</comment>
        <sql>
            ALTER TABLE `app_emp_attend_record`
            MODIFY COLUMN `dept_id`  int(11) NOT NULL AFTER `id`,
            MODIFY COLUMN `time`  datetime NOT NULL COMMENT '时间' AFTER `emp_id`,
            MODIFY COLUMN `type`  int(11) NOT NULL COMMENT '方式 1-电子围栏 2-闸机' AFTER `time`,
            MODIFY COLUMN `direction`  int(11) NOT NULL COMMENT '方向 0-出 1-进' AFTER `type`,
            MODIFY COLUMN `name`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '电子围栏或者闸机名称' AFTER `photo`,
            MODIFY COLUMN `lng`  double NULL DEFAULT NULL AFTER `name`;
        </sql>
    </changeSet>

    <changeSet id="40" author="xuguocheng">
        <comment>增加索引</comment>
        <sql>
            ALTER TABLE `app_face_gate_record`
            MODIFY COLUMN `show_time`  datetime NOT NULL COMMENT '识别时间' AFTER `device_key`,
            MODIFY COLUMN `type`  int(11) NULL DEFAULT 1 COMMENT '识别出的人员类型，1-员工  2-访客 3-陌生人' AFTER `emp_name`,
            MODIFY COLUMN `rec_mode`  int(11) NULL DEFAULT NULL COMMENT '识别模式，1:刷脸，2:刷卡，3:双重认证， 4:人证比对' AFTER `type`,
            MODIFY COLUMN `photo_url`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '照片' AFTER `rec_mode`,
            ADD INDEX `idx_deptId_time` (`dept_id`, `show_time`) USING BTREE ;
        </sql>
    </changeSet>

    <changeSet id="41" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `app_face_gate_visitor`
            ADD COLUMN `id_card_no`  varchar(18) NULL COMMENT '身份证号码' AFTER `phone`;
        </sql>
    </changeSet>

    <changeSet id="42" author="xuguocheng">
        <comment>删除无用字段</comment>
        <sql>
            ALTER TABLE `app_face_gate`
            DROP COLUMN `status`,
            DROP COLUMN `scene_guid`,
            DROP COLUMN `user_guid`,
            DROP COLUMN `version_no`,
            DROP COLUMN `system_version_no`,
            DROP COLUMN `app_id`,
            DROP COLUMN `reg_num`,
            DROP COLUMN `need_upgrade_app`,
            DROP COLUMN `need_upgrade_system`,
            DROP COLUMN `need_upgrade`,
            DROP COLUMN `expired`,
            MODIFY COLUMN `direction`  int(11) NOT NULL COMMENT '方向(0-出 1-进)' AFTER `name`,
            MODIFY COLUMN `lng`  double NULL DEFAULT NULL AFTER `direction`,
            MODIFY COLUMN `state`  int(11) NULL DEFAULT NULL COMMENT '设备网络状态，1：在线，2：离线' AFTER `tag`;
        </sql>
    </changeSet>

    <changeSet id="43" author="xuguocheng">
        <comment>清理字段</comment>
        <sql>
            ALTER TABLE `app_face_gate_person`
            DROP COLUMN `type`,
            DROP COLUMN `tag`,
            DROP COLUMN `pass_start_time`,
            DROP COLUMN `pass_end_time`,
            DROP COLUMN `face_permission`,
            DROP COLUMN `card_permission`,
            DROP COLUMN `card_face_permission`,
            DROP COLUMN `idcard_face_permission`,
            DROP COLUMN `effective_time`,
            MODIFY COLUMN `task_type`  int(11) NOT NULL DEFAULT 0 COMMENT '授权执行状态，0-未开始执行，1-人员注册完成，2-人员照片注册完成，3-人员授权完成' AFTER `person_guid`,
            MODIFY COLUMN `message`  varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '授权反馈信息' AFTER `task_type`;
        </sql>
    </changeSet>

    <changeSet id="44" author="xuguocheng">
        <comment>新增表</comment>
        <sql>
            CREATE TABLE `app_emp_dept_day` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `dept_id` int(11) NOT NULL COMMENT '项目ID',
            `date` date NOT NULL COMMENT '日期',
            `total_emp_num` int(11) NOT NULL DEFAULT '0' COMMENT '在职人数',
            `manager_num` int(11) NOT NULL DEFAULT '0' COMMENT '管理人数',
            `worker_num` int(11) NOT NULL DEFAULT '0' COMMENT '工人人数',
            `plan_attend_num` int(11) NOT NULL DEFAULT '0' COMMENT '计划出勤人数',
            `actual_attend_num` int(11) NOT NULL DEFAULT '0' COMMENT '实际出勤人数',
            `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间吧',
            PRIMARY KEY (`id`),
            UNIQUE KEY `uk_deptId_date` (`dept_id`,`date`) USING BTREE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目每日统计';
        </sql>
    </changeSet>

    <changeSet id="45" author="xuguocheng">
        <comment>设置默认值</comment>
        <sql>
            ALTER TABLE `app_emp`
            MODIFY COLUMN `work_role_id`  int(11) NULL DEFAULT 2 COMMENT '工人类型ID' AFTER `emp_code`,
            MODIFY COLUMN `work_role_name`  varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '建筑工人' COMMENT '工人类型名称' AFTER `work_role_id`;
        </sql>
    </changeSet>

    <changeSet id="46" author="xuguocheng">
        <comment>新增索引</comment>
        <sql>
            ALTER TABLE `app_emp`
            ADD INDEX `idx_dept_id` (`dept_id`) USING BTREE ;
        </sql>
    </changeSet>

    <changeSet id="47" author="qinzexing">
        <comment>新建表</comment>
        <sql>
            CREATE TABLE `app_fence_time`  (
            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
            `fence_id` int(11) NOT NULL COMMENT '电子围栏ID',
            `start_time` time NOT NULL COMMENT '开始时间',
            `end_time` time NOT NULL COMMENT '结束时间',
            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
            `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            PRIMARY KEY (`id`)
            ) ENGINE = InnoDB DEFAULT CHARACTER SET = utf8mb4 COMMENT = '电子围栏时间';
        </sql>
    </changeSet>

    <changeSet id="48" author="xuguocheng">
        <comment>新建表</comment>
        <sql>
            CREATE TABLE `app_sync`  (
             `id` int(11) NOT NULL AUTO_INCREMENT,
             `dept_id` int(11) NOT NULL COMMENT '项目id',
             `platform` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台编号',
             `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '平台名称',
             `app_key` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '平台凭证',
             `app_secret` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '秘钥',
             `host` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务地址',
             `token` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接口token',
             `ext_1` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '扩展字段1',
             `ext_2` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '扩展字段2',
             `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记:0-未删除 1-已删除',
             `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
             `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
             PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '应用同步信息表' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="49" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `app_emp`
                ADD COLUMN `platform` varchar(32) NULL COMMENT '绑定的硬件平台' AFTER `device_id`;
        </sql>
    </changeSet>

    <changeSet id="50" author="xuguocheng">
        <comment>调整字段顺序</comment>
        <sql>
            ALTER TABLE `app_emp`
                MODIFY COLUMN `emp_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '人员编号' AFTER `ename`,
                MODIFY COLUMN `id_card_no` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '身份证号' AFTER `work_card_end_date`,
                MODIFY COLUMN `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户头像' AFTER `id_card_end_date`,
                MODIFY COLUMN `head_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '身份证头像' AFTER `avatar`;
        </sql>
    </changeSet>

    <changeSet id="51" author="xuguocheng">
        <comment>删除废弃字段</comment>
        <sql>
            ALTER TABLE `app_emp`
            DROP COLUMN `project_id`;
        </sql>
    </changeSet>

    <changeSet id="52" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `app_emp_contract`
                ADD COLUMN `dept_id` int NULL COMMENT '项目ID' AFTER `id`,
                MODIFY COLUMN `emp_id` int(11) NOT NULL COMMENT '人员ID' AFTER `id`,
                MODIFY COLUMN `pay_type` int(11) NULL DEFAULT NULL COMMENT '工资核定方式: 1-按小时 2-按天 3-按月 ' AFTER `state`,
                ADD COLUMN `pay_way` varchar(32) NULL COMMENT '工资发放方式' AFTER `pay_type`;
        </sql>
    </changeSet>

    <changeSet id="53" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `app_payroll`
                MODIFY COLUMN `year` int(11) NULL DEFAULT NULL COMMENT '年份' AFTER `group_name`,
                MODIFY COLUMN `month` int(11) NULL DEFAULT NULL COMMENT '月份' AFTER `year`,
                ADD COLUMN `start_date` date NULL COMMENT '开始日期' AFTER `month`,
                ADD COLUMN `end_date` date NULL COMMENT '结束日期' AFTER `start_date`;
        </sql>
    </changeSet>

    <changeSet id="54" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `app_train`
                ADD COLUMN `train_code` varchar(32) NULL COMMENT '培训类型-编码' AFTER `train_type`,
                ADD COLUMN `train_name` varchar(32) NULL COMMENT '培训类型-名称' AFTER `train_code`;
        </sql>
    </changeSet>

    <changeSet id="55" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `app_train`
                CHANGE COLUMN `train_code` `train_type_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '培训类型-编码' AFTER `train_type`,
                CHANGE COLUMN `train_name` `train_type_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '培训类型-名称' AFTER `train_type_code`;
        </sql>
    </changeSet>

    <changeSet id="56" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `app_sync`
                ADD COLUMN `ext_3` varchar(64) NULL COMMENT '扩展字段3' AFTER `ext_2`;
        </sql>
    </changeSet>

    <changeSet id="57" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `app_face_gate`
                ADD COLUMN `model` varchar(32) NULL COMMENT '型号' AFTER `direction`,
                MODIFY COLUMN `lng` double NULL DEFAULT NULL COMMENT '经度' AFTER `direction`,
                MODIFY COLUMN `lat` double NULL DEFAULT NULL COMMENT '纬度' AFTER `lng`;
        </sql>
    </changeSet>

    <changeSet id="58" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `app_face_gate`
                ADD COLUMN `time` datetime NULL COMMENT '设备数据更新时间' AFTER `state`;
        </sql>
    </changeSet>

    <changeSet id="59" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `app_emp`
                ADD COLUMN `verify_state` int NULL COMMENT '人证核验状态:0-未验证 1-成功 2-失败' AFTER `enter_train_flag`;
        </sql>
    </changeSet>

    <changeSet id="60" author="xuguocheng">
        <comment>修改字段</comment>
        <sql>
            ALTER TABLE `app_emp`
                MODIFY COLUMN `work_card_no` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '工作证编号' AFTER `card_type`,
                MODIFY COLUMN `id_card_no` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '身份证号' AFTER `work_card_end_date`;
        </sql>
    </changeSet>

    <changeSet id="61" author="xuguocheng">
        <comment>新建表</comment>
        <sql>
            CREATE TABLE `app_indoor_position_map`  (
                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                `dept_id` int(11) NOT NULL COMMENT '项目ID',
                `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '地图名称',
                `guid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '唯一编码',
                `pixel_width` double(10, 2) NULL DEFAULT NULL COMMENT '像素宽带',
                `pixel_length` double(10, 2) NULL DEFAULT NULL COMMENT '像素长度',
                `real_width` double(10, 2) NULL DEFAULT NULL COMMENT '实际宽度',
                `real_length` double(10, 2) NULL DEFAULT NULL COMMENT '实际长度',
                `rotation` double(10, 2) NULL DEFAULT NULL COMMENT '正北偏角',
                `img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地图图片',
                `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
                `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '室内定位-地图' ROW_FORMAT = Dynamic;

            CREATE TABLE `app_indoor_position_station`  (
                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                `dept_id` int(11) NOT NULL COMMENT '项目ID',
                `guid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '唯一标识',
                `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
                `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码',
                `map_id` int(11) NULL DEFAULT NULL COMMENT '地图ID',
                `x` double(10, 2) NULL DEFAULT NULL COMMENT 'x轴坐标',
                `y` double(10, 2) NULL DEFAULT NULL COMMENT 'y轴坐标',
                `z` double(10, 2) NULL DEFAULT NULL COMMENT 'z轴坐标',
                `lng` double(10, 2) NULL DEFAULT NULL COMMENT '经度',
                `lat` double(10, 2) NULL DEFAULT NULL COMMENT '纬度',
                `status` int(11) NOT NULL DEFAULT 0 COMMENT '状态:0-离线 1-在线',
                `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
                `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '室内定位-基站' ROW_FORMAT = Dynamic;

            CREATE TABLE `app_indoor_position_tag`  (
                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                `dept_id` int(11) NOT NULL COMMENT '项目ID',
                `guid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '唯一编码',
                `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码',
                `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
                `bind_flag` int(11) NOT NULL DEFAULT 0 COMMENT '绑定标记:0-未绑定 1-已绑定',
                `group_id` int(11) NULL DEFAULT NULL COMMENT '分组ID',
                `group_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分组名称',
                `emp_id` int(11) NULL DEFAULT NULL COMMENT '人员ID',
                `emp_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '人员姓名',
                `phone` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '人员手机号',
                `idCardNo` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '人员身份证',
                `status` int(11) NOT NULL DEFAULT 0 COMMENT '状态:0-离线 1-在线',
                `time` datetime(0) NULL DEFAULT NULL COMMENT '数据通信时间',
                `map_id` int(11) NULL DEFAULT NULL COMMENT '地图ID',
                `x` double(10, 2) NULL DEFAULT NULL COMMENT 'x轴坐标',
                `y` double(10, 2) NULL DEFAULT NULL COMMENT 'y轴坐标',
                `lng` double(10, 2) NULL DEFAULT NULL COMMENT '经度',
                `lat` double(10, 2) NULL DEFAULT NULL COMMENT '纬度',
                `distance` int(11) NULL DEFAULT NULL COMMENT '距离',
                `battery_power` int(11) NULL DEFAULT 0 COMMENT '电量',
                `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
                `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '室内定位-标签' ROW_FORMAT = Dynamic;

            CREATE TABLE `app_indoor_position_tag_log`  (
                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                `dept_id` int(11) NOT NULL COMMENT '项目ID',
                `tag_id` int(11) NOT NULL COMMENT '标签ID',
                `map_id` int(11) NOT NULL COMMENT '地图ID',
                `time` datetime(0) NOT NULL COMMENT '时间',
                `x` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'x轴坐标',
                `y` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'y轴坐标',
                `lng` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '经度',
                `lat` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '纬度',
                `distance` int(11) NULL DEFAULT NULL COMMENT '距离',
                `battery_power` int(11) NULL DEFAULT NULL COMMENT '电量',
                `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
                `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE,
                INDEX `idx_tagId_time`(`tag_id`, `time`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="62" author="xuguocheng">
        <comment>修改字段</comment>
        <sql>
            ALTER TABLE `app_face_gate_record`
                MODIFY COLUMN `photo_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '照片' AFTER `rec_mode`;
        </sql>
    </changeSet>

    <changeSet id="63" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `app_indoor_position_tag_log`
                ADD COLUMN `station_id` int NOT NULL COMMENT '基站ID' AFTER `map_id`;
            ALTER TABLE `app_indoor_position_tag`
                ADD COLUMN `station_id` int NULL COMMENT '基站ID' AFTER `map_id`;
        </sql>
    </changeSet>

    <changeSet id="64" author="xuguocheng">
        <comment>修改字段</comment>
        <sql>
            ALTER TABLE `app_indoor_position_tag_log`
                MODIFY COLUMN `x` double(10, 2) NULL DEFAULT NULL COMMENT 'x轴坐标' AFTER `time`,
                MODIFY COLUMN `y` double(10, 2) NULL DEFAULT NULL COMMENT 'y轴坐标' AFTER `x`,
                MODIFY COLUMN `lng` double(10, 2) NULL DEFAULT NULL COMMENT '经度' AFTER `y`,
                MODIFY COLUMN `lat` double(10, 2) NULL DEFAULT NULL COMMENT '纬度' AFTER `lng`;
        </sql>
    </changeSet>

    <changeSet id="65" author="xuguocheng">
        <comment>修改字段长度</comment>
        <sql>
            ALTER TABLE `app_emp`
                MODIFY COLUMN `emp_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '人员姓名' AFTER `group_name`,
                MODIFY COLUMN `ename` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '姓名拼音' AFTER `emp_name`;

            ALTER TABLE `app_face_gate_record`
                MODIFY COLUMN `emp_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '人员姓名' AFTER `emp_id`;
        </sql>
    </changeSet>

    <changeSet id="66" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `app_emp_warn_rule`
                MODIFY COLUMN `dept_id` int(11) NOT NULL COMMENT '项目ID' AFTER `id`,
                MODIFY COLUMN `rule_type` int(11) NOT NULL COMMENT '报警规则类型' AFTER `name`,
                ADD COLUMN `rule_param` text NULL COMMENT '报警报警参数' AFTER `rule_type`,
                MODIFY COLUMN `warn_type` int(11) NOT NULL DEFAULT 1 COMMENT '报警类型' AFTER `rule_type`;

            ALTER TABLE `app_emp_warn`
                MODIFY COLUMN `rule_id` int(11) NOT NULL COMMENT '报警规则ID' AFTER `dept_id`,
                MODIFY COLUMN `rule_type` int(11) NOT NULL COMMENT '报警规则类型' AFTER `rule_id`,
                ADD COLUMN `rule_param` text NULL COMMENT '报警规则参数' AFTER `rule_type`,
                ADD COLUMN `trigger_param` text NULL COMMENT '触发参数' AFTER `rule_param`,
                MODIFY COLUMN `trigger_object_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '触发业务对象ID' AFTER `trigger_time`,
                MODIFY COLUMN `lat` double NULL DEFAULT NULL COMMENT '经度(废弃)' AFTER `trigger_object_id`,
                MODIFY COLUMN `lng` double NULL DEFAULT NULL COMMENT '纬度(废弃)' AFTER `lat`;
        </sql>
    </changeSet>

    <changeSet id="67" author="xuguocheng">
        <comment>新建表</comment>
        <sql>
            CREATE TABLE `app_emp_leave`  (
                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                `dept_id` int(11) NOT NULL COMMENT '项目ID',
                `emp_id` int(11) NOT NULL COMMENT '人员ID',
                `emp_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '人员姓名',
                `start_time` datetime(0) NOT NULL COMMENT '开始时间',
                `end_time` datetime(0) NOT NULL COMMENT '结束时间',
                `type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '请假类型',
                `reason` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '理由',
                `remark` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审批意见',
                `commit_user_id` int(11) NULL DEFAULT NULL COMMENT '提交人',
                `commit_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提交人姓名',
                `commit_time` datetime(0) NULL DEFAULT NULL COMMENT '提交时间',
                `audit_user_id` int(11) NULL DEFAULT NULL COMMENT '审核人',
                `audit_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审核人姓名',
                `audit_time` datetime(0) NULL DEFAULT NULL COMMENT '审核时间',
                `state` int(11) NOT NULL DEFAULT 0 COMMENT '状态',
                `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
                `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '人员管理-请假记录' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="68" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `app_emp_warn`
                ADD COLUMN `trigger_key` varchar(64) NULL COMMENT '触发报警key' AFTER `trigger_object_id`;
        </sql>
    </changeSet>

    <changeSet id="69" author="xuguocheng">
        <comment>修改字段</comment>
        <sql>
            ALTER TABLE `app_indoor_position_station`
                MODIFY COLUMN `lng` double(10, 6) NULL DEFAULT NULL COMMENT '经度' AFTER `z`,
                MODIFY COLUMN `lat` double(10, 6) NULL DEFAULT NULL COMMENT '纬度' AFTER `lng`;
        </sql>
    </changeSet>

    <changeSet id="70" author="xuguocheng">
        <comment>新建表</comment>
        <sql>
            CREATE TABLE `app_uniform_receive`  (
                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                `dept_id` int(11) NOT NULL COMMENT '项目ID',
                `guid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '唯一编码',
                `emp_id` int(11) NULL DEFAULT NULL COMMENT '人员ID',
                `emp_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '人员姓名',
                `date` date NULL DEFAULT NULL COMMENT '领用日期',
                `create_user_id` int(11) NULL DEFAULT NULL COMMENT '创建人ID',
                `create_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人姓名',
                `ext` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '扩展信息',
                `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
                `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '工服领用-记录' ROW_FORMAT = Dynamic;

            CREATE TABLE `app_uniform_receive_detail`  (
               `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
               `dept_id` int(11) NOT NULL COMMENT '项目ID',
               `receive_id` int(11) NOT NULL COMMENT '领用记录ID',
               `item_id` int(11) NULL DEFAULT NULL COMMENT '物品ID',
               `item_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '物品名称',
               `item_amount` int(11) NOT NULL COMMENT '物品数量',
               `del_flag` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '删除标记',
               `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
               `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
               PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '工服领用-详情' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="71" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `app_face_gate`
                ADD COLUMN `area_id` int NULL COMMENT '工区ID' AFTER `direction`,
                ADD COLUMN `area_name` varchar(32) NULL COMMENT '工区' AFTER `area_id`;
        </sql>
    </changeSet>

    <changeSet id="72" author="xuguocheng">
        <comment>修改字段</comment>
        <sql>
            ALTER TABLE `app_indoor_position_tag`
                MODIFY COLUMN `lng` double(10, 6) NULL DEFAULT NULL COMMENT '经度' AFTER `y`,
                MODIFY COLUMN `lat` double(10, 6) NULL DEFAULT NULL COMMENT '纬度' AFTER `lng`;
        </sql>
    </changeSet>

    <changeSet id="73" author="xuguocheng">
        <comment>修改字段</comment>
        <sql>
            ALTER TABLE `app_emp`
                MODIFY COLUMN `corp_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合作单位名称' AFTER `corp_id`;
        </sql>
    </changeSet>

    <changeSet id="74" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `app_emp_work_type`
                ADD COLUMN `spec` int NOT NULL DEFAULT 0 COMMENT '是否特殊工种' AFTER `work_code`;
        </sql>
    </changeSet>

    <changeSet id="75" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `app_indoor_position_tag`
            DROP COLUMN `phone`,
            DROP COLUMN `idCardNo`,
            ADD COLUMN `z` double(10, 2) NULL AFTER `y`,
            ADD COLUMN `state` int NULL COMMENT '标签状态' AFTER `battery_power`,
            ADD COLUMN `sos` int NULL COMMENT 'SOS标志' AFTER `state`;
        </sql>
    </changeSet>

    <changeSet id="76" author="xuguocheng">
        <comment>删除废弃表</comment>
        <sql>
            DROP TABLE app_sync_detail;
            DROP TABLE app_sync_record;
        </sql>
    </changeSet>

    <changeSet id="77" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `app_train`
                MODIFY COLUMN `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键' FIRST,
                ADD COLUMN `sync_flag` int NULL DEFAULT 0 COMMENT '同步标记' AFTER `source`;

            ALTER TABLE `app_emp`
                MODIFY COLUMN `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键' FIRST,
                ADD COLUMN `guid` varchar(32) NULL COMMENT 'GUID' AFTER `id`,
                MODIFY COLUMN `group_id` int(11) NULL DEFAULT NULL COMMENT '班组ID' AFTER `corp_name`,
                ADD COLUMN `sync_flag` int NULL DEFAULT 0 COMMENT '同步状态' AFTER `verify_state`;
        </sql>
    </changeSet>

    <changeSet id="78" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `app_indoor_position_station`
                ADD COLUMN `ext1` varchar(64) NULL COMMENT '扩展字段1' AFTER `lat`,
                ADD COLUMN `ext2` varchar(64) NULL COMMENT '扩展字段2' AFTER `ext1`,
                ADD COLUMN `ext3` varchar(64) NULL COMMENT '扩展字段3' AFTER `ext2`;
        </sql>
    </changeSet>

    <changeSet id="79" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `app_emp_data`
            DROP COLUMN `device_id`,
            DROP COLUMN `helmet_state`,
            MODIFY COLUMN `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键' FIRST,
            ADD COLUMN `dept_id` int NULL COMMENT '项目ID' AFTER `id`,
            ADD COLUMN `area_id` int NULL COMMENT '工区ID' AFTER `locale_state`,
            ADD COLUMN `area_name` varchar(32) NULL COMMENT '工区名称' AFTER `area_id`,
            MODIFY COLUMN `lng_wgs84` double NULL DEFAULT NULL COMMENT '经度84' AFTER `gps_time`,
            MODIFY COLUMN `lat_wgs84` double NULL DEFAULT NULL COMMENT '纬度84' AFTER `lng_wgs84`;
        </sql>
    </changeSet>

    <changeSet id="80" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `app_emp_data`
                ADD COLUMN `locale_time` datetime NULL COMMENT '现场时间' AFTER `locale_state`;
        </sql>
    </changeSet>

    <changeSet id="81" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `app_indoor_position_tag`
                ADD COLUMN `distance1` int NULL COMMENT '距离1' AFTER `distance`;
        </sql>
    </changeSet>

    <changeSet id="82" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `app_indoor_position_map`
                ADD COLUMN `area_id` int NULL COMMENT '区域ID' AFTER `guid`,
                ADD COLUMN `area_name` varchar(32) NULL COMMENT '区域名称' AFTER `area_id`;
        </sql>
    </changeSet>

    <changeSet id="83" author="xuguocheng">
        <comment>新建表</comment>
        <sql>
            CREATE TABLE `app_helmet_config`  (
              `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
              `dept_id` int(11) NOT NULL COMMENT '项目ID',
              `platform` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '平台',
              `host` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务器',
              `user` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '账号',
              `pass` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '密码',
              `ext_1` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '扩展字段1',
              `ext_2` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '扩展字段2',
              `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
              `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '更新时间',
              `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
              PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '安全帽-配置' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="84" author="xuguocheng">
        <comment>新建表</comment>
        <sql>
            CREATE TABLE `app_helmet_file`  (
                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                `dept_id` int(11) NOT NULL COMMENT '项目ID',
                `emp_id` int(11) NOT NULL COMMENT '人员ID',
                `time` datetime(0) NOT NULL COMMENT '时间',
                `file_type` int(11) NOT NULL COMMENT '文件类型',
                `file_url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件地址',
                `lng` double(10, 6) NULL DEFAULT NULL COMMENT '经度',
                `lat` double(10, 6) NULL DEFAULT NULL COMMENT '纬度',
                `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
                `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '安全帽-文件' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="85" author="xuguocheng">
        <comment>新增字段</comment>
        <sql>
            ALTER TABLE `app_uniform_receive`
                ADD COLUMN `company` varchar(128) NULL COMMENT '公司名称' AFTER `emp_name`;
        </sql>
    </changeSet>

    <changeSet id="86" author="qinzexing">
        <comment>更新人员表中错误的工人类型</comment>
        <sql>
            UPDATE app_emp SET work_role_id = 1 WHERE work_role_name = '管理人员';
            UPDATE app_emp SET work_role_id = 2 WHERE work_role_name = '建筑工人';
        </sql>
    </changeSet>

    <changeSet id="87" author="qzexing">
        <comment>修改人员设备表</comment>
        <sql>
            ALTER TABLE `app_emp_device`
                ADD COLUMN `guid` varchar(32) NULL COMMENT 'GUID' AFTER `id`,
                ADD COLUMN `dept_id` int(11) NULL COMMENT '组织机构ID' AFTER `guid`,
                ADD COLUMN `device_type` int(11) NULL COMMENT '硬件类型 2-智能安全帽 34-智能手环' AFTER `dept_id`,
                ADD COLUMN `platform` varchar(32) NULL COMMENT '硬件平台' AFTER `device_type`,
                ADD COLUMN `sn` varchar(32) NULL COMMENT '硬件SN' AFTER `platform`,
                ADD COLUMN `color` varchar(20) NULL COMMENT '颜色' AFTER `sn`,
                ADD COLUMN `net_state` int NOT NULL DEFAULT 0 COMMENT '网络状态(0-离线 1-在线)' AFTER `color`,
                ADD COLUMN `battery_power` int(11) NULL COMMENT '电池电量(百分比)' AFTER `net_state`,
                ADD COLUMN `time` datetime NULL COMMENT '最近通信时间' AFTER `battery_power`,
                ADD COLUMN `bind_flag` int(11) NULL COMMENT '绑定状态（0-未绑定 1-绑定）' AFTER `device_id`,
                MODIFY COLUMN `emp_id` int(11) NULL COMMENT '人员ID' AFTER `color`,
                MODIFY COLUMN `device_id` int(11) NULL COMMENT '设备ID' AFTER `emp_id`,
                MODIFY COLUMN `bind_time` datetime NULL COMMENT '绑定时间' AFTER `device_id`,
                MODIFY COLUMN `bind_user` varchar(25)  NULL COMMENT '绑定人' AFTER `bind_time`;
        </sql>
    </changeSet>

    <changeSet id="88" author="qzexing">
        <comment>修改人员设备数据表</comment>
        <sql>
            ALTER TABLE `app_device_card_data`
                MODIFY COLUMN `device_id` int(11) NULL COMMENT '硬件ID(工牌)' AFTER `id`,
                ADD COLUMN `emp_device_id` int(11) NULL COMMENT '人员设备ID' AFTER `device_id`,
                ADD COLUMN `body_temp` double NULL COMMENT '体温（℃）' AFTER `acceleration_z`,
                ADD COLUMN `heart_rate` int(11) NULL COMMENT '心率（bpm）' AFTER `body_temp`,
                ADD COLUMN `blood_pressure` varchar(30) NULL COMMENT '血压（mmHg）' AFTER `heart_rate`,
                ADD COLUMN `blood_oxygen` int(11) NULL COMMENT '血氧（%）' AFTER `blood_pressure`,
                ADD COLUMN `blood_sugar` double NULL COMMENT '血糖（mg/dL）' AFTER `blood_oxygen`;
        </sql>
    </changeSet>

    <changeSet id="89" author="xuguocheng">
        <comment>人员数据新增健康信息</comment>
        <sql>
            ALTER TABLE `app_emp_data`
                ADD COLUMN `body_temp` double(5, 1) NULL COMMENT '体温（℃）' AFTER `battery_power`,
                ADD COLUMN `heart_rate` int NULL COMMENT '心率（bpm）' AFTER `body_temp`,
                ADD COLUMN `blood_oxygen` int NULL COMMENT '血氧（%）' AFTER `heart_rate`,
                ADD COLUMN `diastolic_pressure` int NULL COMMENT '舒张压' AFTER `blood_oxygen`,
                ADD COLUMN `systolic_pressure` int NULL COMMENT '收缩压' AFTER `diastolic_pressure`,
                ADD COLUMN `blood_sugar` double(5, 1) NULL COMMENT '血糖（mg/dL）' AFTER `systolic_pressure`;
        </sql>
    </changeSet>

    <changeSet id="90" author="xuguocheng">
        <comment>人员证书增加字段</comment>
        <sql>
            ALTER TABLE `app_emp_cert`
                MODIFY COLUMN `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键' FIRST,
                ADD COLUMN `dept_id` int NULL COMMENT '项目ID' AFTER `id`,
                ADD COLUMN `operation_item_id` int NULL COMMENT '准操项目ID' AFTER `cert_type_name`,
                ADD COLUMN `operation_item_name` varchar(32) NULL COMMENT '准操项目名称' AFTER `operation_item_id`;

            ALTER TABLE `app_emp_cert_attach`
                MODIFY COLUMN `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键' FIRST,
                ADD COLUMN `dept_id` int NULL COMMENT '项目ID' AFTER `id`,
                MODIFY COLUMN `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '附件地址' AFTER `name`,
                MODIFY COLUMN `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间' AFTER `del_flag`,
                MODIFY COLUMN `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间' AFTER `update_time`;
        </sql>
    </changeSet>

    <changeSet id="91" author="qzexing">
        <comment>人员工种新增字段</comment>
        <sql>
            ALTER TABLE `app_emp_work_type`
                ADD COLUMN `need_cert` int(11) NOT NULL DEFAULT 0 COMMENT '是否需要持证上岗 0-不需要 1-需要' AFTER `spec`;
        </sql>
    </changeSet>

    <changeSet id="92" author="xuguocheng">
        <comment>修改人员信息工种字段长度</comment>
        <sql>
            ALTER TABLE `app_emp`
                MODIFY COLUMN `work_type_name` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工种名称' AFTER `work_type_id`;
        </sql>
    </changeSet>

    <changeSet id="93" author="qzexing">
        <comment>人员基站标签新增字段</comment>
        <sql>
            ALTER TABLE `app_indoor_position_tag`
                ADD COLUMN `bind_type` int(11) NULL COMMENT '绑定类型 1-人员 2-设备' AFTER `bind_flag`,
                ADD COLUMN `device_code` varchar(64) NULL COMMENT '设备唯一标识' AFTER `emp_name`;

            update app_indoor_position_tag set bind_type = 1 where bind_flag = 1;
        </sql>
    </changeSet>

</databaseChangeLog>
