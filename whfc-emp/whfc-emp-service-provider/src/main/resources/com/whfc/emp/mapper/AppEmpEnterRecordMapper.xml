<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppEmpEnterRecordMapper">
    <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppEmpEnterRecord">
        <!--@mbg.generated-->
        <!--@Table app_emp_enter_record-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="emp_id" jdbcType="INTEGER" property="empId"/>
        <result column="dept_id" jdbcType="INTEGER" property="deptId"/>
        <result column="corp_id" jdbcType="INTEGER" property="corpId"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="date" jdbcType="DATE" property="date"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, emp_id, dept_id, corp_id, `type`, `date`, create_time, update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from app_emp_enter_record
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete from app_emp_enter_record
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppEmpEnterRecord"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into app_emp_enter_record (emp_id, dept_id, corp_id,
        `type`, `date`, create_time,
        update_time)
        values (#{empId,jdbcType=INTEGER}, #{deptId,jdbcType=INTEGER}, #{corpId,jdbcType=INTEGER},
        #{type,jdbcType=INTEGER}, #{date,jdbcType=DATE}, #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppEmpEnterRecord"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into app_emp_enter_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="empId != null">
                emp_id,
            </if>
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="corpId != null">
                corp_id,
            </if>
            <if test="type != null">
                `type`,
            </if>
            <if test="date != null">
                `date`,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="empId != null">
                #{empId,jdbcType=INTEGER},
            </if>
            <if test="deptId != null">
                #{deptId,jdbcType=INTEGER},
            </if>
            <if test="corpId != null">
                #{corpId,jdbcType=INTEGER},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="date != null">
                #{date,jdbcType=DATE},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppEmpEnterRecord">
        <!--@mbg.generated-->
        update app_emp_enter_record
        <set>
            <if test="empId != null">
                emp_id = #{empId,jdbcType=INTEGER},
            </if>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=INTEGER},
            </if>
            <if test="corpId != null">
                corp_id = #{corpId,jdbcType=INTEGER},
            </if>
            <if test="type != null">
                `type` = #{type,jdbcType=INTEGER},
            </if>
            <if test="date != null">
                `date` = #{date,jdbcType=DATE},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.whfc.emp.entity.AppEmpEnterRecord">
        <!--@mbg.generated-->
        update app_emp_enter_record
        set emp_id = #{empId,jdbcType=INTEGER},
        dept_id = #{deptId,jdbcType=INTEGER},
        corp_id = #{corpId,jdbcType=INTEGER},
        `type` = #{type,jdbcType=INTEGER},
        `date` = #{date,jdbcType=DATE},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectEnterpriseEnterRecordStat" resultType="com.whfc.emp.dto.AppEmpAnaWeekDataDTO">
        select `date`,
               ifnull(sum(case type when 1 then 1 else 0 end),0) as enterNum,
               ifnull(sum(case type when 2 then 1 else 0 end),0) as outerNum
        from app_emp_enter_record
        where dept_id in
        <foreach collection="deptIds" item="deptId" separator="," open="(" close=")">
            #{deptId}
        </foreach>
        and `date` between date(#{startTime}) and date(#{endTime})
        group by `date`
        order by `date`
    </select>

    <select id="selectDayStatStat" resultType="com.whfc.emp.dto.AppEmpAnaWeekDataDTO">
        select `date`,
               ifnull(sum(case type when 1 then 1 else 0 end),0) as enterNum,
               ifnull(sum(case type when 2 then 1 else 0 end),0) as outerNum
          from app_emp_enter_record
         where dept_id= #{deptId}
           and `date` between date(#{startTime}) and date(#{endTime})
        group by `date`
        order by `date`
    </select>

    <select id="selectTotalStat" resultType="com.whfc.emp.dto.AppEmpAnaWeekDataDTO">
        select ifnull(sum(case type when 1 then 1 else 0 end),0) as enterNum,
               ifnull(sum(case type when 2 then 1 else 0 end),0) as outerNum
          from app_emp_enter_record
         where dept_id= #{deptId}
           and `date` between date(#{startTime}) and date(#{endTime})
    </select>

    <select id="selectEmpLog" resultType="com.whfc.emp.dto.AppEmpDTO">
        SELECT
	    ae.group_name,
	    ae.emp_name,
	    aeer.type
        FROM
	    app_emp_enter_record aeer
	    INNER JOIN app_emp ae on aeer.emp_id = ae.id
        where
	    aeer.dept_id = #{deptId}
	    and aeer.date = date(#{date})
    </select>
</mapper>