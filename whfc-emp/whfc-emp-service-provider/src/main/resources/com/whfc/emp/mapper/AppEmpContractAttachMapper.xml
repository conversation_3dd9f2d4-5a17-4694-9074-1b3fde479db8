<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppEmpContractAttachMapper">
    <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppEmpContractAttach">
        <!--@mbg.generated-->
        <!--@Table app_emp_contract_attach-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="contract_id" jdbcType="INTEGER" property="contractId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="url" jdbcType="VARCHAR" property="url"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, contract_id, `name`, url, del_flag, update_time, create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from app_emp_contract_attach
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete from app_emp_contract_attach
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppEmpContractAttach"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into app_emp_contract_attach (contract_id, `name`, url,
        del_flag, update_time, create_time
        )
        values (#{contractId,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, #{url,jdbcType=VARCHAR},
        #{delFlag,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}
        )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.whfc.emp.entity.AppEmpContractAttach" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into app_emp_contract_attach
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="contractId != null">
                contract_id,
            </if>
            <if test="name != null">
                `name`,
            </if>
            <if test="url != null">
                url,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="contractId != null">
                #{contractId,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="url != null">
                #{url,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppEmpContractAttach">
        <!--@mbg.generated-->
        update app_emp_contract_attach
        <set>
            <if test="contractId != null">
                contract_id = #{contractId,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="url != null">
                url = #{url,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.whfc.emp.entity.AppEmpContractAttach">
        <!--@mbg.generated-->
        update app_emp_contract_attach
        set contract_id = #{contractId,jdbcType=INTEGER},
        `name` = #{name,jdbcType=VARCHAR},
        url = #{url,jdbcType=VARCHAR},
        del_flag = #{delFlag,jdbcType=INTEGER},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        create_time = #{createTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <select id="selectByContractId" resultType="com.whfc.emp.dto.AppAttachDTO">
      select `name`,
      url
      from app_emp_contract_attach
      where del_flag = 0
      and contract_id = #{id}
    </select>
    <insert id="batchInsert">
        INSERT INTO app_emp_contract_attach
        (
        contract_id,
        `name`,
        url
        )
        values
        <foreach collection="list" item="item" separator="," >
            (
            #{item.contractId},
            #{item.name},
            #{item.url}
            )
        </foreach>
    </insert>
    <update id="deleteLogicByContractId">
        update app_emp_contract_attach
        set del_flag = 1
        where contract_id = #{contractId}
    </update>
</mapper>