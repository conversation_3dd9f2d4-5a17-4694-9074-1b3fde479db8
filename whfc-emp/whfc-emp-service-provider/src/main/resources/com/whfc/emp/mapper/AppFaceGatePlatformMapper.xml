<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppFaceGatePlatformMapper">
    <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppFaceGatePlatform">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="platform" jdbcType="VARCHAR" property="platform"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="factory" jdbcType="VARCHAR" property="factory"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="state" jdbcType="INTEGER" property="state"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,
        platform,
        `name`,
        factory,
        remark,
        `state`,
        del_flag,
        update_time,
        create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from app_face_gate_platform
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete
        from app_face_gate_platform
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppFaceGatePlatform"
            useGeneratedKeys="true">
        insert into app_face_gate_platform (platform, `name`, factory,
                                            remark, `state`, del_flag,
                                            update_time, create_time)
        values (#{platform,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{factory,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR}, #{state,jdbcType=INTEGER}, #{delFlag,jdbcType=INTEGER},
                #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppFaceGatePlatform"
            useGeneratedKeys="true">
        insert into app_face_gate_platform
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="platform != null">
                platform,
            </if>
            <if test="name != null">
                `name`,
            </if>
            <if test="factory != null">
                factory,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="state != null">
                `state`,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="platform != null">
                #{platform,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="factory != null">
                #{factory,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                #{state,jdbcType=INTEGER},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppFaceGatePlatform">
        update app_face_gate_platform
        <set>
            <if test="platform != null">
                platform = #{platform,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="factory != null">
                factory = #{factory,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                `state` = #{state,jdbcType=INTEGER},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.whfc.emp.entity.AppFaceGatePlatform">
        update app_face_gate_platform
        set platform    = #{platform,jdbcType=VARCHAR},
            `name`      = #{name,jdbcType=VARCHAR},
            factory     = #{factory,jdbcType=VARCHAR},
            remark      = #{remark,jdbcType=VARCHAR},
            `state`     = #{state,jdbcType=INTEGER},
            del_flag    = #{delFlag,jdbcType=INTEGER},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            create_time = #{createTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectAll" resultType="com.whfc.emp.dto.AppFaceGatePlatformDTO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM app_face_gate_platform
        WHERE del_flag = 0
    </select>
</mapper>