<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppEmpDataMapper">
  <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppEmpData">
    <!--@mbg.generated-->
    <!--@Table app_emp_data-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="emp_id" jdbcType="INTEGER" property="empId" />
    <result column="net_state" jdbcType="INTEGER" property="netState" />
    <result column="locale_state" jdbcType="INTEGER" property="localeState" />
    <result column="locale_time" jdbcType="TIMESTAMP" property="localeTime" />
    <result column="area_id" jdbcType="INTEGER" property="areaId" />
    <result column="area_name" jdbcType="VARCHAR" property="areaName" />
    <result column="attend_state" jdbcType="INTEGER" property="attendState" />
    <result column="time" jdbcType="TIMESTAMP" property="time" />
    <result column="lng_flag" jdbcType="VARCHAR" property="lngFlag" />
    <result column="lat_flag" jdbcType="VARCHAR" property="latFlag" />
    <result column="lng" jdbcType="DOUBLE" property="lng" />
    <result column="lat" jdbcType="DOUBLE" property="lat" />
    <result column="gps_time" jdbcType="TIMESTAMP" property="gpsTime" />
    <result column="lng_wgs84" jdbcType="DOUBLE" property="lngWgs84" />
    <result column="lat_wgs84" jdbcType="DOUBLE" property="latWgs84" />
    <result column="battery_state" jdbcType="INTEGER" property="batteryState" />
    <result column="battery_power" jdbcType="INTEGER" property="batteryPower" />
    <result column="body_temp" jdbcType="DOUBLE" property="bodyTemp" />
    <result column="heart_rate" jdbcType="INTEGER" property="heartRate" />
    <result column="blood_oxygen" jdbcType="INTEGER" property="bloodOxygen" />
    <result column="diastolic_pressure" jdbcType="INTEGER" property="diastolicPressure" />
    <result column="systolic_pressure" jdbcType="INTEGER" property="systolicPressure" />
    <result column="blood_sugar" jdbcType="DOUBLE" property="bloodSugar" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
      id,
      dept_id,
      emp_id,
      net_state,
      locale_state,
      locale_time,
      area_id,
      area_name,
      attend_state,
      `time`,
      lng_flag,
      lat_flag,
      lng,
      lat,
      gps_time,
      lng_wgs84,
      lat_wgs84,
      battery_state,
      battery_power,
      body_temp,
      heart_rate,
      blood_oxygen,
      diastolic_pressure,
      systolic_pressure,
      blood_sugar,
      update_time,
      create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from app_emp_data
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from app_emp_data
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppEmpData" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_emp_data
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="empId != null">
        emp_id,
      </if>
      <if test="netState != null">
        net_state,
      </if>
      <if test="localeState != null">
        locale_state,
      </if>
      <if test="localeTime != null">
        locale_time,
      </if>
      <if test="areaId != null">
        area_id,
      </if>
      <if test="areaName != null">
        area_name,
      </if>
      <if test="attendState != null">
        attend_state,
      </if>
      <if test="time != null">
        `time`,
      </if>
      <if test="lngFlag != null">
        lng_flag,
      </if>
      <if test="latFlag != null">
        lat_flag,
      </if>
      <if test="lng != null">
        lng,
      </if>
      <if test="lat != null">
        lat,
      </if>
      <if test="gpsTime != null">
        gps_time,
      </if>
      <if test="lngWgs84 != null">
        lng_wgs84,
      </if>
      <if test="latWgs84 != null">
        lat_wgs84,
      </if>
      <if test="batteryState != null">
        battery_state,
      </if>
      <if test="batteryPower != null">
        battery_power,
      </if>
      <if test="bodyTemp != null">
        body_temp,
      </if>
      <if test="heartRate != null">
        heart_rate,
      </if>
      <if test="bloodOxygen != null">
        blood_oxygen,
      </if>
      <if test="diastolicPressure != null">
        diastolic_pressure,
      </if>
      <if test="systolicPressure != null">
        systolic_pressure,
      </if>
      <if test="bloodSugar != null">
        blood_sugar,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="empId != null">
        #{empId,jdbcType=INTEGER},
      </if>
      <if test="netState != null">
        #{netState,jdbcType=INTEGER},
      </if>
      <if test="localeState != null">
        #{localeState,jdbcType=INTEGER},
      </if>
      <if test="localeTime != null">
        #{localeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="areaId != null">
        #{areaId,jdbcType=INTEGER},
      </if>
      <if test="areaName != null">
        #{areaName,jdbcType=VARCHAR},
      </if>
      <if test="attendState != null">
        #{attendState,jdbcType=INTEGER},
      </if>
      <if test="time != null">
        #{time,jdbcType=TIMESTAMP},
      </if>
      <if test="lngFlag != null">
        #{lngFlag,jdbcType=VARCHAR},
      </if>
      <if test="latFlag != null">
        #{latFlag,jdbcType=VARCHAR},
      </if>
      <if test="lng != null">
        #{lng,jdbcType=DOUBLE},
      </if>
      <if test="lat != null">
        #{lat,jdbcType=DOUBLE},
      </if>
      <if test="gpsTime != null">
        #{gpsTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lngWgs84 != null">
        #{lngWgs84,jdbcType=DOUBLE},
      </if>
      <if test="latWgs84 != null">
        #{latWgs84,jdbcType=DOUBLE},
      </if>
      <if test="batteryState != null">
        #{batteryState,jdbcType=INTEGER},
      </if>
      <if test="batteryPower != null">
        #{batteryPower,jdbcType=INTEGER},
      </if>
      <if test="bodyTemp != null">
        #{bodyTemp,jdbcType=DOUBLE},
      </if>
      <if test="heartRate != null">
        #{heartRate,jdbcType=INTEGER},
      </if>
      <if test="bloodOxygen != null">
        #{bloodOxygen,jdbcType=INTEGER},
      </if>
      <if test="diastolicPressure != null">
        #{diastolicPressure,jdbcType=INTEGER},
      </if>
      <if test="systolicPressure != null">
        #{systolicPressure,jdbcType=INTEGER},
      </if>
      <if test="bloodSugar != null">
        #{bloodSugar,jdbcType=DOUBLE},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppEmpData">
    <!--@mbg.generated-->
    update app_emp_data
    <set>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="empId != null">
        emp_id = #{empId,jdbcType=INTEGER},
      </if>
      <if test="netState != null">
        net_state = #{netState,jdbcType=INTEGER},
      </if>
      <if test="localeState != null">
        locale_state = #{localeState,jdbcType=INTEGER},
      </if>
      <if test="localeTime != null">
        locale_time = #{localeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="areaId != null">
        area_id = #{areaId,jdbcType=INTEGER},
      </if>
      <if test="areaName != null">
        area_name = #{areaName,jdbcType=VARCHAR},
      </if>
      <if test="attendState != null">
        attend_state = #{attendState,jdbcType=INTEGER},
      </if>
      <if test="time != null">
        `time` = #{time,jdbcType=TIMESTAMP},
      </if>
      <if test="lngFlag != null">
        lng_flag = #{lngFlag,jdbcType=VARCHAR},
      </if>
      <if test="latFlag != null">
        lat_flag = #{latFlag,jdbcType=VARCHAR},
      </if>
      <if test="lng != null">
        lng = #{lng,jdbcType=DOUBLE},
      </if>
      <if test="lat != null">
        lat = #{lat,jdbcType=DOUBLE},
      </if>
      <if test="gpsTime != null">
        gps_time = #{gpsTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lngWgs84 != null">
        lng_wgs84 = #{lngWgs84,jdbcType=DOUBLE},
      </if>
      <if test="latWgs84 != null">
        lat_wgs84 = #{latWgs84,jdbcType=DOUBLE},
      </if>
      <if test="batteryState != null">
        battery_state = #{batteryState,jdbcType=INTEGER},
      </if>
      <if test="batteryPower != null">
        battery_power = #{batteryPower,jdbcType=INTEGER},
      </if>
      <if test="bodyTemp != null">
        body_temp = #{bodyTemp,jdbcType=DOUBLE},
      </if>
      <if test="heartRate != null">
        heart_rate = #{heartRate,jdbcType=INTEGER},
      </if>
      <if test="bloodOxygen != null">
        blood_oxygen = #{bloodOxygen,jdbcType=INTEGER},
      </if>
      <if test="diastolicPressure != null">
        diastolic_pressure = #{diastolicPressure},
      </if>
      <if test="systolicPressure != null">
        systolic_pressure = #{systolicPressure,jdbcType=INTEGER},
      </if>
      <if test="bloodSugar != null">
        blood_sugar = #{bloodSugar,jdbcType=DOUBLE},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

    <insert id="insertOrUpdate">
        insert into app_emp_data
        (
            dept_id,
            emp_id,
            net_state,
            locale_state,
            area_id,
            area_name,
            attend_state,
            `time`,
            lng_flag,
            lat_flag,
            lng,
            lat,
            gps_time,
            lng_wgs84,
            lat_wgs84,
            battery_state,
            battery_power,
            body_temp,
            heart_rate,
            blood_oxygen,
            systolic_pressure,
            diastolic_pressure,
            blood_sugar
        )
        values
        (
            #{deptId,jdbcType=INTEGER},
            #{empId,jdbcType=INTEGER},
            #{netState,jdbcType=INTEGER},
            #{localeState,jdbcType=INTEGER},
            #{areaId,jdbcType=INTEGER},
            #{areaName,jdbcType=VARCHAR},
            #{attendState,jdbcType=INTEGER},
            #{time,jdbcType=TIMESTAMP},
            #{lngFlag,jdbcType=VARCHAR},
            #{latFlag,jdbcType=VARCHAR},
            #{lng,jdbcType=DOUBLE},
            #{lat,jdbcType=DOUBLE},
            #{gpsTime,jdbcType=TIMESTAMP},
            #{lngWgs84} ,
            #{latWgs84} ,
            #{batteryState,jdbcType=INTEGER},
            #{batteryPower,jdbcType=INTEGER},
            #{bodyTemp,jdbcType=DOUBLE},
            #{heartRate,jdbcType=INTEGER},
            #{bloodOxygen,jdbcType=INTEGER},
            #{systolicPressure,jdbcType=INTEGER},
            #{diastolicPressure,jdbcType=INTEGER},
            #{bloodSugar,jdbcType=DOUBLE}
        )
        on duplicate key update
        <if test="deptId != null">
            dept_id = #{deptId,jdbcType=INTEGER},
        </if>
        <if test="netState != null">
            net_state = #{netState,jdbcType=INTEGER},
        </if>
        <if test="localeState != null">
            locale_state = #{localeState,jdbcType=INTEGER},
        </if>
        <if test="areaId != null">
            area_id = #{areaId,jdbcType=INTEGER},
        </if>
        <if test="areaName != null">
            area_name = #{areaName,jdbcType=VARCHAR},
        </if>
        <if test="attendState != null">
            attend_state = #{attendState,jdbcType=INTEGER},
        </if>
        <if test="time != null">
            time = #{time,jdbcType=TIMESTAMP},
        </if>
        <if test="lngFlag != null">
            lng_flag = #{lngFlag,jdbcType=VARCHAR},
        </if>
        <if test="latFlag != null">
            lat_flag = #{latFlag,jdbcType=VARCHAR},
        </if>
        <if test="lng != null">
            lng = #{lng,jdbcType=DOUBLE},
        </if>
        <if test="lat != null">
            lat = #{lat,jdbcType=DOUBLE},
        </if>
        <if test="gpsTime != null">
            gps_time = #{gpsTime,jdbcType=TIMESTAMP},
        </if>
        <if test="lngWgs84 != null">
            lng_wgs84 = #{lngWgs84,jdbcType=DOUBLE},
        </if>
        <if test="latWgs84 != null">
            lat_wgs84 = #{latWgs84,jdbcType=DOUBLE},
        </if>
        <if test="batteryState != null">
            battery_state = #{batteryState,jdbcType=INTEGER},
        </if>
        <if test="batteryPower != null">
            battery_power = #{batteryPower,jdbcType=INTEGER},
        </if>
        <if test="bodyTemp != null">
            body_temp = #{bodyTemp,jdbcType=DOUBLE},
        </if>
        <if test="heartRate != null">
            heart_rate = #{heartRate,jdbcType=INTEGER},
        </if>
        <if test="bloodOxygen != null">
            blood_oxygen = #{bloodOxygen,jdbcType=INTEGER},
        </if>
        <if test="diastolicPressure != null">
            diastolic_pressure = #{diastolicPressure,jdbcType=INTEGER},
        </if>
        <if test="systolicPressure != null">
            systolic_pressure = #{systolicPressure,jdbcType=INTEGER},
        </if>
        <if test="bloodSugar != null">
            blood_sugar = #{bloodSugar,jdbcType=DOUBLE},
        </if>
        update_time = now(),
        emp_id = #{empId,jdbcType=INTEGER}
    </insert>

    <select id="selectByEmpId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from app_emp_data
        where emp_id = #{empId}
    </select>

    <update id="batchUpdateNetStateOffline">
        <![CDATA[
        update app_emp_data
          set net_state = 0
        WHERE net_state = 1
          and `time` < DATE_ADD(now(), INTERVAL - 300 MINUTE)
        ]]>
    </update>

    <update id="batchUpdateAttendStateAbsence">
         update app_emp_data
       set attend_state = 0
     where attend_state = 1
    </update>

    <update id="updateEmpLocaleIn">
        update app_emp_data
        set locale_state = 1,
            area_id = #{areaId},
            area_name = #{areaName},
            locale_time = #{localeTime}
        where emp_id = #{empId}
    </update>

    <update id="updateEmpLocaleOut">
        update app_emp_data
        set locale_state = 0,
            area_id = null,
            area_name = null,
            locale_time = null
        where emp_id = #{empId}
    </update>

    <update id="updateEmpAttendState">
        update app_emp_data
           set attend_state = #{attendState}
         where emp_id = #{empId}
    </update>

    <update id="clearEmpData">
        update app_emp_data
           set net_state = 0,
               locale_state = 0,
               area_id = null ,
               area_name = null,
               attend_state = 0,
               `time` = null ,
               lng_flag = null ,
               lat_flag = null ,
               lng = null ,
               lat = null ,
               gps_time = null ,
               lng_wgs84 = null ,
               lat_wgs84 = null ,
               battery_state = null ,
               battery_power = null
        where emp_id = #{empId}
    </update>

    <select id="countAttendNum" resultType="java.lang.Integer">
        select count(1)
        from app_emp_data aed
        inner join app_emp ae on ae.id = aed.emp_id
        where aed.attend_state = 1
        AND ae.del_flag = 0
        AND ae.post_state = 1
        and ae.dept_id in
        <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
            #{deptId}
        </foreach>
    </select>

    <select id="countLocaleNum" resultType="java.lang.Integer">
        select count(1)
        from app_emp_data aed
        inner join app_emp ae on ae.id = aed.emp_id
        where aed.locale_state = 1
        AND ae.del_flag = 0
        AND ae.post_state = 1
        and ae.dept_id in
        <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
            #{deptId}
        </foreach>
    </select>

    <select id="selectAttendNum" resultType="com.whfc.emp.dto.AppEmpAnaNumDTO">
        SELECT
	    COUNT(ae.id) as empTotal,
        IFNULL(SUM(aed.attend_state),0) as attendNum,
        ifnull(sum(aed.locale_state),0) as localeNum
        FROM
	    app_emp ae
	    LEFT JOIN app_emp_data aed ON aed.emp_id = ae.id
        WHERE
	    ae.del_flag = 0
	    AND ae.post_state = 1
	    AND ae.dept_id =#{deptId}
    </select>

    <select id="countEmpAttendNumByWorkRole" resultType="com.whfc.emp.dto.AppEmpWorkRoleNumDTO">
        SELECT
            work_role_id,
            work_role_name,
            COUNT( DISTINCT ae.id ) num,
            IFNULL(SUM(aed.attend_state),0) as attendNum,
            IFNULL(SUM(aed.locale_state),0) as localeNum
        FROM
            app_emp ae
                LEFT JOIN app_emp_data aed ON aed.emp_id = ae.id
        WHERE
            ae.del_flag = 0
          AND ae.post_state = 1
          AND ae.dept_id = #{deptId}
        GROUP BY
            ae.work_role_id
    </select>

    <select id="selectAreaLocaleStat" resultType="com.whfc.emp.dto.area.AppAreaAttendStat">
        select ifnull(sum(aed.locale_state),0) as localeNum
          from app_emp ae
         inner join app_emp_data aed on ae.id = aed.emp_id
        where ae.dept_id = #{deptId}
         -- and ae.post_state = 1
          and ae.del_flag = 0
          and aed.area_id = #{areaId}
          and aed.locale_state = 1
    </select>

    <select id="selectAreaLocaleStatByWorkRole" resultType="com.whfc.emp.dto.AppEmpWorkRoleNumDTO">
        select
            work_role_id,work_role_name,IFNULL(SUM(locale_state),0) localeNum
        from app_emp ae
        inner join app_emp_data aed on ae.id = aed.emp_id
        where ae.dept_id = #{deptId}
        -- and ae.post_state = 1
        and ae.del_flag = 0
        and aed.area_id = #{areaId}
        and aed.locale_state = 1
        GROUP BY ae.work_role_id
    </select>

    <select id="selectAreaLocaleGroupStat" resultType="com.whfc.emp.dto.area.AppAreaAttendStat">
        select ae.group_id,
               ae.group_name,
               ifnull(sum(aed.locale_state),0) as localeNum
        from app_emp ae
         inner join app_emp_data aed on ae.id = aed.emp_id
        where ae.dept_id = #{deptId}
         -- and ae.post_state = 1
          and ae.del_flag = 0
          and aed.area_id = #{areaId}
          and aed.locale_state = 1
         group by ae.group_id,ae.group_name
    </select>

    <select id="selectAreaLocaleEmpList" resultType="com.whfc.emp.dto.AppEmpDTO">
        select ae.id as empId,
               ae.emp_name,
               ae.work_type_name,
               ae.work_role_name,
               ae.group_name,
               ae.corp_name,
               aed.locale_time
        from app_emp ae
        inner join app_emp_data aed on ae.id = aed.emp_id
        where ae.dept_id = #{deptId}
          -- and ae.post_state = 1
          and ae.del_flag = 0
          and aed.area_id = #{areaId}
          and aed.locale_state = 1
         order by aed.locale_time desc
    </select>

    <select id="selectAreaLocaleEmpListByTime" resultType="com.whfc.emp.dto.AppEmpDTO">
        select ae.id as empId,
               ae.emp_name,
               ae.work_type_name,
               ae.work_role_name,
               ae.group_name,
               ae.corp_name,
               aed.locale_time
        from app_emp ae
        inner join app_emp_data aed on ae.id = aed.emp_id
        where ae.dept_id = #{deptId}
          -- and ae.post_state = 1
          and ae.del_flag = 0
          and aed.area_id = #{areaId}
          and aed.locale_state = 1
        <if test="startTime != null">
          and aed.locale_time >= #{startTime}
        </if>
        <if test="endTime != null">
          and #{endTime} >= aed.locale_time
        </if>
        <if test="keyword != null and keyword != ''">
            and (
               ae.emp_name like concat('%',#{keyword},'%')
            or ae.corp_name like concat('%',#{keyword},'%')
            or ae.group_name like concat('%',#{keyword},'%')
            )
        </if>
        order by aed.locale_time desc
    </select>

  <select id="selectByEmpIdList" resultMap="BaseResultMap">
      SELECT
        aed.emp_id,
        ae.dept_id,
        aed.net_state,
        aed.battery_power,
        aed.time
      FROM app_emp_data aed
      LEFT JOIN app_emp ae ON ae.id = aed.emp_id
      WHERE aed.emp_id IN
      <foreach collection="empIds" item="item" index="index" open="(" separator="," close=")">
          #{item}
      </foreach>
    </select>
</mapper>