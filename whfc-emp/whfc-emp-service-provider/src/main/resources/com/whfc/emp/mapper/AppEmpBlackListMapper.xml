<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppEmpBlackListMapper">
    <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppEmpBlackList">
        <!--@mbg.generated-->
        <!--@Table app_emp_black_list-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="emp_id" jdbcType="INTEGER" property="empId"/>
        <result column="emp_name" jdbcType="VARCHAR" property="empName"/>
        <result column="id_card_no" jdbcType="VARCHAR" property="idCardNo"/>
        <result column="dept_id" jdbcType="INTEGER" property="deptId"/>
        <result column="dept_name" jdbcType="VARCHAR" property="deptName"/>
        <result column="time" jdbcType="TIMESTAMP" property="time"/>
        <result column="user_id" jdbcType="INTEGER" property="userId"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <result column="reason" jdbcType="VARCHAR" property="reason"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, emp_id, emp_name, id_card_no, dept_id, dept_name, `time`, user_id, user_name,
        reason, del_flag, update_time, create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from app_emp_black_list
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete from app_emp_black_list
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppEmpBlackList"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into app_emp_black_list (emp_id, emp_name, id_card_no,
        dept_id, dept_name, `time`,
        user_id, user_name, reason,
        del_flag, update_time, create_time
        )
        values (#{empId,jdbcType=INTEGER}, #{empName,jdbcType=VARCHAR}, #{idCardNo,jdbcType=VARCHAR},
        #{deptId,jdbcType=INTEGER}, #{deptName,jdbcType=VARCHAR}, #{time,jdbcType=TIMESTAMP},
        #{userId,jdbcType=INTEGER}, #{userName,jdbcType=VARCHAR}, #{reason,jdbcType=VARCHAR},
        #{delFlag,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}
        )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppEmpBlackList"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into app_emp_black_list
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="empId != null">
                emp_id,
            </if>
            <if test="empName != null">
                emp_name,
            </if>
            <if test="idCardNo != null">
                id_card_no,
            </if>
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="deptName != null">
                dept_name,
            </if>
            <if test="time != null">
                `time`,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="userName != null">
                user_name,
            </if>
            <if test="reason != null">
                reason,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="empId != null">
                #{empId,jdbcType=INTEGER},
            </if>
            <if test="empName != null">
                #{empName,jdbcType=VARCHAR},
            </if>
            <if test="idCardNo != null">
                #{idCardNo,jdbcType=VARCHAR},
            </if>
            <if test="deptId != null">
                #{deptId,jdbcType=INTEGER},
            </if>
            <if test="deptName != null">
                #{deptName,jdbcType=VARCHAR},
            </if>
            <if test="time != null">
                #{time,jdbcType=TIMESTAMP},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=INTEGER},
            </if>
            <if test="userName != null">
                #{userName,jdbcType=VARCHAR},
            </if>
            <if test="reason != null">
                #{reason,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppEmpBlackList">
        <!--@mbg.generated-->
        update app_emp_black_list
        <set>
            <if test="empId != null">
                emp_id = #{empId,jdbcType=INTEGER},
            </if>
            <if test="empName != null">
                emp_name = #{empName,jdbcType=VARCHAR},
            </if>
            <if test="idCardNo != null">
                id_card_no = #{idCardNo,jdbcType=VARCHAR},
            </if>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=INTEGER},
            </if>
            <if test="deptName != null">
                dept_name = #{deptName,jdbcType=VARCHAR},
            </if>
            <if test="time != null">
                `time` = #{time,jdbcType=TIMESTAMP},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=INTEGER},
            </if>
            <if test="userName != null">
                user_name = #{userName,jdbcType=VARCHAR},
            </if>
            <if test="reason != null">
                reason = #{reason,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.whfc.emp.entity.AppEmpBlackList">
        <!--@mbg.generated-->
        update app_emp_black_list
        set emp_id = #{empId,jdbcType=INTEGER},
        emp_name = #{empName,jdbcType=VARCHAR},
        id_card_no = #{idCardNo,jdbcType=VARCHAR},
        dept_id = #{deptId,jdbcType=INTEGER},
        dept_name = #{deptName,jdbcType=VARCHAR},
        `time` = #{time,jdbcType=TIMESTAMP},
        user_id = #{userId,jdbcType=INTEGER},
        user_name = #{userName,jdbcType=VARCHAR},
        reason = #{reason,jdbcType=VARCHAR},
        del_flag = #{delFlag,jdbcType=INTEGER},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        create_time = #{createTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <select id="selectByDeptIds" resultType="com.whfc.emp.dto.AppEmpBlackDTO">
        select id,
        emp_name,
        id_card_no,
        dept_name,
        `time`,
        user_name,
        emp_id,
        reason
        from app_emp_black_list
        where del_flag = 0
        and  dept_id=#{deptId}
        <if test="keyword!=null and keyword!=''">
            and (emp_name like concat('%',#{keyword},'%')
            or id_card_no like concat('%',#{keyword},'%'))
        </if>
        order by id desc
    </select>
    <insert id="batchInsert">
        INSERT INTO app_emp_black_list
        (
        emp_id,
        emp_name,
        dept_id,
        id_card_no,
        reason,
        user_id,
        user_name,
        `time`
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.empId},
            #{item.empName},
            #{item.deptId},
            #{item.idCardNo},
            #{item.reason},
            #{item.userId},
            #{item.userName},
            #{item.time}
            )
        </foreach>
    </insert>
    <update id="deleteLogicById">
        update app_emp_black_list
        set del_flag = 1
        where id = #{id}
    </update>
    <select id="countByEmpIdAndDeptId" resultType="java.lang.Integer">
        select count(1)
        from app_emp_black_list
        where del_flag = 0
        and emp_id = #{empId}
        and dept_id = #{deptId}
    </select>
</mapper>