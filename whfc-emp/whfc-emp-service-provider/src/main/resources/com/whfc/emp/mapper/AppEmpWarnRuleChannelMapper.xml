<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppEmpWarnRuleChannelMapper">
    <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppEmpWarnRuleChannel">
        <!--@mbg.generated-->
        <!--@Table app_emp_warn_rule_channel-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="rule_id" jdbcType="INTEGER" property="ruleId"/>
        <result column="msg_channel" jdbcType="INTEGER" property="msgChannel"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, rule_id, msg_channel, del_flag, update_time, create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from app_emp_warn_rule_channel
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete from app_emp_warn_rule_channel
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppEmpWarnRuleChannel"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into app_emp_warn_rule_channel (rule_id, msg_channel, del_flag,
        update_time, create_time)
        values (#{ruleId,jdbcType=INTEGER}, #{msgChannel,jdbcType=INTEGER}, #{delFlag,jdbcType=INTEGER},
        #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.whfc.emp.entity.AppEmpWarnRuleChannel" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into app_emp_warn_rule_channel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ruleId != null">
                rule_id,
            </if>
            <if test="msgChannel != null">
                msg_channel,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ruleId != null">
                #{ruleId,jdbcType=INTEGER},
            </if>
            <if test="msgChannel != null">
                #{msgChannel,jdbcType=INTEGER},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppEmpWarnRuleChannel">
        <!--@mbg.generated-->
        update app_emp_warn_rule_channel
        <set>
            <if test="ruleId != null">
                rule_id = #{ruleId,jdbcType=INTEGER},
            </if>
            <if test="msgChannel != null">
                msg_channel = #{msgChannel,jdbcType=INTEGER},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.whfc.emp.entity.AppEmpWarnRuleChannel">
        <!--@mbg.generated-->
        update app_emp_warn_rule_channel
        set rule_id = #{ruleId,jdbcType=INTEGER},
        msg_channel = #{msgChannel,jdbcType=INTEGER},
        del_flag = #{delFlag,jdbcType=INTEGER},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        create_time = #{createTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <select id="selectByRuleId" resultType="java.lang.Integer">
      select msg_channel
      from app_emp_warn_rule_channel
      where del_flag = 0
      and rule_id = #{ruleId}
    </select>
    <update id="deleteByRuleId">
        update app_emp_warn_rule_channel
        set del_flag = 1
        where rule_id = #{ruleId}
    </update>
    <insert id="batchInsert">
        INSERT INTO app_emp_warn_rule_channel
        (rule_id,msg_channel)
        values
        <foreach collection="msgChannelList" item="msgChannel" separator=",">
        (#{ruleId},#{msgChannel})
        </foreach>
    </insert>
</mapper>