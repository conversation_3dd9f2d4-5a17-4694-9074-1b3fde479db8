<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppEmpConfigMapper">
    <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppEmpConfig">
        <!--@mbg.generated-->
        <!--@Table app_emp_config-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="dept_id" jdbcType="INTEGER" property="deptId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="value" jdbcType="VARCHAR" property="value"/>
        <result column="enable_flag" jdbcType="INTEGER" property="enableFlag"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, dept_id, `name`, code, `value`, enable_flag, del_flag, update_time, create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from app_emp_config
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete
        from app_emp_config
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppEmpConfig"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into app_emp_config (dept_id, `name`, code,
                                    `value`, enable_flag, del_flag,
                                    update_time, create_time)
        values (#{deptId,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, #{code,jdbcType=VARCHAR},
                #{value,jdbcType=VARCHAR}, #{enableFlag,jdbcType=INTEGER}, #{delFlag,jdbcType=INTEGER},
                #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppEmpConfig"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into app_emp_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="name != null">
                `name`,
            </if>
            <if test="code != null">
                code,
            </if>
            <if test="value != null">
                `value`,
            </if>
            <if test="enableFlag != null">
                enable_flag,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                #{deptId,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="code != null">
                #{code,jdbcType=VARCHAR},
            </if>
            <if test="value != null">
                #{value,jdbcType=VARCHAR},
            </if>
            <if test="enableFlag != null">
                #{enableFlag,jdbcType=INTEGER},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppEmpConfig">
        <!--@mbg.generated-->
        update app_emp_config
        <set>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="code != null">
                code = #{code,jdbcType=VARCHAR},
            </if>
            <if test="value != null">
                `value` = #{value,jdbcType=VARCHAR},
            </if>
            <if test="enableFlag != null">
                enable_flag = #{enableFlag,jdbcType=INTEGER},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.whfc.emp.entity.AppEmpConfig">
        <!--@mbg.generated-->
        update app_emp_config
        set dept_id     = #{deptId,jdbcType=INTEGER},
            `name`      = #{name,jdbcType=VARCHAR},
            code        = #{code,jdbcType=VARCHAR},
            `value`     = #{value,jdbcType=VARCHAR},
            enable_flag = #{enableFlag,jdbcType=INTEGER},
            del_flag    = #{delFlag,jdbcType=INTEGER},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            create_time = #{createTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectByDeptIdAndCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from app_emp_config
        where del_flag = 0
          and dept_id = #{deptId}
          and code = #{code}
    </select>

    <select id="selectByDeptId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from app_emp_config
        where del_flag = 0
          and dept_id = #{deptId}
    </select>

    <insert id="insertOrUpdate">
        insert into app_emp_config
                (`value`,
                 enable_flag,
                 dept_id,
                 `code`)
                values
        <foreach collection="list" item="item" separator=",">
            (#{item.value,jdbcType=VARCHAR},
             #{item.enableFlag,jdbcType=INTEGER},
             #{item.deptId,jdbcType=INTEGER},
             #{item.code,jdbcType=VARCHAR})
        </foreach>
        on duplicate key update `value`     =values(`value`),
                                enable_flag = values(enable_flag),
                                dept_id     = values(dept_id),
                                code        = values(code)
    </insert>

    <update id="delByDeptIdAndCode">
        DELETE FROM app_emp_config
        WHERE dept_id = #{deptId}
          AND code = #{code}
    </update>

    <select id="selectDeptIdByCodeAndValue" resultType="java.lang.Integer">
        SELECT dept_id
        FROM app_emp_config
        WHERE del_flag = 0
        AND code = #{code}
        AND value = #{value}
    </select>
</mapper>