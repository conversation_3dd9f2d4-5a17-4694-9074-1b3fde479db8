<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppFaceGatePersonMapper">
    <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppFaceGatePerson">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="dept_id" jdbcType="INTEGER" property="deptId" />
        <result column="face_gate_id" jdbcType="INTEGER" property="faceGateId"/>
        <result column="device_key" jdbcType="VARCHAR" property="deviceKey"/>
        <result column="emp_id" jdbcType="INTEGER" property="empId"/>
        <result column="person_guid" jdbcType="VARCHAR" property="personGuid"/>
        <result column="task_type" jdbcType="INTEGER" property="taskType"/>
        <result column="message" jdbcType="VARCHAR" property="message"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,
        dept_id,
        face_gate_id,
        device_key,
        emp_id,
        person_guid,
        task_type,
        message,
        del_flag,
        update_time,
        create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from app_face_gate_person
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from app_face_gate_person
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppFaceGatePerson" useGeneratedKeys="true">
        insert into app_face_gate_person
        (
            dept_id,
            face_gate_id,
            device_key,
            emp_id,
            person_guid,
            task_type,
            message,
            del_flag,
            update_time,
            create_time
        )
        values
        (
            #{deptId},
            #{faceGateId,jdbcType=INTEGER},
            #{deviceKey,jdbcType=VARCHAR},
            #{empId,jdbcType=INTEGER},
            #{personGuid,jdbcType=VARCHAR},
            #{taskType,jdbcType=INTEGER},
            #{message,jdbcType=VARCHAR},
            #{delFlag,jdbcType=INTEGER},
            #{updateTime,jdbcType=TIMESTAMP},
            #{createTime,jdbcType=TIMESTAMP}
        )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppFaceGatePerson"
            useGeneratedKeys="true">
        insert into app_face_gate_person
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="faceGateId != null">
                face_gate_id,
            </if>
            <if test="deviceKey != null">
                device_key,
            </if>
            <if test="empId != null">
                emp_id,
            </if>
            <if test="personGuid != null">
                person_guid,
            </if>
            <if test="taskType != null">
                task_type,
            </if>
            <if test="message != null">
                message,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                #{deptId},
            </if>
            <if test="faceGateId != null">
                #{faceGateId,jdbcType=INTEGER},
            </if>
            <if test="deviceKey != null">
                #{deviceKey,jdbcType=VARCHAR},
            </if>
            <if test="empId != null">
                #{empId,jdbcType=INTEGER},
            </if>
            <if test="personGuid != null">
                #{personGuid,jdbcType=VARCHAR},
            </if>
            <if test="taskType != null">
                #{taskType,jdbcType=INTEGER},
            </if>
            <if test="message != null">
                #{message,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppFaceGatePerson">
        update app_face_gate_person
        <set>
            <if test="deptId != null">
                dept_id = #{deptId},
            </if>
            <if test="faceGateId != null">
                face_gate_id = #{faceGateId,jdbcType=INTEGER},
            </if>
            <if test="deviceKey != null">
                device_key = #{deviceKey,jdbcType=VARCHAR},
            </if>
            <if test="empId != null">
                emp_id = #{empId,jdbcType=INTEGER},
            </if>
            <if test="personGuid != null">
                person_guid = #{personGuid,jdbcType=VARCHAR},
            </if>
            <if test="taskType != null">
                task_type = #{taskType,jdbcType=INTEGER},
            </if>
            <if test="message != null">
                message = #{message,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.whfc.emp.entity.AppFaceGatePerson">
        update app_face_gate_person
        set dept_id = #{deptId},
        face_gate_id = #{faceGateId,jdbcType=INTEGER},
        device_key = #{deviceKey,jdbcType=VARCHAR},
        emp_id = #{empId,jdbcType=INTEGER},
        person_guid = #{personGuid,jdbcType=VARCHAR},
        task_type = #{taskType,jdbcType=INTEGER},
        message = #{message,jdbcType=VARCHAR},
        del_flag = #{delFlag,jdbcType=INTEGER},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        create_time = #{createTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="countByFaceGateId" resultType="java.lang.Integer">
      select count(1)
        from app_face_gate_person
       where del_flag=0
         and face_gate_id=#{faceGateId}
    </select>

    <select id="selectByFaceGateIdAndEmpId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from app_face_gate_person
        where del_flag=0
        and face_gate_id=#{faceGateId}
        and emp_id=#{empId}
        order by id desc
        limit 1
    </select>

    <select id="selectPersonGuidByFaceGateIdAndEmpId" resultType="java.lang.String">
        select person_guid
        from app_face_gate_person
        where del_flag=0
        and face_gate_id=#{faceGateId}
        and emp_id=#{empId}
        order by id desc
        limit 1
    </select>

    <select id="selectPersonByFaceGateId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from app_face_gate_person
        where del_flag=0
        and face_gate_id=#{faceGateId}
    </select>

    <select id="selectByEmpIdList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from app_face_gate_person
        where del_flag=0
        and emp_id in
        <foreach collection="empIdList" item="empId" separator="," open="(" close=")">
            #{empId}
        </foreach>
    </select>

    <select id="selectPersonGuidByFaceGateId" resultType="java.lang.String">
        select person_guid
        from app_face_gate_person
        where del_flag = 0
        and face_gate_id = #{faceGateId}
    </select>

    <select id="selectByPersonGuidAndDeviceKey" resultMap="BaseResultMap">
        select  afgp.id,
                afgp.face_gate_id,
                afgp.device_key,
                afgp.emp_id,
                afgp.person_guid,
                afgp.del_flag,
                afgp.update_time,
                afgp.create_time
        FROM app_face_gate_person afgp
        INNER JOIN app_face_gate afg ON afg.id = afgp.face_gate_id
        where afgp.person_guid = #{personGuid}
        and afg.device_key = #{deviceKey}
        and afgp.del_flag = 0
        limit 1
    </select>

    <update id="delBYDeviceKeyAndParsonGuid">
        update app_face_gate_person
        set del_flag=1
        where del_flag=0
        and device_key=#{deviceKey}
        and (1=0
        <foreach collection="personGuidList" item="personGuid">
            or person_guid=#{personGuid}
        </foreach>
        )
    </update>

    <update id="delByDeviceKeyAndEmpId">
        update app_face_gate_person
        set del_flag=1
        where del_flag=0
          and device_key=#{deviceKey}
          and emp_id = #{empId}
    </update>

    <select id="selectByFaceGateId" resultType="com.whfc.emp.dto.AppFaceGateEmpDTO">
        select  afgp.emp_id,
                ae.emp_name ,
                ae.phone,
                ae.avatar,
                ae.work_type_name,
                ae.group_name,
                ae.key_position_flag,
                ae.key_position_auth,
                afgp.message as stateMessage,
                afgp.task_type
        from app_face_gate_person afgp
        inner join app_emp ae on ae.id=afgp.emp_id
        where afgp.del_flag=0
        and afgp.face_gate_id=#{faceGateId}
        <if test="type == 1">
            and task_type = 3
        </if>
        <if test="type == 2">
            and task_type in (0,1,2)
        </if>
        <if test="type == 3">
            and task_type in (-1,-2,-3)
        </if>
        <if test="keyword != null and keyword!=''">
            and ae.emp_name like concat('%',#{keyword},'%')
        </if>
        <if test="groupId != null">
            and ae.group_id = #{groupId}
        </if>
        <if test="workTypeId != null">
            and ae.work_type_id = #{workTypeId}
        </if>
        order by afgp.id desc
    </select>

    <select id="selectUnGrantEmp" resultType="com.whfc.emp.dto.AppFaceGateEmpDTO">
        SELECT  ae.id AS empId,
                ae.phone ,
                ae.avatar,
                ae.emp_name,
                ae.group_name,
                ae.work_type_name
        FROM app_emp ae
        where ae.del_flag=0
        and ae.dept_id=#{deptId}
        and ae.post_state=1
        and ae.id not in (
            select emp_id
            from app_face_gate_person
            where face_gate_id=#{faceGateId}
            and del_flag=0
        )
        <if test="keyword!=null and keyword!=''">
            and ae.emp_name like concat('%',#{keyword},'%')
        </if>
        <if test="groupId!=null">
            and ae.group_id = #{groupId}
        </if>
        order by ae.id desc
    </select>

    <select id="selectNeedGrantEmp" resultType="com.whfc.emp.dto.AppFaceGateEmpDTO">
         SELECT ae.id as empId,
                ae.emp_name,
                ae.phone,
                ae.avatar,
                afgp.id as personId,
                afgp.face_gate_id,
                afgp.device_key,
                afgp.person_guid,
                afgp.task_type
        FROM app_face_gate_person afgp
        INNER JOIN app_emp ae on ae.id = afgp.emp_id
        inner join app_face_gate afg on afgp.face_gate_id = afg.id
        where afg.state = 1
          and afg.del_flag = 0
          and afgp.del_flag = 0
          and afgp.task_type in (0,1,2)
    </select>

    <select id="selectNeedUnGrantEmp" resultType="com.whfc.emp.dto.AppFaceGateEmpDTO">
        SELECT ae.id as empId,
               ae.emp_name,
               ae.phone,
               ae.avatar,
               afgp.id as personId,
               afgp.face_gate_id,
               afgp.device_key,
               afgp.person_guid,
               afgp.task_type
        FROM app_face_gate_person afgp
         INNER JOIN app_emp ae on ae.id = afgp.emp_id
         inner join app_face_gate afg on afgp.face_gate_id = afg.id
        where afg.state = 1
          and afg.del_flag = 0
          and afgp.del_flag = 0
          and afgp.task_type =  11
    </select>

    <select id="selectByFaceGateIdAndEmpIdAndTask" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from app_face_gate_person
        where del_flag=0
        and face_gate_id=#{faceGateId}
        and emp_id=#{empId}
        and task_type in (0,1,2)
    </select>

    <select id="selectByFailFaceGateId" resultType="com.whfc.emp.dto.AppFaceGateEmpDTO">
        select  afgp.emp_id,
                ae.emp_name ,
                ae.phone,
                ae.avatar,
                ae.work_type_name,
                ae.group_name,
                ae.key_position_flag,
                ae.key_position_auth
          from app_face_gate_person afgp
         inner join app_emp ae on ae.id=afgp.emp_id
         where ae.del_flag=0
           and afgp.del_flag=0
           and afgp.face_gate_id=#{faceGateId}
        <if test="keyword != null and keyword!=''">
            and ae.emp_name like concat('%',#{keyword},'%')
        </if>
        <if test="groupId != null">
            and ae.group_id = #{groupId}
        </if>
        and afgp.task_type in (-1,-2,-3)
        order by afgp.id desc
    </select>

    <update id="updateGrantInfo">
        update app_face_gate_person
           set task_type = #{taskType},
               person_guid = #{personGuid}
         where id = #{id}
    </update>

    <update id="updateTaskType">
        update app_face_gate_person
           set task_type = #{taskType},
               message = #{message}
         where id = #{id}
    </update>

    <select id="selectByEmpIdAndFackGateList" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List"/>
        FROM app_face_gate_person
        WHERE del_flag = 0 AND emp_id = #{empId}
        AND face_gate_id IN
        <foreach collection="faceGateIds" item="faceGateId" separator="," open="(" close=")">
            #{faceGateId}
        </foreach>
    </select>

    <select id="selectCountByFaceGateId" resultType="com.whfc.emp.dto.AppFaceGateDTO">
        SELECT face_gate_id, COUNT(*) AS authEmpNum
        FROM app_face_gate_person
        WHERE del_flag = 0
        AND dept_id = #{deptId}
        AND task_type = 3
        GROUP BY face_gate_id
    </select>

    <select id="countByEmpId" resultType="java.lang.Integer">
        SELECT count(1)
        FROM app_face_gate_person
        WHERE del_flag = 0
        AND emp_id = #{empId}
    </select>
</mapper>