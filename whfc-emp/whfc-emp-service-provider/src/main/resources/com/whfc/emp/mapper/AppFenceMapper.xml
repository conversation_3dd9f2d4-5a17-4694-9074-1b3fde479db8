<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppFenceMapper">
    <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppFence">
        <!--@mbg.generated-->
        <!--@Table app_fence-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="dept_id" jdbcType="INTEGER" property="deptId"/>
        <result column="rule_id" jdbcType="INTEGER" property="ruleId"/>
        <result column="module_type" jdbcType="INTEGER" property="moduleType"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="polygon" jdbcType="OTHER" property="polygon"/>
        <result column="center" jdbcType="INTEGER" property="center"/>
        <result column="radius" jdbcType="DOUBLE" property="radius"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, dept_id, rule_id, module_type, `type`, `name`, polygon, center, radius, del_flag,
        update_time, create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from app_fence
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete from app_fence
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppFence"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into app_fence (dept_id, rule_id, module_type,
        `type`, `name`, polygon,
        center, radius, del_flag,
        update_time, create_time)
        values (#{deptId,jdbcType=INTEGER}, #{ruleId,jdbcType=INTEGER}, #{moduleType,jdbcType=INTEGER},
        #{type,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, #{polygon,jdbcType=OTHER},
        #{center,jdbcType=INTEGER}, #{radius,jdbcType=DOUBLE}, #{delFlag,jdbcType=INTEGER},
        #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppFence"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into app_fence
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="ruleId != null">
                rule_id,
            </if>
            <if test="moduleType != null">
                module_type,
            </if>
            <if test="type != null">
                `type`,
            </if>
            <if test="name != null">
                `name`,
            </if>
            <if test="polygon != null">
                polygon,
            </if>
            <if test="center != null">
                center,
            </if>
            <if test="radius != null">
                radius,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                #{deptId,jdbcType=INTEGER},
            </if>
            <if test="ruleId != null">
                #{ruleId,jdbcType=INTEGER},
            </if>
            <if test="moduleType != null">
                #{moduleType,jdbcType=INTEGER},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="polygon != null">
                #{polygon,jdbcType=OTHER},
            </if>
            <if test="center != null">
                #{center,jdbcType=INTEGER},
            </if>
            <if test="radius != null">
                #{radius,jdbcType=DOUBLE},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppFence">
        <!--@mbg.generated-->
        update app_fence
        <set>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=INTEGER},
            </if>
            <if test="ruleId != null">
                rule_id = #{ruleId,jdbcType=INTEGER},
            </if>
            <if test="moduleType != null">
                module_type = #{moduleType,jdbcType=INTEGER},
            </if>
            <if test="type != null">
                `type` = #{type,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="polygon != null">
                polygon = #{polygon,jdbcType=OTHER},
            </if>
            <if test="center != null">
                center = #{center,jdbcType=INTEGER},
            </if>
            <if test="radius != null">
                radius = #{radius,jdbcType=DOUBLE},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.whfc.emp.entity.AppFence">
        <!--@mbg.generated-->
        update app_fence
        set dept_id = #{deptId,jdbcType=INTEGER},
        rule_id = #{ruleId,jdbcType=INTEGER},
        module_type = #{moduleType,jdbcType=INTEGER},
        `type` = #{type,jdbcType=INTEGER},
        `name` = #{name,jdbcType=VARCHAR},
        polygon = #{polygon,jdbcType=OTHER},
        center = #{center,jdbcType=INTEGER},
        radius = #{radius,jdbcType=DOUBLE},
        del_flag = #{delFlag,jdbcType=INTEGER},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        create_time = #{createTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <select id="selectByParam" resultType="com.whfc.emp.dto.AppFenceDTO">
        select af.name,
        af.id,
        af.type,
        af.radius,
        af.dept_id,
        ASTEXT(af.center) as center,
        ASTEXT(af.polygon) as polygon
        from app_fence af
        where af.del_flag=0
        and af.dept_id = #{deptId}
        <if test="keyword != null and keyword!=''">
            and af.name like concat('%',#{keyword},'%')
        </if>
        order by af.id desc
    </select>
    <insert id="insertSelectiveByParam">
        insert into app_fence
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="polygon != null">
                polygon,
            </if>
            <if test="center != null">
                center,
            </if>
            <if test="radius != null">
                radius,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                #{deptId,jdbcType=INTEGER},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="polygon != null">
                GEOMFROMTEXT(#{polygon}),
            </if>
            <if test="center != null">
                GEOMFROMTEXT(#{center}),
            </if>
            <if test="radius != null">
                #{radius,jdbcType=DOUBLE},
            </if>
        </trim>
    </insert>
    <insert id="updateSelectiveByParam">
        update app_fence
        <set>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=INTEGER},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="polygon != null">
                polygon = GEOMFROMTEXT(#{polygon}),
            </if>
            <if test="center != null">
                center = GEOMFROMTEXT(#{center}),
            </if>
            <if test="radius != null">
                radius = #{radius,jdbcType=DOUBLE},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </insert>
    <update id="deleteLogicById">
        update app_fence
        set del_flag=1
        where id=#{id}
    </update>
    <select id="selectFenceIdListContainsGps" resultType="com.whfc.emp.dto.AppFenceDTO">
        select af.id,
            af.name
        from app_fence af
        inner join app_fence_emp afe on afe.fence_id = af.id
        where af.dept_id = #{deptId}
        and afe.del_flag = 0
        and afe.emp_id = #{empId}
        and af.del_flag = 0
        and (
        ( af.`type` = 1 and ST_Within(GEOMFROMTEXT(concat('POINT(',#{lng},' ',#{lat}, ')')),af.polygon) = 1)
        or ( af.`type` = 2 and af.radius >= st_distance_sphere(GEOMFROMTEXT(concat('POINT(',#{lng},' ',#{lat}, ')')), af.center))
        )
    </select>

    <select id="selectByFenceId" resultType="com.whfc.emp.dto.AppFenceDTO">
        select af.name,
               af.id,
               af.type,
               af.radius,
               af.dept_id,
               IF(af.type = 1, ST_ASTEXT(ST_CENTROID(af.polygon)), ST_ASTEXT(af.center)) AS center,
               ST_ASTEXT(af.polygon) AS polygon
        from app_fence af
                where af.del_flag = 0
                  and af.dept_id = #{deptId}
        <if test="fenceId != null">
            and af.id = #{fenceId}
        </if>
    </select>
</mapper>