<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppBroadcastRecordMapper">
  <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppBroadcastRecord">
    <!--@mbg.generated-->
    <!--@Table app_broadcast_record-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="send_user_id" jdbcType="INTEGER" property="sendUserId" />
    <result column="send_user_name" jdbcType="VARCHAR" property="sendUserName" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, dept_id, content, `type`, send_user_id, send_user_name, del_flag, update_time, 
    create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from app_broadcast_record
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from app_broadcast_record
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppBroadcastRecord" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_broadcast_record (dept_id, content, `type`, 
      send_user_id, send_user_name, del_flag, 
      update_time, create_time)
    values (#{deptId,jdbcType=INTEGER}, #{content,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}, 
      #{sendUserId,jdbcType=INTEGER}, #{sendUserName,jdbcType=VARCHAR}, #{delFlag,jdbcType=INTEGER}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppBroadcastRecord" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_broadcast_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="sendUserId != null">
        send_user_id,
      </if>
      <if test="sendUserName != null">
        send_user_name,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="sendUserId != null">
        #{sendUserId,jdbcType=INTEGER},
      </if>
      <if test="sendUserName != null">
        #{sendUserName,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppBroadcastRecord">
    <!--@mbg.generated-->
    update app_broadcast_record
    <set>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="sendUserId != null">
        send_user_id = #{sendUserId,jdbcType=INTEGER},
      </if>
      <if test="sendUserName != null">
        send_user_name = #{sendUserName,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.whfc.emp.entity.AppBroadcastRecord">
    <!--@mbg.generated-->
    update app_broadcast_record
    set dept_id = #{deptId,jdbcType=INTEGER},
      content = #{content,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=INTEGER},
      send_user_id = #{sendUserId,jdbcType=INTEGER},
      send_user_name = #{sendUserName,jdbcType=VARCHAR},
      del_flag = #{delFlag,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>