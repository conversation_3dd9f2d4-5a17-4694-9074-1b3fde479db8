<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppIndoorPositionStationMapper">
  <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppIndoorPositionStation">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="guid" jdbcType="VARCHAR" property="guid" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="map_id" jdbcType="INTEGER" property="mapId" />
    <result column="x" jdbcType="DOUBLE" property="x" />
    <result column="y" jdbcType="DOUBLE" property="y" />
    <result column="z" jdbcType="DOUBLE" property="z" />
    <result column="lng" jdbcType="DOUBLE" property="lng" />
    <result column="lat" jdbcType="DOUBLE" property="lat" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="ext1" jdbcType="VARCHAR" property="ext1" />
    <result column="ext2" jdbcType="VARCHAR" property="ext2" />
    <result column="ext3" jdbcType="VARCHAR" property="ext3" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id,
    dept_id,
    guid,
    name,
    code,
    map_id,
    x,
    y,
    z,
    lng,
    lat,
    status,
    ext1,
    ext2,
    ext3,
    del_flag,
    update_time,
    create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from app_indoor_position_station
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from app_indoor_position_station
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.whfc.emp.entity.AppIndoorPositionStation" useGeneratedKeys="true" keyProperty="id">
    insert into app_indoor_position_station
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="guid != null">
        guid,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="mapId != null">
        map_id,
      </if>
      <if test="x != null">
        x,
      </if>
      <if test="y != null">
        y,
      </if>
      <if test="z != null">
        z,
      </if>
      <if test="lng != null">
        lng,
      </if>
      <if test="lat != null">
        lat,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="ext1 != null">
        ext1,
      </if>
      <if test="ext2 != null">
        ext2,
      </if>
      <if test="ext3 != null">
        ext3,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="guid != null">
        #{guid,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="mapId != null">
        #{mapId,jdbcType=INTEGER},
      </if>
      <if test="x != null">
        #{x,jdbcType=DOUBLE},
      </if>
      <if test="y != null">
        #{y,jdbcType=DOUBLE},
      </if>
      <if test="z != null">
        #{z,jdbcType=DOUBLE},
      </if>
      <if test="lng != null">
        #{lng,jdbcType=DOUBLE},
      </if>
      <if test="lat != null">
        #{lat,jdbcType=DOUBLE},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="ext1 != null">
        #{ext1,jdbcType=VARCHAR},
      </if>
      <if test="ext2 != null">
        #{ext2,jdbcType=VARCHAR},
      </if>
      <if test="ext3 != null">
        #{ext3,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppIndoorPositionStation">
    update app_indoor_position_station
    <set>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="guid != null">
        guid = #{guid,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="mapId != null">
        map_id = #{mapId,jdbcType=INTEGER},
      </if>
      <if test="x != null">
        x = #{x,jdbcType=DOUBLE},
      </if>
      <if test="y != null">
        y = #{y,jdbcType=DOUBLE},
      </if>
      <if test="z != null">
        z = #{z,jdbcType=DOUBLE},
      </if>
      <if test="lng != null">
        lng = #{lng,jdbcType=DOUBLE},
      </if>
      <if test="lat != null">
        lat = #{lat,jdbcType=DOUBLE},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="ext1 != null">
        ext1 = #{ext1,jdbcType=VARCHAR},
      </if>
      <if test="ext2 != null">
        ext2 = #{ext2,jdbcType=VARCHAR},
      </if>
      <if test="ext3 != null">
        ext3 = #{ext3,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByDeptId" resultType="com.whfc.emp.dto.AppIndoorPositionStationDTO">
    select id as stationId,
            guid,
            name,
            code,
            map_id,
            x,
            y,
            z,
            lng,
            lat,
            status,
            ext1,
            ext2,
            ext3
       from app_indoor_position_station
      where dept_id = #{deptId}
        and del_flag = 0
  </select>

  <select id="selectByMapId" resultType="com.whfc.emp.dto.AppIndoorPositionStationDTO">
    select id as stationId,
           guid,
           name,
           code,
           x,
           y
    from app_indoor_position_station
    where map_id = #{mapId}
      and del_flag = 0
  </select>

  <select id="selectByGuid" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List">
    </include>
      from app_indoor_position_station
     where guid = #{guid}
       and del_flag = 0
  </select>

  <select id="selectByCode" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List">
    </include>
    from app_indoor_position_station
    where code = #{code}
    and del_flag = 0
  </select>

  <update id="logicDeleteById">
    update app_indoor_position_station
   set del_flag = 1
     where id = #{id}
  </update>

  <update id="clearByMapId">
    update app_indoor_position_station
      set map_id = null,
          x=null,
          y=null
    where map_id = #{mapId}
  </update>
</mapper>