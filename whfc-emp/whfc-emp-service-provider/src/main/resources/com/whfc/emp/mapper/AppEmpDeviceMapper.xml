<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppEmpDeviceMapper">
    <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppEmpDevice">
        <!--@mbg.generated-->
        <!--@Table app_emp_devie-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="guid" jdbcType="VARCHAR" property="guid"/>
        <result column="dept_id" jdbcType="INTEGER" property="deptId"/>
        <result column="device_type" jdbcType="INTEGER" property="deviceType"/>
        <result column="platform" jdbcType="VARCHAR" property="platform"/>
        <result column="sn" jdbcType="VARCHAR" property="sn"/>
        <result column="net_state" jdbcType="INTEGER" property="netState"/>
        <result column="battery_power" jdbcType="INTEGER" property="batteryPower"/>
        <result column="time" jdbcType="TIMESTAMP" property="time"/>
        <result column="color" jdbcType="VARCHAR" property="color"/>
        <result column="emp_id" jdbcType="INTEGER" property="empId"/>
        <result column="device_id" jdbcType="INTEGER" property="deviceId"/>
        <result column="bind_flag" jdbcType="INTEGER" property="bindFlag"/>
        <result column="bind_time" jdbcType="TIMESTAMP" property="bindTime"/>
        <result column="bind_user" jdbcType="VARCHAR" property="bindUser"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        guid,
        dept_id,
        device_type,
        platform,
        sn,
        net_state,
        battery_power,
        time,
        color,
        emp_id,
        device_id,
        bind_flag,
        bind_time,
        bind_user,
        del_flag,
        update_time,
        create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from app_emp_device
        where id = #{id,jdbcType=INTEGER}
    </select>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppEmpDevice"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into app_emp_device
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="guid != null">
                guid,
            </if>
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="deviceType != null">
                device_type,
            </if>
            <if test="platform != null">
                platform,
            </if>
            <if test="sn != null">
                sn,
            </if>
            <if test="netState != null">
                net_state,
            </if>
            <if test="batteryPower != null">
                battery_power,
            </if>
            <if test="time != null">
                time,
            </if>
            <if test="color != null">
                color,
            </if>
            <if test="empId != null">
                emp_id,
            </if>
            <if test="deviceId != null">
                device_id,
            </if>
            <if test="bindFlag != null">
                bind_flag,
            </if>
            <if test="bindTime != null">
                bind_time,
            </if>
            <if test="bindUser != null">
                bind_user,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="guid != null">
                #{guid,jdbcType=VARCHAR},
            </if>
            <if test="deptId != null">
                #{deptId,jdbcType=INTEGER},
            </if>
            <if test="deviceType != null">
                #{deviceType,jdbcType=INTEGER},
            </if>
            <if test="platform != null">
                #{platform,jdbcType=VARCHAR},
            </if>
            <if test="sn != null">
                #{sn,jdbcType=VARCHAR},
            </if>
            <if test="netState != null">
                #{netState,jdbcType=INTEGER},
            </if>
            <if test="batteryPower != null">
                #{batteryPower,jdbcType=INTEGER},
            </if>
            <if test="time != null">
                #{time,jdbcType=TIMESTAMP},
            </if>
            <if test="color != null">
                #{color,jdbcType=VARCHAR},
            </if>
            <if test="empId != null">
                #{empId,jdbcType=INTEGER},
            </if>
            <if test="deviceId != null">
                #{deviceId,jdbcType=INTEGER},
            </if>
            <if test="bindFlag != null">
                #{bindFlag,jdbcType=INTEGER},
            </if>
            <if test="bindTime != null">
                #{bindTime,jdbcType=TIMESTAMP},
            </if>
            <if test="bindUser != null">
                #{bindUser,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppEmpDevice">
        <!--@mbg.generated-->
        update app_emp_device
        <set>
            <if test="guid != null">
                guid = #{guid,jdbcType=VARCHAR},
            </if>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=INTEGER},
            </if>
            <if test="deviceType != null">
                device_type = #{deviceType,jdbcType=INTEGER},
            </if>
            <if test="platform != null">
                platform = #{platform,jdbcType=VARCHAR},
            </if>
            <if test="sn != null">
                sn = #{sn,jdbcType=VARCHAR},
            </if>
            <if test="netState != null">
                net_state = #{netState,jdbcType=INTEGER},
            </if>
            <if test="batteryPower != null">
                battery_power = #{batteryPower,jdbcType=INTEGER},
            </if>
            <if test="time != null">
                time = #{time,jdbcType=TIMESTAMP},
            </if>
            color = #{color,jdbcType=VARCHAR},
            <if test="empId != null">
                emp_id = #{empId,jdbcType=INTEGER},
            </if>
            <if test="deviceId != null">
                device_id = #{deviceId,jdbcType=INTEGER},
            </if>
            <if test="bindFlag != null">
                bind_flag = #{bindFlag,jdbcType=INTEGER},
            </if>
            <if test="bindTime != null">
                bind_time = #{bindTime,jdbcType=TIMESTAMP},
            </if>
            <if test="bindUser != null">
                bind_user = #{bindUser,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="countByEmpId" resultType="java.lang.Integer">
        select count(*)
        from app_emp_device aed
        where aed.emp_id = #{empId}
          and aed.del_flag = 0
    </select>

    <select id="selectList" resultType="com.whfc.emp.dto.device.AppEmpDeviceDTO">
        SELECT aed.guid,
               aed.dept_id,
               aed.device_type,
               aed.platform,
               aed.sn,
               aed.net_state,
               aed.battery_power,
               aed.time,
               aed.color,
               aed.bind_flag,
               aed.emp_id,
               ae.group_name,
               ae.emp_name,
               ae.gender,
               aed.bind_time
        FROM app_emp_device aed
                 LEFT JOIN app_emp ae ON ae.id = aed.emp_id
        WHERE aed.del_flag = 0
          AND aed.dept_id = #{param.deptId}
        <if test="param.corpId != null">
            AND ae.corp_id = #{param.corpId}
        </if>
        <if test="param.groupId != null">
            AND ae.group_id = #{param.groupId}
        </if>
        <if test="param.keyword != null and param.keyword != ''">
            AND (ae.emp_name LIKE CONCAT('%', #{param.keyword}, '%')
                OR aed.sn LIKE CONCAT('%', #{param.keyword}, '%'))
        </if>
        <if test="param.bindFlag != null">
            AND aed.bind_flag = #{param.bindFlag}
        </if>
        <if test="param.netState != null">
            AND aed.net_state = #{param.netState}
        </if>
        ORDER BY aed.time DESC
    </select>

    <select id="countDeviceNetState" resultType="java.util.Map">
        SELECT COUNT(CASE WHEN net_state = 1 THEN 1 ELSE NULL END) onlineNum,
               COUNT(CASE WHEN net_state = 0 THEN 1 ELSE NULL END) offlineNum
        FROM app_emp_device aed
                 LEFT JOIN app_emp ae ON ae.id = aed.emp_id
        WHERE aed.del_flag = 0
          AND aed.dept_id = #{param.deptId}
        <if test="param.corpId != null">
            AND ae.corp_id = #{param.corpId}
        </if>
        <if test="param.groupId != null">
            AND ae.group_id = #{param.groupId}
        </if>
        <if test="param.keyword != null and param.keyword != ''">
            AND (ae.emp_name LIKE CONCAT('%', #{param.keyword}, '%')
                OR aed.sn LIKE CONCAT('%', #{param.keyword}, '%'))
        </if>
        <if test="param.bindFlag != null">
            AND aed.bind_flag = #{param.bindFlag}
        </if>
        <if test="param.netState != null">
            AND aed.net_state = #{param.netState}
        </if>
    </select>


    <select id="selectNoInitData" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM app_emp_device
        WHERE del_flag = 0
          AND guid IS NULL
    </select>

    <select id="selectByPlatformAndSn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from app_emp_device
        where sn = #{sn}
          AND platform = #{platform}
          and del_flag = 0
    </select>

    <select id="selectByGuid" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from app_emp_device
        where guid = #{guid}
          and del_flag = 0
    </select>

    <update id="logicDel">
        update app_emp_device
        set del_flag = 1
        where guid = #{guid}
    </update>

    <update id="updateBindState">
        update app_emp_device
        set emp_id    = #{empId},
            bind_time = #{bindTime},
            bind_user = #{username},
            bind_flag = #{bindFlag}
        where id = #{id}
    </update>

    <select id="selectByEmpIdAndType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from app_emp_device
        where emp_id = #{empId}
          and device_type = #{deviceType}
          and del_flag = 0
    </select>

    <update id="updateDeviceState">
        update app_emp_device
        set net_state = #{netState},
        <if test="batteryPower != null">
            battery_power = #{batteryPower},
        </if>
        time = #{time}
        where id = #{id}
    </update>

    <select id="selectByEmpId" resultType="com.whfc.emp.dto.device.AppEmpDeviceDTO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM app_emp_device
        where emp_id = #{empId}
          and del_flag = 0
    </select>

    <select id="countOnlineDevice" resultType="com.whfc.entity.dto.board.AppDeviceStatDTO">
        SELECT dept_id,
               device_type,
               COUNT(id)                                 total,
               COUNT(CASE WHEN net_state = 1 THEN 1 END) onlineNum
        FROM app_emp_device
        WHERE del_flag = 0
          AND emp_id IS NOT NULL
          AND bind_flag = 1
        GROUP BY dept_id, device_type
    </select>

    <select id="countDeviceByDeptId" resultType="com.whfc.entity.dto.board.AppDeviceStatDTO">
        SELECT COUNT(id)                                 total,
               COUNT(CASE WHEN net_state = 1 THEN 1 END) onlineNum
        FROM app_emp_device
        WHERE del_flag = 0
          AND emp_id IS NOT NULL
          AND bind_flag = 1
          AND dept_id = #{deptId}
    </select>
</mapper>