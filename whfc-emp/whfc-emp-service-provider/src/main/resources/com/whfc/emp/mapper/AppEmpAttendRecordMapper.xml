<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppEmpAttendRecordMapper">
    <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppEmpAttendRecord">
        <!--@mbg.generated-->
        <!--@Table app_emp_attend_record-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="emp_id" jdbcType="INTEGER" property="empId"/>
        <result column="dept_id" jdbcType="INTEGER" property="deptId"/>
        <result column="time" jdbcType="TIMESTAMP" property="time"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="direction" jdbcType="INTEGER" property="direction"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="photo" jdbcType="VARCHAR" property="photo"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        emp_id,
        dept_id,
        `time`,
        `type`,
        direction,
        `name`,
        photo,
        del_flag,
        update_time,
        create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from app_emp_attend_record
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete from app_emp_attend_record
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppEmpAttendRecord"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into app_emp_attend_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="empId != null">
                emp_id,
            </if>
            <if test="photo != null">
                photo,
            </if>
            <if test="lat != null">
                lat,
            </if>
            <if test="lng != null">
                lng,
            </if>
            <if test="location != null">
                location,
            </if>
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="time != null">
                `time`,
            </if>
            <if test="type != null">
                `type`,
            </if>
            <if test="direction != null">
                direction,
            </if>
            <if test="name != null">
                `name`,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="empId != null">
                #{empId,jdbcType=INTEGER},
            </if>
            <if test="photo != null">
                #{photo},
            </if>
            <if test="lat != null">
                #{lat},
            </if>
            <if test="lng != null">
                #{lng},
            </if>
            <if test="location != null">
                #{location},
            </if>
            <if test="deptId != null">
                #{deptId,jdbcType=INTEGER},
            </if>
            <if test="time != null">
                #{time,jdbcType=TIMESTAMP},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="direction != null">
                #{direction,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppEmpAttendRecord">
        <!--@mbg.generated-->
        update app_emp_attend_record
        <set>
            <if test="empId != null">
                emp_id = #{empId,jdbcType=INTEGER},
            </if>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=INTEGER},
            </if>
            <if test="time != null">
                `time` = #{time,jdbcType=TIMESTAMP},
            </if>
            <if test="type != null">
                `type` = #{type,jdbcType=INTEGER},
            </if>
            <if test="direction != null">
                direction = #{direction,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectByDeptIdAndDate" resultType="com.whfc.emp.dto.AppEmpInputDataDTO">
        select aear.id as attendRecordId,
        aear.`time`,
        aear.`type`,
        aear.direction,
        ae.emp_name,
        ae.corp_name,
        ae.group_id,
        ae.group_name,
        ae.work_type_name,
        ae.phone,
        ae.dept_name,
        aear.photo,
        aear.name AS deviceName
        from app_emp_attend_record aear
        inner join app_emp ae on ae.id = aear.emp_id
        where ae.del_flag = 0
        and aear.del_flag = 0
        and ae.post_state = 1
        <if test="startTime!=null">
            and aear.`time`>= #{startTime}
        </if>
        <if test="endTime!=null">
            and #{endTime} >= aear.`time`
        </if>
        <if test="type != null">
            and aear.type = #{type}
        </if>
        <if test="deptId != null ">
            and aear.dept_id=#{deptId}
        </if>
        <if test="corpId != null">
            and ae.corp_id = #{corpId}
        </if>
        <if test="groupId != null">
            and ae.group_id = #{groupId}
        </if>
        <if test="workTypeId != null">
            and ae.work_type_id = #{workTypeId}
        </if>
        <if test="keyword!=null and keyword!=''">
            and ae.emp_name like concat('%',#{keyword},'%')
        </if>
        order by aear.`time` desc
    </select>

    <select id="selectByEmpIdAndDate" resultType="com.whfc.emp.dto.AppEmpAttendRecordDTO">
        select direction as state,
               `time`,
               name,
               photo
        from app_emp_attend_record
        where del_flag = 0
        and emp_id = #{empId}
        and `time` >=#{startTime}
        and #{endTime} >= `time`
        <if test="direction != null">
        and direction = #{direction}
        </if>
        <if test="attendType != null">
        and `type` = #{attendType}
        </if>
        order by `time`
    </select>

    <select id="selectLastAttendRecord" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from app_emp_attend_record
        where del_flag = 0
        and emp_id = #{empId}
        and `type` = #{attendType}
        and date(`time`) = #{date}
        order by `time` desc limit 1
    </select>

    <select id="selectByDeptIdAndTime" resultType="com.whfc.emp.dto.BoardEmpAttendRecordDTO">
        SELECT aear.time,
        ae.emp_name,
        ae.work_role_name,
        ae.work_type_name,
        aear.direction,
        aear.`name`,
        (select round(ifnull(work_times/3600,0),2) from app_emp_day where date = date(#{startTime}) and emp_id = ae.id)
        as workTimes
        from app_emp_attend_record aear
        inner join app_emp ae on ae.id = aear.emp_id
        where ae.del_flag = 0
        and aear.del_flag = 0
        <if test="startTime!=null">
            and aear.time >= #{startTime}
        </if>
        <if test="endTime!=null">
            and #{endTime} >= aear.time
        </if>
        <if test="deptId != null ">
            and aear.dept_id=#{deptId}
        </if>
        order by aear.id desc
        limit 20
    </select>

    <select id="selectAttendRecordByDeptIdAndTime" resultType="com.whfc.emp.dto.AppEmpAttendRecordDTO">
        select emp_id,
        `time`,
        direction,
        `type`,
        `name`
        from app_emp_attend_record
        where dept_id = #{deptId}
        and del_flag = 0
        and `time`>=#{startTime}
        and #{endTime} >=`time`
        and type  = #{attendType}
        order by emp_id,`time`
    </select>

    <select id="selectEmpAttendRecCount" resultType="com.whfc.emp.dto.stat.EmpAttendStat">
        select emp_id,
               `time`,
               count(*) as cnt
          from app_emp_attend_record
         where emp_id >0
          <![CDATA[
           and `time` >= #{startTime}
           and `time` <= #{endTime}
          ]]>
          group by emp_id,`time`
          having cnt > 1
    </select>

    <select id="selectAttendRecordByEmpIdAndTime" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
          from app_emp_attend_record
         where emp_id = #{empId}
           and `time` = #{time}
    </select>

    <select id="countByEmpIdAndTime" resultType="java.lang.Integer">
        select count(*)
          from app_emp_attend_record
         where emp_id = #{empId}
           and `time` = #{time}
    </select>

    <delete id="batchDelete">
        delete from app_emp_attend_record
         where id in
         <foreach collection="idList" item="id" open="(" close=")" separator=",">
             #{id,jdbcType=INTEGER}
         </foreach>
    </delete>

    <update id="deleteLogicById">
        update app_emp_attend_record
        set del_flag = 1
        where id = #{id}
    </update>
</mapper>