<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppEmpDeviceLogMapper">
  <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppEmpDeviceLog">
    <!--@mbg.generated-->
    <!--@Table app_emp_device_log-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="emp_id" jdbcType="INTEGER" property="empId" />
    <result column="device_id" jdbcType="INTEGER" property="deviceId" />
    <result column="time" jdbcType="TIMESTAMP" property="time" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, emp_id, device_id, `time`, `type`, user_id, user_name, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from app_emp_device_log
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from app_emp_device_log
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppEmpDeviceLog" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_emp_device_log (emp_id, device_id, `time`, 
      `type`, user_id, user_name, 
      update_time, create_time)
    values (#{empId,jdbcType=INTEGER}, #{deviceId,jdbcType=INTEGER}, #{time,jdbcType=TIMESTAMP}, 
      #{type,jdbcType=INTEGER}, #{userId,jdbcType=INTEGER}, #{userName,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppEmpDeviceLog" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_emp_device_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="empId != null">
        emp_id,
      </if>
      <if test="deviceId != null">
        device_id,
      </if>
      <if test="time != null">
        `time`,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="empId != null">
        #{empId,jdbcType=INTEGER},
      </if>
      <if test="deviceId != null">
        #{deviceId,jdbcType=INTEGER},
      </if>
      <if test="time != null">
        #{time,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppEmpDeviceLog">
    <!--@mbg.generated-->
    update app_emp_device_log
    <set>
      <if test="empId != null">
        emp_id = #{empId,jdbcType=INTEGER},
      </if>
      <if test="deviceId != null">
        device_id = #{deviceId,jdbcType=INTEGER},
      </if>
      <if test="time != null">
        `time` = #{time,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=INTEGER},
      </if>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.whfc.emp.entity.AppEmpDeviceLog">
    <!--@mbg.generated-->
    update app_emp_device_log
    set emp_id = #{empId,jdbcType=INTEGER},
      device_id = #{deviceId,jdbcType=INTEGER},
      `time` = #{time,jdbcType=TIMESTAMP},
      `type` = #{type,jdbcType=INTEGER},
      user_id = #{userId,jdbcType=INTEGER},
      user_name = #{userName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>