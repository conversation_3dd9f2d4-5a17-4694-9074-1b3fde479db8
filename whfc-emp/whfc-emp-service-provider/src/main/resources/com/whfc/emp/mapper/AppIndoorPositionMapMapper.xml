<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppIndoorPositionMapMapper">
  <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppIndoorPositionMap">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="guid" jdbcType="VARCHAR" property="guid" />
    <result column="area_id" jdbcType="INTEGER" property="areaId" />
    <result column="area_name" jdbcType="VARCHAR" property="areaName" />
    <result column="pixel_width" jdbcType="DOUBLE" property="pixelWidth" />
    <result column="pixel_length" jdbcType="DOUBLE" property="pixelLength" />
    <result column="real_width" jdbcType="DOUBLE" property="realWidth" />
    <result column="real_length" jdbcType="DOUBLE" property="realLength" />
    <result column="rotation" jdbcType="DOUBLE" property="rotation" />
    <result column="img_url" jdbcType="VARCHAR" property="imgUrl" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id,
    dept_id,
    `name`,
    guid,
    area_id,
    area_name,
    pixel_width,
    pixel_length,
    real_width,
    real_length,
    rotation,
    img_url,
    del_flag,
    update_time,
    create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from app_indoor_position_map
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from app_indoor_position_map
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.whfc.emp.entity.AppIndoorPositionMap" useGeneratedKeys="true" keyProperty="id">
    insert into app_indoor_position_map
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="guid != null">
        guid,
      </if>
      <if test="areaId != null">
        area_id,
      </if>
      <if test="areaName != null">
        area_name,
      </if>
      <if test="pixelWidth != null">
        pixel_width,
      </if>
      <if test="pixelLength != null">
        pixel_length,
      </if>
      <if test="realWidth != null">
        real_width,
      </if>
      <if test="realLength != null">
        real_length,
      </if>
      <if test="rotation != null">
        rotation,
      </if>
      <if test="imgUrl != null">
        img_url,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="guid != null">
        #{guid,jdbcType=VARCHAR},
      </if>
      <if test="areaId != null">
        #{areaId,jdbcType=INTEGER},
      </if>
      <if test="areaName != null">
        #{areaName,jdbcType=VARCHAR},
      </if>
      <if test="pixelWidth != null">
        #{pixelWidth,jdbcType=DOUBLE},
      </if>
      <if test="pixelLength != null">
        #{pixelLength,jdbcType=DOUBLE},
      </if>
      <if test="realWidth != null">
        #{realWidth,jdbcType=DOUBLE},
      </if>
      <if test="realLength != null">
        #{realLength,jdbcType=DOUBLE},
      </if>
      <if test="rotation != null">
        #{rotation,jdbcType=DOUBLE},
      </if>
      <if test="imgUrl != null">
        #{imgUrl,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppIndoorPositionMap">
    update app_indoor_position_map
    <set>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="guid != null">
        guid = #{guid,jdbcType=VARCHAR},
      </if>
      <if test="areaId != null">
        area_id = #{areaId,jdbcType=INTEGER},
      </if>
      <if test="areaName != null">
        area_name = #{areaName,jdbcType=VARCHAR},
      </if>
      <if test="pixelWidth != null">
        pixel_width = #{pixelWidth,jdbcType=DOUBLE},
      </if>
      <if test="pixelLength != null">
        pixel_length = #{pixelLength,jdbcType=DOUBLE},
      </if>
      <if test="realWidth != null">
        real_width = #{realWidth,jdbcType=DOUBLE},
      </if>
      <if test="realLength != null">
        real_length = #{realLength,jdbcType=DOUBLE},
      </if>
      <if test="rotation != null">
        rotation = #{rotation,jdbcType=DOUBLE},
      </if>
      <if test="imgUrl != null">
        img_url = #{imgUrl,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByDeptId" resultType="com.whfc.emp.dto.AppIndoorPositionMapDTO">
    select id as mapId,
           `name`,
           guid,
           area_id,
           area_name,
           pixel_width,
           pixel_length,
           real_width,
           real_length,
           rotation,
           img_url
      from app_indoor_position_map
      where dept_id = #{deptId}
        and del_flag = 0
  </select>

  <select id="selectByGuid" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List">
    </include>
      from app_indoor_position_map
     where guid = #{guid}
      and del_flag = 0
  </select>

  <update id="logicDeleteById">
    update app_indoor_position_map
       set del_flag = 1
     where id = #{id}
  </update>
</mapper>