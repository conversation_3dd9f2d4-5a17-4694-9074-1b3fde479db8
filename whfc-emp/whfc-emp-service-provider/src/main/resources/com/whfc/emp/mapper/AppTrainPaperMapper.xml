<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppTrainPaperMapper">
  <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppTrainPaper">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="train_id" jdbcType="INTEGER" property="trainId" />
    <result column="exam_no" jdbcType="VARCHAR" property="examNo" />
    <result column="group_no" jdbcType="INTEGER" property="groupNo" />
    <result column="course_id" jdbcType="VARCHAR" property="courseId" />
    <result column="order" jdbcType="INTEGER" property="order" />
    <result column="qsn_code" jdbcType="VARCHAR" property="qsnCode" />
    <result column="qsn_file_name" jdbcType="VARCHAR" property="qsnFileName" />
    <result column="qsn_answer" jdbcType="VARCHAR" property="qsnAnswer" />
    <result column="qsn_category" jdbcType="INTEGER" property="qsnCategory" />
    <result column="qsn_kind" jdbcType="INTEGER" property="qsnKind" />
    <result column="qsn_important" jdbcType="INTEGER" property="qsnImportant" />
    <result column="source" jdbcType="INTEGER" property="source" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="upload_time" jdbcType="TIMESTAMP" property="uploadTime" />
    <result column="content_id" jdbcType="INTEGER" property="contentId" />
    <result column="qsn_content" jdbcType="LONGVARCHAR" property="qsnContent" />
    <result column="desc" jdbcType="VARCHAR" property="desc" />
    <result column="analysis" jdbcType="LONGVARCHAR" property="analysis" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, train_id, exam_no, group_no, course_id, `order`, qsn_code, qsn_file_name, qsn_answer,
    qsn_category, qsn_kind, qsn_important, `source`, version, upload_time, content_id, 
    qsn_content, `desc`, analysis, del_flag, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from app_train_paper
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from app_train_paper
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.whfc.emp.entity.AppTrainPaper">
    insert into app_train_paper (id, train_id, exam_no,
      group_no, course_id, `order`, 
      qsn_code, qsn_file_name, qsn_answer, 
      qsn_category, qsn_kind, qsn_important, 
      `source`, version, upload_time, 
      content_id, qsn_content, `desc`, 
      analysis, del_flag, update_time, 
      create_time)
    values (#{id,jdbcType=INTEGER}, #{trainId,jdbcType=INTEGER}, #{examNo,jdbcType=VARCHAR}, 
      #{groupNo,jdbcType=INTEGER}, #{courseId,jdbcType=VARCHAR}, #{order,jdbcType=INTEGER}, 
      #{qsnCode,jdbcType=VARCHAR}, #{qsnFileName,jdbcType=VARCHAR}, #{qsnAnswer,jdbcType=VARCHAR}, 
      #{qsnCategory,jdbcType=INTEGER}, #{qsnKind,jdbcType=INTEGER}, #{qsnImportant,jdbcType=INTEGER}, 
      #{source,jdbcType=INTEGER}, #{version,jdbcType=INTEGER}, #{uploadTime,jdbcType=TIMESTAMP}, 
      #{contentId,jdbcType=INTEGER}, #{qsnContent,jdbcType=LONGVARCHAR}, #{desc,jdbcType=VARCHAR}, 
      #{analysis,jdbcType=LONGVARCHAR}, #{delFlag,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.whfc.emp.entity.AppTrainPaper">
    insert into app_train_paper
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="trainId != null">
        train_id,
      </if>
      <if test="examNo != null">
        exam_no,
      </if>
      <if test="groupNo != null">
        group_no,
      </if>
      <if test="courseId != null">
        course_id,
      </if>
      <if test="order != null">
        `order`,
      </if>
      <if test="qsnCode != null">
        qsn_code,
      </if>
      <if test="qsnFileName != null">
        qsn_file_name,
      </if>
      <if test="qsnAnswer != null">
        qsn_answer,
      </if>
      <if test="qsnCategory != null">
        qsn_category,
      </if>
      <if test="qsnKind != null">
        qsn_kind,
      </if>
      <if test="qsnImportant != null">
        qsn_important,
      </if>
      <if test="source != null">
        `source`,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="uploadTime != null">
        upload_time,
      </if>
      <if test="contentId != null">
        content_id,
      </if>
      <if test="qsnContent != null">
        qsn_content,
      </if>
      <if test="desc != null">
        `desc`,
      </if>
      <if test="analysis != null">
        analysis,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="trainId != null">
        #{trainId,jdbcType=INTEGER},
      </if>
      <if test="examNo != null">
        #{examNo,jdbcType=VARCHAR},
      </if>
      <if test="groupNo != null">
        #{groupNo,jdbcType=INTEGER},
      </if>
      <if test="courseId != null">
        #{courseId,jdbcType=VARCHAR},
      </if>
      <if test="order != null">
        #{order,jdbcType=INTEGER},
      </if>
      <if test="qsnCode != null">
        #{qsnCode,jdbcType=VARCHAR},
      </if>
      <if test="qsnFileName != null">
        #{qsnFileName,jdbcType=VARCHAR},
      </if>
      <if test="qsnAnswer != null">
        #{qsnAnswer,jdbcType=VARCHAR},
      </if>
      <if test="qsnCategory != null">
        #{qsnCategory,jdbcType=INTEGER},
      </if>
      <if test="qsnKind != null">
        #{qsnKind,jdbcType=INTEGER},
      </if>
      <if test="qsnImportant != null">
        #{qsnImportant,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        #{source,jdbcType=INTEGER},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="uploadTime != null">
        #{uploadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="contentId != null">
        #{contentId,jdbcType=INTEGER},
      </if>
      <if test="qsnContent != null">
        #{qsnContent,jdbcType=LONGVARCHAR},
      </if>
      <if test="desc != null">
        #{desc,jdbcType=VARCHAR},
      </if>
      <if test="analysis != null">
        #{analysis,jdbcType=LONGVARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppTrainPaper">
    update app_train_paper
    <set>
      <if test="trainId != null">
        train_id = #{trainId,jdbcType=INTEGER},
      </if>
      <if test="examNo != null">
        exam_no = #{examNo,jdbcType=VARCHAR},
      </if>
      <if test="groupNo != null">
        group_no = #{groupNo,jdbcType=INTEGER},
      </if>
      <if test="courseId != null">
        course_id = #{courseId,jdbcType=VARCHAR},
      </if>
      <if test="order != null">
        `order` = #{order,jdbcType=INTEGER},
      </if>
      <if test="qsnCode != null">
        qsn_code = #{qsnCode,jdbcType=VARCHAR},
      </if>
      <if test="qsnFileName != null">
        qsn_file_name = #{qsnFileName,jdbcType=VARCHAR},
      </if>
      <if test="qsnAnswer != null">
        qsn_answer = #{qsnAnswer,jdbcType=VARCHAR},
      </if>
      <if test="qsnCategory != null">
        qsn_category = #{qsnCategory,jdbcType=INTEGER},
      </if>
      <if test="qsnKind != null">
        qsn_kind = #{qsnKind,jdbcType=INTEGER},
      </if>
      <if test="qsnImportant != null">
        qsn_important = #{qsnImportant,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        `source` = #{source,jdbcType=INTEGER},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="uploadTime != null">
        upload_time = #{uploadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="contentId != null">
        content_id = #{contentId,jdbcType=INTEGER},
      </if>
      <if test="qsnContent != null">
        qsn_content = #{qsnContent,jdbcType=LONGVARCHAR},
      </if>
      <if test="desc != null">
        `desc` = #{desc,jdbcType=VARCHAR},
      </if>
      <if test="analysis != null">
        analysis = #{analysis,jdbcType=LONGVARCHAR},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.whfc.emp.entity.AppTrainPaper">
    update app_train_paper
    set train_id = #{trainId,jdbcType=INTEGER},
      exam_no = #{examNo,jdbcType=VARCHAR},
      group_no = #{groupNo,jdbcType=INTEGER},
      course_id = #{courseId,jdbcType=VARCHAR},
      `order` = #{order,jdbcType=INTEGER},
      qsn_code = #{qsnCode,jdbcType=VARCHAR},
      qsn_file_name = #{qsnFileName,jdbcType=VARCHAR},
      qsn_answer = #{qsnAnswer,jdbcType=VARCHAR},
      qsn_category = #{qsnCategory,jdbcType=INTEGER},
      qsn_kind = #{qsnKind,jdbcType=INTEGER},
      qsn_important = #{qsnImportant,jdbcType=INTEGER},
      `source` = #{source,jdbcType=INTEGER},
      version = #{version,jdbcType=INTEGER},
      upload_time = #{uploadTime,jdbcType=TIMESTAMP},
      content_id = #{contentId,jdbcType=INTEGER},
      qsn_content = #{qsnContent,jdbcType=LONGVARCHAR},
      `desc` = #{desc,jdbcType=VARCHAR},
      analysis = #{analysis,jdbcType=LONGVARCHAR},
      del_flag = #{delFlag,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>