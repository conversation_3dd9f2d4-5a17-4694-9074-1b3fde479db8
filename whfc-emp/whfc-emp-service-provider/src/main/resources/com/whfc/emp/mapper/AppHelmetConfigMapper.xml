<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppHelmetConfigMapper">
  <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppHelmetConfig">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="platform" jdbcType="VARCHAR" property="platform" />
    <result column="host" jdbcType="VARCHAR" property="host" />
    <result column="user" jdbcType="VARCHAR" property="user" />
    <result column="pass" jdbcType="VARCHAR" property="pass" />
    <result column="ext_1" jdbcType="VARCHAR" property="ext1" />
    <result column="ext_2" jdbcType="VARCHAR" property="ext2" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, dept_id, platform, host, user, pass, ext_1, ext_2, del_flag, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from app_helmet_config
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from app_helmet_config
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.whfc.emp.entity.AppHelmetConfig">
    insert into app_helmet_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="platform != null">
        platform,
      </if>
      <if test="host != null">
        host,
      </if>
      <if test="user != null">
        user,
      </if>
      <if test="pass != null">
        pass,
      </if>
      <if test="ext1 != null">
        ext_1,
      </if>
      <if test="ext2 != null">
        ext_2,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="platform != null">
        #{platform,jdbcType=VARCHAR},
      </if>
      <if test="host != null">
        #{host,jdbcType=VARCHAR},
      </if>
      <if test="user != null">
        #{user,jdbcType=VARCHAR},
      </if>
      <if test="pass != null">
        #{pass,jdbcType=VARCHAR},
      </if>
      <if test="ext1 != null">
        #{ext1,jdbcType=VARCHAR},
      </if>
      <if test="ext2 != null">
        #{ext2,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppHelmetConfig">
    update app_helmet_config
    <set>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="platform != null">
        platform = #{platform,jdbcType=VARCHAR},
      </if>
      <if test="host != null">
        host = #{host,jdbcType=VARCHAR},
      </if>
      <if test="user != null">
        user = #{user,jdbcType=VARCHAR},
      </if>
      <if test="pass != null">
        pass = #{pass,jdbcType=VARCHAR},
      </if>
      <if test="ext1 != null">
        ext_1 = #{ext1,jdbcType=VARCHAR},
      </if>
      <if test="ext2 != null">
        ext_2 = #{ext2,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByDeptIdAndPlatform" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from app_helmet_config
    where (dept_id = #{deptId} or dept_id = 0)
      and platform = #{platform}
      and del_flag = 0
    order by dept_id desc
    limit 1
  </select>

  <select id="selectByDeptId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from app_helmet_config
    where dept_id = #{deptId}
      and del_flag = 0
  </select>

  <select id="selectByPlatform" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from app_helmet_config
    where platform = #{platform}
      and del_flag = 0
  </select>
</mapper>