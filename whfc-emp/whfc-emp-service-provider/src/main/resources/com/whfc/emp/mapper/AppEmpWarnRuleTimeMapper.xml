<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppEmpWarnRuleTimeMapper">
    <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppEmpWarnRuleTime">
        <!--@mbg.generated-->
        <!--@Table app_emp_warn_rule_time-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="rule_id" jdbcType="INTEGER" property="ruleId"/>
        <result column="start_time" jdbcType="TIME" property="startTime"/>
        <result column="end_time" jdbcType="TIME" property="endTime"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, rule_id, start_time, end_time, del_flag, update_time, create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from app_emp_warn_rule_time
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete from app_emp_warn_rule_time
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppEmpWarnRuleTime"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into app_emp_warn_rule_time (rule_id, start_time, end_time,
        del_flag, update_time, create_time
        )
        values (#{ruleId,jdbcType=INTEGER}, #{startTime,jdbcType=TIME}, #{endTime,jdbcType=TIME},
        #{delFlag,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}
        )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppEmpWarnRuleTime"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into app_emp_warn_rule_time
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ruleId != null">
                rule_id,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ruleId != null">
                #{ruleId,jdbcType=INTEGER},
            </if>
            <if test="startTime != null">
                #{startTime,jdbcType=TIME},
            </if>
            <if test="endTime != null">
                #{endTime,jdbcType=TIME},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppEmpWarnRuleTime">
        <!--@mbg.generated-->
        update app_emp_warn_rule_time
        <set>
            <if test="ruleId != null">
                rule_id = #{ruleId,jdbcType=INTEGER},
            </if>
            <if test="startTime != null">
                start_time = #{startTime,jdbcType=TIME},
            </if>
            <if test="endTime != null">
                end_time = #{endTime,jdbcType=TIME},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.whfc.emp.entity.AppEmpWarnRuleTime">
        <!--@mbg.generated-->
        update app_emp_warn_rule_time
        set rule_id = #{ruleId,jdbcType=INTEGER},
        start_time = #{startTime,jdbcType=TIME},
        end_time = #{endTime,jdbcType=TIME},
        del_flag = #{delFlag,jdbcType=INTEGER},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        create_time = #{createTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <select id="selectByRuleId" resultType="com.whfc.emp.dto.AppWarnTimeDTO">
      select start_time,
      end_time
      from app_emp_warn_rule_time
      where rule_id = #{ruleId}
      and del_flag = 0
    </select>
    <update id="deleteByRuleId">
        UPDATE app_emp_warn_rule_time
        SET del_flag = 1
        where rule_id = #{ruleId}
    </update>
    <insert id="batchInsert">
        insert into app_emp_warn_rule_time
        (
        rule_id,
        start_time,
        end_time
        )
        values
        <foreach collection="list" item="item" separator=",">
        (
        #{item.ruleId},
        #{item.startTime},
        #{item.endTime}
        )
        </foreach>
    </insert>
</mapper>