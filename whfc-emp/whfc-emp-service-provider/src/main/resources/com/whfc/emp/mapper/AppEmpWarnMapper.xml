<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppEmpWarnMapper">
    <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppEmpWarn">
        <!--@mbg.generated-->
        <!--@Table app_emp_warn-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="dept_id" jdbcType="INTEGER" property="deptId"/>
        <result column="rule_id" jdbcType="INTEGER" property="ruleId"/>
        <result column="rule_type" jdbcType="INTEGER" property="ruleType"/>
        <result column="trigger_time" jdbcType="TIMESTAMP" property="triggerTime"/>
        <result column="trigger_object_id" jdbcType="VARCHAR" property="triggerObjectId"/>
        <result column="trigger_param" jdbcType="VARCHAR" property="triggerParam"/>
        <result column="trigger_key" jdbcType="VARCHAR" property="triggerKey" />
        <result column="lat" jdbcType="DOUBLE" property="lat"/>
        <result column="lng" jdbcType="DOUBLE" property="lng"/>
        <result column="state" jdbcType="INTEGER" property="state"/>
        <result column="handle_time" jdbcType="TIMESTAMP" property="handleTime"/>
        <result column="handle_result" jdbcType="VARCHAR" property="handleResult"/>
        <result column="handle_remark" jdbcType="VARCHAR" property="handleRemark"/>
        <result column="handle_user_id" jdbcType="INTEGER" property="handleUserId"/>
        <result column="handle_user_name" jdbcType="VARCHAR" property="handleUserName"/>
        <result column="handle_user_phone" jdbcType="VARCHAR" property="handleUserPhone"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, dept_id, rule_id, rule_type, trigger_time, trigger_object_id, trigger_param, trigger_key, lat, lng, `state`,
        handle_time, handle_result, handle_remark, handle_user_id, handle_user_name, handle_user_phone,
        del_flag, update_time, create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from app_emp_warn
        where id = #{id,jdbcType=INTEGER}
    </select>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppEmpWarn"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into app_emp_warn
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="ruleId != null">
                rule_id,
            </if>
            <if test="ruleType != null">
                rule_type,
            </if>
            <if test="triggerTime != null">
                trigger_time,
            </if>
            <if test="triggerObjectId != null">
                trigger_object_id,
            </if>
            <if test="triggerParam != null">
                trigger_param,
            </if>
            <if test="triggerKey != null">
                trigger_key,
            </if>
            <if test="lat != null">
                lat,
            </if>
            <if test="lng != null">
                lng,
            </if>
            <if test="state != null">
                `state`,
            </if>
            <if test="handleTime != null">
                handle_time,
            </if>
            <if test="handleResult != null">
                handle_result,
            </if>
            <if test="handleRemark != null">
                handle_remark,
            </if>
            <if test="handleUserId != null">
                handle_user_id,
            </if>
            <if test="handleUserName != null">
                handle_user_name,
            </if>
            <if test="handleUserPhone != null">
                handle_user_phone,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                #{deptId,jdbcType=INTEGER},
            </if>
            <if test="ruleId != null">
                #{ruleId,jdbcType=INTEGER},
            </if>
            <if test="ruleType != null">
                #{ruleType,jdbcType=INTEGER},
            </if>
            <if test="triggerTime != null">
                #{triggerTime,jdbcType=TIMESTAMP},
            </if>
            <if test="triggerObjectId != null">
                #{triggerObjectId,jdbcType=VARCHAR},
            </if>
            <if test="triggerParam != null">
                #{triggerParam,jdbcType=VARCHAR},
            </if>
            <if test="triggerKey != null">
                #{triggerKey,jdbcType=VARCHAR},
            </if>
            <if test="lat != null">
                #{lat,jdbcType=DOUBLE},
            </if>
            <if test="lng != null">
                #{lng,jdbcType=DOUBLE},
            </if>
            <if test="state != null">
                #{state,jdbcType=INTEGER},
            </if>
            <if test="handleTime != null">
                #{handleTime,jdbcType=TIMESTAMP},
            </if>
            <if test="handleResult != null">
                #{handleResult,jdbcType=VARCHAR},
            </if>
            <if test="handleRemark != null">
                #{handleRemark,jdbcType=VARCHAR},
            </if>
            <if test="handleUserId != null">
                #{handleUserId,jdbcType=INTEGER},
            </if>
            <if test="handleUserName != null">
                #{handleUserName,jdbcType=VARCHAR},
            </if>
            <if test="handleUserPhone != null">
                #{handleUserPhone,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppEmpWarn">
        <!--@mbg.generated-->
        update app_emp_warn
        <set>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=INTEGER},
            </if>
            <if test="ruleId != null">
                rule_id = #{ruleId,jdbcType=INTEGER},
            </if>
            <if test="ruleType != null">
                rule_type = #{ruleType,jdbcType=INTEGER},
            </if>
            <if test="triggerTime != null">
                trigger_time = #{triggerTime,jdbcType=TIMESTAMP},
            </if>
            <if test="triggerObjectId != null">
                trigger_object_id = #{triggerObjectId,jdbcType=VARCHAR},
            </if>
            <if test="triggerParam != null">
                trigger_param = #{triggerParam,jdbcType},
            </if>
            <if test="triggerKey != null">
                trigger_key = #{triggerKey,jdbcType=VARCHAR},
            </if>
            <if test="lat != null">
                lat = #{lat,jdbcType=DOUBLE},
            </if>
            <if test="lng != null">
                lng = #{lng,jdbcType=DOUBLE},
            </if>
            <if test="state != null">
                `state` = #{state,jdbcType=INTEGER},
            </if>
            <if test="handleTime != null">
                handle_time = #{handleTime,jdbcType=TIMESTAMP},
            </if>
            <if test="handleResult != null">
                handle_result = #{handleResult,jdbcType=VARCHAR},
            </if>
            <if test="handleRemark != null">
                handle_remark = #{handleRemark,jdbcType=VARCHAR},
            </if>
            <if test="handleUserId != null">
                handle_user_id = #{handleUserId,jdbcType=INTEGER},
            </if>
            <if test="handleUserName != null">
                handle_user_name = #{handleUserName,jdbcType=VARCHAR},
            </if>
            <if test="handleUserPhone != null">
                handle_user_phone = #{handleUserPhone,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectByDeptId" resultType="com.whfc.emp.dto.AppWarnEmpRecordDTO">
        select id as warnId,
        trigger_object_id as empId,
        rule_type,
        trigger_time,
        trigger_key,
        trigger_param,
        handle_time,
        handle_user_name,
        state,
        dept_id
        from app_emp_warn
        where del_flag=0
        <if test="state!=null">
            and state = #{state}
        </if>
        <if test="ruleType!=null">
            and rule_type = #{ruleType}
        </if>
        <if test="startTime!=null">
            and trigger_time >= #{startTime}
        </if>
        <if test="endTime!=null">
            and #{endTime} >= trigger_time
        </if>
        <if test="deptId!=null">
            and dept_id=#{deptId}
        </if>
        order by id desc
    </select>

    <select id="countWarnNum" resultType="java.lang.Integer">
        select count(1)
        from app_emp_warn
        where del_flag = 0
        and dept_id in
        <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
            #{deptId}
        </foreach>
        and trigger_time BETWEEN #{startTime} AND #{endTime}
        <if test="ruleType!=null">
            and rule_type = #{ruleType}
        </if>
    </select>

    <select id="selectEmpWarnList" resultType="com.whfc.emp.dto.AppWarnEmpRecordDTO">
        select id as warnId,
        trigger_object_id as empId,
        rule_type,
        trigger_time,
        trigger_param,
        trigger_key,
        lat,
        lng
        from app_emp_warn
        where del_flag=0
        <if test="state!=null">
            and state = #{state}
        </if>
        <if test="deptId!=null">
            and dept_id=#{deptId}
        </if>
        order by id desc
    </select>

    <select id="countWarnTypeNum" resultType="com.whfc.emp.dto.AppWarnEmpRecordNumDTO">
        SELECT
        COUNT( 1 ) as num,
        rule_type
        FROM
        app_emp_warn aew
        where del_flag=0
        <if test="state!=null">
            and state = #{state}
        </if>
        <if test="startTime!=null">
            and trigger_time >= #{startTime}
        </if>
        <if test="endTime!=null">
            and #{endTime} >= trigger_time
        </if>
        <if test="deptId!=null">
            and dept_id=#{deptId}
        </if>
        GROUP BY
        rule_type
    </select>

    <update id="batchUpdateState">
        update app_emp_warn
        set state = 1,
        handle_time = NOW(),
        handle_result = #{handleResult},
        handle_remark = #{handleRemark},
        handle_user_id = #{userId},
        handle_user_name = #{userName},
        handle_user_phone = #{phone}
        where 1 = 0
        <foreach collection="warnIdList" item="warnId">
            or id = #{warnId}
        </foreach>
    </update>

    <update id="deleteLogicByObjectIds">
        update app_emp_warn
        set del_flag = 1
        where 1 = 0
        <foreach collection="warnIdList" item="warnId">
            or id = #{warnId}
        </foreach>
    </update>

    <select id="countRecordByHour" resultType="com.whfc.entity.dto.warn.AppWarnRuleType">
        SELECT
            DATE_FORMAT( trigger_time, '%H:00' ) dateStr,
            COUNT( id ) warnNum
        FROM
            app_emp_warn
        WHERE
            del_flag = 0
            AND dept_id = #{deptId}
            AND trigger_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY dateStr
    </select>

    <select id="countWarnRecord" resultType="com.whfc.entity.dto.warn.AppWarnRuleType">
        SELECT
            aew.dept_id,
            device_type AS type,
            COUNT(DISTINCT  aew.id)                    warnNum,
            SUM(CASE WHEN state = 0 THEN 1 ELSE 0 END) unHandledNum,
            SUM(CASE WHEN state = 1 THEN 1 ELSE 0 END) handledNum
        FROM app_emp_warn aew
         LEFT JOIN app_emp_device aed ON aed.emp_id = aew.trigger_object_id
        WHERE aew.del_flag = 0
             AND trigger_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY dept_id, device_type
    </select>
</mapper>