<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppSyncMapper">
  <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppSync">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="platform" jdbcType="VARCHAR" property="platform" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="app_key" jdbcType="VARCHAR" property="appKey" />
    <result column="app_secret" jdbcType="VARCHAR" property="appSecret" />
    <result column="host" jdbcType="VARCHAR" property="host" />
    <result column="token" jdbcType="VARCHAR" property="token" />
    <result column="ext_1" jdbcType="VARCHAR" property="ext1" />
    <result column="ext_2" jdbcType="VARCHAR" property="ext2" />
    <result column="ext_3" jdbcType="VARCHAR" property="ext3" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, dept_id, platform, `name`, app_key, app_secret, host, token, ext_1, ext_2, ext_3, del_flag, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from app_sync
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from app_sync
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.whfc.emp.entity.AppSync">
    insert into app_sync (id, dept_id, platform,
      name, app_key, app_secret, 
      host, token, ext_1, 
      ext_2, del_flag, update_time, 
      create_time)
    values (#{id,jdbcType=INTEGER}, #{deptId,jdbcType=INTEGER}, #{platform,jdbcType=VARCHAR}, 
      #{name,jdbcType=VARCHAR}, #{appKey,jdbcType=VARCHAR}, #{appSecret,jdbcType=VARCHAR}, 
      #{host,jdbcType=VARCHAR}, #{token,jdbcType=VARCHAR}, #{ext1,jdbcType=VARCHAR}, 
      #{ext2,jdbcType=VARCHAR}, #{delFlag,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.whfc.emp.entity.AppSync">
    insert into app_sync
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="platform != null">
        platform,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="appKey != null">
        app_key,
      </if>
      <if test="appSecret != null">
        app_secret,
      </if>
      <if test="host != null">
        host,
      </if>
      <if test="token != null">
        token,
      </if>
      <if test="ext1 != null">
        ext_1,
      </if>
      <if test="ext2 != null">
        ext_2,
      </if>
      <if test="ext3 != null">
        ext_3,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="platform != null">
        #{platform,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="appKey != null">
        #{appKey,jdbcType=VARCHAR},
      </if>
      <if test="appSecret != null">
        #{appSecret,jdbcType=VARCHAR},
      </if>
      <if test="host != null">
        #{host,jdbcType=VARCHAR},
      </if>
      <if test="token != null">
        #{token,jdbcType=VARCHAR},
      </if>
      <if test="ext1 != null">
        #{ext1,jdbcType=VARCHAR},
      </if>
      <if test="ext2 != null">
        #{ext2,jdbcType=VARCHAR},
      </if>
      <if test="ext3 != null">
        #{ext3,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppSync">
    update app_sync
    <set>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="platform != null">
        platform = #{platform,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="appKey != null">
        app_key = #{appKey,jdbcType=VARCHAR},
      </if>
      <if test="appSecret != null">
        app_secret = #{appSecret,jdbcType=VARCHAR},
      </if>
      <if test="host != null">
        host = #{host,jdbcType=VARCHAR},
      </if>
      <if test="token != null">
        token = #{token,jdbcType=VARCHAR},
      </if>
      <if test="ext1 != null">
        ext_1 = #{ext1,jdbcType=VARCHAR},
      </if>
      <if test="ext2 != null">
        ext_2 = #{ext2,jdbcType=VARCHAR},
      </if>
      <if test="ext2 != null">
        ext_3 = #{ext2,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.whfc.emp.entity.AppSync">
    update app_sync
    set dept_id = #{deptId,jdbcType=INTEGER},
      platform = #{platform,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      app_key = #{appKey,jdbcType=VARCHAR},
      app_secret = #{appSecret,jdbcType=VARCHAR},
      host = #{host,jdbcType=VARCHAR},
      token = #{token,jdbcType=VARCHAR},
      ext_1 = #{ext1,jdbcType=VARCHAR},
      ext_2 = #{ext2,jdbcType=VARCHAR},
      del_flag = #{delFlag,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByDeptIdAndPlatform" resultMap="BaseResultMap">
    select id,platform, name, app_key, app_secret, host, token, ext_1, ext_2 ,ext_3
      from app_sync
     where dept_id = #{deptId}
       and platform = #{platform}
       and del_flag = 0
     order by id desc
    limit 1
  </select>

  <select id="selectByDeptId" resultMap="BaseResultMap">
    select dept_id,platform, name, app_key, app_secret, host, token, ext_1, ext_2, ext_3
    from app_sync
    where dept_id = #{deptId}
      and del_flag = 0
    order by id desc
  </select>
</mapper>