<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppEmpWarnRuleObjectMapper">
  <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppEmpWarnRuleObject">
    <!--@mbg.generated-->
    <!--@Table app_emp_warn_rule_object-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="rule_id" jdbcType="INTEGER" property="ruleId" />
    <result column="object_id" jdbcType="VARCHAR" property="objectId" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, rule_id, object_id, del_flag, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from app_emp_warn_rule_object
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from app_emp_warn_rule_object
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppEmpWarnRuleObject" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_emp_warn_rule_object (rule_id, object_id, del_flag, 
      update_time, create_time)
    values (#{ruleId,jdbcType=INTEGER}, #{objectId,jdbcType=VARCHAR}, #{delFlag,jdbcType=INTEGER}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppEmpWarnRuleObject" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_emp_warn_rule_object
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ruleId != null">
        rule_id,
      </if>
      <if test="objectId != null">
        object_id,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ruleId != null">
        #{ruleId,jdbcType=INTEGER},
      </if>
      <if test="objectId != null">
        #{objectId,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppEmpWarnRuleObject">
    <!--@mbg.generated-->
    update app_emp_warn_rule_object
    <set>
      <if test="ruleId != null">
        rule_id = #{ruleId,jdbcType=INTEGER},
      </if>
      <if test="objectId != null">
        object_id = #{objectId,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.whfc.emp.entity.AppEmpWarnRuleObject">
    <!--@mbg.generated-->
    update app_emp_warn_rule_object
    set rule_id = #{ruleId,jdbcType=INTEGER},
      object_id = #{objectId,jdbcType=VARCHAR},
      del_flag = #{delFlag,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByRuleId" resultType="com.whfc.emp.dto.AppEmpDTO">
    select ae.emp_name ,
           ae.id as empId,
           ae.work_type_name
    from app_emp_warn_rule_object ao
    inner join app_emp ae on ae.id = ao.object_id
    where ae.del_flag = 0
     and ao.del_flag = 0
     and ao.rule_id = #{ruleId}
  </select>

  <update id="deleteLogicByRuleId">
    update app_emp_warn_rule_object
    set del_flag = 1
    where rule_id = #{ruleId}
  </update>

  <insert id="batchInsert">
    INSERT INTO app_emp_warn_rule_object
    (
     rule_id,
     object_id
    )
    VALUES
    <foreach collection="empIdList" item="empId" separator=",">
    (#{ruleId},
     #{empId}
    )
    </foreach>
  </insert>

  <select id="selectByEmpId" resultType="com.whfc.emp.dto.AppFenceDTO">
    select ao.rule_id,
           af.`type`,
          af.radius,
          ASTEXT(af.center) as center,
          ASTEXT(af.polygon) as polygon
    from app_emp_warn_rule_object ao
    inner join app_emp_warn_rule  ar on ar.id = ao.rule_id
    inner join app_emp_warn_rule_fence af on af.rule_id = ar.id
    where ar.del_flag = 0
          and ar.enable_flag = 1
          and ao.del_flag = 0
          and af.del_flag = 0
          and ao.object_id = #{empId}
  </select>
</mapper>