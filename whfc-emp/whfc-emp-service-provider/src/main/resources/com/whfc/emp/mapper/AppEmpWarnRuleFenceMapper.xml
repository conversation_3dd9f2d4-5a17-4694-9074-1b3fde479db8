<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppEmpWarnRuleFenceMapper">
  <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppEmpWarnRuleFence">
    <!--@mbg.generated-->
    <!--@Table app_emp_warn_rule_fence-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="rule_id" jdbcType="INTEGER" property="ruleId" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="polygon" jdbcType="OTHER" property="polygon" />
    <result column="center" jdbcType="INTEGER" property="center" />
    <result column="radius" jdbcType="DOUBLE" property="radius" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, dept_id, rule_id, `type`, polygon, center, radius, del_flag, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from app_emp_warn_rule_fence
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from app_emp_warn_rule_fence
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppEmpWarnRuleFence" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_emp_warn_rule_fence (dept_id, rule_id, `type`, 
      polygon, center, radius, 
      del_flag, update_time, create_time
      )
    values (#{deptId,jdbcType=INTEGER}, #{ruleId,jdbcType=INTEGER}, #{type,jdbcType=INTEGER}, 
      #{polygon,jdbcType=OTHER}, #{center,jdbcType=INTEGER}, #{radius,jdbcType=DOUBLE}, 
      #{delFlag,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppEmpWarnRuleFence" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_emp_warn_rule_fence
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="ruleId != null">
        rule_id,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="polygon != null">
        polygon,
      </if>
      <if test="center != null">
        center,
      </if>
      <if test="radius != null">
        radius,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="ruleId != null">
        #{ruleId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="polygon != null">
        #{polygon,jdbcType=OTHER},
      </if>
      <if test="center != null">
        #{center,jdbcType=INTEGER},
      </if>
      <if test="radius != null">
        #{radius,jdbcType=DOUBLE},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppEmpWarnRuleFence">
    <!--@mbg.generated-->
    update app_emp_warn_rule_fence
    <set>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="ruleId != null">
        rule_id = #{ruleId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="polygon != null">
        polygon = #{polygon,jdbcType=OTHER},
      </if>
      <if test="center != null">
        center = #{center,jdbcType=INTEGER},
      </if>
      <if test="radius != null">
        radius = #{radius,jdbcType=DOUBLE},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.whfc.emp.entity.AppEmpWarnRuleFence">
    <!--@mbg.generated-->
    update app_emp_warn_rule_fence
    set dept_id = #{deptId,jdbcType=INTEGER},
      rule_id = #{ruleId,jdbcType=INTEGER},
      `type` = #{type,jdbcType=INTEGER},
      polygon = #{polygon,jdbcType=OTHER},
      center = #{center,jdbcType=INTEGER},
      radius = #{radius,jdbcType=DOUBLE},
      del_flag = #{delFlag,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <update id="deleteLogicByRuleId">
    update app_emp_warn_rule_fence
    set del_flag = 1
    where rule_id = #{ruleId}
  </update>

  <insert id="insertSelectiveByParam" parameterType="com.whfc.emp.param.AppEmpWarnAddParam">
    insert into app_emp_warn_rule_fence
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="polygon != null">
        polygon,
      </if>
      <if test="center != null">
        center,
      </if>
      <if test="radius != null">
        radius,
      </if>
      <if test="ruleId != null">
        rule_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">

      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="polygon != null">
        GEOMFROMTEXT(#{polygon}),
      </if>
      <if test="center != null">
        GEOMFROMTEXT(#{center}),
      </if>
      <if test="radius != null">
        #{radius,jdbcType=DOUBLE},
      </if>
      <if test="ruleId != null">
        #{ruleId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>

  <select id="selectByRuleId" resultType="com.whfc.emp.dto.AppFenceDTO">
      select  af.type,
              af.radius,
              ASTEXT(af.center) as center,
              ASTEXT(af.polygon) as polygon
      from app_emp_warn_rule_fence af
      where af.del_flag=0
            and af.rule_id = #{ruleId}
      limit 1
  </select>
</mapper>