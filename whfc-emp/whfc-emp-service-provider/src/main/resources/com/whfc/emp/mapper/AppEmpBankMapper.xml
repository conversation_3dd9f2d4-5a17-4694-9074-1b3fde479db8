<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppEmpBankMapper">
    <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppEmpBank">
        <!--@mbg.generated-->
        <!--@Table app_emp_bank-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="emp_id" jdbcType="INTEGER" property="empId"/>
        <result column="bank_code" jdbcType="VARCHAR" property="bankCode"/>
        <result column="bank_name" jdbcType="VARCHAR" property="bankName"/>
        <result column="bank_number" jdbcType="VARCHAR" property="bankNumber"/>
        <result column="enable_flag" jdbcType="INTEGER" property="enableFlag"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, emp_id, bank_code, bank_name, bank_number, enable_flag, del_flag, update_time,
        create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from app_emp_bank
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete from app_emp_bank
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppEmpBank"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into app_emp_bank (emp_id, bank_code, bank_name,
        bank_number, enable_flag, del_flag,
        update_time, create_time)
        values (#{empId,jdbcType=INTEGER}, #{bankCode,jdbcType=VARCHAR}, #{bankName,jdbcType=VARCHAR},
        #{bankNumber,jdbcType=VARCHAR}, #{enableFlag,jdbcType=INTEGER}, #{delFlag,jdbcType=INTEGER},
        #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppEmpBank"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into app_emp_bank
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="empId != null">
                emp_id,
            </if>
            <if test="bankCode != null">
                bank_code,
            </if>
            <if test="bankName != null">
                bank_name,
            </if>
            <if test="bankNumber != null">
                bank_number,
            </if>
            <if test="enableFlag != null">
                enable_flag,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="empId != null">
                #{empId,jdbcType=INTEGER},
            </if>
            <if test="bankCode != null">
                #{bankCode,jdbcType=VARCHAR},
            </if>
            <if test="bankName != null">
                #{bankName,jdbcType=VARCHAR},
            </if>
            <if test="bankNumber != null">
                #{bankNumber,jdbcType=VARCHAR},
            </if>
            <if test="enableFlag != null">
                #{enableFlag,jdbcType=INTEGER},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppEmpBank">
        <!--@mbg.generated-->
        update app_emp_bank
        <set>
            <if test="empId != null">
                emp_id = #{empId,jdbcType=INTEGER},
            </if>
            <if test="bankCode != null">
                bank_code = #{bankCode,jdbcType=VARCHAR},
            </if>
            <if test="bankName != null">
                bank_name = #{bankName,jdbcType=VARCHAR},
            </if>
            <if test="bankNumber != null">
                bank_number = #{bankNumber,jdbcType=VARCHAR},
            </if>
            <if test="enableFlag != null">
                enable_flag = #{enableFlag,jdbcType=INTEGER},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.whfc.emp.entity.AppEmpBank">
        <!--@mbg.generated-->
        update app_emp_bank
        set emp_id = #{empId,jdbcType=INTEGER},
        bank_code = #{bankCode,jdbcType=VARCHAR},
        bank_name = #{bankName,jdbcType=VARCHAR},
        bank_number = #{bankNumber,jdbcType=VARCHAR},
        enable_flag = #{enableFlag,jdbcType=INTEGER},
        del_flag = #{delFlag,jdbcType=INTEGER},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        create_time = #{createTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <select id="selectBankNumberByEmpId" resultType="java.lang.String">
      select bank_number
      from app_emp_bank
      where del_flag = 0
      and enable_flag = 1
      and emp_id = #{empId}
      limit 1
    </select>
    <update id="deleteLogicByEmpId">
        update app_emp_bank
        set del_flag = 1
        where emp_id = #{empId}
    </update>
    <select id="selectBankDTOList" resultType="com.whfc.emp.dto.AppEmpBankDTO">
        select id as bankId,
        bank_code,
        bank_name,
        bank_number,
        enable_flag
        from app_emp_bank
        where del_flag = 0
        and emp_id = #{empId}
    </select>
    <select id="selectByBankNumber" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from app_emp_bank
        where del_flag = 0
        and bank_number = #{bankNumber}
    </select>
    <update id="deleteLogicById">
        update app_emp_bank
        set del_flag = 1
        where id = #{bankId}
    </update>
    <select id="selectByEmpId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from app_emp_bank
        where del_flag = 0
        and enable_flag = 1
        and emp_id = #{empId}
    </select>
</mapper>