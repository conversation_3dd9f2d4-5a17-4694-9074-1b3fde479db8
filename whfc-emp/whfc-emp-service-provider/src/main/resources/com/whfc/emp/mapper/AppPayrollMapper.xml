<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppPayrollMapper">
  <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppPayroll">
    <!--@mbg.generated-->
    <!--@Table app_payroll-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="corp_id" jdbcType="INTEGER" property="corpId" />
    <result column="corp_name" jdbcType="VARCHAR" property="corpName" />
    <result column="clearing_form" jdbcType="INTEGER" property="clearingForm" />
    <result column="group_id" jdbcType="INTEGER" property="groupId" />
    <result column="group_name" jdbcType="VARCHAR" property="groupName" />
    <result column="year" jdbcType="INTEGER" property="year" />
    <result column="month" jdbcType="INTEGER" property="month" />
    <result column="start_date" jdbcType="DATE" property="startDate" />
    <result column="end_date" jdbcType="DATE" property="endDate" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id,
    dept_id,
    corp_id,
    corp_name,
    clearing_form,
    group_id,
    group_name,
    `year`,
    `month`,
    start_date,
    end_date,
    `state`,
    del_flag,
    update_time,
    create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from app_payroll
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from app_payroll
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppPayroll" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_payroll (dept_id, corp_id, corp_name, 
      clearing_form, group_id, group_name, 
      `year`, `month`, `state`, 
      del_flag, update_time, create_time
      )
    values (#{deptId,jdbcType=INTEGER}, #{corpId,jdbcType=INTEGER}, #{corpName,jdbcType=VARCHAR}, 
      #{clearingForm,jdbcType=INTEGER}, #{groupId,jdbcType=INTEGER}, #{groupName,jdbcType=VARCHAR}, 
      #{year,jdbcType=INTEGER}, #{month,jdbcType=INTEGER}, #{state,jdbcType=INTEGER}, 
      #{delFlag,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppPayroll" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_payroll
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="corpId != null">
        corp_id,
      </if>
      <if test="corpName != null">
        corp_name,
      </if>
      <if test="clearingForm != null">
        clearing_form,
      </if>
      <if test="groupId != null">
        group_id,
      </if>
      <if test="groupName != null">
        group_name,
      </if>
      <if test="year != null">
        `year`,
      </if>
      <if test="month != null">
        `month`,
      </if>
      <if test="startDate != null">
        `start_date`,
      </if>
      <if test="endDate != null">
        `end_date`,
      </if>
      <if test="state != null">
        `state`,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="corpId != null">
        #{corpId,jdbcType=INTEGER},
      </if>
      <if test="corpName != null">
        #{corpName,jdbcType=VARCHAR},
      </if>
      <if test="clearingForm != null">
        #{clearingForm,jdbcType=INTEGER},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=INTEGER},
      </if>
      <if test="groupName != null">
        #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="year != null">
        #{year,jdbcType=INTEGER},
      </if>
      <if test="month != null">
        #{month,jdbcType=INTEGER},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="state != null">
        #{state,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppPayroll">
    <!--@mbg.generated-->
    update app_payroll
    <set>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="corpId != null">
        corp_id = #{corpId,jdbcType=INTEGER},
      </if>
      <if test="corpName != null">
        corp_name = #{corpName,jdbcType=VARCHAR},
      </if>
      <if test="clearingForm != null">
        clearing_form = #{clearingForm,jdbcType=INTEGER},
      </if>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=INTEGER},
      </if>
      <if test="groupName != null">
        group_name = #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="year != null">
        `year` = #{year,jdbcType=INTEGER},
      </if>
      <if test="month != null">
        `month` = #{month,jdbcType=INTEGER},
      </if>
      <if test="startDate != null">
        start_date = #{startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null">
        end_date = #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="state != null">
        `state` = #{state,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.whfc.emp.entity.AppPayroll">
    <!--@mbg.generated-->
    update app_payroll
    set dept_id = #{deptId,jdbcType=INTEGER},
      corp_id = #{corpId,jdbcType=INTEGER},
      corp_name = #{corpName,jdbcType=VARCHAR},
      clearing_form = #{clearingForm,jdbcType=INTEGER},
      group_id = #{groupId,jdbcType=INTEGER},
      group_name = #{groupName,jdbcType=VARCHAR},
      `year` = #{year,jdbcType=INTEGER},
      `month` = #{month,jdbcType=INTEGER},
      `state` = #{state,jdbcType=INTEGER},
      del_flag = #{delFlag,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByParam" parameterType="com.whfc.emp.param.AppPayrollListParam" resultType="com.whfc.emp.dto.AppPayrollDTO">
  	select id as payrollId,
  		corp_name,
  		dept_id,
  		`year`,
  		`month`,
        start_date,
        end_date,
  		`state`,
  		group_name,
        clearing_form
  	from app_payroll
  	where dept_id=#{deptId}
      and del_flag=0
      <if test="year != null">
          and `year`=#{year}
      </if>
      <if test="month != null">
          and `month`=#{month}
      </if>
      <if test="corpId != null ">
          and corp_id = #{corpId}
      </if>
      <if test="keyword!=null and keyword!=''">
        and (corp_name like concat('%',#{keyword},'%')
           or group_name like concat('%',#{keyword},'%') )
      </if>
      order by id desc
  </select>

  <update id="deleteLogicById">
  	update app_payroll
  	set del_flag=1
  	where id=#{payrollId}
  </update>

  <update id="updateState">
  	update app_payroll
  	set state=1
  	where id=#{payrollId}
  </update>

  <select id="countByDeptIdAndCorpId" resultType="java.lang.Integer">
    select count(1)
    from app_payroll
    where del_flag = 0
        <if test="year != null">
          and year=#{year}
        </if>
        <if test="month != null">
          and month=#{month}
        </if>
        <if test="groupId != null ">
          and group_id = #{groupId}
        </if>
        <if test="deptId != null ">
          and dept_id = #{deptId}
        </if>
  </select>
</mapper>