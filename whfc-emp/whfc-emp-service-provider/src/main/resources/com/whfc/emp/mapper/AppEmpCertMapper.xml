<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppEmpCertMapper">
    <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppEmpCert">
        <!--@mbg.generated-->
        <!--@Table app_emp_cert-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="dept_id" jdbcType="INTEGER" property="deptId"/>
        <result column="emp_id" jdbcType="INTEGER" property="empId"/>
        <result column="cert_type_id" jdbcType="INTEGER" property="certTypeId"/>
        <result column="cert_type_name" jdbcType="VARCHAR" property="certTypeName"/>
        <result column="operation_item_id" jdbcType="INTEGER" property="operationItemId"/>
        <result column="operation_item_name" jdbcType="VARCHAR" property="operationItemName"/>
        <result column="cert_name" jdbcType="VARCHAR" property="certName"/>
        <result column="cert_code" jdbcType="VARCHAR" property="certCode"/>
        <result column="level" jdbcType="VARCHAR" property="level"/>
        <result column="cert_start_date" jdbcType="DATE" property="certStartDate"/>
        <result column="cert_expire_date" jdbcType="DATE" property="certExpireDate"/>
        <result column="cert_grant_org" jdbcType="VARCHAR" property="certGrantOrg"/>
        <result column="cert_file" jdbcType="VARCHAR" property="certFile"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        dept_id,
        emp_id,
        cert_type_id,
        cert_type_name,
        operation_item_id,
        operation_item_name,
        cert_name,
        cert_code,
        `level`,
        cert_start_date,
        cert_expire_date,
        cert_grant_org,
        cert_file,
        del_flag,
        update_time,
        create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from app_emp_cert
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete from app_emp_cert
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppEmpCert"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into app_emp_cert
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="empId != null">
                emp_id,
            </if>
            <if test="certTypeId != null">
                cert_type_id,
            </if>
            <if test="certTypeName != null">
                cert_type_name,
            </if>
            <if test="operationItemId != null">
                operation_item_id,
            </if>
            <if test="operationItemName != null">
                operation_item_name,
            </if>
            <if test="certName != null">
                cert_name,
            </if>
            <if test="certCode != null">
                cert_code,
            </if>
            <if test="level != null">
                `level`,
            </if>
            <if test="certStartDate != null">
                cert_start_date,
            </if>
            <if test="certExpireDate != null">
                cert_expire_date,
            </if>
            <if test="certGrantOrg != null">
                cert_grant_org,
            </if>
            <if test="certFile != null">
                cert_file,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                #{deptId,jdbcType=INTEGER},
            </if>
            <if test="empId != null">
                #{empId,jdbcType=INTEGER},
            </if>
            <if test="certTypeId != null">
                #{certTypeId,jdbcType=INTEGER},
            </if>
            <if test="certTypeName != null">
                #{certTypeName,jdbcType=VARCHAR},
            </if>
            <if test="operationItemId != null">
                #{operationItemId,jdbcType=INTEGER},
            </if>
            <if test="operationItemName != null">
                #{operationItemName,jdbcType=VARCHAR},
            </if>
            <if test="certName != null">
                #{certName,jdbcType=VARCHAR},
            </if>
            <if test="certCode != null">
                #{certCode,jdbcType=VARCHAR},
            </if>
            <if test="level != null">
                #{level,jdbcType=VARCHAR},
            </if>
            <if test="certStartDate != null">
                #{certStartDate,jdbcType=DATE},
            </if>
            <if test="certExpireDate != null">
                #{certExpireDate,jdbcType=DATE},
            </if>
            <if test="certGrantOrg != null">
                #{certGrantOrg,jdbcType=VARCHAR},
            </if>
            <if test="certFile != null">
                #{certFile,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppEmpCert">
        <!--@mbg.generated-->
        update app_emp_cert
        <set>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=INTEGER},
            </if>
            <if test="empId != null">
                emp_id = #{empId,jdbcType=INTEGER},
            </if>
            <if test="certTypeId != null">
                cert_type_id = #{certTypeId,jdbcType=INTEGER},
            </if>
            <if test="certTypeName != null">
                cert_type_name = #{certTypeName,jdbcType=VARCHAR},
            </if>
            <if test="operationItemId != null">
                operation_item_id = #{operationItemId,jdbcType=INTEGER},
            </if>
            <if test="operationItemName != null">
                operation_item_name = #{operationItemName,jdbcType=VARCHAR},
            </if>
            <if test="certName != null">
                cert_name = #{certName,jdbcType=VARCHAR},
            </if>
            <if test="certCode != null">
                cert_code = #{certCode,jdbcType=VARCHAR},
            </if>
            <if test="level != null">
                `level` = #{level,jdbcType=VARCHAR},
            </if>
            <if test="certStartDate != null">
                cert_start_date = #{certStartDate,jdbcType=DATE},
            </if>
            <if test="certExpireDate != null">
                cert_expire_date = #{certExpireDate,jdbcType=DATE},
            </if>
            <if test="certGrantOrg != null">
                cert_grant_org = #{certGrantOrg,jdbcType=VARCHAR},
            </if>
            <if test="certFile != null">
                cert_file = #{certFile,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="deleteLogic">
      update app_emp_cert
      set del_flag = 1
      where id = #{certId,jdbcType=INTEGER}
    </update>

    <update id="updateCertFile">
      update app_emp_cert
      set cert_file = #{fileUrl,jdbcType=VARCHAR}
      where id = #{certId,jdbcType=INTEGER}
    </update>

    <select id="selectByEmpId" resultType="com.whfc.emp.dto.AppEmpCertDTO">
        select id as certId,
               cert_type_id,
               cert_type_name,
               operation_item_id,
               operation_item_name,
               cert_name,
               cert_code,
               `level`,
               cert_start_date,
               cert_expire_date,
               cert_grant_org,
               cert_file
        from app_emp_cert
        where del_flag = 0
        and emp_id = #{empId}
    </select>

    <select id="selectByCertCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from app_emp_cert
        where dept_id = #{deptId}
          and cert_code = #{certCode}
          and del_flag = 0
    </select>
</mapper>