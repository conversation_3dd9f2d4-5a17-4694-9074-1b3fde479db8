<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppEmpRewardPunishmentMapper">
    <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppEmpRewardPunishment">
        <!--@mbg.generated-->
        <!--@Table app_emp_reward_punishment-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="emp_id" jdbcType="INTEGER" property="empId"/>
        <result column="emp_name" jdbcType="VARCHAR" property="empName"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="item" jdbcType="INTEGER" property="item"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="date" jdbcType="DATE" property="date"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, emp_id, emp_name, `type`, item, description, `date`, del_flag, update_time, create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from app_emp_reward_punishment
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete from app_emp_reward_punishment
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppEmpRewardPunishment"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into app_emp_reward_punishment (emp_id, emp_name, `type`,
        item, description, `date`,
        del_flag, update_time, create_time
        )
        values (#{empId,jdbcType=INTEGER}, #{empName,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER},
        #{item,jdbcType=INTEGER}, #{description,jdbcType=VARCHAR}, #{date,jdbcType=DATE},
        #{delFlag,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}
        )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.whfc.emp.entity.AppEmpRewardPunishment" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into app_emp_reward_punishment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="empId != null">
                emp_id,
            </if>
            <if test="empName != null">
                emp_name,
            </if>
            <if test="type != null">
                `type`,
            </if>
            <if test="item != null">
                item,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="date != null">
                `date`,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="empId != null">
                #{empId,jdbcType=INTEGER},
            </if>
            <if test="empName != null">
                #{empName,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="item != null">
                #{item,jdbcType=INTEGER},
            </if>
            <if test="description != null">
                #{description,jdbcType=VARCHAR},
            </if>
            <if test="date != null">
                #{date,jdbcType=DATE},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppEmpRewardPunishment">
        <!--@mbg.generated-->
        update app_emp_reward_punishment
        <set>
            <if test="empId != null">
                emp_id = #{empId,jdbcType=INTEGER},
            </if>
            <if test="empName != null">
                emp_name = #{empName,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                `type` = #{type,jdbcType=INTEGER},
            </if>
            <if test="item != null">
                item = #{item,jdbcType=INTEGER},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=VARCHAR},
            </if>
            <if test="date != null">
                `date` = #{date,jdbcType=DATE},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.whfc.emp.entity.AppEmpRewardPunishment">
        <!--@mbg.generated-->
        update app_emp_reward_punishment
        set emp_id = #{empId,jdbcType=INTEGER},
        emp_name = #{empName,jdbcType=VARCHAR},
        `type` = #{type,jdbcType=INTEGER},
        item = #{item,jdbcType=INTEGER},
        description = #{description,jdbcType=VARCHAR},
        `date` = #{date,jdbcType=DATE},
        del_flag = #{delFlag,jdbcType=INTEGER},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        create_time = #{createTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <select id="selectByProjectId" resultType="com.whfc.emp.dto.AppEmpRewardDTO">
        select aerp.emp_name,
        aerp.id,
        aerp.`type`,
        aerp.`item`,
        aerp.description,
        aerp.`date`
        from app_emp_reward_punishment aerp
        inner join app_emp ae on ae.id = aerp.emp_id
        where ae.del_flag = 0
        and aerp.del_flag = 0
        and ae.dept_id = #{projectId}
        <if test="startDate!=null">
            and aerp.`date`>= #{startDate}
        </if>
        <if test="endDate!=null">
            and #{endDate} >= aerp.`date`
        </if>
        <if test="keyword!=null and keyword!=''">
            and aerp.emp_name like concat('%',#{keyword},'%')
        </if>
        order by aerp.id desc
    </select>
    <update id="deleteLogicById">
    update app_emp_reward_punishment
    set del_flag = 1
    where id = #{id}
  </update>
    <select id="selectByEmpId" resultType="com.whfc.emp.dto.AppEmpRewardDTO">
    select `date`,
    `type`,
    `item`,
    description
    from app_emp_reward_punishment
    where del_flag = 0
    and emp_id = #{empId}
  </select>
</mapper>