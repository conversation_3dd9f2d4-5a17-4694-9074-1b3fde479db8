<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppEmpMapper">
    <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppEmp">
        <!--@mbg.generated-->
        <!--@Table app_emp-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="dept_id" jdbcType="INTEGER" property="deptId"/>
        <result column="dept_name" jdbcType="VARCHAR" property="deptName"/>
        <result column="corp_id" jdbcType="INTEGER" property="corpId"/>
        <result column="corp_name" jdbcType="VARCHAR" property="corpName"/>
        <result column="group_id" jdbcType="INTEGER" property="groupId"/>
        <result column="group_name" jdbcType="VARCHAR" property="groupName"/>
        <result column="emp_name" jdbcType="VARCHAR" property="empName"/>
        <result column="ename" jdbcType="VARCHAR" property="ename"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="gender" jdbcType="INTEGER" property="gender"/>
        <result column="birthday" jdbcType="DATE" property="birthday"/>
        <result column="id_card_no" jdbcType="VARCHAR" property="idCardNo"/>
        <result column="head_img" jdbcType="VARCHAR" property="headImg"/>
        <result column="avatar" jdbcType="VARCHAR" property="avatar"/>
        <result column="card_type" jdbcType="INTEGER" property="cardType"/>
        <result column="work_card_no" jdbcType="VARCHAR" property="workCardNo"/>
        <result column="work_card_end_date" jdbcType="DATE" property="workCardEndDate"/>
        <result column="id_card_front" jdbcType="VARCHAR" property="idCardFront"/>
        <result column="id_card_back" jdbcType="VARCHAR" property="idCardBack"/>
        <result column="id_card_grant_org" jdbcType="VARCHAR" property="idCardGrantOrg"/>
        <result column="id_card_start_date" jdbcType="DATE" property="idCardStartDate"/>
        <result column="id_card_end_date" jdbcType="DATE" property="idCardEndDate"/>
        <result column="nation" jdbcType="VARCHAR" property="nation"/>
        <result column="province" jdbcType="VARCHAR" property="province"/>
        <result column="city" jdbcType="VARCHAR" property="city"/>
        <result column="area" jdbcType="VARCHAR" property="area"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="education" jdbcType="INTEGER" property="education"/>
        <result column="degree" jdbcType="INTEGER" property="degree"/>
        <result column="emp_code" jdbcType="VARCHAR" property="empCode"/>
        <result column="work_role_id" jdbcType="INTEGER" property="workRoleId"/>
        <result column="work_role_name" jdbcType="VARCHAR" property="workRoleName"/>
        <result column="work_type_id" jdbcType="INTEGER" property="workTypeId"/>
        <result column="work_type_name" jdbcType="VARCHAR" property="workTypeName"/>
        <result column="leader_flag" jdbcType="INTEGER" property="leaderFlag"/>
        <result column="post_state" jdbcType="INTEGER" property="postState"/>
        <result column="bind_flag" jdbcType="INTEGER" property="bindFlag"/>
        <result column="device_id" jdbcType="INTEGER" property="deviceId"/>
        <result column="platform" jdbcType="VARCHAR" property="platform"/>
        <result column="sn" jdbcType="VARCHAR" property="sn"/>
        <result column="color" jdbcType="VARCHAR" property="color"/>
        <result column="qr" jdbcType="VARCHAR" property="qr"/>
        <result column="enter_time" jdbcType="DATE" property="enterTime"/>
        <result column="outer_time" jdbcType="DATE" property="outerTime"/>
        <result column="key_position_flag" jdbcType="INTEGER" property="keyPositionFlag"/>
        <result column="key_position_auth" jdbcType="INTEGER" property="keyPositionAuth"/>
        <result column="enter_train_flag" jdbcType="INTEGER" property="enterTrainFlag"/>
        <result column="verify_state" jdbcType="INTEGER" property="verifyState"/>
        <result column="sync_flag" jdbcType="INTEGER" property="syncFlag"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        dept_id,
        dept_name,
        corp_id,
        corp_name,
        group_id,
        group_name,
        emp_name,
        ename,
        phone,
        gender,
        birthday,
        id_card_no,
        head_img,
        avatar,
        card_type,
        work_card_no,
        work_card_end_date,
        id_card_front,
        id_card_back,
        id_card_grant_org,
        id_card_start_date,
        id_card_end_date,
        nation,
        province,
        city,
        area,
        address,
        education,
        `degree`,
        emp_code,
        work_role_id,
        work_role_name,
        work_type_id,
        work_type_name,
        leader_flag,
        post_state,
        bind_flag,
        device_id,
        platform,
        sn,
        color,
        qr,
        enter_time,
        outer_time,
        key_position_flag,
        key_position_auth,
        enter_train_flag,
        verify_state,
        sync_flag,
        del_flag,
        update_time,
        create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from app_emp
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete
        from app_emp
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppEmp"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into app_emp
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="deptName != null">
                dept_name,
            </if>
            <if test="corpId != null">
                corp_id,
            </if>
            <if test="corpName != null">
                corp_name,
            </if>
            <if test="groupId != null">
                group_id,
            </if>
            <if test="groupName != null">
                group_name,
            </if>
            <if test="empName != null">
                emp_name,
            </if>
            <if test="ename != null">
                ename,
            </if>
            <if test="phone != null">
                phone,
            </if>
            <if test="gender != null">
                gender,
            </if>
            <if test="birthday != null">
                birthday,
            </if>
            <if test="idCardNo != null">
                id_card_no,
            </if>
            <if test="headImg != null">
                head_img,
            </if>
            <if test="avatar != null">
                avatar,
            </if>
            <if test="cardType != null">
                card_type,
            </if>
            <if test="workCardNo != null">
                work_card_no,
            </if>
            <if test="workCardEndDate != null">
                work_card_end_date,
            </if>
            <if test="idCardFront != null">
                id_card_front,
            </if>
            <if test="idCardBack != null">
                id_card_back,
            </if>
            <if test="idCardGrantOrg != null">
                id_card_grant_org,
            </if>
            <if test="idCardStartDate != null">
                id_card_start_date,
            </if>
            <if test="idCardEndDate != null">
                id_card_end_date,
            </if>
            <if test="nation != null">
                nation,
            </if>
            <if test="province != null">
                province,
            </if>
            <if test="city != null">
                city,
            </if>
            <if test="area != null">
                area,
            </if>
            <if test="address != null">
                address,
            </if>
            <if test="education != null">
                education,
            </if>
            <if test="degree != null">
                `degree`,
            </if>
            <if test="empCode != null">
                emp_code,
            </if>
            <if test="workRoleId != null">
                work_role_id,
            </if>
            <if test="workRoleName != null">
                work_role_name,
            </if>
            <if test="workTypeId != null">
                work_type_id,
            </if>
            <if test="workTypeName != null">
                work_type_name,
            </if>
            <if test="leaderFlag != null">
                leader_flag,
            </if>
            <if test="postState != null">
                post_state,
            </if>
            <if test="bindFlag != null">
                bind_flag,
            </if>
            <if test="deviceId != null">
                device_id,
            </if>
            <if test="sn != null">
                sn,
            </if>
            <if test="color != null">
                color,
            </if>
            <if test="qr != null">
                qr,
            </if>
            <if test="enterTime != null">
                enter_time,
            </if>
            <if test="outerTime != null">
                outer_time,
            </if>
            <if test="keyPositionFlag != null">
                key_position_flag,
            </if>
            <if test="keyPositionAuth != null">
                key_position_auth,
            </if>
            <if test="enterTrainFlag != null">
                enter_train_flag,
            </if>
            <if test="verifyState != null">
                verify_state,
            </if>
            <if test="syncFlag != null">
                sync_flag,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                #{deptId,jdbcType=INTEGER},
            </if>
            <if test="deptName != null">
                #{deptName,jdbcType=VARCHAR},
            </if>
            <if test="corpId != null">
                #{corpId,jdbcType=INTEGER},
            </if>
            <if test="corpName != null">
                #{corpName,jdbcType=VARCHAR},
            </if>
            <if test="groupId != null">
                #{groupId,jdbcType=INTEGER},
            </if>
            <if test="groupName != null">
                #{groupName,jdbcType=VARCHAR},
            </if>
            <if test="empName != null">
                #{empName,jdbcType=VARCHAR},
            </if>
            <if test="ename != null">
                #{ename,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="gender != null">
                #{gender,jdbcType=INTEGER},
            </if>
            <if test="birthday != null">
                #{birthday,jdbcType=DATE},
            </if>
            <if test="idCardNo != null">
                #{idCardNo,jdbcType=VARCHAR},
            </if>
            <if test="headImg != null">
                #{headImg,jdbcType=VARCHAR},
            </if>
            <if test="avatar != null">
                #{avatar,jdbcType=VARCHAR},
            </if>
            <if test="cardType != null">
                #{cardType,jdbcType=INTEGER},
            </if>
            <if test="workCardNo != null">
                #{workCardNo,jdbcType=VARCHAR},
            </if>
            <if test="workCardEndDate != null">
                #{workCardEndDate,jdbcType=DATE},
            </if>
            <if test="idCardFront != null">
                #{idCardFront,jdbcType=VARCHAR},
            </if>
            <if test="idCardBack != null">
                #{idCardBack,jdbcType=VARCHAR},
            </if>
            <if test="idCardGrantOrg != null">
                #{idCardGrantOrg,jdbcType=VARCHAR},
            </if>
            <if test="idCardStartDate != null">
                #{idCardStartDate,jdbcType=DATE},
            </if>
            <if test="idCardEndDate != null">
                #{idCardEndDate,jdbcType=DATE},
            </if>
            <if test="nation != null">
                #{nation,jdbcType=VARCHAR},
            </if>
            <if test="province != null">
                #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                #{city,jdbcType=VARCHAR},
            </if>
            <if test="area != null">
                #{area,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="education != null">
                #{education,jdbcType=INTEGER},
            </if>
            <if test="degree != null">
                #{degree,jdbcType=INTEGER},
            </if>
            <if test="empCode != null">
                #{empCode,jdbcType=VARCHAR},
            </if>
            <if test="workRoleId != null">
                #{workRoleId,jdbcType=INTEGER},
            </if>
            <if test="workRoleName != null">
                #{workRoleName,jdbcType=VARCHAR},
            </if>
            <if test="workTypeId != null">
                #{workTypeId,jdbcType=INTEGER},
            </if>
            <if test="workTypeName != null">
                #{workTypeName,jdbcType=VARCHAR},
            </if>
            <if test="leaderFlag != null">
                #{leaderFlag,jdbcType=INTEGER},
            </if>
            <if test="postState != null">
                #{postState,jdbcType=INTEGER},
            </if>
            <if test="bindFlag != null">
                #{bindFlag,jdbcType=INTEGER},
            </if>
            <if test="deviceId != null">
                #{deviceId,jdbcType=INTEGER},
            </if>
            <if test="sn != null">
                #{sn,jdbcType=VARCHAR},
            </if>
            <if test="color != null">
                #{color,jdbcType=VARCHAR},
            </if>
            <if test="qr != null">
                #{qr,jdbcType=VARCHAR},
            </if>
            <if test="enterTime != null">
                #{enterTime,jdbcType=DATE},
            </if>
            <if test="outerTime != null">
                #{outerTime,jdbcType=DATE},
            </if>
            <if test="keyPositionFlag != null">
                #{keyPositionFlag,jdbcType=INTEGER},
            </if>
            <if test="keyPositionAuth != null">
                #{keyPositionAuth,jdbcType=INTEGER},
            </if>
            <if test="enterTrainFlag != null">
                #{enterTrainFlag,jdbcType=INTEGER},
            </if>
            <if test="verifyState != null">
                #{verifyState,jdbcType=INTEGER},
            </if>
            <if test="syncFlag != null">
                #{syncFlag,jdbcType=INTEGER},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppEmp">
        <!--@mbg.generated-->
        update app_emp
        <set>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=INTEGER},
            </if>
            <if test="deptName != null">
                dept_name = #{deptName,jdbcType=VARCHAR},
            </if>
            <if test="corpId != null">
                corp_id = #{corpId,jdbcType=INTEGER},
            </if>
            <if test="corpName != null">
                corp_name = #{corpName,jdbcType=VARCHAR},
            </if>
            <if test="groupId != null">
                group_id = #{groupId,jdbcType=INTEGER},
            </if>
            <if test="groupName != null">
                group_name = #{groupName,jdbcType=VARCHAR},
            </if>
            <if test="empName != null">
                emp_name = #{empName,jdbcType=VARCHAR},
            </if>
            <if test="ename != null">
                ename = #{ename,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="gender != null">
                gender = #{gender,jdbcType=INTEGER},
            </if>
            <if test="birthday != null">
                birthday = #{birthday,jdbcType=DATE},
            </if>
            <if test="idCardNo != null">
                id_card_no = #{idCardNo,jdbcType=VARCHAR},
            </if>
            <if test="headImg != null">
                head_img = #{headImg,jdbcType=VARCHAR},
            </if>
            <if test="avatar != null">
                avatar = #{avatar,jdbcType=VARCHAR},
            </if>
            <if test="cardType != null">
                card_type = #{cardType,jdbcType=INTEGER},
            </if>
            <if test="workCardNo != null">
                work_card_no = #{workCardNo,jdbcType=VARCHAR},
            </if>
            <if test="workCardEndDate != null">
                work_card_end_date = #{workCardEndDate,jdbcType=DATE},
            </if>
            <if test="idCardFront != null">
                id_card_front = #{idCardFront,jdbcType=VARCHAR},
            </if>
            <if test="idCardBack != null">
                id_card_back = #{idCardBack,jdbcType=VARCHAR},
            </if>
            <if test="idCardGrantOrg != null">
                id_card_grant_org = #{idCardGrantOrg,jdbcType=VARCHAR},
            </if>
            <if test="idCardStartDate != null">
                id_card_start_date = #{idCardStartDate,jdbcType=DATE},
            </if>
            <if test="idCardEndDate != null">
                id_card_end_date = #{idCardEndDate,jdbcType=DATE},
            </if>
            <if test="nation != null">
                nation = #{nation,jdbcType=VARCHAR},
            </if>
            <if test="province != null">
                province = #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                city = #{city,jdbcType=VARCHAR},
            </if>
            <if test="area != null">
                area = #{area,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="education != null">
                education = #{education,jdbcType=INTEGER},
            </if>
            <if test="degree != null">
                `degree` = #{degree,jdbcType=INTEGER},
            </if>
            <if test="empCode != null">
                emp_code = #{empCode,jdbcType=VARCHAR},
            </if>
            <if test="workRoleId != null">
                work_role_id = #{workRoleId,jdbcType=INTEGER},
            </if>
            <if test="workRoleName != null">
                work_role_name = #{workRoleName,jdbcType=VARCHAR},
            </if>
            <if test="workTypeId != null">
                work_type_id = #{workTypeId,jdbcType=INTEGER},
            </if>
            <if test="workTypeName != null">
                work_type_name = #{workTypeName,jdbcType=VARCHAR},
            </if>
            <if test="leaderFlag != null">
                leader_flag = #{leaderFlag,jdbcType=INTEGER},
            </if>
            <if test="postState != null">
                post_state = #{postState,jdbcType=INTEGER},
            </if>
            <if test="bindFlag != null">
                bind_flag = #{bindFlag,jdbcType=INTEGER},
            </if>
            <if test="deviceId != null">
                device_id = #{deviceId,jdbcType=INTEGER},
            </if>
            <if test="sn != null">
                sn = #{sn,jdbcType=VARCHAR},
            </if>
            <if test="color != null">
                color = #{color,jdbcType=VARCHAR},
            </if>
            <if test="qr != null">
                qr = #{qr,jdbcType=VARCHAR},
            </if>
            <if test="enterTime != null">
                enter_time = #{enterTime,jdbcType=DATE},
            </if>
            <if test="outerTime != null">
                outer_time = #{outerTime,jdbcType=DATE},
            </if>
            <if test="keyPositionFlag != null">
                key_position_flag = #{keyPositionFlag,jdbcType=INTEGER},
            </if>
            <if test="keyPositionAuth != null">
                key_position_auth = #{keyPositionAuth,jdbcType=INTEGER},
            </if>
            <if test="enterTrainFlag != null">
                enter_train_flag = #{enterTrainFlag,jdbcType=INTEGER},
            </if>
            <if test="verifyState != null">
                verify_state = #{verifyState,jdbcType=INTEGER},
            </if>
            <if test="syncFlag != null">
                sync_flag = #{syncFlag,jdbcType=INTEGER},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectDeptEmpList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List">
        </include>
          from app_emp ae
          where ae.dept_id = #{deptId}
          and ae.del_flag = 0
    </select>

    <select id="selectEmpListExclude" resultType="com.whfc.emp.dto.AppEmpDTO">
        SELECT  ae.id as empId,
                ae.emp_name,
                ae.work_type_name,
                ae.dept_id,
                ae.dept_name,
                ae.id_card_no,
                ae.phone,
                ae.avatar,
                ae.group_name,
                ae.group_id
        FROM app_emp ae
        WHERE ae.dept_id = #{deptId}
          and ae.del_flag = 0
        <if test="empIdList!=null and empIdList.size()>0">
          AND ae.id not in (
            <foreach collection="empIdList" item="empId" separator=",">
                #{empId}
            </foreach>
            )
        </if>
    </select>

    <select id="selectEmpListExcludes" resultType="com.whfc.emp.dto.AppEmpDTO">
        SELECT  ae.id as empId,
        ae.emp_name,
        ae.work_type_name,
        ae.dept_id,
        ae.dept_name,
        ae.id_card_no,
        ae.phone,
        ae.avatar,
        ae.group_name,
        ae.group_id
        FROM app_emp ae
        WHERE ae.dept_id = #{deptId}
        and ae.del_flag = 0
        <if test="groupId !=null">
            and ae.group_id = #{groupId}
        </if>
        <if test="keyword !=null">
            and ae.emp_name like concat('%',#{keyword},'%')
        </if>
        <if test="empIdList!=null and empIdList.size()>0">
            AND ae.id not in (
            <foreach collection="empIdList" item="empId" separator=",">
                #{empId}
            </foreach>
            )
        </if>
    </select>

    <select id="selectBindEmpByCorpIdOrGroupId" resultMap="BaseResultMap">
        select ae.id,ae.emp_name
        from app_emp ae
        inner join app_emp_device aed on ae.id = aed.emp_id
         where ae.dept_id = #{deptId}
           AND ae.del_flag = 0
        <if test="corpId != null">
            AND ae.corp_id = #{corpId}
        </if>
        <if test="groupId != null">
            AND ae.group_id = #{groupId}
        </if>
    </select>

    <select id="selectByProjectIdAndEmpName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from app_emp
        where del_flag = 0
          and dept_id = #{projectId}
          and emp_name = #{empName}
    </select>

    <select id="selectByParam" resultType="com.whfc.emp.dto.AppEmpDTO">
        SELECT ae.id AS empId,
               ae.emp_name,
               ae.id_card_no,
               ae.phone,
               ae.gender,
               ae.birthday,
               ae.nation,
               ae.avatar,
               ae.dept_name,
               ae.corp_name,
               ae.work_role_id,
               ae.work_role_name,
               ae.work_type_name,
               ae.post_state,
               ae.enter_time,
               ae.outer_time,
               ae.group_id,
               aeg.group_name,
               ifnull(ae.verify_state,0) as verify_state,
               ae.sync_flag
        FROM app_emp ae
        LEFT JOIN app_emp_group aeg on aeg.id = ae.group_id
        where ae.del_flag = 0
        AND ae.dept_id = #{deptId}
        <if test="gender != null">
            and ae.gender = #{gender}
        </if>
        <if test="corpId != null">
            and ae.corp_id = #{corpId}
        </if>
        <if test="workTypeId != null">
            and ae.work_type_id = #{workTypeId}
        </if>
        <if test="workRoleId != null">
            and ae.work_role_id = #{workRoleId}
        </if>
        <if test="postState != null">
            and ae.post_state = #{postState}
        </if>
        <if test="keyword != null and keyword != ''">
            and ae.emp_name like concat('%', #{keyword}, '%')
        </if>
        <if test="groupId != null">
            and ae.group_id = #{groupId}
        </if>
        order by ae.id desc
    </select>

    <select id="selectByDeptId" resultType="com.whfc.emp.dto.AppEmpDTO">
        select id as empId,
               emp_name,
               work_type_name,
               dept_id,
               dept_name,
               id_card_no,
               phone,
               avatar,
               group_name,
               group_id
        from app_emp
        where del_flag = 0
         and dept_id = #{deptId}
         and post_state = 1
        <if test="keyword != null and keyword != ''">
            and emp_name like concat('%', #{keyword}, '%')
        </if>
    </select>

    <update id="updateQrByEmpId">
        update app_emp
        set qr=#{qr}
        where id = #{empId}
    </update>

    <update id="delLogicByEmpId">
        update app_emp
        set del_flag=1
        where id = #{empId}
    </update>

    <update id="updatePostState">
        update app_emp
                set post_state=#{postState}
        <if test="postState == 1">
            , enter_time=#{date}
        </if>
        <if test="postState == 2">
            , outer_time=#{date}
        </if>
        where id = #{empId}
    </update>

    <update id="updatePostStateByEmpIds">
        update app_emp
                set post_state=#{postState}
        <if test="postState == 1">
            , enter_time=#{date}
        </if>
        <if test="postState == 2">
            , outer_time=#{date}
        </if>
        where id in
        <foreach close=")" collection="empIds" item="emdId" open="(" separator=",">
            #{emdId}
        </foreach>
    </update>

    <select id="selectEmpDTOById" resultType="com.whfc.emp.dto.AppEmpDTO">
        SELECT avatar,
               emp_name,
               gender,
               birthday,
               phone,
               id_card_no,
               enter_time,
               work_role_name,
               work_type_name,
               dept_name,
               leader_flag,
               corp_name,
               qr,
               group_name,
               dept_id
        FROM app_emp
        WHERE id = #{empId} AND del_flag = 0
    </select>

    <select id="selectAnaEmpByDeptId" resultType="com.whfc.emp.dto.AppEmpDTO">
        SELECT ae.id  AS empId,
               ae.work_role_name,
               ae.work_role_id,
               ifnull(ae.work_type_name,'未知') as work_type_name,
               ifnull(ae.work_type_id,-1) as work_type_id,
               ifnull(ae.corp_id,-1) as corp_id,
               ae.gender,
               ifnull(aed.attend_state, 0) as attendState,
               ifnull(aed.locale_state, 0) as localeState,
               aed.time
        FROM app_emp ae
        LEFT JOIN app_emp_data aed ON aed.emp_id = ae.id
        where ae.del_flag = 0
          and ae.post_state = 1
          and ae.dept_id = #{deptId}
    </select>

    <select id="selectRecentEnterEmp" resultType="com.whfc.emp.dto.AppAnaEmpDTO">
        select ae.avatar,
               ae.emp_name   as name,
               ae.enter_time as `date`
        from app_emp ae
        where ae.del_flag = 0
          and ae.post_state = 1
          and ae.dept_id = #{deptId}
        order by ae.enter_time desc
    </select>

    <select id="selectWorkTypeList" resultType="com.whfc.emp.dto.AppWorkTypeDTO">
        select DISTINCT work_type_id,
                        work_type_name
        from app_emp
        where del_flag = 0
          and dept_id = #{deptId}
    </select>

    <select id="selectIndexEmpList" resultType="com.whfc.emp.dto.AppEmpDTO">
        select ae.id as empId,
               ae.emp_name,
               ae.work_type_name,
               ae.work_type_id,
               ae.work_role_name,
               ae.work_role_id,
               ae.group_id,
               ae.group_name,
               ae.phone,
               ae.leader_flag,
               aed2.device_type,
               aed2.sn,
               aed2.color,
               aed.attend_state,
               aed.net_state,
               aed.lng,
               aed.lat,
               aed.gps_time,
               aed.locale_state,
               aed.time
        from app_emp ae
        inner join app_emp_data aed on ae.id = aed.emp_id
        inner join app_emp_device aed2 on ae.id = aed2.emp_id
        where ae.post_state = 1
         and ae.del_flag = 0
         and ae.dept_id = #{deptId}
        <if test="keyword != null and keyword != ''">
            and (ae.emp_name like concat('%', #{keyword}, '%')
                    or ae.work_type_name like concat('%', #{keyword}, '%')
                    )
        </if>
        <if test="attendState != null">
            and aed.attend_state = #{attendState}
        </if>
        <if test="localeState != null">
            and aed.locale_state = #{localeState}
        </if>
        <if test="groupId != null">
            and ae.group_id = #{groupId}
        </if>
        <if test="empId != null">
            and ae.id = #{empId}
        </if>
    </select>

    <update id="updateKeyPositionAuth">
        update app_emp
        set key_position_auth = #{keyPositionAuth}
        where id = #{empId}
    </update>

    <update id="updateEmpInfoByIdCardNo">
        update app_emp
        set `nation`            = #{nation},
            `phone`             = #{phone},
            `address`           = #{address},
            `id_card_grant_org` = #{org}
        where id_card_no = #{idcardNo}
    </update>

    <update id="delLogicByDeptIdAndIdCardNo">
        update app_emp
        set del_flag=1
        where dept_id = #{deptId}
          and id_card_no = #{idCardNo}
    </update>

    <select id="selectByDeptIdAndIdCardNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from app_emp
        where dept_id = #{deptId}
          and id_card_no = #{idCardNo}
          and del_flag = 0
        order by id desc limit 1
    </select>

    <update id="delLogicByDeptIdAndEmpCode">
        update app_emp
        set del_flag=1
        where dept_id = #{deptId}
          and emp_code = #{empCode}
    </update>

    <select id="selectByDeptIdAndEmpCode" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM app_emp
        WHERE dept_id = #{deptId}
          and emp_code = #{empCode}
          and del_flag = 0
        order by id desc limit 1
    </select>

    <select id="selectByDeptIdAndPhone" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM app_emp
        WHERE dept_id = #{deptId}
          and phone = #{phone}
          and del_flag = 0
        order by id desc limit 1
    </select>

    <select id="selectGisEmpData" resultType="com.whfc.emp.dto.GisEmpDTO">
        select ae.id,
               ae.emp_name   as name,
               ae.dept_name,
               ae.gender,
               ae.work_type_name,
               aed.lng_wgs84 as lng,
               aed.lat_wgs84 as lat
        from app_emp ae
                     inner join app_emp_data aed on aed.emp_id = ae.id
        where ae.del_flag = 0
          and ae.dept_id = #{deptId}
          and aed.net_state = 1
    </select>

    <select id="selectEmpListByDeptId" resultType="com.whfc.emp.dto.AppEmpDTO">
        select ae.id as empId,
               ae.emp_name,
               ae.emp_code
        from app_emp ae
        where ae.dept_id = #{deptId}
          and ae.post_state = 1
          and del_flag = 0
    </select>

    <select id="selectEmpByDeptIdAndKeyword" resultType="com.whfc.emp.dto.AppEmpDTO">
        select ae.id                        as empId,
               ae.emp_name,
               ifnull(ae.group_id,0)        as groupId,
               ifnull(ae.group_name,'未分组') as groupName,
               ae.work_type_name,
               ae.work_type_id,
               ae.work_role_id,
               ae.leader_flag,
               ae.phone,
               ae.ename,
               ae.gender,
               ifnull(aeda.net_state, 0)    as net_state,
               aeda.lng,
               aeda.lat,
               ifnull(aeda.attend_state, 0) as attend_state,
               ifnull(aeda.locale_state, 0) as locale_state,
               aeda.time,
               aeda.gps_time,
               aeda.battery_power
        from app_emp ae
        left join app_emp_data aeda on ae.id = aeda.emp_id
       where ae.post_state = 1
         AND ae.del_flag = 0
         and ae.dept_id = #{deptId}
        <if test="keyword != null and keyword != ''">
            and (ae.group_name like concat('%', #{keyword}, '%')
                 or ae.emp_name like concat('%', #{keyword}, '%'))
        </if>
        order by ae.ename
    </select>

    <select id="selectOpenApiEmpList" resultType="com.whfc.emp.dto.AppEmpDTO">
        select ae.id as empId,
               ae.emp_name,
               ae.emp_code,
               ae.group_id,
               ae.group_name,
               ae.gender
        from app_emp ae
        where ae.del_flag = 0
          AND ae.post_state = 1
          and ae.dept_id = #{deptId}
    </select>

    <select id="selectPostEmpNum" resultType="java.lang.Integer">
        select count(1)
        from app_emp
        where del_flag = 0
        AND post_state = 1
        and dept_id in
        <foreach close=")" collection="deptIds" item="deptId" open="(" separator=",">
            #{deptId}
        </foreach>
    </select>

    <select id="countLocaleNum" resultType="java.lang.Integer">
        select ifnull(count(ae.id), 0)
        from app_emp ae
        left join app_emp_data aeda on ae.id = aeda.emp_id
        where ae.post_state = 1
        and aeda.attend_state = 1
        and aeda.locale_state = 1
        and ae.dept_id  = #{deptId}
    </select>

    <select id="countByDeptId" resultType="java.lang.Integer">
        select count(1)
        from app_emp
        where del_flag = 0
          and post_state = 1
          and dept_id = #{deptId}
    </select>

    <select id="countByWorkTypeId" resultType="java.lang.Integer">
        SELECT count(*)
        FROM app_emp
        WHERE del_flag = 0
          and post_state = 1
          and dept_id = #{deptId}
          and work_type_id = #{workTypeId}
    </select>

    <select id="countByWorkRole" resultType="java.lang.Integer">
        SELECT count(*)
        FROM app_emp
        WHERE del_flag = 0
          and post_state = 1
          and dept_id = #{deptId}
          and work_role_id = #{workRoleId}
    </select>

    <select id="countByGroup" resultType="java.lang.Integer">
        SELECT count(*)
        FROM app_emp
        WHERE del_flag = 0
          and post_state = 1
          and dept_id = #{deptId}
          and group_id = #{groupId}
    </select>

    <select id="selectEmpNumByDeptId" resultType="com.whfc.emp.dto.AppEmpAnaNumDTO">
        SELECT COUNT(id)                                  as empTotal,
               ifnull(sum(IF(gender = 1, 1, 0)), 0)       as male,
               ifnull(sum(IF(gender = 2, 1, 0)), 0)       as female
        FROM app_emp
        WHERE del_flag = 0
          AND post_state = 1
          AND dept_id = #{deptId}
    </select>


    <select id="countByWorkRoleId" resultType="com.whfc.emp.dto.AppEmpWorkRoleNumDTO">
        SELECT work_role_id,work_role_name,count(*) num
        FROM app_emp
        WHERE del_flag = 0
        and post_state = 1
        and dept_id = #{deptId}
        GROUP BY work_role_id
    </select>

    <select id="selectBirthdayByDeptId" resultType="com.whfc.emp.dto.EmpAge">
        SELECT birthday,
               FLOOR(DATEDIFF(NOW(),birthday)/365) as age,
               gender
        FROM app_emp
        WHERE dept_id = #{deptId}
          AND post_state = 1
          and del_flag = 0
    </select>

    <select id="selectEnterpriseAgeStat" resultType="com.whfc.emp.dto.EmpAge">
        SELECT ifnull(FLOOR(DATEDIFF(NOW(),birthday)/365),-1) as age,
               count(*) as num
        FROM app_emp
        WHERE dept_id in
        <foreach collection="deptIds" item="deptId" open="(" separator="," close=")" >
            #{deptId}
        </foreach>
          AND post_state = 1
          and del_flag = 0
        GROUP BY age
    </select>

    <select id="selectEnterpriseCorpStat" resultType="com.whfc.emp.dto.AppEmpAttendGroupDTO">
        SELECT ifnull(ae.corp_name,'待分组') AS name,
               COUNT(0)   AS num
        FROM app_emp ae
        WHERE ae.dept_id in
        <foreach collection="deptIds" item="deptId" open="(" separator="," close=")" >
            #{deptId}
        </foreach>
          AND ae.post_state = 1
          AND ae.del_flag = 0
        GROUP BY ae.corp_name
    </select>

    <select id="selectEnterpriseWorkStat" resultType="com.whfc.emp.dto.AppEmpAttendGroupDTO">
        SELECT  ifnull(ae.work_type_name,'其他') as name,
                COUNT(0) AS num
        FROM app_emp ae
        WHERE ae.dept_id in
        <foreach collection="deptIds" item="deptId" open="(" separator="," close=")" >
            #{deptId}
        </foreach>
          AND ae.post_state = 1
          AND ae.del_flag = 0
        GROUP BY ae.work_type_name
    </select>

    <select id="selectEnterpriseSpecWorkStat" resultType="com.whfc.emp.dto.AppEmpAttendGroupDTO">
        SELECT  ifnull(ae.work_type_name,'其他') as name,
                COUNT(0) AS num
        FROM app_emp ae
        inner join app_emp_work_type aet on ae.work_type_id = aet.id
        WHERE ae.dept_id in
        <foreach collection="deptIds" item="deptId" open="(" separator="," close=")" >
            #{deptId}
        </foreach>
        AND ae.post_state = 1
        AND ae.del_flag = 0
        and aet.spec = 1
        GROUP BY ae.work_type_name
    </select>

    <select id="selectGroupNumByDeptId" resultType="com.whfc.emp.dto.AppEmpAttendGroupDTO">
        SELECT IFNULL(aeg.id,-1) as id,
               ifnull(aeg.group_name,'未知') as name,
               COUNT(0)       as num
          FROM app_emp ae
          LEFT JOIN app_emp_group aeg ON ae.group_id = aeg.id
         WHERE ae.dept_id = #{deptId}
           AND ae.post_state = 1
           AND ae.del_flag = 0
          group by aeg.id,aeg.group_name
    </select>

    <select id="selectCorpNumByDeptId" resultType="com.whfc.emp.dto.AppEmpAttendGroupDTO">
        SELECT ifnull(ae.corp_id,-1) AS id,
               ifnull(ae.corp_name,'未知') AS name,
               COUNT(0)   AS num
          FROM app_emp ae
         WHERE ae.dept_id = #{deptId}
          AND ae.post_state = 1
          AND ae.del_flag = 0
        GROUP BY ae.corp_id
    </select>

    <select id="selectWorkNumByDeptId" resultType="com.whfc.emp.dto.AppEmpAttendGroupDTO">
        SELECT  ifnull(aet.id,-1) as id,
                ifnull(aet.name,'未知') as name,
                COUNT(0) AS num
        FROM app_emp ae
        LEFT JOIN app_emp_work_type aet ON ae.work_type_id = aet.id
        WHERE ae.dept_id = #{deptId}
          AND ae.post_state = 1
          AND ae.del_flag = 0
        GROUP BY ae.work_type_id
    </select>

    <select id="selectHelmetEmpList" resultType="com.whfc.emp.dto.AppEmpDTO">
        select ae.id     as empId,
               ae.emp_name,
               ae.phone,
               ae.leader_flag,
               ae.work_type_name,
               ae.work_type_id,
               ae.work_role_name,
               ae.work_role_id,
               ae.group_id,
               ae.group_name,
               aed2.device_type,
               aed2.platform,
               aed2.sn,
               aed2.color,
               aed.attend_state,
               aed.net_state,
               aed.lng,
               aed.lat,
               aed.locale_state,
               aed.time
        from app_emp ae
        left join app_emp_data aed on ae.id = aed.emp_id
        left join app_emp_device aed2 on ae.id = aed2.emp_id
         where ae.dept_id = #{deptId}
          and ae.post_state = 1
          and ae.del_flag = 0
        <choose>
            <when test="bindFlag == 1">
                and aed2.bind_flag = 1
            </when>
            <when test="bindFlag == 0">
                and ( aed2.bind_flag = 0 or aed2.bind_flag is null )
            </when>
        </choose>
        <if test="keyword != null">
            and ae.emp_name like concat('%', #{keyword}, '%')
        </if>
    </select>

    <select id="selectEmpNumByParam" resultType="com.whfc.emp.dto.AppEmpNumDTO">
        SELECT count(0)                                    as empAllNum,
               IFNULL(sum(if(ae.post_state = 1, 1, 0)), 0) as empOuterNum,
               IFNULL(sum(if(ae.post_state = 2, 1, 0)), 0) as empEnterNum
        FROM app_emp ae
                where ae.del_flag = 0
                  AND ae.dept_id = #{deptId}
        <if test="gender != null">
            and ae.gender = #{gender}
        </if>
        <if test="corpId != null">
            and ae.corp_id = #{corpId}
        </if>
        <if test="workTypeId != null">
            and ae.work_type_id = #{workTypeId}
        </if>
        <if test="workRoleId != null">
            and ae.work_role_id = #{workRoleId}
        </if>
        <if test="postState != null">
            and ae.post_state = #{postState}
        </if>
        <if test="keyword != null and keyword != ''">
            and ae.emp_name like concat('%', #{keyword}, '%')
        </if>
        <if test="groupId != null">
            and ae.group_id = #{groupId}
        </if>
        order by ae.id desc
    </select>

    <update id="updateEmpEnterTrainFlag">
        UPDATE app_emp
        SET enter_train_flag = #{enterTrainFlag}
        WHERE id = #{empId}
    </update>

    <update id="updateEmpVerifyState">
        UPDATE app_emp
        SET verify_state = #{verifyState}
        WHERE id = #{empId}
    </update>

    <update id="updateEmpSyncFlag">
        UPDATE app_emp
        SET sync_flag = #{syncFlag}
        WHERE id = #{empId}
    </update>

    <insert id="batchInsertEmp">
        INSERT INTO app_emp(dept_id, emp_name, emp_code)
                VALUES
        <foreach collection="empList" item="emp" separator=",">
            (#{emp.deptId},
             #{emp.empName},
             #{emp.empCode})
        </foreach>
    </insert>

    <select id="selectDelEmpList" resultType="com.whfc.emp.dto.AppEmpDTO">
        select id as emp_id,
               dept_id
        from app_emp
        where del_flag = 1
    </select>

    <select id="selectOuterEmpList" resultType="com.whfc.emp.dto.AppEmpDTO">
        select id as emp_id,
               dept_id
        from app_emp
        where del_flag = 0
         and post_state = 2
    </select>

    <update id="updateWorkTypeId">
        update app_emp
        set work_type_id = #{workTypeId}
        where dept_id = #{deptId}
          and work_type_name = #{workTypeName}
    </update>

    <update id="updateWorkTypeName">
        UPDATE app_emp
        SET work_type_name = #{workTypeName}
        WHERE work_type_id = #{workTypeId}
          and dept_id = #{deptId}
    </update>

    <select id="selectOddWorkTypeEmp" resultMap="BaseResultMap">
        SELECT ae.* FROM app_emp ae
            LEFT JOIN app_emp_work_type aewt ON ae.work_type_id = aewt.id
        WHERE ae.dept_id &lt;&gt; aewt.dept_id
    </select>

    <select id="selectByDeptIdsAndEmpId" resultType="java.lang.Integer">
        SELECT id
        FROM app_emp
        WHERE del_flag = 0
        AND id = #{empId}
        AND dept_id IN
        <foreach collection="deptIdList" item="deptId" open="(" close=")" separator=",">
            #{deptId}
        </foreach>
    </select>

    <select id="checkEmpName" resultType="int">
        SELECT count(0)
        FROM app_emp
        WHERE del_flag = 0
        AND dept_id = #{deptId}
        AND emp_name = #{name}
    </select>
</mapper>