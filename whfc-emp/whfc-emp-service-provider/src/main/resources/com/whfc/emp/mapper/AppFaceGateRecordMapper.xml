<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppFaceGateRecordMapper">
    <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppFaceGateRecord">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="dept_id" jdbcType="INTEGER" property="deptId" />
        <result column="face_gate_id" jdbcType="INTEGER" property="faceGateId"/>
        <result column="device_key" jdbcType="VARCHAR" property="deviceKey"/>
        <result column="person_guid" jdbcType="VARCHAR" property="personGuid"/>
        <result column="emp_id" jdbcType="INTEGER" property="empId"/>
        <result column="emp_name" jdbcType="VARCHAR" property="empName"/>
        <result column="temperature" jdbcType="DOUBLE" property="temperature"/>
        <result column="photo_url" jdbcType="VARCHAR" property="photoUrl"/>
        <result column="show_time" jdbcType="TIMESTAMP" property="showTime"/>
        <result column="rec_mode" jdbcType="INTEGER" property="recMode"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,
        dept_id,
        face_gate_id,
        device_key,
        person_guid,
        emp_id,
        emp_name,
        temperature,
        photo_url,
        show_time,
        rec_mode,
        `type`,
        del_flag,
        update_time,
        create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from app_face_gate_record
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from app_face_gate_record
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppFaceGateRecord"
            useGeneratedKeys="true">
        insert into app_face_gate_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="faceGateId != null">
                face_gate_id,
            </if>
            <if test="deviceKey != null">
                device_key,
            </if>
            <if test="personGuid != null">
                person_guid,
            </if>
            <if test="empId != null">
                emp_id,
            </if>
            <if test="empName != null">
                emp_name,
            </if>
            <if test="temperature != null">
                temperature,
            </if>
            <if test="photoUrl != null">
                photo_url,
            </if>
            <if test="showTime != null">
                show_time,
            </if>
            <if test="recMode != null">
                rec_mode,
            </if>
            <if test="type != null">
                `type`,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null ">
                #{deptId},
            </if>
            <if test="faceGateId != null">
                #{faceGateId,jdbcType=INTEGER},
            </if>
            <if test="deviceKey != null">
                #{deviceKey,jdbcType=VARCHAR},
            </if>
            <if test="personGuid != null">
                #{personGuid,jdbcType=VARCHAR},
            </if>
            <if test="empId != null">
                #{empId,jdbcType=INTEGER},
            </if>
            <if test="empName != null">
                #{empName,jdbcType=VARCHAR},
            </if>
            <if test="temperature != null">
                #{temperature,jdbcType=DOUBLE},
            </if>
            <if test="photoUrl != null">
                #{photoUrl,jdbcType=VARCHAR},
            </if>
            <if test="showTime != null">
                #{showTime,jdbcType=TIMESTAMP},
            </if>
            <if test="recMode != null">
                #{recMode,jdbcType=INTEGER},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppFaceGateRecord">
        update app_face_gate_record
        <set>
            <if test="deptId != null">
                dept_id = #{deptId},
            </if>
            <if test="faceGateId != null">
                face_gate_id = #{faceGateId,jdbcType=INTEGER},
            </if>
            <if test="deviceKey != null">
                device_key = #{deviceKey,jdbcType=VARCHAR},
            </if>
            <if test="personGuid != null">
                person_guid = #{personGuid,jdbcType=VARCHAR},
            </if>
            <if test="empId != null">
                emp_id = #{empId,jdbcType=INTEGER},
            </if>
            <if test="empName != null">
                emp_name = #{empName,jdbcType=VARCHAR},
            </if>
            <if test="temperature != null">
                temperature = #{temperature,jdbcType=DOUBLE},
            </if>
            <if test="photoUrl != null">
                photo_url = #{photoUrl,jdbcType=VARCHAR},
            </if>
            <if test="showTime != null">
                show_time = #{showTime,jdbcType=TIMESTAMP},
            </if>
            <if test="recMode != null">
                rec_mode = #{recMode,jdbcType=INTEGER},
            </if>
            <if test="type != null">
                `type` = #{type,jdbcType=INTEGER},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectByDeptIdAndKeyword" resultType="com.whfc.emp.dto.AppFaceGateRecordDTO">
        SELECT afgr.emp_id,
               IFNULL(afgr.emp_name,'陌生人' ) AS empName,
               IFNULL(ae.group_name ,'无') AS groupName,
               afg.name ,
               afg.device_key,
               afg.direction,
               afgr.rec_mode,
               afgr.photo_url,
               afgr.show_time,
               afgr.temperature,
               ae.phone,
               ae.gender,
               ae.birthday,
               ae.id_card_no
        FROM app_face_gate_record afgr
        LEFT JOIN app_face_gate afg ON afg.id=afgr.face_gate_id
        LEFT JOIN app_emp ae ON ae.id=afgr.emp_id
        WHERE afgr.dept_id=#{deptId}
          and afgr.show_time between #{startTime} and #{endTime}
        <if test="keyword != null and keyword!=''">
            AND ae.emp_name LIKE CONCAT('%',#{keyword},'%')
        </if>
        AND afgr.`type` != 3
        ORDER BY afgr.id DESC
    </select>

    <select id="selectByDeptIdAndFaceGate" resultType="com.whfc.emp.dto.AppFaceGateRecordDTO">
        SELECT  ae.group_name,
                ae.work_type_name,
                afgr.emp_name,
                afg.direction,
                afgr.show_time
        FROM app_face_gate_record afgr
        INNER JOIN app_face_gate afg ON afg.id=afgr.face_gate_id
        INNER JOIN app_emp ae ON ae.id=afgr.emp_id
        WHERE afgr.dept_id=#{deptId}
          and afgr.show_time between #{startTime} and #{endTime}
          AND afgr.`type` != 3
          and (
        <foreach collection="faceGateIds" item="faceGateId" separator="or">
            afgr.face_gate_id=#{faceGateId}
        </foreach>
        )
        ORDER BY afgr.id DESC
    </select>

    <select id="selectByDeptIdAndTime" resultType="com.whfc.emp.dto.AppFaceGateRecordDTO">
        SELECT afgr.photo_url,
               afgr.show_time,
               afg.direction,
               afg.device_key,
               afgr.emp_id
        FROM  app_face_gate_record afgr
        inner JOIN app_face_gate afg ON afg.id=afgr.face_gate_id
        where afgr.dept_id = #{deptId}
        and afgr.show_time between #{startTime} and #{endTime}
        AND afgr.`type` != 3
        order by afgr.id
    </select>

    <select id="selectStrangerList" resultType="com.whfc.emp.dto.AppFaceGateRecordDTO">
        SELECT  distinct ifnull(afgr.emp_name,'陌生人' )AS empName,
                afg.name ,
                afgr.rec_mode,
                afgr.photo_url,
                afgr.show_time,
                afg.direction,
                afgr.temperature
        FROM  app_face_gate_record afgr
        inner JOIN app_face_gate afg ON afg.id=afgr.face_gate_id
        where afgr.dept_id=#{deptId}
        <if test="startTime != null">
            and afgr.show_time>=#{startTime}
        </if>
        <if test="endTime!=null">
            and #{endTime}>=afgr.show_time
        </if>
        and afgr.`type` = 3
        order by afgr.id desc
    </select>

    <select id="selectEmpRecCount" resultType="com.whfc.emp.dto.stat.FacegateRecEmpStat">
        select emp_id,
               show_time,
               count(*) as cnt
          from app_face_gate_record
          where emp_id >0
          <![CDATA[
            and show_time >=#{startTime}
            and show_time <=#{endTime}
          ]]>
          group by emp_id,show_time
          having cnt > 1
    </select>

    <select id="selectRecRecordByEmpIdAndTime" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
          from app_face_gate_record
         where emp_id = #{empId}
           and show_time = #{showTime}
    </select>

    <delete id="batchDelete">
        delete
        from app_face_gate_record
                where id in
        <foreach collection="idList" item="id" separator="," open="(" close=")">
            #{id,jdbcType=INTEGER}
        </foreach>
    </delete>

    <select id="selectRecordAnalysis" resultType="com.whfc.emp.dto.AppEmpBoardRecordAnalysisDTO">
        SELECT
            SUM(IF(afg.direction = 0,1,0)) as outNum,
            SUM(IF(afg.direction = 1,1,0)) as inNum
        FROM
            app_face_gate_record afgr
            INNER JOIN app_face_gate afg ON afg.id = afgr.face_gate_id
        WHERE
            afgr.dept_id = #{deptId}
        <if test="startDate != null and endDate != null">
            AND afgr.show_time BETWEEN #{startDate} AND #{endDate}
        </if>
    </select>

    <select id="selectLatestRecord" resultType="com.whfc.emp.dto.AppFaceGateRecordDTO">
        SELECT
           ae.emp_name,
           ae.work_type_name,
           afgr.photo_url,
           afgr.show_time,
           afg.direction
        FROM
           app_face_gate_record afgr
           INNER JOIN app_face_gate afg ON afg.id = afgr.face_gate_id
           INNER JOIN app_emp ae on ae.id = afgr.emp_id
        WHERE
            afgr.dept_id = #{deptId}
        and afg.direction = #{direction}
        <if test="startDate != null and endDate != null">
            AND afgr.show_time BETWEEN #{startDate} AND #{endDate}
        </if>
        order by  afgr.show_time desc
        limit 1
    </select>

    <select id="selectRecordList" resultType="com.whfc.emp.dto.AppFaceGateRecordDTO">
        SELECT
        afgr.emp_id,
        ae.emp_name,
        ae.work_type_name,
        afgr.photo_url,
        afgr.show_time,
        afg.direction
        FROM app_face_gate_record afgr
        INNER JOIN app_face_gate afg ON afg.id = afgr.face_gate_id
        INNER JOIN app_emp ae on ae.id = afgr.emp_id
        WHERE afgr.dept_id = #{deptId}
        and afg.direction = #{direction}
        <if test="startDate != null and endDate != null">
            AND afgr.show_time BETWEEN #{startDate} AND #{endDate}
        </if>
        order by  afgr.show_time desc
    </select>

    <select id="selectAreaAttendData" resultType="com.whfc.emp.dto.AppEmpAttendGroupDTO">
        select fg.area_id,fg.area_name,count(DISTINCT emp_id) as num
        from app_face_gate_record fgr
        inner join app_face_gate fg on fgr.face_gate_id = fg.id
        where fgr.dept_id = #{deptId}
        and fg.dept_id = #{deptId}
        and fg.direction = 1
        <![CDATA[
          and show_time>= #{startTime}
          and show_time<= #{endTime}
        ]]>
        GROUP BY fg.area_id,fg.area_name
    </select>
</mapper>