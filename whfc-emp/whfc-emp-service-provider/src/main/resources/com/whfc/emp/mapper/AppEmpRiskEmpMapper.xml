<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppEmpRiskEmpMapper">
  <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppEmpRiskEmp">
    <!--@mbg.generated-->
    <!--@Table app_emp_risk_emp-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="risk_id" jdbcType="INTEGER" property="riskId" />
    <result column="emp_id" jdbcType="INTEGER" property="empId" />
    <result column="sign_time" jdbcType="TIMESTAMP" property="signTime" />
    <result column="sign_img_url" jdbcType="VARCHAR" property="signImgUrl" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, risk_id, emp_id, sign_time, sign_img_url, del_flag, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from app_emp_risk_emp
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from app_emp_risk_emp
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppEmpRiskEmp" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_emp_risk_emp (risk_id, emp_id, sign_time, 
      sign_img_url, del_flag, update_time, 
      create_time)
    values (#{riskId,jdbcType=INTEGER}, #{empId,jdbcType=INTEGER}, #{signTime,jdbcType=TIMESTAMP}, 
      #{signImgUrl,jdbcType=VARCHAR}, #{delFlag,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppEmpRiskEmp" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_emp_risk_emp
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="riskId != null">
        risk_id,
      </if>
      <if test="empId != null">
        emp_id,
      </if>
      <if test="signTime != null">
        sign_time,
      </if>
      <if test="signImgUrl != null">
        sign_img_url,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="riskId != null">
        #{riskId,jdbcType=INTEGER},
      </if>
      <if test="empId != null">
        #{empId,jdbcType=INTEGER},
      </if>
      <if test="signTime != null">
        #{signTime,jdbcType=TIMESTAMP},
      </if>
      <if test="signImgUrl != null">
        #{signImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppEmpRiskEmp">
    <!--@mbg.generated-->
    update app_emp_risk_emp
    <set>
      <if test="riskId != null">
        risk_id = #{riskId,jdbcType=INTEGER},
      </if>
      <if test="empId != null">
        emp_id = #{empId,jdbcType=INTEGER},
      </if>
      <if test="signTime != null">
        sign_time = #{signTime,jdbcType=TIMESTAMP},
      </if>
      <if test="signImgUrl != null">
        sign_img_url = #{signImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.whfc.emp.entity.AppEmpRiskEmp">
    <!--@mbg.generated-->
    update app_emp_risk_emp
    set risk_id = #{riskId,jdbcType=INTEGER},
      emp_id = #{empId,jdbcType=INTEGER},
      sign_time = #{signTime,jdbcType=TIMESTAMP},
      sign_img_url = #{signImgUrl,jdbcType=VARCHAR},
      del_flag = #{delFlag,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectEmpList" resultMap="BaseResultMap">
    SELECT
      <include refid="Base_Column_List" />
    FROM app_emp_risk_emp
    WHERE del_flag = 0
    AND risk_id = #{riskId}
    AND emp_id = #{empId}
  </select>
</mapper>