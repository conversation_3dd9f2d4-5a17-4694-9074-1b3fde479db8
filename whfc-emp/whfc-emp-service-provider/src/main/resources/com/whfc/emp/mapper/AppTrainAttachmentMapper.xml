<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppTrainAttachmentMapper">
    <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppTrainAttachment">
        <!--@mbg.generated-->
        <!--@Table app_train_attachment-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="train_id" jdbcType="INTEGER" property="trainId"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="url" jdbcType="VARCHAR" property="url"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, train_id, `type`, url, del_flag, update_time, create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from app_train_attachment
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete from app_train_attachment
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.whfc.emp.entity.AppTrainAttachment">
        <!--@mbg.generated-->
        insert into app_train_attachment (id, train_id, `type`,
        url, del_flag, update_time,
        create_time)
        values (#{id,jdbcType=INTEGER}, #{trainId,jdbcType=INTEGER}, #{type,jdbcType=INTEGER},
        #{url,jdbcType=VARCHAR}, #{delFlag,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP},
        #{createTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="com.whfc.emp.entity.AppTrainAttachment">
        <!--@mbg.generated-->
        insert into app_train_attachment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="trainId != null">
                train_id,
            </if>
            <if test="type != null">
                `type`,
            </if>
            <if test="url != null">
                url,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="trainId != null">
                #{trainId,jdbcType=INTEGER},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="url != null">
                #{url,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppTrainAttachment">
        <!--@mbg.generated-->
        update app_train_attachment
        <set>
            <if test="trainId != null">
                train_id = #{trainId,jdbcType=INTEGER},
            </if>
            <if test="type != null">
                `type` = #{type,jdbcType=INTEGER},
            </if>
            <if test="url != null">
                url = #{url,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.whfc.emp.entity.AppTrainAttachment">
        <!--@mbg.generated-->
        update app_train_attachment
        set train_id = #{trainId,jdbcType=INTEGER},
        `type` = #{type,jdbcType=INTEGER},
        url = #{url,jdbcType=VARCHAR},
        del_flag = #{delFlag,jdbcType=INTEGER},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        create_time = #{createTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <insert id="insertAll">
        insert into app_train_attachment ( train_id, `type`,url)
        values
        <foreach collection="trainPhotos" item="trainPhoto" separator=",">
            (#{trainId},#{type},#{trainPhoto})
        </foreach>
    </insert>
    <select id="selectByTrainId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from app_train_attachment
        where
        del_flag = 0
        and train_id = #{trainId}
    </select>
    <update id="delByTrainId">
         update app_train_attachment
         set del_flag=1
         where
         del_flag=0
         and train_id = #{trainId}
    </update>
</mapper>