<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppUniformReceiveMapper">
    <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppUniformReceive">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="dept_id" jdbcType="INTEGER" property="deptId"/>
        <result column="guid" jdbcType="VARCHAR" property="guid"/>
        <result column="emp_id" jdbcType="INTEGER" property="empId"/>
        <result column="emp_name" jdbcType="VARCHAR" property="empName"/>
        <result column="company" jdbcType="VARCHAR" property="company"/>
        <result column="date" jdbcType="DATE" property="date"/>
        <result column="ext" jdbcType="LONGVARCHAR" property="ext"/>
        <result column="create_user_id" jdbcType="INTEGER" property="createUserId"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,
        dept_id,
        guid,
        emp_id,
        emp_name,
        company,
        `date`,
        ext,
        create_user_id,
        create_user_name,
        del_flag,
        update_time,
        create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from app_uniform_receive
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete
        from app_uniform_receive
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insertSelective" parameterType="com.whfc.emp.entity.AppUniformReceive" useGeneratedKeys="true" keyProperty="id">
        insert into app_uniform_receive
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="guid != null">
                guid,
            </if>
            <if test="empId != null">
                emp_id,
            </if>
            <if test="empName != null">
                emp_name,
            </if>
            <if test="company != null">
                company,
            </if>
            <if test="date != null">
                date,
            </if>
            <if test="createUserId != null">
                create_user_id,
            </if>
            <if test="createUserName != null">
                create_user_name,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="ext != null">
                ext,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="deptId != null">
                #{deptId,jdbcType=INTEGER},
            </if>
            <if test="guid != null">
                #{guid,jdbcType=VARCHAR},
            </if>
            <if test="empId != null">
                #{empId,jdbcType=INTEGER},
            </if>
            <if test="empName != null">
                #{empName,jdbcType=VARCHAR},
            </if>
            <if test="company != null">
                #{company,jdbcType=VARCHAR},
            </if>
            <if test="date != null">
                #{date,jdbcType=DATE},
            </if>
            <if test="createUserId != null">
                #{createUserId,jdbcType=INTEGER},
            </if>
            <if test="createUserName != null">
                #{createUserName,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="ext != null">
                #{ext,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppUniformReceive">
        update app_uniform_receive
        <set>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=INTEGER},
            </if>
            <if test="guid != null">
                guid = #{guid,jdbcType=VARCHAR},
            </if>
            <if test="empId != null">
                emp_id = #{empId,jdbcType=INTEGER},
            </if>
            <if test="empName != null">
                emp_name = #{empName,jdbcType=VARCHAR},
            </if>
            <if test="company != null">
                company = #{company,jdbcType=VARCHAR},
            </if>
            <if test="date != null">
                date = #{date,jdbcType=DATE},
            </if>
            <if test="createUserId != null">
                create_user_id = #{createUserId,jdbcType=INTEGER},
            </if>
            <if test="createUserName != null">
                create_user_name = #{createUserName,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="ext != null">
                ext = #{ext,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectList" resultType="com.whfc.emp.dto.uniform.AppUniformReceiveDTO">
        select distinct aur.id as receive_id,
                        aur.guid,
                        aur.emp_id,
                        aur.emp_name,
                        aur.date,
                        ae.corp_name,
                        aur.company,
                        aur.ext,
                        aur.create_user_id,
                        aur.create_user_name,
                        aur.create_time
        from app_uniform_receive aur
        inner join app_uniform_receive_detail aurd on aur.id = aurd.receive_id
        inner join app_emp ae on aur.emp_id = ae.id
        where aur.dept_id = #{deptId}
          and aur.del_flag = 0
        <if test="corpName != null and corpName != ''">
            and ae.corp_name like concat('%',#{corpName},'%')
        </if>
        <if test="empName != null and empName != ''">
            and aur.emp_name like concat('%',#{empName},'%')
        </if>
        <if test="itemName != null and itemName != ''">
            and aurd.item_name like concat('%',#{itemName},'%')
        </if>
        <if test="startTime != null">
            and aur.date >= #{startTime}
        </if>
        <if test="endTime != null">
            and #{endTime} >= aur.date
        </if>
        order by aur.id desc
    </select>

    <select id="selectByReceiveIds" resultType="com.whfc.emp.dto.uniform.AppUniformReceiveDTO">
        select distinct aur.id as receive_id,
                aur.guid,
                aur.emp_id,
                aur.emp_name,
                aur.date,
                ae.corp_name,
                aur.company,
                aur.ext,
                aur.create_user_id,
                aur.create_user_name,
                aur.create_time
        from app_uniform_receive aur
        inner join app_emp ae on aur.emp_id = ae.id
        where aur.dept_id = #{deptId}
        <if test="receiveIds!=null and receiveIds.size()>0">
            and aur.id in (
            <foreach collection="receiveIds" item="receiveId" separator=",">
                #{receiveId}
            </foreach>
            )
        </if>
        and aur.del_flag = 0
        order by aur.id desc
    </select>

    <select id="selectDtoByGuid" resultType="com.whfc.emp.dto.uniform.AppUniformReceiveDTO">
        select aur.id as receive_id,
               aur.guid,
               aur.emp_id,
               aur.emp_name,
               aur.company,
               aur.date,
               aur.ext,
               aur.create_user_id,
               aur.create_user_name,
               aur.create_time
          from app_uniform_receive aur
        where aur.guid = #{guid}
    </select>

    <select id="selectByGuid" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List">
        </include>
        from app_uniform_receive
        where guid = #{guid}
    </select>

    <update id="logicDeleteById">
        update app_uniform_receive
        set del_flag = 1
        where id = #{id}
    </update>

</mapper>