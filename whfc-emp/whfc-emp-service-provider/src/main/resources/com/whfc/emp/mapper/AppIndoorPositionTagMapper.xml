<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppIndoorPositionTagMapper">
  <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppIndoorPositionTag">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="guid" jdbcType="VARCHAR" property="guid" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="bind_flag" jdbcType="INTEGER" property="bindFlag" />
    <result column="bind_type" jdbcType="INTEGER" property="bindType" />
    <result column="group_id" jdbcType="INTEGER" property="groupId" />
    <result column="group_name" jdbcType="VARCHAR" property="groupName" />
    <result column="emp_id" jdbcType="INTEGER" property="empId" />
    <result column="emp_name" jdbcType="VARCHAR" property="empName" />
    <result column="device_code" jdbcType="VARCHAR" property="deviceCode" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="time" jdbcType="TIMESTAMP" property="time" />
    <result column="map_id" jdbcType="INTEGER" property="mapId" />
    <result column="station_id" jdbcType="INTEGER" property="stationId" />
    <result column="x" jdbcType="DOUBLE" property="x" />
    <result column="y" jdbcType="DOUBLE" property="y" />
    <result column="z" jdbcType="DOUBLE" property="z" />
    <result column="lng" jdbcType="DOUBLE" property="lng" />
    <result column="lat" jdbcType="DOUBLE" property="lat" />
    <result column="distance" jdbcType="INTEGER" property="distance" />
    <result column="distance1" jdbcType="INTEGER" property="distance1" />
    <result column="battery_power" jdbcType="INTEGER" property="batteryPower" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="sos" jdbcType="INTEGER" property="sos" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id,
    dept_id,
    guid,
    code,
    `name`,
    bind_flag,
    bind_type,
    group_id,
    group_name,
    emp_id,
    emp_name,
    device_code,
    status,
    `time`,
    map_id,
    station_id,
    x,
    y,
    z,
    lng,
    lat,
    distance,
    distance1,
    battery_power,
    `state`,
    sos,
    del_flag,
    update_time,
    create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from app_indoor_position_tag
    where id = #{id,jdbcType=INTEGER}
  </select>

  <insert id="insertSelective" parameterType="com.whfc.emp.entity.AppIndoorPositionTag" keyProperty="id" useGeneratedKeys="true">
    insert into app_indoor_position_tag
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="guid != null">
        guid,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="bindFlag != null">
        bind_flag,
      </if>
      <if test="bindType != null">
        bind_type,
      </if>
      <if test="groupId != null">
        group_id,
      </if>
      <if test="groupName != null">
        group_name,
      </if>
      <if test="empId != null">
        emp_id,
      </if>
      <if test="empName != null">
        emp_name,
      </if>
      <if test="deviceCode != null">
        device_code,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="time != null">
        time,
      </if>
      <if test="mapId != null">
        map_id,
      </if>
      <if test="stationId != null">
        station_id,
      </if>
      <if test="x != null">
        x,
      </if>
      <if test="y != null">
        y,
      </if>
      <if test="z != null">
        z,
      </if>
      <if test="lng != null">
        lng,
      </if>
      <if test="lat != null">
        lat,
      </if>
      <if test="distance != null">
        distance,
      </if>
      <if test="distance1 != null">
        distance1,
      </if>
      <if test="batteryPower != null">
        battery_power,
      </if>
      <if test="state != null">
        `state`,
      </if>
      <if test="sos != null">
        sos,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="guid != null">
        #{guid,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="bindFlag != null">
        #{bindFlag,jdbcType=INTEGER},
      </if>
      <if test="bindType != null">
        #{bindType,jdbcType=INTEGER},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=INTEGER},
      </if>
      <if test="groupName != null">
        #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="empId != null">
        #{empId,jdbcType=INTEGER},
      </if>
      <if test="empName != null">
        #{empName,jdbcType=VARCHAR},
      </if>
      <if test="deviceCode != null">
        #{deviceCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="time != null">
        #{time,jdbcType=TIMESTAMP},
      </if>
      <if test="mapId != null">
        #{mapId,jdbcType=INTEGER},
      </if>
      <if test="stationId != null">
        #{stationId,jdbcType=INTEGER},
      </if>
      <if test="x != null">
        #{x,jdbcType=DOUBLE},
      </if>
      <if test="y != null">
        #{y,jdbcType=DOUBLE},
      </if>
      <if test="z != null">
        #{z,jdbcType=DOUBLE},
      </if>
      <if test="lng != null">
        #{lng,jdbcType=DOUBLE},
      </if>
      <if test="lat != null">
        #{lat,jdbcType=DOUBLE},
      </if>
      <if test="distance != null">
        #{distance,jdbcType=INTEGER},
      </if>
      <if test="distance1 != null">
        #{distance1,jdbcType=INTEGER},
      </if>
      <if test="batteryPower != null">
        #{batteryPower,jdbcType=INTEGER},
      </if>
      <if test="state != null">
        #{state,jdbcType=INTEGER},
      </if>
      <if test="sos != null">
        #{sos,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppIndoorPositionTag">
    update app_indoor_position_tag
    <set>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="guid != null">
        guid = #{guid,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="bindFlag != null">
        bind_flag = #{bindFlag,jdbcType=INTEGER},
      </if>
      <if test="bindType != null">
        bind_type = #{bindType,jdbcType=INTEGER},
      </if>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=INTEGER},
      </if>
      <if test="groupName != null">
        group_name = #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="empId != null">
        emp_id = #{empId,jdbcType=INTEGER},
      </if>
      <if test="empName != null">
        emp_name = #{empName,jdbcType=VARCHAR},
      </if>
      <if test="deviceCode != null">
        device_code = #{deviceCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="time != null">
        time = #{time,jdbcType=TIMESTAMP},
      </if>
      <if test="mapId != null">
        map_id = #{mapId,jdbcType=INTEGER},
      </if>
      <if test="stationId != null">
        station_id = #{stationId,jdbcType=INTEGER},
      </if>
      <if test="x != null">
        x = #{x,jdbcType=DOUBLE},
      </if>
      <if test="y != null">
        y = #{y,jdbcType=DOUBLE},
      </if>
      <if test="z != null">
        z = #{z,jdbcType=DOUBLE},
      </if>
      <if test="lng != null">
        lng = #{lng,jdbcType=DOUBLE},
      </if>
      <if test="lat != null">
        lat = #{lat,jdbcType=DOUBLE},
      </if>
      <if test="distance != null">
        distance = #{distance,jdbcType=INTEGER},
      </if>
      <if test="distance1 != null">
        distance1 = #{distance1,jdbcType=INTEGER},
      </if>
      <if test="batteryPower != null">
        battery_power = #{batteryPower,jdbcType=INTEGER},
      </if>
      <if test="state != null">
        `state` = #{state,jdbcType=INTEGER},
      </if>
      <if test="sos != null">
        sos = #{sos,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByDeptId" resultType="com.whfc.emp.dto.AppIndoorPositionTagDTO">
    select id as tagId,
           guid,
           code,
           `name`,
           bind_flag,
           bind_type,
           group_id,
           group_name,
           emp_id,
           emp_name,
           device_code,
           status,
           `time`,
           map_id,
           station_id,
           x,
           y,
           z,
           lng,
           lat,
           distance,
           distance1,
           battery_power,
           state,
           sos
      from app_indoor_position_tag
     where dept_id = #{deptId}
       and del_flag = 0
     <if test="bindFlag!=null">
       and bind_flag = #{bindFlag}
     </if>
     <if test="bindType != null">
        and bind_type = #{bindType}
     </if>
     <if test="keyword!=null and keyword.length()>0">
      and (
            guid like concat('%',#{keyword},'%')
         or emp_name like concat('%',#{keyword},'%')
       )
     </if>
  </select>

  <select id="selectPositionList" resultType="com.whfc.emp.dto.AppIndoorPositionTagDTO">
    select id as tagId,
            guid,
            code,
            `name`,
            group_id,
            group_name,
            emp_id,
            emp_name,
            status,
            `time`,
            map_id,
           station_id,
            x,
            y,
            z,
            lng,
            lat,
            distance,
            distance1,
            battery_power,
            state,
            sos
      from app_indoor_position_tag
      where dept_id = #{deptId}
      and del_flag = 0
      <if test="time!=null">
      and `time`>=#{time}
      </if>
      <if test="keyword!=null and keyword.length()>0">
        and (
        guid like concat('%',#{keyword},'%')
        or emp_name like concat('%',#{keyword},'%')
        )
      </if>
      <if test="bindFlag!=null">
        and bind_flag = #{bindFlag}
      </if>
      <if test="bindType != null">
        and bind_type = #{bindType}
      </if>
      order by `time` desc

  </select>

  <select id="selectPositionStat" resultType="com.whfc.emp.dto.AppIndoorPositionStatDTO">
    select t.station_id,
           m.name as station_name,
           count(t.id) as positionNum
    from app_indoor_position_tag t
    inner join app_indoor_position_station m on t.station_id = m.id
    where t.dept_id = #{deptId}
      and t.del_flag = 0
      and t.bind_flag  =1
    <if test="time!=null">
      and t.`time`>=#{time}
    </if>
     group by t.station_id
  </select>

  <select id="selectMapStat" resultType="com.whfc.emp.dto.AppIndoorPositionStatDTO">
    select m.id as map_id,
          m.name as map_name,
          m.area_id,
          m.area_name,
          count(t.id) as positionNum
    from app_indoor_position_tag t
    inner join app_indoor_position_map m on t.map_id = m.id
    inner join app_emp ae on t.emp_id = ae.id
    where t.dept_id = #{deptId}
    and t.map_id = #{mapId}
    and t.del_flag = 0
    and t.bind_flag = 1
    <if test="time!=null">
      and t.`time`>=#{time}
    </if>
  </select>

  <select id="selectMapStatByWorkRole" resultType="com.whfc.emp.dto.AppEmpWorkRoleNumDTO">
    select work_role_id,work_role_name,COUNT(ae.id) num
    from app_indoor_position_tag t
    inner join app_indoor_position_map m on t.map_id = m.id
    inner join app_emp ae on t.emp_id = ae.id
    where t.dept_id = #{deptId}
    and t.map_id = #{mapId}
    and t.del_flag = 0
    and t.bind_flag = 1
    <if test="time!=null">
      and t.`time`>=#{time}
    </if>
    GROUP BY ae.work_role_id
  </select>

  <select id="selectMaxDistance" resultType="com.whfc.emp.dto.AppIndoorPositionStatDTO">
    select max(distance) as maxDistance,
           max(distance1) as maxDistance1
    from app_indoor_position_tag t
    where t.dept_id = #{deptId}
    and t.del_flag = 0
    and t.bind_flag  =1
  </select>

  <select id="selectByGuid" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List">
    </include>
      from app_indoor_position_tag
     where guid = #{guid}
       and del_flag = 0
  </select>

  <select id="selectByDeptIdAndGuid" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List">
    </include>
    from app_indoor_position_tag
    where dept_id = #{deptId}
      and guid = #{guid}
      and del_flag = 0
  </select>

  <select id="selectByDeptIdAndEmpId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List">
    </include>
      from app_indoor_position_tag
     where dept_id = #{deptId}
       and emp_id = #{empId}
       and del_flag = 0
  </select>

  <select id="selectByDeptIdAndDeviceCode" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List">
    </include>
      from app_indoor_position_tag
     where dept_id = #{deptId}
       and device_code = #{deviceCode}
       and del_flag = 0
  </select>

  <update id="unbindTag">
    update app_indoor_position_tag
    set emp_id = null,
        emp_name = null,
        device_code = null,
        bind_flag = 0,
        bind_type = null
    where id = #{tagId}
  </update>

  <update id="logicDeleteById">
    update app_indoor_position_tag
    set del_flag = 1,
        bind_flag = 0,
        bind_type = null,
        emp_id = null,
        emp_name = null
    where id = #{tagId}
      and del_flag = 0
  </update>

</mapper>