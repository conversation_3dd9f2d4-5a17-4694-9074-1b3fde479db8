<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppTrainFailDataMapper">
    <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppTrainFailData">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="data" jdbcType="LONGVARCHAR" property="data"/>
        <result column="error_msg" jdbcType="LONGNVARCHAR" property="errorMsg"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,
        `type`,
        `data`,
        error_msg
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from app_train_fail_data
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete
        from app_train_fail_data
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.whfc.emp.entity.AppTrainFailData">
        insert into app_train_fail_data (id, `type`, `data`)
        values (#{id,jdbcType=INTEGER}, #{type,jdbcType=INTEGER}, #{data,jdbcType=LONGVARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.whfc.emp.entity.AppTrainFailData">
        insert into app_train_fail_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="type != null">
                `type`,
            </if>
            <if test="data != null">
                `data`,
            </if>
            <if test="errorMsg != null">
                error_msg,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="data != null">
                #{data,jdbcType=LONGVARCHAR},
            </if>
            <if test="errorMsg != null">
                #{errorMsg},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppTrainFailData">
        update app_train_fail_data
        <set>
            <if test="type != null">
                `type` = #{type,jdbcType=INTEGER},
            </if>
            <if test="data != null">
                `data` = #{data,jdbcType=LONGVARCHAR},
            </if>
            <if test="errorMsg != null">
                error_msg = #{errorMsg},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.whfc.emp.entity.AppTrainFailData">
        update app_train_fail_data
        set `type`    = #{type,jdbcType=INTEGER},
            `data`    = #{data,jdbcType=LONGVARCHAR},
            error_msg = #{errorMsg}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectByType" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM app_train_fail_data
        WHERE type = #{type}
          AND del_flag = 0
    </select>

    <delete id="delByType">
        DELETE FROM app_train_fail_data
        WHERE type = #{type}
    </delete>
</mapper>