<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppFenceTimeMapper">
  <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppFenceTime">
    <!--@mbg.generated-->
    <!--@Table app_fence_time-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="fence_id" jdbcType="INTEGER" property="fenceId" />
    <result column="start_time" jdbcType="TIME" property="startTime" />
    <result column="end_time" jdbcType="TIME" property="endTime" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, fence_id, start_time, end_time, del_flag, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from app_fence_time
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from app_fence_time
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppFenceTime" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_fence_time (fence_id, start_time, end_time, 
      del_flag, update_time, create_time
      )
    values (#{fenceId,jdbcType=INTEGER}, #{startTime,jdbcType=TIME}, #{endTime,jdbcType=TIME}, 
      #{delFlag,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppFenceTime" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_fence_time
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="fenceId != null">
        fence_id,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="fenceId != null">
        #{fenceId,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIME},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIME},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppFenceTime">
    <!--@mbg.generated-->
    update app_fence_time
    <set>
      <if test="fenceId != null">
        fence_id = #{fenceId,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIME},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIME},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.whfc.emp.entity.AppFenceTime">
    <!--@mbg.generated-->
    update app_fence_time
    set fence_id = #{fenceId,jdbcType=INTEGER},
      start_time = #{startTime,jdbcType=TIME},
      end_time = #{endTime,jdbcType=TIME},
      del_flag = #{delFlag,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByFenceId" resultType="com.whfc.emp.dto.AppFenceTimeDTO">
    SELECT
        fence_id,
        start_time,
        end_time
      FROM app_fence_time
    WHERE del_flag = 0
    AND fence_id = #{fenceId}
  </select>

  <select id="logicDelByFenceId" >
    UPDATE app_fence_time SET del_flag = 1 WHERE fence_id = #{fenceId}
  </select>

  <insert id="batchInsert">
    INSERT INTO app_fence_time(fence_id, start_time, end_time)
    VALUES
    <foreach collection="timeList" item="time" separator=",">
      (#{time.fenceId},#{time.startTime},#{time.endTime})
    </foreach>
  </insert>

  <select id="selectByFenceIds" resultType="com.whfc.emp.dto.AppFenceTimeDTO">
    SELECT
        aft.fence_id,
        af.name AS fenceName,
        aft.start_time,
        aft.end_time
    FROM app_fence_time aft
    LEFT JOIN app_fence af ON af.id = aft.fence_id
    WHERE af.del_flag = 0
    AND aft.del_flag = 0
    AND aft.fence_id IN
    <foreach collection="fenceIds" item="fenceId" open="(" close=")" separator=",">
      #{fenceId}
    </foreach>
  </select>
</mapper>