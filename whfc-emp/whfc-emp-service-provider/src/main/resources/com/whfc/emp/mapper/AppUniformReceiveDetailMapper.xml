<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppUniformReceiveDetailMapper">
  <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppUniformReceiveDetail">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="receive_id" jdbcType="INTEGER" property="receiveId" />
    <result column="item_id" jdbcType="INTEGER" property="itemId" />
    <result column="item_name" jdbcType="VARCHAR" property="itemName" />
    <result column="item_amount" jdbcType="INTEGER" property="itemAmount" />
    <result column="del_flag" jdbcType="VARCHAR" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id,
    dept_id,
    receive_id,
    item_id,
    item_name,
    item_amount,
    del_flag,
    update_time,
    create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from app_uniform_receive_detail
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from app_uniform_receive_detail
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.whfc.emp.entity.AppUniformReceiveDetail">
    insert into app_uniform_receive_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="receiveId != null">
        receive_id,
      </if>
      <if test="itemId != null">
        item_id,
      </if>
      <if test="itemName != null">
        item_name,
      </if>
      <if test="itemAmount != null">
        item_amount,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="receiveId != null">
        #{receiveId,jdbcType=INTEGER},
      </if>
      <if test="itemId != null">
        #{itemId,jdbcType=INTEGER},
      </if>
      <if test="itemName != null">
        #{itemName,jdbcType=VARCHAR},
      </if>
      <if test="itemAmount != null">
        #{itemAmount,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppUniformReceiveDetail">
    update app_uniform_receive_detail
    <set>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="receiveId != null">
        receive_id = #{receiveId,jdbcType=INTEGER},
      </if>
      <if test="itemId != null">
        item_id = #{itemId,jdbcType=INTEGER},
      </if>
      <if test="itemName != null">
        item_name = #{itemName,jdbcType=VARCHAR},
      </if>
      <if test="itemAmount != null">
        item_amount = #{itemAmount,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByReceiveId" resultType="com.whfc.emp.dto.uniform.AppUniformReceiveDetailDTO">
    select item_id,item_name,item_amount
      from app_uniform_receive_detail
     where receive_id = #{receiveId}
      and del_flag = 0
  </select>

  <select id="selectByReceiveIds" resultType="com.whfc.emp.dto.uniform.AppUniformReceiveDetailDTO">
    select receive_id,item_id,item_name,item_amount
      from app_uniform_receive_detail
     where receive_id in
      <foreach collection="receiveIds" item="item" separator="," open="(" close=")">
        #{item}
      </foreach>
      and del_flag = 0
  </select>

  <update id="logicDeleteByReceiveId">
    update app_uniform_receive_detail
      set del_flag = 1
     where receive_id = #{receiveId}
       and del_flag = 0
  </update>

  <insert id="batchInsert">
    insert into app_uniform_receive_detail
    (
      dept_id,
      receive_id,
      item_id,
      item_name,
      item_amount
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
        #{item.deptId,jdbcType=INTEGER},
        #{item.receiveId,jdbcType=INTEGER},
        #{item.itemId,jdbcType=INTEGER},
        #{item.itemName,jdbcType=VARCHAR},
        #{item.itemAmount,jdbcType=INTEGER}
      )
    </foreach>
  </insert>

  <select id="selectItemList" resultType="java.lang.String">
    select DISTINCT item_name
      from app_uniform_receive_detail
     where dept_id = #{deptId}
      order by item_name
  </select>

</mapper>