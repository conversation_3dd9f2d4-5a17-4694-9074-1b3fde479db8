<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppEmpHealthReportImgMapper">
  <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppEmpHealthReportImg">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="health_report_id" jdbcType="INTEGER" property="healthReportId" />
    <result column="img_url" jdbcType="VARCHAR" property="imgUrl" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, health_report_id, img_url, del_flag, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from app_emp_health_report_img
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from app_emp_health_report_img
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppEmpHealthReportImg" useGeneratedKeys="true">
    insert into app_emp_health_report_img (health_report_id, img_url, del_flag,
      update_time, create_time)
    values (#{healthReportId,jdbcType=INTEGER}, #{imgUrl,jdbcType=VARCHAR}, #{delFlag,jdbcType=INTEGER}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppEmpHealthReportImg" useGeneratedKeys="true">
    insert into app_emp_health_report_img
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="healthReportId != null">
        health_report_id,
      </if>
      <if test="imgUrl != null">
        img_url,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="healthReportId != null">
        #{healthReportId,jdbcType=INTEGER},
      </if>
      <if test="imgUrl != null">
        #{imgUrl,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppEmpHealthReportImg">
    <!--@mbg.generated-->
    update app_emp_health_report_img
    <set>
      <if test="healthReportId != null">
        health_report_id = #{healthReportId,jdbcType=INTEGER},
      </if>
      <if test="imgUrl != null">
        img_url = #{imgUrl,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.whfc.emp.entity.AppEmpHealthReportImg">
    update app_emp_health_report_img
    set health_report_id = #{healthReportId,jdbcType=INTEGER},
      img_url = #{imgUrl,jdbcType=VARCHAR},
      del_flag = #{delFlag,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectImgList" resultType="java.lang.String">
    SELECT img_url
    FROM app_emp_health_report_img
    WHERE del_flag = 0
    AND health_report_id = #{healthReportId}
  </select>

  <update id="logDel">
    UPDATE app_emp_health_report_img SET del_flag = 1 WHERE health_report_id = #{healthReportId}
  </update>
</mapper>