<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppTrainMapper">
  <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppTrain">
    <!--@mbg.generated-->
    <!--@Table app_train-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="guid" jdbcType="VARCHAR" property="guid" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="train_type" jdbcType="INTEGER" property="trainType" />
    <result column="train_type_code" jdbcType="VARCHAR" property="trainTypeCode" />
    <result column="train_type_name" jdbcType="VARCHAR" property="trainTypeName" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="duration" jdbcType="DOUBLE" property="duration" />
    <result column="organizer_id" jdbcType="INTEGER" property="organizerId" />
    <result column="organizer" jdbcType="VARCHAR" property="organizer" />
    <result column="trainer_id" jdbcType="INTEGER" property="trainerId" />
    <result column="trainer" jdbcType="VARCHAR" property="trainer" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="source" jdbcType="VARCHAR" property="source" />
    <result column="sync_flag" jdbcType="INTEGER" property="syncFlag" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
      id,
      guid,
      dept_id,
      train_type,
      train_type_code,
      train_type_name,
      `name`,
      `date`,
      duration,
      organizer_id,
      organizer,
      trainer_id,
      trainer,
      address,
      content,
      `state`,
      `source`,
      sync_flag,
      del_flag,
      update_time,
      create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from app_train
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from app_train
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppTrain" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_train
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="guid != null">
        guid,
      </if>
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="trainType != null">
        train_type,
      </if>
      <if test="trainTypeCode != null">
        train_type_code,
      </if>
      <if test="trainTypeName != null">
        train_type_name,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="date != null">
        `date`,
      </if>
      <if test="duration != null">
        duration,
      </if>
      <if test="organizerId != null">
        organizer_id,
      </if>
      <if test="organizer != null">
        organizer,
      </if>
      <if test="trainerId != null">
        trainer_id,
      </if>
      <if test="trainer != null">
        trainer,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="state != null">
        `state`,
      </if>
      <if test="source != null">
        `source`,
      </if>
      <if test="syncFlag != null">
        sync_flag,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="guid != null">
        #{guid,jdbcType=VARCHAR},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="trainType != null">
        #{trainType,jdbcType=INTEGER},
      </if>
      <if test="trainTypeCode != null">
        #{trainTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="trainTypeName != null">
        #{trainTypeName,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="date != null">
        #{date,jdbcType=DATE},
      </if>
      <if test="duration != null">
        #{duration,jdbcType=DOUBLE},
      </if>
      <if test="organizerId != null">
        #{organizerId,jdbcType=INTEGER},
      </if>
      <if test="organizer != null">
        #{organizer,jdbcType=VARCHAR},
      </if>
      <if test="trainerId != null">
        #{trainerId,jdbcType=INTEGER},
      </if>
      <if test="trainer != null">
        #{trainer,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=LONGVARCHAR},
      </if>
      <if test="state != null">
        #{state,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="syncFlag != null">
        #{syncFlag,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppTrain">
    <!--@mbg.generated-->
    update app_train
    <set>
      <if test="guid != null">
        guid = #{guid,jdbcType=VARCHAR},
      </if>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="trainType != null">
        train_type = #{trainType,jdbcType=INTEGER},
      </if>
      <if test="trainTypeCode != null">
        train_type_code = #{trainTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="trainTypeName != null">
        train_type_name = #{trainTypeName,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="date != null">
        `date` = #{date,jdbcType=DATE},
      </if>
      <if test="duration != null">
        duration = #{duration,jdbcType=DOUBLE},
      </if>
      <if test="organizerId != null">
        organizer_id = #{organizerId,jdbcType=INTEGER},
      </if>
      <if test="organizer != null">
        organizer = #{organizer,jdbcType=VARCHAR},
      </if>
      <if test="trainerId != null">
        trainer_id = #{trainerId,jdbcType=INTEGER},
      </if>
      <if test="trainer != null">
        trainer = #{trainer,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=LONGVARCHAR},
      </if>
      <if test="state != null">
        `state` = #{state,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        `source` = #{source,jdbcType=VARCHAR},
      </if>
      <if test="syncFlag != null">
        sync_flag = #{syncFlag,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

    <select id="selectByParam" resultType="com.whfc.emp.dto.AppTrainDTO">
        SELECT t.id AS trainId,
               t.guid,
               t.NAME,
               t.train_type,
               t.train_type_code,
               t.train_type_name,
               t.content,
               t.`date`,
               t.duration,
               t.address,
               t.organizer_id,
               t.organizer,
               t.trainer,
               t.trainer_id,
               t.state,
               t.dept_id,
               t.dept_id,
               t.source,
               t.sync_flag
        FROM app_train t
         where t.del_flag = 0
           and t.dept_id = #{deptId}
        <if test="trainType != null">
            and t.train_type = #{trainType}
        </if>
        <if test="state != null">
            and t.state = #{state}
        </if>
        <if test="startTime != null and endTime != null">
            and t.`date` between date(#{startTime}) and date(#{endTime})
        </if>
        <if test="keyword != null and keyword != ''">
            and t.name like concat('%', #{keyword}, '%')
        </if>
        order by t.id desc
    </select>

    <update id="deleteLogicById">
        update app_train
        set del_flag=1
        where id = #{trainId}
    </update>

    <update id="updateState">
        update app_train
        set state=1
        where id = #{trainId}
    </update>

    <update id="updateSyncFlag">
        update app_train
        set sync_flag= #{syncFlag}
        where id = #{trainId}
    </update>

    <select id="selectByEmpId" resultType="com.whfc.emp.dto.AppTrainEmpDTO">
        SELECT t.id as trainId,
               t.name,
               t.train_type,
               t.train_type_name,
               t.date,
               t.address,
               t.source,
               ate.id AS trainEmpId,
               ate.score,
               ate.pass_flag
        FROM app_train t
        INNER JOIN app_train_emp ate ON t.id = ate.train_id
        where ate.emp_id = #{empId}
          and t.state = 1
    </select>

    <select id="selectByGuid" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM app_train
        WHERE del_flag = 0
          AND guid = #{guid}
    </select>

    <select id="selectTrainStat" resultType="com.whfc.emp.dto.AppTrainStatDTO">
      select t.train_type,
             t.train_type_name,
             count(distinct t.id) as trainNum,
             count(distinct te.emp_id) as trainEmpNum
      from app_train t
      inner join app_train_emp te on t.id = te.train_id
      where t.dept_id = #{deptId}
        and t.del_flag = 0
      GROUP BY t.train_type,t.train_type_name
    </select>

    <select id="selectEnterpriseTrainStat" resultType="com.whfc.emp.dto.AppTrainStatDTO">
      select t.dept_id,
             t.train_type,
             t.train_type_name,
             count(distinct t.id) as trainNum,
             count(distinct te.emp_id) as trainEmpNum
      from app_train t
      inner join app_train_emp te on t.id = te.train_id
      where t.dept_id in
        <foreach collection="deptIds" item="deptId" separator="," open="(" close=")">
          #{deptId}
        </foreach>
        and t.del_flag = 0
      GROUP BY t.dept_id,t.train_type,t.train_type_name
    </select>
</mapper>