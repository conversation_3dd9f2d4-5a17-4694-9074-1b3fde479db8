<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppFaceGateFaceMapper">
  <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppFaceGateFace">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="face_gate_id" jdbcType="INTEGER" property="faceGateId" />
    <result column="device_key" jdbcType="VARCHAR" property="deviceKey" />
    <result column="emp_id" jdbcType="INTEGER" property="empId" />
    <result column="person_guid" jdbcType="VARCHAR" property="personGuid" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="face_guid" jdbcType="VARCHAR" property="faceGuid" />
    <result column="face_url" jdbcType="VARCHAR" property="faceUrl" />
    <result column="valid_level" jdbcType="INTEGER" property="validLevel" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, dept_id, face_gate_id, device_key, emp_id, person_guid, type, face_guid, face_url, valid_level,
    state, del_flag, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from app_face_gate_face
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from app_face_gate_face
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.whfc.emp.entity.AppFaceGateFace">
    insert into app_face_gate_face (id, dept_id, face_gate_id, device_key,
      emp_id, person_guid, type,
      face_guid, face_url, valid_level,
      state, del_flag, update_time,
      create_time)
    values (#{id,jdbcType=INTEGER}, #{deptId}, #{faceGateId,jdbcType=INTEGER}, #{deviceKey,jdbcType=VARCHAR},
      #{empId,jdbcType=INTEGER}, #{personGuid,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER},
      #{faceGuid,jdbcType=VARCHAR}, #{faceUrl,jdbcType=VARCHAR}, #{validLevel,jdbcType=INTEGER},
      #{state,jdbcType=INTEGER}, #{delFlag,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP},
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.whfc.emp.entity.AppFaceGateFace">
    insert into app_face_gate_face
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="faceGateId != null">
        face_gate_id,
      </if>
      <if test="deviceKey != null">
        device_key,
      </if>
      <if test="empId != null">
        emp_id,
      </if>
      <if test="personGuid != null">
        person_guid,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="faceGuid != null">
        face_guid,
      </if>
      <if test="faceUrl != null">
        face_url,
      </if>
      <if test="validLevel != null">
        valid_level,
      </if>
      <if test="state != null">
        state,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="deptId != null">
        #{deptId},
      </if>
      <if test="faceGateId != null">
        #{faceGateId,jdbcType=INTEGER},
      </if>
      <if test="deviceKey != null">
        #{deviceKey,jdbcType=VARCHAR},
      </if>
      <if test="empId != null">
        #{empId,jdbcType=INTEGER},
      </if>
      <if test="personGuid != null">
        #{personGuid,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="faceGuid != null">
        #{faceGuid,jdbcType=VARCHAR},
      </if>
      <if test="faceUrl != null">
        #{faceUrl,jdbcType=VARCHAR},
      </if>
      <if test="validLevel != null">
        #{validLevel,jdbcType=INTEGER},
      </if>
      <if test="state != null">
        #{state,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppFaceGateFace">
    update app_face_gate_face
    <set>
      <if test="deptId != null">
        dept_id = #{deptId},
      </if>
      <if test="faceGateId != null">
        face_gate_id = #{faceGateId,jdbcType=INTEGER},
      </if>
      <if test="deviceKey != null">
        device_key = #{deviceKey,jdbcType=VARCHAR},
      </if>
      <if test="empId != null">
        emp_id = #{empId,jdbcType=INTEGER},
      </if>
      <if test="personGuid != null">
        person_guid = #{personGuid,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="faceGuid != null">
        face_guid = #{faceGuid,jdbcType=VARCHAR},
      </if>
      <if test="faceUrl != null">
        face_url = #{faceUrl,jdbcType=VARCHAR},
      </if>
      <if test="validLevel != null">
        valid_level = #{validLevel,jdbcType=INTEGER},
      </if>
      <if test="state != null">
        state = #{state,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.whfc.emp.entity.AppFaceGateFace">
    update app_face_gate_face
    set dept_id = #{deptId},
      face_gate_id = #{faceGateId,jdbcType=INTEGER},
      device_key = #{deviceKey,jdbcType=VARCHAR},
      emp_id = #{empId,jdbcType=INTEGER},
      person_guid = #{personGuid,jdbcType=VARCHAR},
      type = #{type,jdbcType=INTEGER},
      face_guid = #{faceGuid,jdbcType=VARCHAR},
      face_url = #{faceUrl,jdbcType=VARCHAR},
      valid_level = #{validLevel,jdbcType=INTEGER},
      state = #{state,jdbcType=INTEGER},
      del_flag = #{delFlag,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

</mapper>