<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppEmpDeptDayMapper">
    <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppEmpDeptDay">
        <!--@mbg.generated-->
        <!--@Table app_emp_day-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="dept_id" jdbcType="INTEGER" property="deptId"/>
        <result column="date" jdbcType="DATE" property="date"/>
        <result column="total_emp_num" jdbcType="INTEGER" property="totalEmpNum"/>
        <result column="manager_num" jdbcType="INTEGER" property="managerNum"/>
        <result column="worker_num" jdbcType="INTEGER" property="workerNum"/>
        <result column="plan_attend_num" jdbcType="INTEGER" property="planAttendNum"/>
        <result column="actual_attend_num" jdbcType="INTEGER" property="actualAttendNum"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        dept_id,
        `date`,
        total_emp_num,
        manager_num,
        worker_num,
        plan_attend_num,
        actual_attend_num,
        update_time,
        create_time
    </sql>

    <insert id="insertOrUpdate">
        insert into app_emp_dept_day
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="date != null">
                `date`,
            </if>
            <if test="totalEmpNum != null">
                total_emp_num,
            </if>
            <if test="managerNum != null">
                manager_num,
            </if>
            <if test="workerNum != null">
                worker_num,
            </if>
            <if test="planAttendNum != null">
                plan_attend_num,
            </if>
            <if test="actualAttendNum != null">
                actual_attend_num,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                #{deptId,jdbcType=INTEGER},
            </if>
            <if test="date != null">
                #{date,jdbcType=DATE},
            </if>
            <if test="totalEmpNum != null">
                #{totalEmpNum,jdbcType=INTEGER},
            </if>
            <if test="managerNum != null">
                #{managerNum,jdbcType=INTEGER},
            </if>
            <if test="workerNum != null">
                #{workerNum,jdbcType=INTEGER},
            </if>
            <if test="planAttendNum != null">
                #{planAttendNum,jdbcType=INTEGER},
            </if>
            <if test="actualAttendNum != null">
                #{actualAttendNum,jdbcType=INTEGER},
            </if>
        </trim>
        on duplicate key update
        <if test="totalEmpNum != null">
            total_emp_num = #{totalEmpNum,jdbcType=INTEGER},
        </if>
        <if test="managerNum != null">
            manager_num = #{managerNum,jdbcType=INTEGER},
        </if>
        <if test="workerNum != null">
            worker_num = #{workerNum,jdbcType=INTEGER},
        </if>
        <if test="planAttendNum != null">
            plan_attend_num = #{planAttendNum,jdbcType=INTEGER},
        </if>
        <if test="actualAttendNum != null">
            actual_attend_num = #{actualAttendNum,jdbcType=INTEGER},
        </if>
        dept_id = #{deptId,jdbcType=INTEGER}
    </insert>

    <select id="selectByDeptIdAndDate" resultType="com.whfc.emp.param.EmpAttendPlan">
        select `date`,plan_attend_num
          from app_emp_dept_day
         where dept_id = #{deptId,jdbcType=INTEGER}
           and `date` = date(#{date,jdbcType=TIMESTAMP})
    </select>

    <select id="selectByDeptIdAndTimeRange" resultType="com.whfc.emp.param.EmpAttendPlan">
        select `date`,plan_attend_num
        from app_emp_dept_day
        where dept_id = #{deptId,jdbcType=INTEGER}
        <![CDATA[
        and `date` >= #{startDate,jdbcType=TIMESTAMP}
        and `date` <= #{endDate,jdbcType=TIMESTAMP}
        ]]>
    </select>
</mapper>