<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppPayrollEmpMapper">
  <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppPayrollEmp">
    <!--@mbg.generated-->
    <!--@Table app_payroll_emp-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="payroll_id" jdbcType="INTEGER" property="payrollId" />
    <result column="emp_id" jdbcType="INTEGER" property="empId" />
    <result column="attend_days" jdbcType="INTEGER" property="attendDays" />
    <result column="real_attend_days" jdbcType="INTEGER" property="realAttendDays" />
    <result column="unit_price" jdbcType="DOUBLE" property="unitPrice" />
    <result column="work_amount" jdbcType="INTEGER" property="workAmount" />
    <result column="salary_total" jdbcType="DOUBLE" property="salaryTotal" />
    <result column="salary_borrow" jdbcType="DOUBLE" property="salaryBorrow" />
    <result column="salary_deduct" jdbcType="DOUBLE" property="salaryDeduct" />
    <result column="salary_should" jdbcType="DOUBLE" property="salaryShould" />
    <result column="salary_real" jdbcType="DOUBLE" property="salaryReal" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, payroll_id, emp_id, attend_days, real_attend_days, unit_price, work_amount, salary_total, 
    salary_borrow, salary_deduct, salary_should, salary_real, `date`, remark, del_flag, 
    create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from app_payroll_emp
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from app_payroll_emp
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppPayrollEmp" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_payroll_emp (payroll_id, emp_id, attend_days, 
      real_attend_days, unit_price, work_amount, 
      salary_total, salary_borrow, salary_deduct, 
      salary_should, salary_real, `date`, 
      remark, del_flag, create_time, 
      update_time)
    values (#{payrollId,jdbcType=INTEGER}, #{empId,jdbcType=INTEGER}, #{attendDays,jdbcType=INTEGER}, 
      #{realAttendDays,jdbcType=INTEGER}, #{unitPrice,jdbcType=DOUBLE}, #{workAmount,jdbcType=INTEGER}, 
      #{salaryTotal,jdbcType=DOUBLE}, #{salaryBorrow,jdbcType=DOUBLE}, #{salaryDeduct,jdbcType=DOUBLE}, 
      #{salaryShould,jdbcType=DOUBLE}, #{salaryReal,jdbcType=DOUBLE}, #{date,jdbcType=DATE}, 
      #{remark,jdbcType=VARCHAR}, #{delFlag,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppPayrollEmp" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_payroll_emp
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="payrollId != null">
        payroll_id,
      </if>
      <if test="empId != null">
        emp_id,
      </if>
      <if test="attendDays != null">
        attend_days,
      </if>
      <if test="realAttendDays != null">
        real_attend_days,
      </if>
      <if test="unitPrice != null">
        unit_price,
      </if>
      <if test="workAmount != null">
        work_amount,
      </if>
      <if test="salaryTotal != null">
        salary_total,
      </if>
      <if test="salaryBorrow != null">
        salary_borrow,
      </if>
      <if test="salaryDeduct != null">
        salary_deduct,
      </if>
      <if test="salaryShould != null">
        salary_should,
      </if>
      <if test="salaryReal != null">
        salary_real,
      </if>
      <if test="date != null">
        `date`,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="payrollId != null">
        #{payrollId,jdbcType=INTEGER},
      </if>
      <if test="empId != null">
        #{empId,jdbcType=INTEGER},
      </if>
      <if test="attendDays != null">
        #{attendDays,jdbcType=INTEGER},
      </if>
      <if test="realAttendDays != null">
        #{realAttendDays,jdbcType=INTEGER},
      </if>
      <if test="unitPrice != null">
        #{unitPrice,jdbcType=DOUBLE},
      </if>
      <if test="workAmount != null">
        #{workAmount,jdbcType=INTEGER},
      </if>
      <if test="salaryTotal != null">
        #{salaryTotal,jdbcType=DOUBLE},
      </if>
      <if test="salaryBorrow != null">
        #{salaryBorrow,jdbcType=DOUBLE},
      </if>
      <if test="salaryDeduct != null">
        #{salaryDeduct,jdbcType=DOUBLE},
      </if>
      <if test="salaryShould != null">
        #{salaryShould,jdbcType=DOUBLE},
      </if>
      <if test="salaryReal != null">
        #{salaryReal,jdbcType=DOUBLE},
      </if>
      <if test="date != null">
        #{date,jdbcType=DATE},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppPayrollEmp">
    <!--@mbg.generated-->
    update app_payroll_emp
    <set>
      <if test="payrollId != null">
        payroll_id = #{payrollId,jdbcType=INTEGER},
      </if>
      <if test="empId != null">
        emp_id = #{empId,jdbcType=INTEGER},
      </if>
      <if test="attendDays != null">
        attend_days = #{attendDays,jdbcType=INTEGER},
      </if>
      <if test="realAttendDays != null">
        real_attend_days = #{realAttendDays,jdbcType=INTEGER},
      </if>
      <if test="unitPrice != null">
        unit_price = #{unitPrice,jdbcType=DOUBLE},
      </if>
      <if test="workAmount != null">
        work_amount = #{workAmount,jdbcType=INTEGER},
      </if>
      <if test="salaryTotal != null">
        salary_total = #{salaryTotal,jdbcType=DOUBLE},
      </if>
      <if test="salaryBorrow != null">
        salary_borrow = #{salaryBorrow,jdbcType=DOUBLE},
      </if>
      <if test="salaryDeduct != null">
        salary_deduct = #{salaryDeduct,jdbcType=DOUBLE},
      </if>
      <if test="salaryShould != null">
        salary_should = #{salaryShould,jdbcType=DOUBLE},
      </if>
      <if test="salaryReal != null">
        salary_real = #{salaryReal,jdbcType=DOUBLE},
      </if>
      <if test="date != null">
        `date` = #{date,jdbcType=DATE},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.whfc.emp.entity.AppPayrollEmp">
    <!--@mbg.generated-->
    update app_payroll_emp
    set payroll_id = #{payrollId,jdbcType=INTEGER},
      emp_id = #{empId,jdbcType=INTEGER},
      attend_days = #{attendDays,jdbcType=INTEGER},
      real_attend_days = #{realAttendDays,jdbcType=INTEGER},
      unit_price = #{unitPrice,jdbcType=DOUBLE},
      work_amount = #{workAmount,jdbcType=INTEGER},
      salary_total = #{salaryTotal,jdbcType=DOUBLE},
      salary_borrow = #{salaryBorrow,jdbcType=DOUBLE},
      salary_deduct = #{salaryDeduct,jdbcType=DOUBLE},
      salary_should = #{salaryShould,jdbcType=DOUBLE},
      salary_real = #{salaryReal,jdbcType=DOUBLE},
      `date` = #{date,jdbcType=DATE},
      remark = #{remark,jdbcType=VARCHAR},
      del_flag = #{delFlag,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
    <select id="selectByPayrollId" resultType="com.whfc.emp.dto.AppPayrollEmpDTO">
  	select ape.id,
        ape.emp_id,
  		ae.emp_name as empName,
  		ae.id_card_no,
        ae.work_type_id,
  		ae.work_type_name,
  		ae.work_role_name,
        ape.work_amount,
  		ape.attend_days,
  		ape.real_attend_days,
  		ape.salary_total,
  		ape.salary_borrow,
  		ape.salary_should,
  		ape.salary_real,
  		ape.`date`,
  		ape.remark,
        ape.unit_price,
        ape.salary_deduct
  	from app_payroll_emp ape
  	inner join app_emp ae on ae.id=ape.emp_id
  	where ape.del_flag=0
  		and ape.payroll_id=#{payrollId}
  </select>

    <update id="updateByPayrollIdAndEmpId" parameterType="com.whfc.emp.param.AppPayrollDetailEditParam">
        update app_payroll_emp
        <set>
            <if test="payrollId != null">
                payroll_id = #{payrollId,jdbcType=INTEGER},
            </if>
            <if test="empId != null">
                emp_id = #{empId,jdbcType=INTEGER},
            </if>
            <if test="attendDays != null">
                real_attend_days = #{attendDays,jdbcType=INTEGER},
            </if>
            <if test="unitPrice != null">
                unit_price = #{unitPrice,jdbcType=DOUBLE},
            </if>
            <if test="workAmount != null">
                work_amount = #{workAmount,jdbcType=INTEGER},
            </if>
            <if test="salaryTotal != null">
                salary_total = #{salaryTotal,jdbcType=DOUBLE},
            </if>
            <if test="salaryBorrow != null">
                salary_borrow = #{salaryBorrow,jdbcType=DOUBLE},
            </if>
            <if test="salaryDeduct != null">
                salary_deduct = #{salaryDeduct,jdbcType=DOUBLE},
            </if>
            <if test="salaryShould != null">
                salary_should = #{salaryShould,jdbcType=DOUBLE},
            </if>
            <if test="salaryReal != null">
                salary_real = #{salaryReal,jdbcType=DOUBLE},
            </if>
            <if test="date != null">
                `date` = #{date,jdbcType=DATE},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        where payroll_id = #{payrollId}
        and emp_id = #{empId}
    </update>

    <update id="deleteLogicByPayrollId">
    update app_payroll_emp
    set del_flag = 1
    where payroll_id = #{payrollId}
  </update>

    <insert id="batchInsert" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO app_payroll_emp
        (
        payroll_id,
        emp_id,
        attend_days,
        real_attend_days,
        unit_price,
        work_amount,
        salary_total,
        salary_borrow,
        salary_deduct,
        salary_should,
        salary_real,
        `date`,
        remark
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.payrollId},
            #{item.empId},
            #{item.attendDays},
            #{item.realAttendDays},
            #{item.unitPrice},
            #{item.workAmount}
            #{item.salaryTotal},
            #{item.salaryBorrow},
            #{item.salaryDeduct},
            #{item.salaryShould},
            #{item.salaryReal},
            #{item.date},
            #{item.remark}
            )
        </foreach>
    </insert>

    <select id="selectByEmpId" resultType="com.whfc.emp.dto.AppPayrollEmpDTO">
     SELECT
        ape.id,
	    ape.`date`,
	    ape.salary_real,
	    ape.salary_should,
	    ape.salary_total,
	    ape.remark
     FROM
	    app_payroll_emp ape
	 LEFT JOIN app_payroll ap  on ap.id = ape.payroll_id
     WHERE
	    ape.del_flag = 0
	    and ap.state=1
	    AND ape.emp_id = #{empId}
     ORDER BY ape.id DESC
  </select>

    <select id="selectPayrollEmp" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM app_payroll_emp
        WHERE del_flag = 0
        AND payroll_id = #{payrollId}
        AND emp_id = #{empId}
    </select>
</mapper>