<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppEmpHealthReportMapper">
    <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppEmpHealthReport">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="emp_id" jdbcType="INTEGER" property="empId"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="date" jdbcType="DATE" property="date"/>
        <result column="test_result" jdbcType="INTEGER" property="testResult"/>
        <result column="code_state" jdbcType="VARCHAR" property="codeState"/>
        <result column="journey" jdbcType="VARCHAR" property="journey"/>
        <result column="exp_date" jdbcType="DATE" property="expDate"/>
        <result column="first_time" jdbcType="TIMESTAMP" property="firstTime"/>
        <result column="second_time" jdbcType="TIMESTAMP" property="secondTime"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,
        emp_id,
        `type`,
        `date`,
        test_result,
        code_state,
        journey,
        exp_date,
        first_time,
        second_time,
        del_flag,
        update_time,
        create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from app_emp_health_report
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete
        from app_emp_health_report
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppEmpHealthReport"
            useGeneratedKeys="true">
        insert into app_emp_health_report (emp_id, `type`, `date`,
                                           test_result, code_state, journey,
                                           exp_date, first_time, second_time,
                                           del_flag, update_time, create_time)
        values (#{empId,jdbcType=INTEGER}, #{type,jdbcType=VARCHAR}, #{date,jdbcType=DATE},
                #{testResult,jdbcType=INTEGER}, #{codeState,jdbcType=VARCHAR}, #{journey,jdbcType=VARCHAR},
                #{expDate,jdbcType=DATE}, #{firstTime,jdbcType=TIMESTAMP}, #{secondTime,jdbcType=TIMESTAMP},
                #{delFlag,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppEmpHealthReport"
            useGeneratedKeys="true">
        insert into app_emp_health_report
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="empId != null">
                emp_id,
            </if>
            <if test="type != null">
                `type`,
            </if>
            <if test="date != null">
                `date`,
            </if>
            <if test="testResult != null">
                test_result,
            </if>
            <if test="codeState != null">
                code_state,
            </if>
            <if test="journey != null">
                journey,
            </if>
            <if test="expDate != null">
                exp_date,
            </if>
            <if test="firstTime != null">
                first_time,
            </if>
            <if test="secondTime != null">
                second_time,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="empId != null">
                #{empId,jdbcType=INTEGER},
            </if>
            <if test="type != null">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="date != null">
                #{date,jdbcType=DATE},
            </if>
            <if test="testResult != null">
                #{testResult,jdbcType=INTEGER},
            </if>
            <if test="codeState != null">
                #{codeState,jdbcType=VARCHAR},
            </if>
            <if test="journey != null">
                #{journey,jdbcType=VARCHAR},
            </if>
            <if test="expDate != null">
                #{expDate,jdbcType=DATE},
            </if>
            <if test="firstTime != null">
                #{firstTime,jdbcType=TIMESTAMP},
            </if>
            <if test="secondTime != null">
                #{secondTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppEmpHealthReport">
        update app_emp_health_report
        <set>
            <if test="empId != null">
                emp_id = #{empId,jdbcType=INTEGER},
            </if>
            <if test="type != null">
                `type` = #{type,jdbcType=VARCHAR},
            </if>
            <if test="date != null">
                `date` = #{date,jdbcType=DATE},
            </if>
            <if test="testResult != null">
                test_result = #{testResult,jdbcType=INTEGER},
            </if>
            <if test="codeState != null">
                code_state = #{codeState,jdbcType=VARCHAR},
            </if>
            <if test="journey != null">
                journey = #{journey,jdbcType=VARCHAR},
            </if>
            <if test="expDate != null">
                exp_date = #{expDate,jdbcType=DATE},
            </if>
            <if test="firstTime != null">
                first_time = #{firstTime,jdbcType=TIMESTAMP},
            </if>
            <if test="secondTime != null">
                second_time = #{secondTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.whfc.emp.entity.AppEmpHealthReport">
        update app_emp_health_report
        set emp_id      = #{empId,jdbcType=INTEGER},
            `type`      = #{type,jdbcType=VARCHAR},
            `date`      = #{date,jdbcType=DATE},
            test_result = #{testResult,jdbcType=INTEGER},
            code_state  = #{codeState,jdbcType=VARCHAR},
            journey     = #{journey,jdbcType=VARCHAR},
            exp_date    = #{expDate,jdbcType=DATE},
            first_time  = #{firstTime,jdbcType=TIMESTAMP},
            second_time = #{secondTime,jdbcType=TIMESTAMP},
            del_flag    = #{delFlag,jdbcType=INTEGER},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            create_time = #{createTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectByEmpIdAndType" resultType="com.whfc.emp.dto.AppEmpHealthReportDTO">
        SELECT
            id AS healthReportId,
            type,
            date,
            test_result,
            code_state,
            journey,
            exp_date,
            first_time,
            second_time
        FROM app_emp_health_report
        WHERE del_flag = 0
        AND emp_id = #{empId}
        AND type = #{type}
        ORDER BY create_time DESC
    </select>

    <update id="logDel">
        UPDATE app_emp_health_report SET del_flag = 1 WHERE id = #{healthInfoId}
    </update>
</mapper>