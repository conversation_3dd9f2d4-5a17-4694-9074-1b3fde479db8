<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppFaceGateConfigMapper">
  <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppFaceGateConfig">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="platform" jdbcType="VARCHAR" property="platform" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="app_key" jdbcType="VARCHAR" property="appKey" />
    <result column="app_secret" jdbcType="VARCHAR" property="appSecret" />
    <result column="params" jdbcType="LONGVARCHAR" property="params" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, dept_id, platform, app_id, app_key, app_secret, params, remark, del_flag, update_time,
    create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from app_face_gate_config
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from app_face_gate_config
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppFaceGateConfig" useGeneratedKeys="true">
    insert into app_face_gate_config (dept_id, platform, app_id,
      app_key, app_secret, params, 
      remark, del_flag, update_time, 
      create_time)
    values (#{deptId,jdbcType=INTEGER}, #{platform,jdbcType=VARCHAR}, #{appId,jdbcType=VARCHAR}, 
      #{appKey,jdbcType=VARCHAR}, #{appSecret,jdbcType=VARCHAR}, #{params,jdbcType=LONGVARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{delFlag,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppFaceGateConfig" useGeneratedKeys="true">
    insert into app_face_gate_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="platform != null">
        platform,
      </if>
      <if test="appId != null">
        app_id,
      </if>
      <if test="appKey != null">
        app_key,
      </if>
      <if test="appSecret != null">
        app_secret,
      </if>
      <if test="params != null">
        params,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="platform != null">
        #{platform,jdbcType=VARCHAR},
      </if>
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="appKey != null">
        #{appKey,jdbcType=VARCHAR},
      </if>
      <if test="appSecret != null">
        #{appSecret,jdbcType=VARCHAR},
      </if>
      <if test="params != null">
        #{params,jdbcType=LONGVARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppFaceGateConfig">
    update app_face_gate_config
    <set>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="platform != null">
        platform = #{platform,jdbcType=VARCHAR},
      </if>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="appKey != null">
        app_key = #{appKey,jdbcType=VARCHAR},
      </if>
      <if test="appSecret != null">
        app_secret = #{appSecret,jdbcType=VARCHAR},
      </if>
      <if test="params != null">
        params = #{params,jdbcType=LONGVARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.whfc.emp.entity.AppFaceGateConfig">
    update app_face_gate_config
    set dept_id = #{deptId,jdbcType=INTEGER},
      platform = #{platform,jdbcType=VARCHAR},
      app_id = #{appId,jdbcType=VARCHAR},
      app_key = #{appKey,jdbcType=VARCHAR},
      app_secret = #{appSecret,jdbcType=VARCHAR},
      params = #{params,jdbcType=LONGVARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      del_flag = #{delFlag,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByDeptId" resultType="com.whfc.emp.dto.AppFaceGateConfigDTO">
    select afgc.platform,
    afgp.name
    from app_face_gate_config afgc
    left join app_face_gate_platform afgp ON afgp.platform = afgc.platform
    where afgc.dept_id = #{deptId}
    and afgc.del_flag = 0
  </select>

  <select id="selectByDeptIdAndPlatform" resultMap="BaseResultMap">
    select DISTINCT
    <include refid="Base_Column_List"/>
    from app_face_gate_config
    where dept_id = #{deptId}
    and platform = #{platform}
    and del_flag = 0
  </select>

  <select id="selectByPlatform" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from app_face_gate_config
    where platform = #{platform}
    and del_flag = 0
  </select>

  <select id="selectAll" resultType="com.whfc.emp.dto.AppFaceGateConfigDTO">
    SELECT DISTINCT id     AS configId,
                    platform,
                    remark AS `name`
    FROM app_face_gate_config
    WHERE del_flag = 0
      AND (
            app_id IS NOT NULL OR app_key IS NOT NULL OR app_secret IS NOT NULL)
  </select>

  <select id="selectDetailListByDeptId" resultType="com.whfc.emp.dto.AppFaceGateConfigDTO">
    SELECT platform,
    app_id,
    app_key,
    app_secret,
    remark
    FROM app_face_gate_config
    WHERE del_flag = 0
    AND dept_id = #{deptId}
  </select>

  <update id="loginDelByDeptId">
    UPDATE app_face_gate_config
    SET del_flag = 1
    WHERE dept_id = #{deptId}
  </update>

</mapper>