<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppFaceGateMapper">
    <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppFaceGate">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="dept_id" jdbcType="INTEGER" property="deptId"/>
        <result column="device_key" jdbcType="VARCHAR" property="deviceKey"/>
        <result column="platform" jdbcType="VARCHAR" property="platform"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="model" jdbcType="VARCHAR" property="model"/>
        <result column="lat" jdbcType="DOUBLE" property="lat"/>
        <result column="lng" jdbcType="DOUBLE" property="lng"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="direction" jdbcType="INTEGER" property="direction"/>
        <result column="area_id" jdbcType="INTEGER" property="areaId"/>
        <result column="area_name" jdbcType="VARCHAR" property="areaName"/>
        <result column="tag" jdbcType="VARCHAR" property="tag"/>
        <result column="state" jdbcType="INTEGER" property="state"/>
        <result column="time" jdbcType="DATE" property="time"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,
        dept_id,
        device_key,
        platform,
        `name`,
        model,
        lat,
        lng,
        address,
        direction,
        area_id,
        area_name,
        tag,
        `state`,
        `time`,
        del_flag,
        update_time,
        create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from app_face_gate
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete
        from app_face_gate
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppFaceGate"
            useGeneratedKeys="true">
        insert into app_face_gate
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="deviceKey != null">
                device_key,
            </if>
            <if test="platform != null">
                platform,
            </if>
            <if test="name != null">
                `name`,
            </if>
            <if test="model != null">
                `model`,
            </if>
            <if test="lat != null">
                lat,
            </if>
            <if test="lng != null">
                lng,
            </if>
            <if test="address != null">
                address,
            </if>
            <if test="direction != null">
                direction,
            </if>
            <if test="areaId != null">
                area_id,
            </if>
            <if test="areaName != null">
                area_name,
            </if>
            <if test="tag != null">
                tag,
            </if>
            <if test="state != null">
                `state`,
            </if>
            <if test="time != null">
                `time`,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                #{deptId,jdbcType=INTEGER},
            </if>
            <if test="deviceKey != null">
                #{deviceKey,jdbcType=VARCHAR},
            </if>
            <if test="platform != null">
                #{platform},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="model != null">
                #{model,jdbcType=VARCHAR},
            </if>
            <if test="lat != null">
                #{lat,jdbcType=DOUBLE},
            </if>
            <if test="lng != null">
                #{lng,jdbcType=DOUBLE},
            </if>
            <if test="address != null">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="direction != null">
                #{direction,jdbcType=INTEGER},
            </if>
            <if test="areaId != null">
                #{areaId,jdbcType=INTEGER},
            </if>
            <if test="areaName != null">
                #{areaName,jdbcType=VARCHAR},
            </if>
            <if test="tag != null">
                #{tag,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                #{state,jdbcType=INTEGER},
            </if>
            <if test="time != null">
                #{time,jdbcType=INTEGER},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppFaceGate">
        update app_face_gate
        <set>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=INTEGER},
            </if>
            <if test="deviceKey != null">
                device_key = #{deviceKey,jdbcType=VARCHAR},
            </if>
            <if test="platform != null">
                platform = #{platform},
            </if>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="model != null">
                `model` = #{model,jdbcType=VARCHAR},
            </if>
            <if test="lat != null">
                lat = #{lat,jdbcType=DOUBLE},
            </if>
            <if test="lng != null">
                lng = #{lng,jdbcType=DOUBLE},
            </if>
            <if test="address != null">
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="direction != null">
                direction = #{direction,jdbcType=INTEGER},
            </if>
            <if test="areaId != null">
                area_id = #{areaId,jdbcType=INTEGER},
            </if>
            <if test="areaName != null">
                area_name = #{areaName,jdbcType=VARCHAR},
            </if>
            <if test="tag != null">
                tag = #{tag,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                `state` = #{state,jdbcType=INTEGER},
            </if>
            <if test="time != null">
                `time` = #{time,jdbcType=INTEGER},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectByDeptId" resultType="com.whfc.emp.dto.AppFaceGateDTO">
        SELECT afg.id    as faceGateId,
               afg.dept_id,
               afg.device_key,
               afg.`name`,
               afg.model,
               afg.direction,
               afg.area_id,
               afg.area_name,
               afg.lng,
               afg.lat,
               afg.address,
               ifnull(afg.state,0) as state,
               afg.time,
               afg.platform,
               afgp.name as platformName
        FROM app_face_gate afg
        inner join app_face_gate_platform afgp on afg.platform = afgp.platform
        where afg.del_flag = 0
          and afg.dept_id = #{deptId}
        <if test="keyword != null and keyword != ''">
            and afg.`name` like concat('%', #{keyword}, '%')
        </if>
        order by afg.id
    </select>

    <select id="selectByDeviceKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from app_face_gate
        where device_key = #{deviceKey}
          and del_flag = 0
    </select>

    <select id="selectByDeptIdAndDeviceKey" resultType="com.whfc.emp.dto.AppFaceGateDTO">
        SELECT id as faceGateId,
               device_key,
               `name`,
               dept_id,
               direction
        FROM app_face_gate
        where del_flag = 0
          and dept_id = #{deptId}
          and device_key = #{deviceKey}
    </select>

    <update id="deleteLogicById">
        update app_face_gate
        set del_flag=1
        where id = #{faceGateId}
    </update>

    <update id="logicDelByDeptIdAndDeviceKey">
        update app_face_gate
        set del_flag=1
        where dept_id = #{deptId}
          and device_key = #{deviceKey}
    </update>

    <select id="selectByPlatform" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM app_face_gate
        WHERE del_flag = 0
          AND platform = #{platform}
    </select>

    <update id="updateState">
        update app_face_gate
           set state = #{state},`time`=#{time}
         where id = #{id}
    </update>

    <update id="updateOffline">
        update app_face_gate
           set state = 0
         where del_flag = 0
           and DATE_ADD(now(), INTERVAL - 24 HOUR ) > `time`
    </update>

    <select id="statFaceGate" resultType="com.whfc.emp.dto.AppEmpDeviceNumDTO">
        SELECT count(1) as deviceTotal,
               count(case when state = 1 then 1 end) as deviceOnline
          from app_face_gate
        where del_flag = 0
          and dept_id in
          <foreach collection="deptIds" item="deptId" separator="," open="(" close=")">
              #{deptId}
          </foreach>
    </select>
</mapper>