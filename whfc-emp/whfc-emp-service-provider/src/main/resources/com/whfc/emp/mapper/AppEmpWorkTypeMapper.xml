<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppEmpWorkTypeMapper">
    <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppEmpWorkType">
        <!--@mbg.generated-->
        <!--@Table app_emp_work_type-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="dept_id" jdbcType="INTEGER" property="deptId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="work_code" jdbcType="VARCHAR" property="workCode"/>
        <result column="spec" jdbcType="INTEGER" property="spec"/>
        <result column="need_cert" jdbcType="INTEGER" property="needCert"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        dept_id,
        `name`,
        work_code,
        spec,
        need_cert,
        del_flag,
        update_time,
        create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from app_emp_work_type
        where id = #{id,jdbcType=INTEGER}
    </select>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppEmpWorkType"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into app_emp_work_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="name != null">
                `name`,
            </if>
            <if test="workCode != null">
                work_code,
            </if>
            <if test="spec != null">
                spec,
            </if>
            <if test="needCert != null">
                need_cert,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                #{deptId,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="workCode != null">
                #{workCode,jdbcType=VARCHAR},
            </if>
            <if test="spec != null">
                #{spec,jdbcType=INTEGER},
            </if>
            <if test="needCert != null">
                #{needCert,jdbcType=INTEGER},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppEmpWorkType">
        <!--@mbg.generated-->
        update app_emp_work_type
        <set>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="workCode != null">
                work_code = #{workCode,jdbcType=VARCHAR},
            </if>
            <if test="spec != null">
                spec = #{spec,jdbcType=INTEGER},
            </if>
            <if test="needCert != null">
                need_cert = #{needCert,jdbcType=INTEGER},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectByDeptId" resultType="com.whfc.emp.dto.AppWorkTypeDTO">
        select id     as workTypeId,
               `name` as workTypeName,
               work_code,
               spec,
               need_cert
        from app_emp_work_type
        where del_flag = 0
          and dept_id = #{deptId}
        order by id desc
    </select>

    <select id="selectByDeptIdAndName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from app_emp_work_type
        where del_flag = 0
          and dept_id = #{deptId}
          and `name` = #{name}
    </select>

    <select id="selectByDeptIdAndWorkCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from app_emp_work_type
        where del_flag = 0
          and dept_id = #{deptId}
          and `work_code` = #{workCode}
    </select>

    <insert id="batchInsert">
        insert into app_emp_work_type
        (dept_id,
         `name`,
         work_code,
         spec,
         need_cert)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.deptId},
             #{item.workTypeName},
             #{item.workCode},
             #{item.spec},
             #{item.needCert})
        </foreach>
    </insert>

    <update id="deleteLogicById">
        update app_emp_work_type
        set del_flag = 1
        where id = #{id}
    </update>
</mapper>