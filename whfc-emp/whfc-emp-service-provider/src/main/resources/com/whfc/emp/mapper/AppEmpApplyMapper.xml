<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppEmpApplyMapper">
    <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppEmpApply">
        <!--@mbg.generated-->
        <!--@Table app_emp_apply-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="dept_id" jdbcType="INTEGER" property="deptId"/>
        <result column="group_id" jdbcType="INTEGER" property="groupId"/>
        <result column="group_name" jdbcType="VARCHAR" property="groupName"/>
        <result column="corp_id" jdbcType="INTEGER" property="corpId"/>
        <result column="corp_name" jdbcType="VARCHAR" property="corpName"/>
        <result column="emp_name" jdbcType="VARCHAR" property="empName"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="gender" jdbcType="INTEGER" property="gender"/>
        <result column="birthday" jdbcType="DATE" property="birthday"/>
        <result column="id_card_no" jdbcType="VARCHAR" property="idCardNo"/>
        <result column="head_img" jdbcType="VARCHAR" property="headImg"/>
        <result column="avatar" jdbcType="VARCHAR" property="avatar"/>
        <result column="card_type" jdbcType="INTEGER" property="cardType"/>
        <result column="id_card_front" jdbcType="VARCHAR" property="idCardFront"/>
        <result column="id_card_back" jdbcType="VARCHAR" property="idCardBack"/>
        <result column="id_card_grant_org" jdbcType="VARCHAR" property="idCardGrantOrg"/>
        <result column="id_card_start_date" jdbcType="DATE" property="idCardStartDate"/>
        <result column="id_card_end_date" jdbcType="DATE" property="idCardEndDate"/>
        <result column="nation" jdbcType="VARCHAR" property="nation"/>
        <result column="province" jdbcType="VARCHAR" property="province"/>
        <result column="city" jdbcType="VARCHAR" property="city"/>
        <result column="area" jdbcType="VARCHAR" property="area"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="work_role_id" jdbcType="INTEGER" property="workRoleId"/>
        <result column="work_role_name" jdbcType="VARCHAR" property="workRoleName"/>
        <result column="work_type_id" jdbcType="INTEGER" property="workTypeId"/>
        <result column="work_type_name" jdbcType="VARCHAR" property="workTypeName"/>
        <result column="leader_flag" jdbcType="INTEGER" property="leaderFlag"/>
        <result column="enter_time" jdbcType="DATE" property="enterTime"/>
        <result column="check_result" jdbcType="INTEGER" property="checkResult"/>
        <result column="check_name" jdbcType="VARCHAR" property="checkName"/>
        <result column="check_time" jdbcType="TIMESTAMP" property="checkTime"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, dept_id, group_id, group_name, corp_id, corp_name, emp_name, phone, gender, birthday,
        id_card_no, head_img, avatar, card_type, id_card_front, id_card_back, id_card_grant_org,
        id_card_start_date, id_card_end_date, nation, province, city, area, address, work_role_id,
        work_role_name, work_type_id, work_type_name, leader_flag, enter_time, check_result,
        check_name, check_time, del_flag, update_time, create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from app_emp_apply
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete from app_emp_apply
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppEmpApply"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into app_emp_apply (dept_id, group_id, group_name,
        corp_id, corp_name, emp_name,
        phone, gender, birthday,
        id_card_no, head_img, avatar,
        card_type, id_card_front, id_card_back,
        id_card_grant_org, id_card_start_date, id_card_end_date,
        nation, province, city,
        area, address, work_role_id,
        work_role_name, work_type_id, work_type_name,
        leader_flag, enter_time, check_result,
        check_name, check_time, del_flag,
        update_time, create_time)
        values (#{deptId,jdbcType=INTEGER}, #{groupId,jdbcType=INTEGER}, #{groupName,jdbcType=VARCHAR},
        #{corpId,jdbcType=INTEGER}, #{corpName,jdbcType=VARCHAR}, #{empName,jdbcType=VARCHAR},
        #{phone,jdbcType=VARCHAR}, #{gender,jdbcType=INTEGER}, #{birthday,jdbcType=DATE},
        #{idCardNo,jdbcType=VARCHAR}, #{headImg,jdbcType=VARCHAR}, #{avatar,jdbcType=VARCHAR},
        #{cardType,jdbcType=INTEGER}, #{idCardFront,jdbcType=VARCHAR}, #{idCardBack,jdbcType=VARCHAR},
        #{idCardGrantOrg,jdbcType=VARCHAR}, #{idCardStartDate,jdbcType=DATE}, #{idCardEndDate,jdbcType=DATE},
        #{nation,jdbcType=VARCHAR}, #{province,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR},
        #{area,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, #{workRoleId,jdbcType=INTEGER},
        #{workRoleName,jdbcType=VARCHAR}, #{workTypeId,jdbcType=INTEGER}, #{workTypeName,jdbcType=VARCHAR},
        #{leaderFlag,jdbcType=INTEGER}, #{enterTime,jdbcType=DATE}, #{checkResult,jdbcType=INTEGER},
        #{checkName,jdbcType=VARCHAR}, #{checkTime,jdbcType=TIMESTAMP}, #{delFlag,jdbcType=INTEGER},
        #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppEmpApply"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into app_emp_apply
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="groupId != null">
                group_id,
            </if>
            <if test="groupName != null">
                group_name,
            </if>
            <if test="corpId != null">
                corp_id,
            </if>
            <if test="corpName != null">
                corp_name,
            </if>
            <if test="empName != null">
                emp_name,
            </if>
            <if test="phone != null">
                phone,
            </if>
            <if test="gender != null">
                gender,
            </if>
            <if test="birthday != null">
                birthday,
            </if>
            <if test="idCardNo != null">
                id_card_no,
            </if>
            <if test="headImg != null">
                head_img,
            </if>
            <if test="avatar != null">
                avatar,
            </if>
            <if test="cardType != null">
                card_type,
            </if>
            <if test="idCardFront != null">
                id_card_front,
            </if>
            <if test="idCardBack != null">
                id_card_back,
            </if>
            <if test="idCardGrantOrg != null">
                id_card_grant_org,
            </if>
            <if test="idCardStartDate != null">
                id_card_start_date,
            </if>
            <if test="idCardEndDate != null">
                id_card_end_date,
            </if>
            <if test="nation != null">
                nation,
            </if>
            <if test="province != null">
                province,
            </if>
            <if test="city != null">
                city,
            </if>
            <if test="area != null">
                area,
            </if>
            <if test="address != null">
                address,
            </if>
            <if test="workRoleId != null">
                work_role_id,
            </if>
            <if test="workRoleName != null">
                work_role_name,
            </if>
            <if test="workTypeId != null">
                work_type_id,
            </if>
            <if test="workTypeName != null">
                work_type_name,
            </if>
            <if test="leaderFlag != null">
                leader_flag,
            </if>
            <if test="enterTime != null">
                enter_time,
            </if>
            <if test="checkResult != null">
                check_result,
            </if>
            <if test="checkName != null">
                check_name,
            </if>
            <if test="checkTime != null">
                check_time,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                #{deptId,jdbcType=INTEGER},
            </if>
            <if test="groupId != null">
                #{groupId,jdbcType=INTEGER},
            </if>
            <if test="groupName != null">
                #{groupName,jdbcType=VARCHAR},
            </if>
            <if test="corpId != null">
                #{corpId,jdbcType=INTEGER},
            </if>
            <if test="corpName != null">
                #{corpName,jdbcType=VARCHAR},
            </if>
            <if test="empName != null">
                #{empName,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="gender != null">
                #{gender,jdbcType=INTEGER},
            </if>
            <if test="birthday != null">
                #{birthday,jdbcType=DATE},
            </if>
            <if test="idCardNo != null">
                #{idCardNo,jdbcType=VARCHAR},
            </if>
            <if test="headImg != null">
                #{headImg,jdbcType=VARCHAR},
            </if>
            <if test="avatar != null">
                #{avatar,jdbcType=VARCHAR},
            </if>
            <if test="cardType != null">
                #{cardType,jdbcType=INTEGER},
            </if>
            <if test="idCardFront != null">
                #{idCardFront,jdbcType=VARCHAR},
            </if>
            <if test="idCardBack != null">
                #{idCardBack,jdbcType=VARCHAR},
            </if>
            <if test="idCardGrantOrg != null">
                #{idCardGrantOrg,jdbcType=VARCHAR},
            </if>
            <if test="idCardStartDate != null">
                #{idCardStartDate,jdbcType=DATE},
            </if>
            <if test="idCardEndDate != null">
                #{idCardEndDate,jdbcType=DATE},
            </if>
            <if test="nation != null">
                #{nation,jdbcType=VARCHAR},
            </if>
            <if test="province != null">
                #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                #{city,jdbcType=VARCHAR},
            </if>
            <if test="area != null">
                #{area,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="workRoleId != null">
                #{workRoleId,jdbcType=INTEGER},
            </if>
            <if test="workRoleName != null">
                #{workRoleName,jdbcType=VARCHAR},
            </if>
            <if test="workTypeId != null">
                #{workTypeId,jdbcType=INTEGER},
            </if>
            <if test="workTypeName != null">
                #{workTypeName,jdbcType=VARCHAR},
            </if>
            <if test="leaderFlag != null">
                #{leaderFlag,jdbcType=INTEGER},
            </if>
            <if test="enterTime != null">
                #{enterTime,jdbcType=DATE},
            </if>
            <if test="checkResult != null">
                #{checkResult,jdbcType=INTEGER},
            </if>
            <if test="checkName != null">
                #{checkName,jdbcType=VARCHAR},
            </if>
            <if test="checkTime != null">
                #{checkTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppEmpApply">
        <!--@mbg.generated-->
        update app_emp_apply
        <set>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=INTEGER},
            </if>
            <if test="groupId != null">
                group_id = #{groupId,jdbcType=INTEGER},
            </if>
            <if test="groupName != null">
                group_name = #{groupName,jdbcType=VARCHAR},
            </if>
            <if test="corpId != null">
                corp_id = #{corpId,jdbcType=INTEGER},
            </if>
            <if test="corpName != null">
                corp_name = #{corpName,jdbcType=VARCHAR},
            </if>
            <if test="empName != null">
                emp_name = #{empName,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="gender != null">
                gender = #{gender,jdbcType=INTEGER},
            </if>
            <if test="birthday != null">
                birthday = #{birthday,jdbcType=DATE},
            </if>
            <if test="idCardNo != null">
                id_card_no = #{idCardNo,jdbcType=VARCHAR},
            </if>
            <if test="headImg != null">
                head_img = #{headImg,jdbcType=VARCHAR},
            </if>
            <if test="avatar != null">
                avatar = #{avatar,jdbcType=VARCHAR},
            </if>
            <if test="cardType != null">
                card_type = #{cardType,jdbcType=INTEGER},
            </if>
            <if test="idCardFront != null">
                id_card_front = #{idCardFront,jdbcType=VARCHAR},
            </if>
            <if test="idCardBack != null">
                id_card_back = #{idCardBack,jdbcType=VARCHAR},
            </if>
            <if test="idCardGrantOrg != null">
                id_card_grant_org = #{idCardGrantOrg,jdbcType=VARCHAR},
            </if>
            <if test="idCardStartDate != null">
                id_card_start_date = #{idCardStartDate,jdbcType=DATE},
            </if>
            <if test="idCardEndDate != null">
                id_card_end_date = #{idCardEndDate,jdbcType=DATE},
            </if>
            <if test="nation != null">
                nation = #{nation,jdbcType=VARCHAR},
            </if>
            <if test="province != null">
                province = #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                city = #{city,jdbcType=VARCHAR},
            </if>
            <if test="area != null">
                area = #{area,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="workRoleId != null">
                work_role_id = #{workRoleId,jdbcType=INTEGER},
            </if>
            <if test="workRoleName != null">
                work_role_name = #{workRoleName,jdbcType=VARCHAR},
            </if>
            <if test="workTypeId != null">
                work_type_id = #{workTypeId,jdbcType=INTEGER},
            </if>
            <if test="workTypeName != null">
                work_type_name = #{workTypeName,jdbcType=VARCHAR},
            </if>
            <if test="leaderFlag != null">
                leader_flag = #{leaderFlag,jdbcType=INTEGER},
            </if>
            <if test="enterTime != null">
                enter_time = #{enterTime,jdbcType=DATE},
            </if>
            <if test="checkResult != null">
                check_result = #{checkResult,jdbcType=INTEGER},
            </if>
            <if test="checkName != null">
                check_name = #{checkName,jdbcType=VARCHAR},
            </if>
            <if test="checkTime != null">
                check_time = #{checkTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.whfc.emp.entity.AppEmpApply">
        <!--@mbg.generated-->
        update app_emp_apply
        set dept_id = #{deptId,jdbcType=INTEGER},
        group_id = #{groupId,jdbcType=INTEGER},
        group_name = #{groupName,jdbcType=VARCHAR},
        corp_id = #{corpId,jdbcType=INTEGER},
        corp_name = #{corpName,jdbcType=VARCHAR},
        emp_name = #{empName,jdbcType=VARCHAR},
        phone = #{phone,jdbcType=VARCHAR},
        gender = #{gender,jdbcType=INTEGER},
        birthday = #{birthday,jdbcType=DATE},
        id_card_no = #{idCardNo,jdbcType=VARCHAR},
        head_img = #{headImg,jdbcType=VARCHAR},
        avatar = #{avatar,jdbcType=VARCHAR},
        card_type = #{cardType,jdbcType=INTEGER},
        id_card_front = #{idCardFront,jdbcType=VARCHAR},
        id_card_back = #{idCardBack,jdbcType=VARCHAR},
        id_card_grant_org = #{idCardGrantOrg,jdbcType=VARCHAR},
        id_card_start_date = #{idCardStartDate,jdbcType=DATE},
        id_card_end_date = #{idCardEndDate,jdbcType=DATE},
        nation = #{nation,jdbcType=VARCHAR},
        province = #{province,jdbcType=VARCHAR},
        city = #{city,jdbcType=VARCHAR},
        area = #{area,jdbcType=VARCHAR},
        address = #{address,jdbcType=VARCHAR},
        work_role_id = #{workRoleId,jdbcType=INTEGER},
        work_role_name = #{workRoleName,jdbcType=VARCHAR},
        work_type_id = #{workTypeId,jdbcType=INTEGER},
        work_type_name = #{workTypeName,jdbcType=VARCHAR},
        leader_flag = #{leaderFlag,jdbcType=INTEGER},
        enter_time = #{enterTime,jdbcType=DATE},
        check_result = #{checkResult,jdbcType=INTEGER},
        check_name = #{checkName,jdbcType=VARCHAR},
        check_time = #{checkTime,jdbcType=TIMESTAMP},
        del_flag = #{delFlag,jdbcType=INTEGER},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        create_time = #{createTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <select id="selectByDeptId" resultType="com.whfc.emp.dto.AppEmpApplyDTO">
        SELECT
        id as empId,
        emp_name,
        birthday,
        group_name,
        work_type_name,
        gender,
        avatar,
        check_name,
        check_time,
        check_result,
        create_time
        FROM
        app_emp_apply
        WHERE
        del_flag = 0
        and dept_id = #{deptId}
        <if test="state != null">
            and check_result = #{state}
        </if>
        <if test="startTime != null and endTime != null">
            and create_time between #{startTime} and #{endTime}
        </if>
        order by id desc
    </select>
    <select id="selectNumBy" resultType="com.whfc.emp.dto.AppEmpApplyNumDTO">
        SELECT
        COUNT(0) as allNum,
        IFNULL(sum(if(check_result = 0,1,0)),0) as pendingNum,
        IFNULL(sum(if(check_result = 1,1,0)),0) as successNum,
        IFNULL(sum(if(check_result = 2,1,0)),0) as faiNum
        FROM
        app_emp_apply
        where
        del_flag = 0
        and dept_id = #{deptId}
        <if test="startTime != null and endTime != null">
            and create_time between #{startTime} and #{endTime}
        </if>
    </select>
    <update id="del">
        update app_emp_apply
        set del_flag = 1
        where id= #{applyEmpId}
        and   del_flag = 0
    </update>
    <select id="selectByDeptIdAndIdCardNo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM app_emp_apply
        WHERE check_result in (0,1)
        and id_card_no = #{idCardNo}
        and dept_id = #{deptId}
        and del_flag = 0
    </select>
</mapper>