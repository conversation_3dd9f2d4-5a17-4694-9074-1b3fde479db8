<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppHelmetFileMapper">
  <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppHelmetFile">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="emp_id" jdbcType="INTEGER" property="empId" />
    <result column="time" jdbcType="TIMESTAMP" property="time" />
    <result column="file_type" jdbcType="INTEGER" property="fileType" />
    <result column="file_url" jdbcType="VARCHAR" property="fileUrl" />
    <result column="lng" jdbcType="DOUBLE" property="lng" />
    <result column="lat" jdbcType="DOUBLE" property="lat" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id,
    dept_id,
    emp_id,
    `time`,
    file_type,
    file_url,
    lng,
    lat,
    del_flag,
    update_time,
    create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from app_helmet_file
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from app_helmet_file
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.whfc.emp.entity.AppHelmetFile">
    insert into app_helmet_file
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="empId != null">
        emp_id,
      </if>
      <if test="time != null">
        time,
      </if>
      <if test="fileType != null">
        file_type,
      </if>
      <if test="fileUrl != null">
        file_url,
      </if>
      <if test="lng != null">
        lng,
      </if>
      <if test="lat != null">
        lat,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="empId != null">
        #{empId,jdbcType=INTEGER},
      </if>
      <if test="time != null">
        #{time,jdbcType=TIMESTAMP},
      </if>
      <if test="fileType != null">
        #{fileType,jdbcType=INTEGER},
      </if>
      <if test="fileUrl != null">
        #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="lng != null">
        #{lng,jdbcType=DOUBLE},
      </if>
      <if test="lat != null">
        #{lat,jdbcType=DOUBLE},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppHelmetFile">
    update app_helmet_file
    <set>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="empId != null">
        emp_id = #{empId,jdbcType=INTEGER},
      </if>
      <if test="time != null">
        time = #{time,jdbcType=TIMESTAMP},
      </if>
      <if test="fileType != null">
        file_type = #{fileType,jdbcType=INTEGER},
      </if>
      <if test="fileUrl != null">
        file_url = #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="lng != null">
        lng = #{lng,jdbcType=DOUBLE},
      </if>
      <if test="lat != null">
        lat = #{lat,jdbcType=DOUBLE},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectFileList" resultType="com.whfc.emp.dto.helmet.AppHelmetFileDTO">
    select emp_id,
           `time`,
            lng,
            lat,
            file_type,
            file_url
    from app_helmet_file
    where dept_id = #{deptId}
      and del_flag = 0
      <if test="empId != null">
      and emp_id = #{empId}
      </if>
    order by `time` desc
  </select>

  <select id="selectLastFileTime" resultType="java.util.Date">
    select max(`time`)
    from app_helmet_file
    where emp_id = #{empId}
      and del_flag = 0
  </select>

  <insert id="batchInsert">
    insert into app_helmet_file
    (
        dept_id,
        emp_id,
        `time`,
        file_type,
        file_url,
        lng,
        lat
    )
    values
    <foreach collection="fileList" item="file" separator=",">
    (
        #{file.deptId},
        #{file.empId},
        #{file.time},
        #{file.fileType},
        #{file.fileUrl},
        #{file.lng},
        #{file.lat}
    )
    </foreach>
  </insert>
</mapper>