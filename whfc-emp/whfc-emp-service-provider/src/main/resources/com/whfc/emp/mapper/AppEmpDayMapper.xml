<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppEmpDayMapper">
    <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppEmpDay">
        <!--@mbg.generated-->
        <!--@Table app_emp_day-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="dept_id" jdbcType="INTEGER" property="deptId"/>
        <result column="emp_id" jdbcType="INTEGER" property="empId"/>
        <result column="work_role_id" jdbcType="INTEGER" property="workRoleId"/>
        <result column="date" jdbcType="DATE" property="date"/>
        <result column="warn_cnt" jdbcType="INTEGER" property="warnCnt"/>
        <result column="fall_cnt" jdbcType="INTEGER" property="fallCnt"/>
        <result column="drop_cnt" jdbcType="INTEGER" property="dropCnt"/>
        <result column="doff_cnt" jdbcType="INTEGER" property="doffCnt"/>
        <result column="attend_state" jdbcType="INTEGER" property="attendState"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="work_times" jdbcType="INTEGER" property="workTimes"/>
        <result column="helmet_start_time" jdbcType="TIMESTAMP" property="helmetStartTime"/>
        <result column="helmet_end_time" jdbcType="TIMESTAMP" property="helmetEndTime"/>
        <result column="helmet_times" jdbcType="INTEGER" property="helmetTimes"/>
        <result column="face_gate_start_time" jdbcType="TIMESTAMP" property="faceGateStartTime"/>
        <result column="face_gate_end_time" jdbcType="TIMESTAMP" property="faceGateEndTime"/>
        <result column="face_gate_times" jdbcType="INTEGER" property="faceGateTimes"/>
        <result column="attend_type" jdbcType="INTEGER" property="attendType"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, dept_id, emp_id, work_role_id, `date`, warn_cnt, fall_cnt, drop_cnt, doff_cnt,
        attend_state, start_time, end_time, work_times, helmet_start_time, helmet_end_time,
        helmet_times, face_gate_start_time, face_gate_end_time, face_gate_times, attend_type,
        update_time, create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from app_emp_day
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete from app_emp_day
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppEmpDay"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into app_emp_day (dept_id, emp_id, work_role_id,
        `date`, warn_cnt, fall_cnt,
        drop_cnt, doff_cnt, attend_state,
        start_time, end_time, work_times,
        helmet_start_time, helmet_end_time, helmet_times,
        face_gate_start_time, face_gate_end_time,
        face_gate_times, attend_type, update_time,
        create_time)
        values (#{deptId,jdbcType=INTEGER}, #{empId,jdbcType=INTEGER}, #{workRoleId,jdbcType=INTEGER},
        #{date,jdbcType=DATE}, #{warnCnt,jdbcType=INTEGER}, #{fallCnt,jdbcType=INTEGER},
        #{dropCnt,jdbcType=INTEGER}, #{doffCnt,jdbcType=INTEGER}, #{attendState,jdbcType=INTEGER},
        #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, #{workTimes,jdbcType=INTEGER},
        #{helmetStartTime,jdbcType=TIMESTAMP}, #{helmetEndTime,jdbcType=TIMESTAMP}, #{helmetTimes,jdbcType=INTEGER},
        #{faceGateStartTime,jdbcType=TIMESTAMP}, #{faceGateEndTime,jdbcType=TIMESTAMP},
        #{faceGateTimes,jdbcType=INTEGER}, #{attendType,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP},
        #{createTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppEmpDay"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into app_emp_day
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="empId != null">
                emp_id,
            </if>
            <if test="workRoleId != null">
                work_role_id,
            </if>
            <if test="date != null">
                `date`,
            </if>
            <if test="warnCnt != null">
                warn_cnt,
            </if>
            <if test="fallCnt != null">
                fall_cnt,
            </if>
            <if test="dropCnt != null">
                drop_cnt,
            </if>
            <if test="doffCnt != null">
                doff_cnt,
            </if>
            <if test="attendState != null">
                attend_state,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
            <if test="workTimes != null">
                work_times,
            </if>
            <if test="helmetStartTime != null">
                helmet_start_time,
            </if>
            <if test="helmetEndTime != null">
                helmet_end_time,
            </if>
            <if test="helmetTimes != null">
                helmet_times,
            </if>
            <if test="faceGateStartTime != null">
                face_gate_start_time,
            </if>
            <if test="faceGateEndTime != null">
                face_gate_end_time,
            </if>
            <if test="faceGateTimes != null">
                face_gate_times,
            </if>
            <if test="attendType != null">
                attend_type,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                #{deptId,jdbcType=INTEGER},
            </if>
            <if test="empId != null">
                #{empId,jdbcType=INTEGER},
            </if>
            <if test="workRoleId != null">
                #{workRoleId,jdbcType=INTEGER},
            </if>
            <if test="date != null">
                #{date,jdbcType=DATE},
            </if>
            <if test="warnCnt != null">
                #{warnCnt,jdbcType=INTEGER},
            </if>
            <if test="fallCnt != null">
                #{fallCnt,jdbcType=INTEGER},
            </if>
            <if test="dropCnt != null">
                #{dropCnt,jdbcType=INTEGER},
            </if>
            <if test="doffCnt != null">
                #{doffCnt,jdbcType=INTEGER},
            </if>
            <if test="attendState != null">
                #{attendState,jdbcType=INTEGER},
            </if>
            <if test="startTime != null">
                #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="workTimes != null">
                #{workTimes,jdbcType=INTEGER},
            </if>
            <if test="helmetStartTime != null">
                #{helmetStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="helmetEndTime != null">
                #{helmetEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="helmetTimes != null">
                #{helmetTimes,jdbcType=INTEGER},
            </if>
            <if test="faceGateStartTime != null">
                #{faceGateStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="faceGateEndTime != null">
                #{faceGateEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="faceGateTimes != null">
                #{faceGateTimes,jdbcType=INTEGER},
            </if>
            <if test="attendType != null">
                #{attendType,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppEmpDay">
        <!--@mbg.generated-->
        update app_emp_day
        <set>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=INTEGER},
            </if>
            <if test="empId != null">
                emp_id = #{empId,jdbcType=INTEGER},
            </if>
            <if test="workRoleId != null">
                work_role_id = #{workRoleId,jdbcType=INTEGER},
            </if>
            <if test="date != null">
                `date` = #{date,jdbcType=DATE},
            </if>
            <if test="warnCnt != null">
                warn_cnt = #{warnCnt,jdbcType=INTEGER},
            </if>
            <if test="fallCnt != null">
                fall_cnt = #{fallCnt,jdbcType=INTEGER},
            </if>
            <if test="dropCnt != null">
                drop_cnt = #{dropCnt,jdbcType=INTEGER},
            </if>
            <if test="doffCnt != null">
                doff_cnt = #{doffCnt,jdbcType=INTEGER},
            </if>
            <if test="attendState != null">
                attend_state = #{attendState,jdbcType=INTEGER},
            </if>
            <if test="startTime != null">
                start_time = #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                end_time = #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="workTimes != null">
                work_times = #{workTimes,jdbcType=INTEGER},
            </if>
            <if test="helmetStartTime != null">
                helmet_start_time = #{helmetStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="helmetEndTime != null">
                helmet_end_time = #{helmetEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="helmetTimes != null">
                helmet_times = #{helmetTimes,jdbcType=INTEGER},
            </if>
            <if test="faceGateStartTime != null">
                face_gate_start_time = #{faceGateStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="faceGateEndTime != null">
                face_gate_end_time = #{faceGateEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="faceGateTimes != null">
                face_gate_times = #{faceGateTimes,jdbcType=INTEGER},
            </if>
            <if test="attendType != null">
                attend_type = #{attendType,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.whfc.emp.entity.AppEmpDay">
        <!--@mbg.generated-->
        update app_emp_day
        set dept_id = #{deptId,jdbcType=INTEGER},
        emp_id = #{empId,jdbcType=INTEGER},
        work_role_id = #{workRoleId,jdbcType=INTEGER},
        `date` = #{date,jdbcType=DATE},
        warn_cnt = #{warnCnt,jdbcType=INTEGER},
        fall_cnt = #{fallCnt,jdbcType=INTEGER},
        drop_cnt = #{dropCnt,jdbcType=INTEGER},
        doff_cnt = #{doffCnt,jdbcType=INTEGER},
        attend_state = #{attendState,jdbcType=INTEGER},
        start_time = #{startTime,jdbcType=TIMESTAMP},
        end_time = #{endTime,jdbcType=TIMESTAMP},
        work_times = #{workTimes,jdbcType=INTEGER},
        helmet_start_time = #{helmetStartTime,jdbcType=TIMESTAMP},
        helmet_end_time = #{helmetEndTime,jdbcType=TIMESTAMP},
        helmet_times = #{helmetTimes,jdbcType=INTEGER},
        face_gate_start_time = #{faceGateStartTime,jdbcType=TIMESTAMP},
        face_gate_end_time = #{faceGateEndTime,jdbcType=TIMESTAMP},
        face_gate_times = #{faceGateTimes,jdbcType=INTEGER},
        attend_type = #{attendType,jdbcType=INTEGER},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        create_time = #{createTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <select id="selectEmpIdByCorpIdAndDeptId" resultType="java.lang.Integer">
      SELECT distinct ae.id
    FROM app_emp_day aed
    INNER JOIN app_emp ae ON ae.id=aed.emp_id
    WHERE ae.group_id=#{groupId}
    AND ae.dept_id=#{deptId}
      AND aed.`date`&gt;=#{monthBegin}
      AND #{monthEnd}&gt;=aed.`date`
  </select>
    <select id="selectAttendDaysByEmpId" resultType="java.lang.Integer">
    select count(1)
    from app_emp_day
    where emp_id=#{empId}
    and dept_id=#{deptId}
    and `date`&gt;=#{startTime}
    and #{endTime}&gt;=`date`
    and attend_state=1
  </select>
    <select id="selectAttendDayEmp" resultType="com.whfc.emp.dto.AttendDayEmpDTO">
        SELECT distinct
                ae.id AS empId,
                ae.emp_name,
                ae.phone AS empPhone,
                ae.dept_name,
                ae.corp_id,
                ae.corp_name,
                ae.group_id,
                aep.group_name,
                ae.work_role_name,
                aewt.name as work_type_name,
                aed.start_time,
                aed.end_time,
                aed.attend_state,
                round(1.0*aed.work_times/3600,2) AS workTimes
        FROM app_emp_day aed
        INNER JOIN app_emp ae ON ae.id=aed.emp_id
        LEFT OUTER JOIN app_emp_group aep on aep.id = ae.group_id
        LEFT OUTER JOIN app_emp_work_type aewt on aewt.id = ae.work_type_id
        WHERE ae.del_flag = 0
        and ae.post_state = 1
        and aed.dept_id=#{deptId}
        and aed.`date`=#{date}
        <if test="keyword != null and keyword !=''">
            and ae.emp_name like concat('%',#{keyword},'%')
        </if>
        <if test="attendState !=null">
            and aed.attend_state=#{attendState}
        </if>
        <if test="corpId !=null">
            and ae.corp_id=#{corpId}
        </if>
        <if test="groupId !=null">
            and ae.group_id=#{groupId}
        </if>
        order by aed.update_time desc
    </select>
    <select id="selectAttendMonthEmp" resultType="com.whfc.emp.dto.AppEmpAttendMonthDTO">
        SELECT DISTINCT
        ae.emp_name,
        ae.phone AS empPhone,
        ae.dept_name,
        ae.dept_id,
        ae.group_id,
        aep.group_name,
        ae.id AS empId
        FROM
        app_emp_day aed
        INNER JOIN app_emp ae ON ae.id = aed.emp_id
        INNER JOIN app_emp_group aep ON aep.id = ae.group_id
        where ae.del_flag = 0
        and aed.`date`&gt;=#{startTime}
        and #{endTime}&gt;=aed.`date`
        <if test="deptId != null ">
            and aed.dept_id=#{deptId}
        </if>
        <if test="keyword != null and keyword !=''">
            and ae.emp_name like concat('%',#{keyword},'%')
        </if>
        <if test="corpId != null">
            and ae.corp_id = #{corpId}
        </if>
        <if test="groupId != null">
            and ae.group_id = #{groupId}
        </if>

    </select>
    <select id="selectWorkTimesByEmpId" resultType="com.whfc.emp.dto.AppEmpDayWorkTimesDTO">
        select ifnull(round(1.0*work_times/3600,2),0) AS workTimes,
        `date`
        from app_emp_day
        where emp_id=#{empId}
        and `date`&gt;=#{startTime}
        and #{endTime}&gt;=`date`
    </select>

    <select id="selectEmpNumTotalPerDay" resultType="com.whfc.emp.dto.EmpDataDTO">
        SELECT aed.date,
        COUNT(aed.emp_id) as empTotal,
        SUM(IF(aed.attend_state = 1, 1, 0)) as attendNum
        FROM app_emp_day aed
        inner join app_emp ae on aed.emp_id = ae.id
        where aed.dept_id=#{deptId,jdbcType=INTEGER}
        and ae.del_flag = 0
        <![CDATA[
          and aed.date >=date(#{startDate,jdbcType=TIMESTAMP})
          and aed.date <=date(#{endDate,jdbcType=TIMESTAMP})
        ]]>
        GROUP BY aed.date
        order by aed.date desc
    </select>

    <select id="selectEmpNumTotalPerDayByWorkRole" resultType="com.whfc.emp.dto.EmpDataDTO">
        SELECT aed.date,ae.work_role_id,ae.work_role_name,
        IFNULL(COUNT(DISTINCT ae.id),0) empTotal,
        IFNULL(SUM(IF(aed.attend_state = 1, 1, 0)),0) attendNum
        FROM app_emp_day aed
        inner join app_emp ae on aed.emp_id = ae.id
        where aed.dept_id=#{deptId,jdbcType=INTEGER}
        and ae.del_flag = 0
        <![CDATA[
          and aed.date >=date(#{startDate,jdbcType=TIMESTAMP})
          and aed.date <=date(#{endDate,jdbcType=TIMESTAMP})
        ]]>
        GROUP BY aed.date,ae.work_role_id
        order by aed.date desc
    </select>

    <select id="selectEmpDayAttend" resultType="com.whfc.emp.dto.EmpDataDTO">
        SELECT aed.date,
               COUNT(aed.emp_id) as empTotal,
               SUM(IF(aed.attend_state = 1, 1, 0)) as attendNum
        FROM app_emp_day aed
        where aed.dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId}
        </foreach>
          )
        <![CDATA[
          and aed.date >=date(#{startDate,jdbcType=TIMESTAMP})
          and aed.date <=date(#{endDate,jdbcType=TIMESTAMP})
        ]]>
        GROUP BY aed.date
        order by aed.date
    </select>

    <select id="selectEmpMonthAttend" resultType="com.whfc.emp.dto.EmpDataDTO">
        SELECT DATE_FORMAT(aed.date,'%Y-%m') as `month`,
               COUNT(aed.emp_id) as empTotal,
               SUM(IF(aed.attend_state = 1, 1, 0)) as attendNum
         FROM app_emp_day aed
        where aed.dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId}
        </foreach>
        )
        <![CDATA[
          and aed.date >=date(#{startDate})
          and aed.date <=date(#{endDate})
        ]]>
        GROUP BY `month`
        order by `month`
    </select>

    <select id="selectEmpWeekAttend" resultType="com.whfc.emp.dto.EmpDataDTO">
         SELECT week(aed.date,1) as week,
                COUNT(aed.emp_id) as empTotal,
                SUM(IF(aed.attend_state = 1, 1, 0)) as attendNum
        FROM app_emp_day aed
        where aed.dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId}
        </foreach>
        )
        <![CDATA[
          and aed.date >=date(#{startDate})
          and aed.date <=date(#{endDate})
        ]]>
        GROUP BY week
        order by week
    </select>

    <select id="selectEmpProjectAttend" resultType="com.whfc.emp.dto.EmpDataDTO">
        SELECT dept_id,
               COUNT(aed.emp_id) as empTotal,
               SUM(IF(aed.attend_state = 1, 1, 0)) as attendNum
        FROM app_emp_day aed
        where aed.dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId}
        </foreach>
        )
        <![CDATA[
          and aed.date >=date(#{startDate})
          and aed.date <=date(#{endDate})
        ]]>
        GROUP BY aed.dept_id
        order by aed.dept_id
    </select>

    <select id="selectRecentAttendEmp" resultType="com.whfc.emp.dto.AppAnaEmpDTO">
        select ae.emp_name as name ,
               ae.avatar,
			   aed.start_time as `time`
          from app_emp_day aed
        inner join app_emp ae on aed.emp_id =ae.id
        where aed.dept_id = #{deptId}
          and aed.date = DATE_FORMAT(NOW(),'%Y-%m-%d')
          and aed.attend_state = 1
            order by start_time desc
    </select>
    <update id="updateAttendStateAndLocaleState">
        UPDATE app_emp_data
        SET attend_state = #{attendState},locale_state = #{localeState}
        WHERE emp_id = #{empId}
    </update>
    <update id="updateFaceGateTimes">
        update app_emp_day
        set face_gate_times = face_gate_times + #{times}
        where emp_id = #{empId}
        and `date` = #{date}
    </update>
    <update id="updateworkTimes">
        update app_emp_day
        set face_gate_times = face_gate_times + #{times},
        work_times = face_gate_times
        where emp_id = #{empId}
        and `date` = #{date}
    </update>
    <update id="updateLocaleState">
        update app_emp_data
        set locale_state = #{localeState}
        where emp_id = #{empId}
    </update>
    <update id="updateFaceGateEndTime">
        update app_emp_day
        set face_gate_end_time = #{faceGateEndTime},
        end_time = #{faceGateEndTime}
        where emp_id = #{empId}
        and `date` = date(#{date})
        and face_gate_start_time is not null
        and (face_gate_end_time is null or #{faceGateEndTime} &gt; face_gate_end_time)
    </update>
    <update id="updateAttend">
        update app_emp_day
        set attend_state = 1,
        attend_type = #{attendType}
        where emp_id = #{empId}
        and `date` = date(#{date})
        and attend_state = 0
    </update>
    <update id="updateFaceGateStartTime">
        update app_emp_day
        set face_gate_start_time = #{faceGateStartTime},
        start_time = #{faceGateStartTime}
        where emp_id = #{empId}
        and `date` = date(#{date})
        and (face_gate_start_time is null or face_gate_start_time &gt; #{faceGateStartTime})
    </update>
    <select id="selectByEmpIdAndDate" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from app_emp_day
        where emp_id = #{empId}
        and `date` = date(#{date})
    </select>
    <insert id="insertOrUpdate">
        insert into app_emp_day
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="empId != null">
                emp_id,
            </if>
            <if test="workRoleId != null">
                work_role_id,
            </if>
            <if test="date != null">
                `date`,
            </if>
            <if test="warnCnt != null">
                warn_cnt,
            </if>
            <if test="fallCnt != null">
                fall_cnt,
            </if>
            <if test="dropCnt != null">
                drop_cnt,
            </if>
            <if test="doffCnt != null">
                doff_cnt,
            </if>
            <if test="attendState != null">
                attend_state,
            </if>
            <if test="attendType != null">
                attend_type,
            </if>
            <if test="workTimes != null">
                work_times,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
            <if test="helmetStartTime != null">
                helmet_start_time,
            </if>
            <if test="helmetEndTime != null">
                helmet_end_time,
            </if>
            <if test="helmetTimes != null">
                helmet_times,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                #{deptId,jdbcType=INTEGER},
            </if>
            <if test="empId != null">
                #{empId,jdbcType=INTEGER},
            </if>
            <if test="workRoleId != null">
                #{workRoleId,jdbcType=INTEGER} ,
            </if>
            <if test="date != null">
                #{date,jdbcType=DATE},
            </if>
            <if test="warnCnt != null">
                #{warnCnt,jdbcType=INTEGER},
            </if>
            <if test="fallCnt != null">
                #{fallCnt,jdbcType=INTEGER},
            </if>
            <if test="dropCnt != null">
                #{dropCnt,jdbcType=INTEGER},
            </if>
            <if test="doffCnt != null">
                #{doffCnt,jdbcType=INTEGER},
            </if>
            <if test="attendState != null">
                #{attendState,jdbcType=INTEGER},
            </if>
            <if test="attendType != null">
                #{attendType,jdbcType=INTEGER},
            </if>
            <if test="workTimes != null">
                #{workTimes,jdbcType=DOUBLE},
            </if>
            <if test="startTime != null">
                #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="helmetStartTime != null">
                #{helmetStartTime} ,
            </if>
            <if test="helmetEndTime != null">
                #{helmetEndTime} ,
            </if>
            <if test="helmetTimes != null">
                #{helmetTimes} ,
            </if>
        </trim>
        on duplicate key update
        <if test="deptId != null">
            dept_id = #{deptId,jdbcType=INTEGER},
        </if>
        <if test="empId != null">
            emp_id = #{empId,jdbcType=INTEGER},
        </if>
        <if test="date != null">
            `date` = #{date,jdbcType=DATE},
        </if>
        <if test="warnCnt != null">
            warn_cnt = #{warnCnt,jdbcType=INTEGER},
        </if>
        <if test="fallCnt != null">
            fall_cnt = #{fallCnt,jdbcType=INTEGER},
        </if>
        <if test="dropCnt != null">
            drop_cnt = #{dropCnt,jdbcType=INTEGER},
        </if>
        <if test="doffCnt != null">
            doff_cnt = #{doffCnt,jdbcType=INTEGER},
        </if>
        <if test="attendState != null">
            attend_state = #{attendState,jdbcType=INTEGER},
        </if>
        <if test="attendType != null">
            attend_type = #{attendType,jdbcType=INTEGER},
        </if>
        <if test="workTimes != null">
            work_times = #{workTimes,jdbcType=DOUBLE},
        </if>
        <if test="startTime != null">
            start_time = #{startTime,jdbcType=TIMESTAMP},
        </if>
        <if test="endTime != null">
            end_time = #{endTime,jdbcType=TIMESTAMP},
        </if>
        <if test="helmetStartTime != null">
            helmet_start_time = #{helmetStartTime} ,
        </if>
        <if test="helmetEndTime != null">
            helmet_end_time = #{helmetEndTime} ,
        </if>
        <if test="helmetTimes != null">
            helmet_times = #{helmetTimes} ,
        </if>
        emp_id = #{empId},
        `date` = #{date}
    </insert>
    <insert id="batchInsert">
        insert ignore into app_emp_day
        (
        dept_id,
        emp_id,
        work_role_id,
        `date`
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.deptId} ,
            #{item.empId} ,
            #{item.workRoleId},
            #{item.date}
            )
        </foreach>
    </insert>
    <select id="selectWxEmpCurveMapDayDTOListInDept" resultType="com.whfc.emp.dto.WxEmpCurveMapDayDTO">
        select aea.date,
        ifnull(sum(aea.attend_state),0) as attendEmpNum,
        round(ifnull(sum(work_times)/3600,0),2) AS workTimes,
        ifnull(sum(aea.warn_cnt),0) as warn_cnt,
        count(1) as totalEmpNum
        from app_emp_day aea
        inner join app_emp ae on ae.id=aea.emp_id
        where aea.dept_id  =  #{deptId}
        <![CDATA[
     and aea.date >= date(#{startDate})
     and aea.date <= date(#{endDate})
     ]]>
        group by aea.date
        order by aea.date
    </select>
    <select id="adsqMonthStatistics" resultType="com.whfc.entity.dto.adsq.AdsqStatisticsItemDTO">
        SELECT DATE_FORMAT(ed.date, '%m-%d') AS time,ed.date AS date, SUM(ed.attend_state) AS value
        FROM app_emp_day ed
        LEFT JOIN app_emp e ON e.id = ed.emp_id
        WHERE e.del_flag = 0
        AND e.post_state = 1
        AND ed.dept_id = #{deptId}
        <if test="groupId != null">
            AND e.group_id = #{groupId}
        </if>
        AND DATE_FORMAT(date, '%Y-%m') = DATE_FORMAT(#{time}, '%Y-%m')
        GROUP BY date
    </select>
    <update id="updateStartTime">
        update app_emp_day
        set start_time = #{date},
        attend_state = 1
        where emp_id = #{empId}
        and DATE_FORMAT(date, '%Y-%m-%d') = DATE_FORMAT(#{date}, '%Y-%m-%d')
    </update>
    <update id="updateEndTime">
        update app_emp_day
        set end_time = #{date},
        work_times = IF( ifnull( start_time, 0 )= 0, 0,  work_times+ round( UNIX_TIMESTAMP (#{date}) - UNIX_TIMESTAMP(start_time), 2))
        where emp_id = #{empId}
        and DATE_FORMAT(date, '%Y-%m-%d') = DATE_FORMAT(#{date}, '%Y-%m-%d')
    </update>
    <select id="selectEmpDayListByDeptIdAndDate" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from app_emp_day
        where dept_id =#{deptId}
        and `date` = date(#{date})
    </select>
    <select id="countEmpTotal" resultType="java.lang.Integer">
        SELECT
	    COUNT(0) as empTotal
        FROM
	    app_emp_day aed
	    where
	    aed.dept_id=#{deptId}
	    and aed.date = date(#{date})
    </select>

    <select id="selectEmpLogByWorkRole" resultType="com.whfc.emp.dto.AppEmpWorkRoleNumDTO">
        SELECT
            ae.work_role_id,
            ae.work_role_name,
            COUNT(aed.id) num,
            IFNULL(SUM(aed.attend_state),0) attendNum
        FROM
            app_emp ae
        LEFT JOIN app_emp_day aed ON ae.id = aed.emp_id
        where
        aed.dept_id=#{deptId}
        and aed.date = date(#{date})
        AND ae.post_state = 1
        GROUP BY
        ae.work_role_id
    </select>

    <select id="selectAttendDayEmpNum" resultType="com.whfc.emp.dto.AppEmpAttendDayNumDTO">
        SELECT
        COUNT( 0 ) AS empAllNum,
        IFNULL( SUM( IF ( aed.attend_state = 1,1,0 )), 0 ) AS empAttendNum,
        IFNULL( SUM( IF ( aed.attend_state = 0,1,0 )), 0 ) AS empNoAttendNum
        FROM
        ( select attend_state,emp_id from app_emp_day where dept_id= #{deptId}  and `date`= #{date}) aed
        INNER JOIN app_emp ae ON ae.id = aed.emp_id
        WHERE
        ae.del_flag = 0
        and ae.post_state = 1
        <if test="keyword != null and keyword !=''">
            and ae.emp_name like concat('%',#{keyword},'%')
        </if>
        <if test="empId != null">
            and ae.id=#{empId}
        </if>
        <if test="corpId !=null">
            and ae.corp_id=#{corpId}
        </if>
        <if test="groupId !=null">
            and ae.group_id=#{groupId}
        </if>
    </select>

    <select id="selectAttendTimeStat" resultType="com.whfc.emp.dto.AppEmpAttendTimeDTO">
        select DATE_FORMAT(start_time,'%H') as `time`,
               count(*) as num
        from app_emp_day
        where dept_id = #{deptId}
          and `date`= #{date}
          and attend_state = 1
        GROUP BY `time`
    </select>
</mapper>