<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppBroadcastRecordEmpMapper">
  <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppBroadcastRecordEmp">
    <!--@mbg.generated-->
    <!--@Table app_broadcast_record_emp-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="record_id" jdbcType="INTEGER" property="recordId" />
    <result column="emp_id" jdbcType="INTEGER" property="empId" />
    <result column="device_id" jdbcType="INTEGER" property="deviceId" />
    <result column="send_time" jdbcType="TIMESTAMP" property="sendTime" />
    <result column="retry_cnt" jdbcType="INTEGER" property="retryCnt" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, record_id, emp_id, device_id, send_time, retry_cnt, `state`, del_flag, update_time, 
    create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from app_broadcast_record_emp
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from app_broadcast_record_emp
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppBroadcastRecordEmp" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_broadcast_record_emp (record_id, emp_id, device_id, 
      send_time, retry_cnt, `state`, 
      del_flag, update_time, create_time
      )
    values (#{recordId,jdbcType=INTEGER}, #{empId,jdbcType=INTEGER}, #{deviceId,jdbcType=INTEGER}, 
      #{sendTime,jdbcType=TIMESTAMP}, #{retryCnt,jdbcType=INTEGER}, #{state,jdbcType=INTEGER}, 
      #{delFlag,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppBroadcastRecordEmp" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_broadcast_record_emp
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="recordId != null">
        record_id,
      </if>
      <if test="empId != null">
        emp_id,
      </if>
      <if test="deviceId != null">
        device_id,
      </if>
      <if test="sendTime != null">
        send_time,
      </if>
      <if test="retryCnt != null">
        retry_cnt,
      </if>
      <if test="state != null">
        `state`,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="recordId != null">
        #{recordId,jdbcType=INTEGER},
      </if>
      <if test="empId != null">
        #{empId,jdbcType=INTEGER},
      </if>
      <if test="deviceId != null">
        #{deviceId,jdbcType=INTEGER},
      </if>
      <if test="sendTime != null">
        #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="retryCnt != null">
        #{retryCnt,jdbcType=INTEGER},
      </if>
      <if test="state != null">
        #{state,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppBroadcastRecordEmp">
    <!--@mbg.generated-->
    update app_broadcast_record_emp
    <set>
      <if test="recordId != null">
        record_id = #{recordId,jdbcType=INTEGER},
      </if>
      <if test="empId != null">
        emp_id = #{empId,jdbcType=INTEGER},
      </if>
      <if test="deviceId != null">
        device_id = #{deviceId,jdbcType=INTEGER},
      </if>
      <if test="sendTime != null">
        send_time = #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="retryCnt != null">
        retry_cnt = #{retryCnt,jdbcType=INTEGER},
      </if>
      <if test="state != null">
        `state` = #{state,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.whfc.emp.entity.AppBroadcastRecordEmp">
    <!--@mbg.generated-->
    update app_broadcast_record_emp
    set record_id = #{recordId,jdbcType=INTEGER},
      emp_id = #{empId,jdbcType=INTEGER},
      device_id = #{deviceId,jdbcType=INTEGER},
      send_time = #{sendTime,jdbcType=TIMESTAMP},
      retry_cnt = #{retryCnt,jdbcType=INTEGER},
      `state` = #{state,jdbcType=INTEGER},
      del_flag = #{delFlag,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateState">
    UPDATE app_broadcast_record_emp
    SET state = #{state}
    WHERE id = #{id}
  </update>
  <select id="selectUndoneByDeviceId" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM app_broadcast_record_emp
    WHERE del_flag = 0
    AND device_id = #{deviceId}
    AND state IN (0,1)
    ORDER BY send_time DESC
  </select>
  <select id="selectUndoneRecordEmpList" resultType="com.whfc.emp.dto.AppBroadcastRecordEmpDTO">
    SELECT
    <include refid="Base_Column_List"/>
    FROM app_broadcast_record_emp
    WHERE del_flag = 0
    AND state IN (0,1)
    ORDER BY send_time DESC
  </select>

  <update id="logicDelId">
    UPDATE app_broadcast_record_emp SET del_flag = 1 WHERE id = #{id}
    </update>

  <select id="selectBroadcastRecordList" resultType="com.whfc.emp.dto.AppBroadcastRecordEmpDTO">
    SELECT
     abre.id,
     abre.state,
     abre.send_time,
     abr.send_user_name,
     abr.content,
     abr.type
    FROM app_broadcast_record_emp abre
    LEFT JOIN app_broadcast_record abr ON abre.record_id = abr.id
    WHERE abre.del_flag = 0
    AND abr.del_flag = 0
    AND abre.emp_id = #{empId}
    ORDER BY send_time DESC
  </select>
</mapper>