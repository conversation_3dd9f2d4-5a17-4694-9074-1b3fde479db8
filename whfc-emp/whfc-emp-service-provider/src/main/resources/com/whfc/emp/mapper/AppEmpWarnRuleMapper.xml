<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppEmpWarnRuleMapper">
  <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppEmpWarnRule">
    <!--@mbg.generated-->
    <!--@Table app_emp_warn_rule-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="rule_type" jdbcType="INTEGER" property="ruleType" />
    <result column="warn_type" jdbcType="INTEGER" property="warnType" />
    <result column="rule_param" jdbcType="VARCHAR" property="ruleParam" />
    <result column="enable_flag" jdbcType="INTEGER" property="enableFlag" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `name`, dept_id, rule_type, warn_type, rule_param, enable_flag, del_flag, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from app_emp_warn_rule
    where id = #{id,jdbcType=INTEGER}
  </select>

  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppEmpWarnRule" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_emp_warn_rule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        `name`,
      </if>
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="ruleType != null">
        rule_type,
      </if>
      <if test="warnType != null">
        warn_type,
      </if>
      <if test="ruleParam != null">
        rule_param,
      </if>
      <if test="enableFlag != null">
        enable_flag,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="ruleType != null">
        #{ruleType,jdbcType=INTEGER},
      </if>
      <if test="warnType != null">
        #{warnType,jdbcType=INTEGER},
      </if>
      <if test="ruleParam != null">
        #{ruleParam,jdbcType=VARCHAR},
      </if>
      <if test="enableFlag != null">
        #{enableFlag,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppEmpWarnRule">
    <!--@mbg.generated-->
    update app_emp_warn_rule
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="ruleType != null">
        rule_type = #{ruleType,jdbcType=INTEGER},
      </if>
      <if test="warnType != null">
        warn_type = #{warnType,jdbcType=INTEGER},
      </if>
      <if test="ruleParam != null">
        rule_param = #{ruleParam,jdbcType=VARCHAR},
      </if>
      <if test="enableFlag != null">
        enable_flag = #{enableFlag,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByRuleType" resultType="java.lang.Integer">
    select id
    from app_emp_warn_rule
    where rule_type = #{ruleType}
    and dept_id = #{deptId}
    and del_flag = 0
    and enable_flag = 1
  </select>

  <select id="selectRulesByDeptId" resultType="com.whfc.emp.dto.AppWarnRuleDTO">
    select id as ruleId,
            `name`,
            warn_type,
            rule_param,
            rule_type,
            enable_flag
    from app_emp_warn_rule
    where del_flag = 0
    and dept_id = #{deptId}
    and warn_type = #{warnType}
    order by id desc
  </select>

  <select id="selectRuleList" resultType="com.whfc.emp.dto.EmpWarnRuleDTO">
    select id as rule_id,
           rule_type,
           rule_param,
           name,
           enable_flag
      from app_emp_warn_rule
     where dept_id = #{deptId}
       and del_flag = 0
       and enable_flag = 1
  </select>

  <insert id="batchInsert">
    INSERT INTO app_emp_warn_rule
    (
    `name`,
    dept_id,
    rule_type
    )
    values
    <foreach collection="list" item="item" separator="," >
      (
      #{item.name},
      #{item.deptId},
      #{item.ruleType}
      )
    </foreach>
  </insert>

  <update id="updateEnableFlagByRuleId">
    update app_emp_warn_rule
    set enable_flag = #{enableFlag}
    where id = #{ruleId}
  </update>

  <update id="deleteLogic">
    update app_emp_warn_rule
    set del_flag = 1
    where id = #{ruleId}
  </update>
</mapper>