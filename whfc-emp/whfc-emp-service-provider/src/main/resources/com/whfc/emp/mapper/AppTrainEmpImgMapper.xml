<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppTrainEmpImgMapper">
  <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppTrainEmpImg">
    <!--@mbg.generated-->
    <!--@Table app_train_emp_img-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="train_emp_id" jdbcType="INTEGER" property="trainEmpId" />
    <result column="img_url" jdbcType="VARCHAR" property="imgUrl" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, train_emp_id, img_url, del_flag, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from app_train_emp_img
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from app_train_emp_img
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppTrainEmpImg" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_train_emp_img (train_emp_id, img_url, del_flag, 
      update_time, create_time)
    values (#{trainEmpId,jdbcType=INTEGER}, #{imgUrl,jdbcType=VARCHAR}, #{delFlag,jdbcType=INTEGER}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppTrainEmpImg" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_train_emp_img
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="trainEmpId != null">
        train_emp_id,
      </if>
      <if test="imgUrl != null">
        img_url,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="trainEmpId != null">
        #{trainEmpId,jdbcType=INTEGER},
      </if>
      <if test="imgUrl != null">
        #{imgUrl,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppTrainEmpImg">
    <!--@mbg.generated-->
    update app_train_emp_img
    <set>
      <if test="trainEmpId != null">
        train_emp_id = #{trainEmpId,jdbcType=INTEGER},
      </if>
      <if test="imgUrl != null">
        img_url = #{imgUrl,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.whfc.emp.entity.AppTrainEmpImg">
    <!--@mbg.generated-->
    update app_train_emp_img
    set train_emp_id = #{trainEmpId,jdbcType=INTEGER},
      img_url = #{imgUrl,jdbcType=VARCHAR},
      del_flag = #{delFlag,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectImgUrlList" resultType="java.lang.String">
    SELECT img_url
    FROM app_train_emp_img
    WHERE del_flag = 0
    AND train_emp_id = #{trainEmpId}
    </select>

  <update id="logicDel">
    UPDATE app_train_emp_img SET del_flag = 1 WHERE  train_emp_id = #{trainEmpId}
    </update>
</mapper>