<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppFaceGateVisitorMapper">
    <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppFaceGateVisitor">
        <!--@mbg.generated-->
        <!--@Table app_face_gate_visitor-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="open_id" jdbcType="VARCHAR" property="openId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="picture_url" jdbcType="VARCHAR" property="pictureUrl"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="idCardNo" jdbcType="VARCHAR" property="idCardNo"/>
        <result column="face_gate_id" jdbcType="INTEGER" property="faceGateId"/>
        <result column="visitors_type" jdbcType="INTEGER" property="visitorsType"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="state" jdbcType="INTEGER" property="state"/>
        <result column="check_result" jdbcType="INTEGER" property="checkResult"/>
        <result column="check_name" jdbcType="VARCHAR" property="checkName"/>
        <result column="check_time" jdbcType="TIMESTAMP" property="checkTime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        open_id,
        `name`,
        picture_url,
        phone,
        id_card_no,
        face_gate_id,
        visitors_type,
        start_time,
        end_time,
        `state`,
        check_result,
        check_name,
        check_time,
        remark,
        del_flag,
        update_time,
        create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from app_face_gate_visitor
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete from app_face_gate_visitor
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppFaceGateVisitor"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into app_face_gate_visitor (
        open_id,
        `name`,
        picture_url,
        phone,
        id_card_no,
        face_gate_id,
        visitors_type,
        start_time,
        end_time,
        `state`,
        check_result,
        check_name,
        check_time,
        remark,
        del_flag,
        update_time,
        create_time
        )
        values (
        #{openId,jdbcType=VARCHAR},
        #{name,jdbcType=VARCHAR},
        #{pictureUrl,jdbcType=VARCHAR},
        #{phone,jdbcType=VARCHAR},
        #{idCardNo,jdbcType=VARCHAR},
        #{faceGateId,jdbcType=INTEGER},
        #{visitorsType,jdbcType=INTEGER},
        #{startTime,jdbcType=TIMESTAMP},
        #{endTime,jdbcType=TIMESTAMP},
        #{state,jdbcType=INTEGER},
        #{checkResult,jdbcType=INTEGER},
        #{checkName,jdbcType=VARCHAR},
        #{checkTime,jdbcType=TIMESTAMP},
        #{remark,jdbcType=VARCHAR},
        #{delFlag,jdbcType=INTEGER},
        #{updateTime,jdbcType=TIMESTAMP},
        #{createTime,jdbcType=TIMESTAMP}
        )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppFaceGateVisitor"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into app_face_gate_visitor
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="openId != null">
                open_id,
            </if>
            <if test="name != null">
                `name`,
            </if>
            <if test="pictureUrl != null">
                picture_url,
            </if>
            <if test="phone != null">
                phone,
            </if>
            <if test="idCardNo != null">
                id_card_no,
            </if>
            <if test="faceGateId != null">
                face_gate_id,
            </if>
            <if test="visitorsType != null">
                visitors_type,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
            <if test="state != null">
                `state`,
            </if>
            <if test="checkResult != null">
                check_result,
            </if>
            <if test="checkName != null">
                check_name,
            </if>
            <if test="checkTime != null">
                check_time,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="openId != null">
                #{openId,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="pictureUrl != null">
                #{pictureUrl,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="idCardNo != null">
                #{idCardNo,jdbcType=VARCHAR},
            </if>
            <if test="faceGateId != null">
                #{faceGateId,jdbcType=INTEGER},
            </if>
            <if test="visitorsType != null">
                #{visitorsType,jdbcType=INTEGER},
            </if>
            <if test="startTime != null">
                #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="state != null">
                #{state,jdbcType=INTEGER},
            </if>
            <if test="checkResult != null">
                #{checkResult,jdbcType=INTEGER},
            </if>
            <if test="checkName != null">
                #{checkName,jdbcType=VARCHAR},
            </if>
            <if test="checkTime != null">
                #{checkTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppFaceGateVisitor">
        <!--@mbg.generated-->
        update app_face_gate_visitor
        <set>
            <if test="openId != null">
                open_id = #{openId,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="pictureUrl != null">
                picture_url = #{pictureUrl,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="idCardNo != null">
                id_card_no = #{idCardNo,jdbcType=VARCHAR},
            </if>
            <if test="faceGateId != null">
                face_gate_id = #{faceGateId,jdbcType=INTEGER},
            </if>
            <if test="visitorsType != null">
                visitors_type = #{visitorsType,jdbcType=INTEGER},
            </if>
            <if test="startTime != null">
                start_time = #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                end_time = #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="state != null">
                `state` = #{state,jdbcType=INTEGER},
            </if>
            <if test="checkResult != null">
                check_result = #{checkResult,jdbcType=INTEGER},
            </if>
            <if test="checkName != null">
                check_name = #{checkName,jdbcType=VARCHAR},
            </if>
            <if test="checkTime != null">
                check_time = #{checkTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.whfc.emp.entity.AppFaceGateVisitor">
        <!--@mbg.generated-->
        update app_face_gate_visitor
        set open_id = #{openId,jdbcType=VARCHAR},
        `name` = #{name,jdbcType=VARCHAR},
        picture_url = #{pictureUrl,jdbcType=VARCHAR},
        phone = #{phone,jdbcType=VARCHAR},
        id_card_no = #{idCardNo,jdbcType=VARCHAR},
        face_gate_id = #{faceGateId,jdbcType=INTEGER},
        visitors_type = #{visitorsType,jdbcType=INTEGER},
        start_time = #{startTime,jdbcType=TIMESTAMP},
        end_time = #{endTime,jdbcType=TIMESTAMP},
        `state` = #{state,jdbcType=INTEGER},
        check_result = #{checkResult,jdbcType=INTEGER},
        check_name = #{checkName,jdbcType=VARCHAR},
        check_time = #{checkTime,jdbcType=TIMESTAMP},
        remark = #{remark,jdbcType=VARCHAR},
        del_flag = #{delFlag,jdbcType=INTEGER},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        create_time = #{createTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectBYDeptId" resultType="com.whfc.emp.dto.AppFaceGateVisitorDTO">
        SELECT
        afgv.`name`,
        afgv.phone,
        afgv.id_card_no,
        afg.`name` AS faceGateName,
        afgv.picture_url,
        afgv.visitors_type,
        afgv.remark,
        afgv.create_time,
        afgv.state,
        afgv.check_name,
        afgv.check_time,
        afgv.id AS visitorId
        FROM
        app_face_gate_visitor afgv
        INNER JOIN app_face_gate afg ON afg.id = afgv.face_gate_id
        WHERE
        afg.dept_id = #{deptId}
        and afgv.del_flag=0
        <if test="state != null">
            AND afgv.state = #{state}
        </if>
        <if test="startTime != null and endTime != null">
            AND afgv.create_time BETWEEN #{startTime} AND #{endTime}
        </if>
        order by afgv.create_time desc
    </select>

    <update id="updateDelByVisitorId">
        update app_face_gate_visitor
        set del_flag = 1
        where
        del_flag = 0
        and id = #{visitorId}
    </update>

    <select id="selectDayLaborerByOpenIdAndFaceGateId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        app_face_gate_visitor
        where
        visitors_type = 2
        and face_gate_id = #{faceGateId}
        and open_id = #{openid}
        ORDER BY update_time desc
        LIMIT 1
    </select>

    <select id="selectOverdue" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        app_face_gate_visitor
        where
        state = 1
        and del_flag = 0
        and end_time = date(#{time})
        ORDER BY update_time desc
    </select>
</mapper>