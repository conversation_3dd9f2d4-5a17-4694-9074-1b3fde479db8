<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppEmpConfessMapper">
  <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppEmpConfess">
    <!--@mbg.generated-->
    <!--@Table app_emp_confess-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="emp_id" jdbcType="INTEGER" property="empId" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="corp_id" jdbcType="INTEGER" property="corpId" />
    <result column="corp_name" jdbcType="VARCHAR" property="corpName" />
    <result column="project_part" jdbcType="VARCHAR" property="projectPart" />
    <result column="principal_id" jdbcType="INTEGER" property="principalId" />
    <result column="principal_name" jdbcType="VARCHAR" property="principalName" />
    <result column="confidant_id" jdbcType="INTEGER" property="confidantId" />
    <result column="confidant_name" jdbcType="VARCHAR" property="confidantName" />
    <result column="be_confidant_id" jdbcType="INTEGER" property="beConfidantId" />
    <result column="be_confidant_name" jdbcType="VARCHAR" property="beConfidantName" />
    <result column="sign_img_url" jdbcType="VARCHAR" property="signImgUrl" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, emp_id, project_name, `date`, corp_id, corp_name, project_part, principal_id, 
    principal_name, confidant_id, confidant_name, be_confidant_id, be_confidant_name, 
    sign_img_url, del_flag, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from app_emp_confess
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from app_emp_confess
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppEmpConfess" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_emp_confess (emp_id, project_name, `date`, 
      corp_id, corp_name, project_part, 
      principal_id, principal_name, confidant_id, 
      confidant_name, be_confidant_id, be_confidant_name, 
      sign_img_url, del_flag, update_time, 
      create_time)
    values (#{empId,jdbcType=INTEGER}, #{projectName,jdbcType=VARCHAR}, #{date,jdbcType=DATE}, 
      #{corpId,jdbcType=INTEGER}, #{corpName,jdbcType=VARCHAR}, #{projectPart,jdbcType=VARCHAR}, 
      #{principalId,jdbcType=INTEGER}, #{principalName,jdbcType=VARCHAR}, #{confidantId,jdbcType=INTEGER}, 
      #{confidantName,jdbcType=VARCHAR}, #{beConfidantId,jdbcType=INTEGER}, #{beConfidantName,jdbcType=VARCHAR}, 
      #{signImgUrl,jdbcType=VARCHAR}, #{delFlag,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppEmpConfess" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into app_emp_confess
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="empId != null">
        emp_id,
      </if>
      <if test="projectName != null">
        project_name,
      </if>
      <if test="date != null">
        `date`,
      </if>
      <if test="corpId != null">
        corp_id,
      </if>
      <if test="corpName != null">
        corp_name,
      </if>
      <if test="projectPart != null">
        project_part,
      </if>
      <if test="principalId != null">
        principal_id,
      </if>
      <if test="principalName != null">
        principal_name,
      </if>
      <if test="confidantId != null">
        confidant_id,
      </if>
      <if test="confidantName != null">
        confidant_name,
      </if>
      <if test="beConfidantId != null">
        be_confidant_id,
      </if>
      <if test="beConfidantName != null">
        be_confidant_name,
      </if>
      <if test="signImgUrl != null">
        sign_img_url,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="empId != null">
        #{empId,jdbcType=INTEGER},
      </if>
      <if test="projectName != null">
        #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="date != null">
        #{date,jdbcType=DATE},
      </if>
      <if test="corpId != null">
        #{corpId,jdbcType=INTEGER},
      </if>
      <if test="corpName != null">
        #{corpName,jdbcType=VARCHAR},
      </if>
      <if test="projectPart != null">
        #{projectPart,jdbcType=VARCHAR},
      </if>
      <if test="principalId != null">
        #{principalId,jdbcType=INTEGER},
      </if>
      <if test="principalName != null">
        #{principalName,jdbcType=VARCHAR},
      </if>
      <if test="confidantId != null">
        #{confidantId,jdbcType=INTEGER},
      </if>
      <if test="confidantName != null">
        #{confidantName,jdbcType=VARCHAR},
      </if>
      <if test="beConfidantId != null">
        #{beConfidantId,jdbcType=INTEGER},
      </if>
      <if test="beConfidantName != null">
        #{beConfidantName,jdbcType=VARCHAR},
      </if>
      <if test="signImgUrl != null">
        #{signImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppEmpConfess">
    <!--@mbg.generated-->
    update app_emp_confess
    <set>
      <if test="empId != null">
        emp_id = #{empId,jdbcType=INTEGER},
      </if>
      <if test="projectName != null">
        project_name = #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="date != null">
        `date` = #{date,jdbcType=DATE},
      </if>
      <if test="corpId != null">
        corp_id = #{corpId,jdbcType=INTEGER},
      </if>
      <if test="corpName != null">
        corp_name = #{corpName,jdbcType=VARCHAR},
      </if>
      <if test="projectPart != null">
        project_part = #{projectPart,jdbcType=VARCHAR},
      </if>
      <if test="principalId != null">
        principal_id = #{principalId,jdbcType=INTEGER},
      </if>
      <if test="principalName != null">
        principal_name = #{principalName,jdbcType=VARCHAR},
      </if>
      <if test="confidantId != null">
        confidant_id = #{confidantId,jdbcType=INTEGER},
      </if>
      <if test="confidantName != null">
        confidant_name = #{confidantName,jdbcType=VARCHAR},
      </if>
      <if test="beConfidantId != null">
        be_confidant_id = #{beConfidantId,jdbcType=INTEGER},
      </if>
      <if test="beConfidantName != null">
        be_confidant_name = #{beConfidantName,jdbcType=VARCHAR},
      </if>
      <if test="signImgUrl != null">
        sign_img_url = #{signImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.whfc.emp.entity.AppEmpConfess">
    <!--@mbg.generated-->
    update app_emp_confess
    set emp_id = #{empId,jdbcType=INTEGER},
      project_name = #{projectName,jdbcType=VARCHAR},
      `date` = #{date,jdbcType=DATE},
      corp_id = #{corpId,jdbcType=INTEGER},
      corp_name = #{corpName,jdbcType=VARCHAR},
      project_part = #{projectPart,jdbcType=VARCHAR},
      principal_id = #{principalId,jdbcType=INTEGER},
      principal_name = #{principalName,jdbcType=VARCHAR},
      confidant_id = #{confidantId,jdbcType=INTEGER},
      confidant_name = #{confidantName,jdbcType=VARCHAR},
      be_confidant_id = #{beConfidantId,jdbcType=INTEGER},
      be_confidant_name = #{beConfidantName,jdbcType=VARCHAR},
      sign_img_url = #{signImgUrl,jdbcType=VARCHAR},
      del_flag = #{delFlag,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByEmpId" resultType="com.whfc.emp.dto.AppEmpConfessDTO">
    SELECT id AS confessId,
    emp_id,
    project_name,
    `date`,
    corp_id,
    corp_name,
    project_part,
    principal_id,
    principal_name,
    confidant_id,
    confidant_name,
    be_confidant_id,
    be_confidant_name,
    sign_img_url
    FROM app_emp_confess
    WHERE del_flag = 0
    AND emp_id = #{empId}
  </select>

  <update id="logicDel">
    UPDATE app_emp_confess SET del_flag = 1 WHERE id = #{confessId}
  </update>

  <update id="updateSignImg">
    UPDATE app_emp_confess SET sign_img_url = #{imgUrl} WHERE id = #{confessId}
  </update>
</mapper>