<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppEmpGroupMapper">
    <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppEmpGroup">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="dept_id" jdbcType="INTEGER" property="deptId"/>
        <result column="corp_id" jdbcType="INTEGER" property="corpId"/>
        <result column="corp_code" jdbcType="VARCHAR" property="corpCode"/>
        <result column="corp_name" jdbcType="VARCHAR" property="corpName"/>
        <result column="group_name" jdbcType="VARCHAR" property="groupName"/>
        <result column="group_code" jdbcType="VARCHAR" property="groupCode"/>
        <result column="responsible_person_name" jdbcType="VARCHAR" property="responsiblePersonName"/>
        <result column="responsible_person_phone" jdbcType="VARCHAR" property="responsiblePersonPhone"/>
        <result column="responsible_idcard_type" jdbcType="INTEGER" property="responsibleIdcardType"/>
        <result column="responsible_idcard_number" jdbcType="VARCHAR" property="responsibleIdcardNumber"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="entry_time" jdbcType="DATE" property="entryTime"/>
        <result column="exit_time" jdbcType="DATE" property="exitTime"/>
        <result column="fvs_device_id" jdbcType="INTEGER" property="fvsDeviceId"/>
        <result column="fvs_device_name" jdbcType="VARCHAR" property="fvsDeviceName"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, dept_id, corp_id, corp_code, corp_name, group_name, group_code, responsible_person_name,
        responsible_person_phone, responsible_idcard_type, responsible_idcard_number, remark,
        entry_time, exit_time, fvs_device_id, fvs_device_name, del_flag,
        update_time, create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from app_emp_group
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete
        from app_emp_group
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppEmpGroup"
            useGeneratedKeys="true">
        insert into app_emp_group (dept_id, corp_id, corp_code,
                                   corp_name, group_name, group_code,
                                   responsible_person_name, responsible_person_phone,
                                   responsible_idcard_type, responsible_idcard_number,
                                   remark, entry_time, exit_time,
                                   fvs_device_id, fvs_device_name, del_flag,
                                   update_time, create_time)
        values (#{deptId,jdbcType=INTEGER}, #{corpId,jdbcType=INTEGER}, #{corpCode,jdbcType=VARCHAR},
                #{corpName,jdbcType=VARCHAR}, #{groupName,jdbcType=VARCHAR}, #{groupCode,jdbcType=VARCHAR},
                #{responsiblePersonName,jdbcType=VARCHAR}, #{responsiblePersonPhone,jdbcType=VARCHAR},
                #{responsibleIdcardType,jdbcType=INTEGER}, #{responsibleIdcardNumber,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR}, #{entryTime,jdbcType=DATE}, #{exitTime,jdbcType=DATE},
                #{fvsDeviceId,jdbcType=INTEGER}, #{fvsDeviceName,jdbcType=VARCHAR},  #{delFlag,jdbcType=INTEGER},
                #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppEmpGroup"
            useGeneratedKeys="true">
        insert into app_emp_group
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="corpId != null">
                corp_id,
            </if>
            <if test="corpCode != null">
                corp_code,
            </if>
            <if test="corpName != null">
                corp_name,
            </if>
            <if test="groupName != null">
                group_name,
            </if>
            <if test="groupCode != null">
                group_code,
            </if>
            <if test="responsiblePersonName != null">
                responsible_person_name,
            </if>
            <if test="responsiblePersonPhone != null">
                responsible_person_phone,
            </if>
            <if test="responsibleIdcardType != null">
                responsible_idcard_type,
            </if>
            <if test="responsibleIdcardNumber != null">
                responsible_idcard_number,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="entryTime != null">
                entry_time,
            </if>
            <if test="exitTime != null">
                exit_time,
            </if>
            <if test="fvsDeviceId != null">
                fvs_device_id,
            </if>
            <if test="fvsDeviceName != null">
                fvs_device_name,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                #{deptId,jdbcType=INTEGER},
            </if>
            <if test="corpId != null">
                #{corpId,jdbcType=INTEGER},
            </if>
            <if test="corpCode != null">
                #{corpCode,jdbcType=VARCHAR},
            </if>
            <if test="corpName != null">
                #{corpName,jdbcType=VARCHAR},
            </if>
            <if test="groupName != null">
                #{groupName,jdbcType=VARCHAR},
            </if>
            <if test="groupCode != null">
                #{groupCode,jdbcType=VARCHAR},
            </if>
            <if test="responsiblePersonName != null">
                #{responsiblePersonName,jdbcType=VARCHAR},
            </if>
            <if test="responsiblePersonPhone != null">
                #{responsiblePersonPhone,jdbcType=VARCHAR},
            </if>
            <if test="responsibleIdcardType != null">
                #{responsibleIdcardType,jdbcType=INTEGER},
            </if>
            <if test="responsibleIdcardNumber != null">
                #{responsibleIdcardNumber,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="entryTime != null">
                #{entryTime,jdbcType=DATE},
            </if>
            <if test="exitTime != null">
                #{exitTime,jdbcType=DATE},
            </if>
            <if test="fvsDeviceId != null">
                #{fvsDeviceId,jdbcType=INTEGER},
            </if>
            <if test="fvsDeviceName != null">
                #{fvsDeviceName,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppEmpGroup">
        update app_emp_group
        <set>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=INTEGER},
            </if>
            <if test="corpId != null">
                corp_id = #{corpId,jdbcType=INTEGER},
            </if>
            <if test="corpCode != null">
                corp_code = #{corpCode,jdbcType=VARCHAR},
            </if>
            <if test="corpName != null">
                corp_name = #{corpName,jdbcType=VARCHAR},
            </if>
            <if test="groupName != null">
                group_name = #{groupName,jdbcType=VARCHAR},
            </if>
            <if test="groupCode != null">
                group_code = #{groupCode,jdbcType=VARCHAR},
            </if>
            <if test="responsiblePersonName != null">
                responsible_person_name = #{responsiblePersonName,jdbcType=VARCHAR},
            </if>
            <if test="responsiblePersonPhone != null">
                responsible_person_phone = #{responsiblePersonPhone,jdbcType=VARCHAR},
            </if>
            <if test="responsibleIdcardType != null">
                responsible_idcard_type = #{responsibleIdcardType,jdbcType=INTEGER},
            </if>
            <if test="responsibleIdcardNumber != null">
                responsible_idcard_number = #{responsibleIdcardNumber,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="entryTime != null">
                entry_time = #{entryTime,jdbcType=DATE},
            </if>
            <if test="exitTime != null">
                exit_time = #{exitTime,jdbcType=DATE},
            </if>
            <if test="fvsDeviceId != null">
                fvs_device_id = #{fvsDeviceId,jdbcType=INTEGER},
            </if>
            <if test="fvsDeviceName != null">
                fvs_device_name = #{fvsDeviceName,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.whfc.emp.entity.AppEmpGroup">
        update app_emp_group
        set dept_id                   = #{deptId,jdbcType=INTEGER},
            corp_id                   = #{corpId,jdbcType=INTEGER},
            corp_code                 = #{corpCode,jdbcType=VARCHAR},
            corp_name                 = #{corpName,jdbcType=VARCHAR},
            group_name                = #{groupName,jdbcType=VARCHAR},
            group_code                = #{groupCode,jdbcType=VARCHAR},
            responsible_person_name   = #{responsiblePersonName,jdbcType=VARCHAR},
            responsible_person_phone  = #{responsiblePersonPhone,jdbcType=VARCHAR},
            responsible_idcard_type   = #{responsibleIdcardType,jdbcType=INTEGER},
            responsible_idcard_number = #{responsibleIdcardNumber,jdbcType=VARCHAR},
            remark                    = #{remark,jdbcType=VARCHAR},
            entry_time                = #{entryTime,jdbcType=DATE},
            exit_time                 = #{exitTime,jdbcType=DATE},
            fvs_device_id             = #{fvsDeviceId,jdbcType=INTEGER},
            fvs_device_name           = #{fvsDeviceName,jdbcType=VARCHAR},
            del_flag                  = #{delFlag,jdbcType=INTEGER},
            update_time               = #{updateTime,jdbcType=TIMESTAMP},
            create_time               = #{createTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectByProjectId" resultType="com.whfc.emp.dto.AppGroupDTO">
        select id as groupId,
               group_name,
               corp_id,
               corp_name,
               responsible_person_name,
               responsible_person_phone,
               responsible_idcard_type,
               responsible_idcard_number,
               group_code,
               fvs_device_id,
               fvs_device_name
        from app_emp_group
         where del_flag = 0
         and dept_id = #{projectId}
        <if test="corpId != null">
            and corp_id = #{corpId}
        </if>
        <if test="keyword != null and keyword != ''">
            and group_name like concat('%', #{keyword}, '%')
        </if>
        order by id desc
    </select>

    <select id="selectByCorpId" resultType="com.whfc.emp.dto.AppGroupDTO">
        select id as groupId,
               group_name
        from app_emp_group
        where del_flag = 0
          and corp_id = #{corpId}
    </select>

    <select id="selectByDeptId" resultType="com.whfc.emp.dto.AppGroupDTO">
        select id as groupId,
               group_name,
               corp_id,
               corp_name
        from app_emp_group
                where del_flag = 0
                  and dept_id = #{deptId}
        <if test="keyword != null and keyword != ''">
            and (group_name like concat('%', #{keyword}, '%')
                    or corp_name like concat('%', #{keyword}, '%'))
        </if>
    </select>

    <select id="selectCorpIdList" resultType="java.lang.Integer">
        select corp_id
          from app_emp_group
        where del_flag = 0
          and dept_id in
            (
            <foreach collection="deptIds" item="deptId" separator=",">
                #{deptId}
            </foreach>
            )
    </select>

    <update id="deleteLogicById">
        update app_emp_group
        set del_flag = 1
        where id = #{groupId}
    </update>

    <select id="selectGroupNameByDeptId" resultType="com.whfc.emp.dto.WxEmpGroupDTO">
        select id         as groupId,
               group_name as groupName
        from app_emp_group
        where del_flag = 0
          and dept_id = #{deptId}
    </select>

    <select id="selectGroupCodeAndDeptId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from app_emp_group
        where del_flag = 0
          and group_code = #{groupCode}
          and dept_id = #{deptId}
    </select>

    <select id="selectGroupAttendNum" resultType="com.whfc.emp.dto.AppEmpAttendGroupDTO">
        SELECT IFNULL(ae.group_id,'-1')                                     as id,
               ifnull(ae.group_name,'未知')                                 as name,
               ifnull(ae.corp_name,'未知')                                  as corpName,
               COUNT(ae.id)                                                  as total,
               ifnull(sum(IF(aed.attend_state = 1, 1, 0)), 0)                as num,
               ifnull(sum(IF(aed.attend_state = 1, 1, 0)), 0) / COUNT(ae.id) as rate
          FROM app_emp ae
          LEFT JOIN  app_emp_group aeg on ae.group_id = aeg.id
          LEFT JOIN  (select t.emp_id,t.attend_state
                      from app_emp_day t
                      where t.dept_id = #{deptId}
                        and t.date = DATE(#{date})
                        and t.attend_state = 1
                  ) aed ON ae.id = aed.emp_id
         WHERE ae.del_flag = 0
           AND ae.post_state = 1
           AND ae.dept_id = #{deptId}
         group by ae.group_id
    </select>

    <select id="selectCorpAttendNum" resultType="com.whfc.emp.dto.AppEmpAttendGroupDTO">
        SELECT IFNULL(ae.corp_id,'-1')                                      as id,
               ifnull(ae.corp_name,'未知')                                   as name,
               ifnull(ae.corp_name,'未知')                                   as corpName,
               COUNT(ae.id)                                                  as total,
               ifnull(sum(IF(aed.attend_state = 1, 1, 0)), 0)                as num,
               ifnull(sum(IF(aed.attend_state = 1, 1, 0)), 0) / COUNT(ae.id) as rate
        FROM app_emp ae
        LEFT JOIN  app_emp_group aeg on ae.group_id = aeg.id
        LEFT JOIN  (select t.emp_id,t.attend_state
                     from app_emp_day t
                    where t.dept_id = #{deptId}
                      and t.date = DATE(#{date})
                      and t.attend_state = 1
                ) aed ON ae.id = aed.emp_id
        WHERE ae.del_flag = 0
          AND ae.post_state = 1
          AND ae.dept_id = #{deptId}
        group by ae.corp_id
    </select>

    <select id="selectFvsGroup" resultType="com.whfc.emp.dto.MapEmpGroupDTO">
        SELECT
            id AS groupId,
            group_name,
            fvs_device_id,
            fvs_device_name
        FROM app_emp_group
        WHERE del_flag = 0
        AND dept_id = #{deptId}
        AND fvs_device_id IS NOT NULL
        <if test="groupId != null">
            AND id = #{groupId}
        </if>
        <if test="fvsDeviceId != null">
            AND fvs_device_id = #{fvsDeviceId}
        </if>
    </select>

    <select id="countByCorp" resultType="com.whfc.emp.dto.train.AppBoardEmpGroupCountDTO">
        SELECT corp_id,corp_name,COUNT(id) AS groupNum
        FROM app_emp_group
        WHERE del_flag = 0
        AND dept_id = #{deptId}
        GROUP BY corp_id
        ORDER BY corp_id
    </select>

    <select id="selectByDeptIdAndName" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM app_emp_group
        WHERE del_flag = 0
        AND dept_id = #{deptId}
        AND group_name = #{groupName}
    </select>

    <select id="selectByCorpIdAndName" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM app_emp_group
        WHERE del_flag = 0
        AND corp_id = #{corpId}
        AND group_name = #{groupName}
        LIMIT 1
    </select>
</mapper>