<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppPayrollAttachMapper">
  <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppPayrollAttach">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="payroll_id" jdbcType="INTEGER" property="payrollId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, payroll_id, name, url, del_flag, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from app_payroll_attach
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from app_payroll_attach
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.whfc.emp.entity.AppPayrollAttach">
    insert into app_payroll_attach (id, payroll_id, name, 
      url, del_flag, update_time, 
      create_time)
    values (#{id,jdbcType=INTEGER}, #{payrollId,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, 
      #{url,jdbcType=VARCHAR}, #{delFlag,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.whfc.emp.entity.AppPayrollAttach" useGeneratedKeys="true" keyProperty="id">
    insert into app_payroll_attach
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="payrollId != null">
        payroll_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="url != null">
        url,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="payrollId != null">
        #{payrollId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppPayrollAttach">
    update app_payroll_attach
    <set>
      <if test="payrollId != null">
        payroll_id = #{payrollId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.whfc.emp.entity.AppPayrollAttach">
    update app_payroll_attach
    set payroll_id = #{payrollId,jdbcType=INTEGER},
      name = #{name,jdbcType=VARCHAR},
      url = #{url,jdbcType=VARCHAR},
      del_flag = #{delFlag,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  
  <select id="selectAttachNumByPayrollId" resultType="java.lang.Integer">
  	select count(1)
  	from app_payroll_attach
  	where del_flag=0
  		and payroll_id=#{payrollId}
  </select>
  
  <select id="selectByPayrollId" resultType="com.whfc.emp.dto.AppPayrollAttachDTO">
  	select id as attachId,
  		url,
  		name as attachName
  	from app_payroll_attach
  	where del_flag=0
  		and payroll_id=#{payrollId}
  </select>
  
  
  <update id="deleteLogicByAttachId">
  	update app_payroll_attach
  	set del_flag=1
  	where id=#{attachId}
  </update>
</mapper>