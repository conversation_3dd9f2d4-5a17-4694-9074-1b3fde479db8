<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppEmpContractMapper">
    <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppEmpContract">
        <!--@mbg.generated-->
        <!--@Table app_emp_contract-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="emp_id" jdbcType="INTEGER" property="empId"/>
        <result column="dept_id" jdbcType="INTEGER" property="deptId"/>
        <result column="contract_no" jdbcType="VARCHAR" property="contractNo"/>
        <result column="contact_type" jdbcType="INTEGER" property="contactType"/>
        <result column="start_date" jdbcType="DATE" property="startDate"/>
        <result column="end_date" jdbcType="DATE" property="endDate"/>
        <result column="state" jdbcType="INTEGER" property="state"/>
        <result column="pay_type" jdbcType="INTEGER" property="payType"/>
        <result column="pay_way" jdbcType="VARCHAR" property="payWay"/>
        <result column="salary" jdbcType="DOUBLE" property="salary"/>
        <result column="contract_file" jdbcType="VARCHAR" property="contractFile"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        emp_id,
        dept_id,
        contract_no,
        contact_type,
        start_date,
        end_date,
        `state`,
        pay_type,
        pay_way,
        salary,
        contract_file,
        del_flag,
        update_time,
        create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from app_emp_contract
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete from app_emp_contract
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppEmpContract"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into app_emp_contract (emp_id, contract_no, contact_type,
        start_date, end_date, `state`,
        pay_type, salary, contract_file,
        del_flag, update_time, create_time
        )
        values (#{empId,jdbcType=INTEGER}, #{contractNo,jdbcType=VARCHAR}, #{contactType,jdbcType=INTEGER},
        #{startDate,jdbcType=DATE}, #{endDate,jdbcType=DATE}, #{state,jdbcType=INTEGER},
        #{payType,jdbcType=INTEGER}, #{salary,jdbcType=DOUBLE}, #{contractFile,jdbcType=VARCHAR},
        #{delFlag,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}
        )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppEmpContract"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into app_emp_contract
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="empId != null">
                emp_id,
            </if>
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="contractNo != null">
                contract_no,
            </if>
            <if test="contactType != null">
                contact_type,
            </if>
            <if test="startDate != null">
                start_date,
            </if>
            <if test="endDate != null">
                end_date,
            </if>
            <if test="state != null">
                `state`,
            </if>
            <if test="payType != null">
                pay_type,
            </if>
            <if test="payWay != null">
                pay_way,
            </if>
            <if test="salary != null">
                salary,
            </if>
            <if test="contractFile != null">
                contract_file,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="empId != null">
                #{empId,jdbcType=INTEGER},
            </if>
            <if test="deptId != null">
                #{deptId,jdbcType=INTEGER},
            </if>
            <if test="contractNo != null">
                #{contractNo,jdbcType=VARCHAR},
            </if>
            <if test="contactType != null">
                #{contactType,jdbcType=INTEGER},
            </if>
            <if test="startDate != null">
                #{startDate,jdbcType=DATE},
            </if>
            <if test="endDate != null">
                #{endDate,jdbcType=DATE},
            </if>
            <if test="state != null">
                #{state,jdbcType=INTEGER},
            </if>
            <if test="payType != null">
                #{payType,jdbcType=INTEGER},
            </if>
            <if test="payWay != null">
                #{payWay,jdbcType=VARCHAR},
            </if>
            <if test="salary != null">
                #{salary,jdbcType=DOUBLE},
            </if>
            <if test="contractFile != null">
                #{contractFile,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppEmpContract">
        <!--@mbg.generated-->
        update app_emp_contract
        <set>
            <if test="empId != null">
                emp_id = #{empId,jdbcType=INTEGER},
            </if>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=INTEGER},
            </if>
            <if test="contractNo != null">
                contract_no = #{contractNo,jdbcType=VARCHAR},
            </if>
            <if test="contactType != null">
                contact_type = #{contactType,jdbcType=INTEGER},
            </if>
            <if test="startDate != null">
                start_date = #{startDate,jdbcType=DATE},
            </if>
            <if test="endDate != null">
                end_date = #{endDate,jdbcType=DATE},
            </if>
            <if test="state != null">
                `state` = #{state,jdbcType=INTEGER},
            </if>
            <if test="payType != null">
                pay_type = #{payType,jdbcType=INTEGER},
            </if>
            <if test="payWay != null">
                pay_way = #{payWay,jdbcType=VARCHAR},
            </if>
            <if test="salary != null">
                salary = #{salary,jdbcType=DOUBLE},
            </if>
            <if test="contractFile != null">
                contract_file = #{contractFile,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.whfc.emp.entity.AppEmpContract">
        <!--@mbg.generated-->
        update app_emp_contract
        set emp_id = #{empId,jdbcType=INTEGER},
        dept_id = #{deptId,jdbcType=INTEGER},
        contract_no = #{contractNo,jdbcType=VARCHAR},
        contact_type = #{contactType,jdbcType=INTEGER},
        start_date = #{startDate,jdbcType=DATE},
        end_date = #{endDate,jdbcType=DATE},
        `state` = #{state,jdbcType=INTEGER},
        pay_type = #{payType,jdbcType=INTEGER},
        pay_way = #{payWay,jdbcType=VARCHAR},
        salary = #{salary,jdbcType=DOUBLE},
        contract_file = #{contractFile,jdbcType=VARCHAR},
        del_flag = #{delFlag,jdbcType=INTEGER},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        create_time = #{createTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectByEmpId" resultType="com.whfc.emp.dto.AppEmpContractDTO">
      select id,
              contract_no,
              contact_type,
              start_date,
              end_date,
              state,
              pay_type,
              pay_way,
              salary
      from app_emp_contract
      where del_flag = 0
      and emp_id = #{empId}
    </select>

    <select id="selectByContractNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from app_emp_contract
        where del_flag = 0
        and contract_no = #{contractNo}
        and dept_id = #{deptId}
    </select>

    <update id="deleteLogicById">
        update app_emp_contract
        set del_flag = 1
        where id = #{id}
    </update>
</mapper>