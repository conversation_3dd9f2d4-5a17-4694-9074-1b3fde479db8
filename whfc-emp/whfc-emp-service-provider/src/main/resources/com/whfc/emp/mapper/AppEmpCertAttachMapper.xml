<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppEmpCertAttachMapper">
    <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppEmpCertAttach">
        <!--@mbg.generated-->
        <!--@Table app_emp_cert_attach-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="dept_id" jdbcType="INTEGER" property="deptId"/>
        <result column="cert_id" jdbcType="INTEGER" property="certId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="url" jdbcType="VARCHAR" property="url"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, dept_id, cert_id, `name`, url, del_flag, update_time, create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from app_emp_cert_attach
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete from app_emp_cert_attach
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppEmpCertAttach"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into app_emp_cert_attach
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="certId != null">
                cert_id,
            </if>
            <if test="name != null">
                `name`,
            </if>
            <if test="url != null">
                url,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                #{deptId,jdbcType=INTEGER},
            </if>
            <if test="certId != null">
                #{certId,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="url != null">
                #{url,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppEmpCertAttach">
        <!--@mbg.generated-->
        update app_emp_cert_attach
        <set>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=INTEGER},
            </if>
            <if test="certId != null">
                cert_id = #{certId,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="url != null">
                url = #{url,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="deleteLogicByCertId">
      update app_emp_cert_attach
      set del_flag = 1
      where cert_id = #{certId,jdbcType=INTEGER}
    </update>

    <select id="selectByCertId" resultType="com.whfc.emp.dto.AppAttachDTO">
        select name,url
        from app_emp_cert_attach
        where cert_id = #{certId,jdbcType=INTEGER}
        and del_flag = 0
    </select>

    <insert id="batchInsert">
        INSERT INTO app_emp_cert_attach
        (
            dept_id,
            cert_id,
            name,
            url
        )
        values
        <foreach collection="fileList" item="item" separator="," >
        (
            #{deptId},
            #{certId},
            #{item.name},
            #{item.url}
        )
        </foreach>
    </insert>
</mapper>