<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppIndoorPositionTagLogMapper">
  <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppIndoorPositionTagLog">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="tag_id" jdbcType="INTEGER" property="tagId" />
    <result column="map_id" jdbcType="INTEGER" property="mapId" />
    <result column="station_id" jdbcType="INTEGER" property="stationId" />
    <result column="time" jdbcType="TIMESTAMP" property="time" />
    <result column="x" jdbcType="DOUBLE" property="x" />
    <result column="y" jdbcType="DOUBLE" property="y" />
    <result column="lng" jdbcType="DOUBLE" property="lng" />
    <result column="lat" jdbcType="DOUBLE" property="lat" />
    <result column="distance" jdbcType="INTEGER" property="distance" />
    <result column="battery_power" jdbcType="INTEGER" property="batteryPower" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id,
    dept_id,
    tag_id,
    map_id,
    station_id,
    `time`,
    x,
    y,
    lng,
    lat,
    distance,
    battery_power,
    del_flag,
    update_time,
    create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from app_indoor_position_tag_log
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from app_indoor_position_tag_log
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.whfc.emp.entity.AppIndoorPositionTagLog">
    insert into app_indoor_position_tag_log (id, dept_id, tag_id, 
      map_id, time, x, y, 
      lng, lat, distance, 
      battery_power, del_flag, update_time, 
      create_time)
    values (#{id,jdbcType=INTEGER}, #{deptId,jdbcType=INTEGER}, #{tagId,jdbcType=INTEGER}, 
      #{mapId,jdbcType=INTEGER}, #{time,jdbcType=TIMESTAMP}, #{x,jdbcType=VARCHAR}, #{y,jdbcType=VARCHAR}, 
      #{lng,jdbcType=VARCHAR}, #{lat,jdbcType=VARCHAR}, #{distance,jdbcType=INTEGER}, 
      #{batteryPower,jdbcType=INTEGER}, #{delFlag,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.whfc.emp.entity.AppIndoorPositionTagLog">
    insert into app_indoor_position_tag_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="tagId != null">
        tag_id,
      </if>
      <if test="mapId != null">
        map_id,
      </if>
      <if test="stationId != null">
        station_id,
      </if>
      <if test="time != null">
        time,
      </if>
      <if test="x != null">
        x,
      </if>
      <if test="y != null">
        y,
      </if>
      <if test="lng != null">
        lng,
      </if>
      <if test="lat != null">
        lat,
      </if>
      <if test="distance != null">
        distance,
      </if>
      <if test="batteryPower != null">
        battery_power,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="tagId != null">
        #{tagId,jdbcType=INTEGER},
      </if>
      <if test="mapId != null">
        #{mapId,jdbcType=INTEGER},
      </if>
      <if test="stationId != null">
        #{stationId,jdbcType=INTEGER},
      </if>
      <if test="time != null">
        #{time,jdbcType=TIMESTAMP},
      </if>
      <if test="x != null">
        #{x,jdbcType=DOUBLE},
      </if>
      <if test="y != null">
        #{y,jdbcType=DOUBLE},
      </if>
      <if test="lng != null">
        #{lng,jdbcType=DOUBLE},
      </if>
      <if test="lat != null">
        #{lat,jdbcType=DOUBLE},
      </if>
      <if test="distance != null">
        #{distance,jdbcType=INTEGER},
      </if>
      <if test="batteryPower != null">
        #{batteryPower,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppIndoorPositionTagLog">
    update app_indoor_position_tag_log
    <set>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="tagId != null">
        tag_id = #{tagId,jdbcType=INTEGER},
      </if>
      <if test="mapId != null">
        map_id = #{mapId,jdbcType=INTEGER},
      </if>
      <if test="stationId != null">
        station_id = #{stationId,jdbcType=INTEGER},
      </if>
      <if test="time != null">
        time = #{time,jdbcType=TIMESTAMP},
      </if>
      <if test="x != null">
        x = #{x,jdbcType=DOUBLE},
      </if>
      <if test="y != null">
        y = #{y,jdbcType=DOUBLE},
      </if>
      <if test="lng != null">
        lng = #{lng,jdbcType=DOUBLE},
      </if>
      <if test="lat != null">
        lat = #{lat,jdbcType=DOUBLE},
      </if>
      <if test="distance != null">
        distance = #{distance,jdbcType=INTEGER},
      </if>
      <if test="batteryPower != null">
        battery_power = #{batteryPower,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.whfc.emp.entity.AppIndoorPositionTagLog">
    update app_indoor_position_tag_log
    set dept_id = #{deptId,jdbcType=INTEGER},
      tag_id = #{tagId,jdbcType=INTEGER},
      map_id = #{mapId,jdbcType=INTEGER},
      time = #{time,jdbcType=TIMESTAMP},
      x = #{x,jdbcType=VARCHAR},
      y = #{y,jdbcType=VARCHAR},
      lng = #{lng,jdbcType=VARCHAR},
      lat = #{lat,jdbcType=VARCHAR},
      distance = #{distance,jdbcType=INTEGER},
      battery_power = #{batteryPower,jdbcType=INTEGER},
      del_flag = #{delFlag,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectPositionLogList" resultType="com.whfc.emp.dto.AppIndoorPositionTagLogDTO">
    select map_id,
           station_id,
            `time`,
            x,
            y,
            lng,
            lat,
            distance,
            battery_power
      from app_indoor_position_tag_log
     where tag_id = #{tagId}
       and del_flag = 0
       and `time` >= #{startTime}
       and #{endTime} >= `time`
       order by `time`
  </select>
</mapper>