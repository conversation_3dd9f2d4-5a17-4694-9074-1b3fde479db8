<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppFenceEmpMapper">
    <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppFenceEmp">
        <!--@mbg.generated-->
        <!--@Table app_fence_emp-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="fence_id" jdbcType="INTEGER" property="fenceId"/>
        <result column="emp_id" jdbcType="INTEGER" property="empId"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, fence_id, emp_id, del_flag, update_time, create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from app_fence_emp
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete from app_fence_emp
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppFenceEmp"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into app_fence_emp (fence_id, emp_id, del_flag,
        update_time, create_time)
        values (#{fenceId,jdbcType=INTEGER}, #{empId,jdbcType=INTEGER}, #{delFlag,jdbcType=INTEGER},
        #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppFenceEmp"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into app_fence_emp
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fenceId != null">
                fence_id,
            </if>
            <if test="empId != null">
                emp_id,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="fenceId != null">
                #{fenceId,jdbcType=INTEGER},
            </if>
            <if test="empId != null">
                #{empId,jdbcType=INTEGER},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppFenceEmp">
        <!--@mbg.generated-->
        update app_fence_emp
        <set>
            <if test="fenceId != null">
                fence_id = #{fenceId,jdbcType=INTEGER},
            </if>
            <if test="empId != null">
                emp_id = #{empId,jdbcType=INTEGER},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.whfc.emp.entity.AppFenceEmp">
        <!--@mbg.generated-->
        update app_fence_emp
        set fence_id = #{fenceId,jdbcType=INTEGER},
        emp_id = #{empId,jdbcType=INTEGER},
        del_flag = #{delFlag,jdbcType=INTEGER},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        create_time = #{createTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <select id="selectByFenceId" resultType="com.whfc.emp.dto.AppFenceEmpDTO">
        select afe.emp_id,
        ae.emp_name as name,
        ae.phone,
        ae.dept_name,
        ae.avatar,
        ae.group_name,
        aewt.name as workName
        from app_fence_emp afe
        inner join app_emp ae on ae.id = afe.emp_id
        inner join app_emp_work_type aewt on ae.work_type_id = aewt.id
        where afe.del_flag = 0
        and fence_id = #{fenceId}
        <if test="keyword!=null and keyword!=''">
            and ae.emp_name like concat('%',#{keyword},'%')
        </if>
        <if test="groupId!=null and groupId!=''">
            and ae.group_id = #{groupId}
        </if>
        <if test="workTypeId!=null and workTypeId!=''">
            and ae.work_type_id = #{workTypeId}
        </if>
    </select>
    <insert id="insertOrUpdate">
        INSERT INTO app_fence_emp
        (fence_id,emp_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.fenceId},
            #{item.empId})
        </foreach>
        on duplicate key update
        fence_id = values(fence_id),
        emp_id = values(emp_id),
        del_flag = 0
    </insert>
    <update id="deleteLogic">
        update app_fence_emp
        set del_flag = 1
        where fence_id = #{fenceId}
        and emp_id = #{empId}
    </update>
    <select id="selectByEmpId" resultType="com.whfc.emp.dto.AppFenceDTO">
        select af.type,
        af.radius,
        ASTEXT(af.center) as center,
        ASTEXT(af.polygon) as polygon
        from app_fence af
        inner join app_fence_emp afe on afe.fence_id = af.id
        where af.del_flag=0
        and afe.del_flag = 0
        and afe.emp_id = #{empId}
    </select>
    <select id="selectUnGrantEmp" resultType="com.whfc.emp.dto.AppFenceEmpDTO">
        SELECT  ae.id AS empId,
                ae.phone ,
                ae.avatar,
                ae.emp_name as name,
                ae.group_name
          FROM app_emp ae
         where ae.del_flag=0
           and ae.post_state = 1
           and ae.dept_id=#{deptId}
           and ae.id not in (
                select emp_id
                from app_fence_emp
                where fence_id=#{fenceId}
                and del_flag=0
            )
        <if test="keyword!=null and keyword!=''">
            and ae.emp_name like concat('%',#{keyword},'%')
        </if>
        <if test="groupId!=null">
            and ae.group_id = #{groupId}
        </if>
        order by ae.id desc
    </select>
</mapper>