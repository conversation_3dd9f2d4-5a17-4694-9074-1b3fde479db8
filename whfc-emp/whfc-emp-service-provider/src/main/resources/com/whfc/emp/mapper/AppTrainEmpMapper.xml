<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.emp.dao.AppTrainEmpMapper">
    <resultMap id="BaseResultMap" type="com.whfc.emp.entity.AppTrainEmp">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="train_id" jdbcType="INTEGER" property="trainId"/>
        <result column="emp_id" jdbcType="INTEGER" property="empId"/>
        <result column="emp_name" jdbcType="VARCHAR" property="empName"/>
        <result column="pass_flag" jdbcType="INTEGER" property="passFlag"/>
        <result column="exam_no" jdbcType="VARCHAR" property="examNo"/>
        <result column="exam_count" jdbcType="INTEGER" property="examCount"/>
        <result column="sign_img_url" jdbcType="VARCHAR" property="signImgUrl"/>
        <result column="score" jdbcType="DOUBLE" property="score"/>
        <result column="train_period" jdbcType="INTEGER" property="trainPeriod"/>
        <result column="total_score" jdbcType="INTEGER" property="totalScore"/>
        <result column="pass_score" jdbcType="INTEGER" property="passScore"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,
        train_id,
        emp_id,
        emp_name,
        pass_flag,
        exam_no,
        exam_count,
        sign_img_url,
        score,
        train_period,
        total_score,
        pass_score,
        update_time,
        create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from app_train_emp
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete
        from app_train_emp
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppTrainEmp"
            useGeneratedKeys="true">
        insert into app_train_emp (train_id, emp_id, emp_name,
                                   pass_flag, exam_no, exam_count,
                                   sign_img_url, score, train_period,
                                   total_score, pass_score, update_time,
                                   create_time)
        values (#{trainId,jdbcType=INTEGER}, #{empId,jdbcType=INTEGER}, #{empName,jdbcType=VARCHAR},
                #{passFlag,jdbcType=INTEGER}, #{examNo,jdbcType=VARCHAR}, #{examCount,jdbcType=INTEGER},
                #{signImgUrl,jdbcType=VARCHAR}, #{score,jdbcType=DOUBLE}, #{trainPeriod,jdbcType=INTEGER},
                #{totalScore,jdbcType=INTEGER}, #{passScore,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP},
                #{createTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.emp.entity.AppTrainEmp"
            useGeneratedKeys="true">
        insert into app_train_emp
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="trainId != null">
                train_id,
            </if>
            <if test="empId != null">
                emp_id,
            </if>
            <if test="empName != null">
                emp_name,
            </if>
            <if test="passFlag != null">
                pass_flag,
            </if>
            <if test="examNo != null">
                exam_no,
            </if>
            <if test="examCount != null">
                exam_count,
            </if>
            <if test="signImgUrl != null">
                sign_img_url,
            </if>
            <if test="score != null">
                score,
            </if>
            <if test="trainPeriod != null">
                train_period,
            </if>
            <if test="totalScore != null">
                total_score,
            </if>
            <if test="passScore != null">
                pass_score,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="trainId != null">
                #{trainId,jdbcType=INTEGER},
            </if>
            <if test="empId != null">
                #{empId,jdbcType=INTEGER},
            </if>
            <if test="empName != null">
                #{empName,jdbcType=VARCHAR},
            </if>
            <if test="passFlag != null">
                #{passFlag,jdbcType=INTEGER},
            </if>
            <if test="examNo != null">
                #{examNo,jdbcType=VARCHAR},
            </if>
            <if test="examCount != null">
                #{examCount,jdbcType=INTEGER},
            </if>
            <if test="signImgUrl != null">
                #{signImgUrl,jdbcType=VARCHAR},
            </if>
            <if test="score != null">
                #{score,jdbcType=DOUBLE},
            </if>
            <if test="trainPeriod != null">
                #{trainPeriod,jdbcType=INTEGER},
            </if>
            <if test="totalScore != null">
                #{totalScore,jdbcType=INTEGER},
            </if>
            <if test="passScore != null">
                #{passScore,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.emp.entity.AppTrainEmp">
        update app_train_emp
        <set>
            <if test="trainId != null">
                train_id = #{trainId,jdbcType=INTEGER},
            </if>
            <if test="empId != null">
                emp_id = #{empId,jdbcType=INTEGER},
            </if>
            <if test="empName != null">
                emp_name = #{empName,jdbcType=VARCHAR},
            </if>
            <if test="passFlag != null">
                pass_flag = #{passFlag,jdbcType=INTEGER},
            </if>
            <if test="examNo != null">
                exam_no = #{examNo,jdbcType=VARCHAR},
            </if>
            <if test="examCount != null">
                exam_count = #{examCount,jdbcType=INTEGER},
            </if>
            <if test="signImgUrl != null">
                sign_img_url = #{signImgUrl,jdbcType=VARCHAR},
            </if>
            <if test="score != null">
                score = #{score,jdbcType=DOUBLE},
            </if>
            <if test="trainPeriod != null">
                train_period = #{trainPeriod,jdbcType=INTEGER},
            </if>
            <if test="totalScore != null">
                total_score = #{totalScore,jdbcType=INTEGER},
            </if>
            <if test="passScore != null">
                pass_score = #{passScore,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.whfc.emp.entity.AppTrainEmp">
        update app_train_emp
        set train_id     = #{trainId,jdbcType=INTEGER},
            emp_id       = #{empId,jdbcType=INTEGER},
            emp_name     = #{empName,jdbcType=VARCHAR},
            pass_flag    = #{passFlag,jdbcType=INTEGER},
            exam_no      = #{examNo,jdbcType=VARCHAR},
            exam_count   = #{examCount,jdbcType=INTEGER},
            sign_img_url = #{signImgUrl,jdbcType=VARCHAR},
            score        = #{score,jdbcType=DOUBLE},
            train_period = #{trainPeriod,jdbcType=INTEGER},
            total_score  = #{totalScore,jdbcType=INTEGER},
            pass_score   = #{passScore,jdbcType=INTEGER},
            update_time  = #{updateTime,jdbcType=TIMESTAMP},
            create_time  = #{createTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectTrainEmpByParam" resultType="com.whfc.emp.dto.TrainEmpDTO">
        SELECT ate.id AS trainEmpId,
               ate.emp_name,
               ae.id_card_no,
               ae.phone AS empPhone,
               ae.dept_name,
               ae.work_role_name,
               ae.work_type_name,
               ae.enter_time,
               ae.id    AS empId,
               ate.score,
               ate.train_period,
               ate.total_score,
               ate.pass_score,
               ate.exam_no,
               ate.exam_count,
               ate.sign_img_url,
               ate.pass_flag,
               ae.group_name
        FROM app_train_emp ate
                     LEFT JOIN app_emp ae ON ae.id = ate.emp_id
                WHERE ate.train_id = #{trainId}
        <if test="passFlag != null">
            and ate.pass_flag = #{passFlag}
        </if>
        <if test="workRoleId != null">
            and ae.work_role_id = #{workRoleId}
        </if>
        <if test="workTypeId != null">
            and ae.work_type_id = #{workTypeId}
        </if>
        <if test="keyword != null and keyword != ''">
            and (ae.emp_name like concat('%', #{keyword}, '%') OR ate.emp_name like concat('%', #{keyword}, '%'))
        </if>
        <if test="groupId != null">
            and ae.group_id = #{groupId}
        </if>
    </select>

    <select id="selectByTrainId" resultType="com.whfc.emp.dto.TrainEmpDTO">
        SELECT ate.emp_id,
               ate.emp_name,
               ate.score,
               ate.pass_flag,
               ae.id_card_no
        FROM app_train_emp ate
         inner JOIN app_emp ae ON ate.emp_id = ae.id
        WHERE ate.train_id = #{trainId}
    </select>

    <select id="countPassEmp" resultType="java.lang.Integer">
        select count(1)
        from app_train_emp
        where train_id = #{trainId}
          and pass_flag = 1
    </select>

    <select id="countEmp" resultType="java.lang.Integer">
        select count(1)
        from app_train_emp
        where train_id = #{trainId}
    </select>

    <select id="countByTrainIdAndEmpId" resultType="java.lang.Integer">
        select count(1)
        from app_train_emp
        where train_id = #{trainId}
          and emp_id = #{empId}
    </select>

    <delete id="deleteByTrainIdAndEmpId">
        delete
        from app_train_emp
        where train_id = #{trainId}
          and emp_id = #{empId}
    </delete>

    <update id="updateScoreByTrainIdAndEmpId">
        update app_train_emp
        set score=#{score},
            pass_flag=#{passFlag}
        where train_id = #{trainId}
          and emp_id = #{empId}
    </update>

    <select id="selectTrainEmpBYEmpIdAndTrainType" resultType="com.whfc.emp.dto.TrainEmpDTO">
        SELECT ae.emp_name,
               ae.id_card_no,
               ae.phone AS empPhone,
               ae.dept_name,
               ae.work_role_name,
               ae.work_type_name,
               ae.enter_time,
               ae.id    AS empId,
               ate.score,
               ate.pass_flag,
               ae.group_name
        FROM app_train_emp ate
                     INNER JOIN app_emp ae ON ae.id = ate.emp_id
                     INNER JOIN app_train `at` ON at.id = ate.train_id
        where ate.pass_flag = 1
          and ate.emp_id = #{empId}
          and at.train_type = #{trainType}
        LIMIT 1
    </select>

    <select id="selectTrainEmp" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM app_train_emp
        WHERE train_id = #{trainId}
        AND emp_id = #{empId}
    </select>
</mapper>