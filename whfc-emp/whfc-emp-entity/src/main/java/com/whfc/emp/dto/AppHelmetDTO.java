package com.whfc.emp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * 人员安全帽列表
 */
@Data
public class AppHelmetDTO implements Serializable {

    /**
     * 人员ID
     */
    private Integer empId;

    /**
     * 人员姓名
     */
    private String empName;

    /**
     * 合作单位
     */
    private String corpName;

    /**
     * 人员班组
     */
    private String groupName;

    /**
     * 工种
     */
    private String workTypeName;

    /**
     * 硬件id
     */
    private Integer deviceId;

    /**
     * 硬件编码
     */
    private String code;

    /**
     * 硬件平台
     */
    private String platform;

    /**
     * 硬件电量
     */
    private Double batteryPower;

    /**
     * 硬件通讯时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date time;

    private Date deviceTime;

    private Double lat;

    private Double lng;

    /**
     * 安全帽类型
     */
    private Integer type;

    /**
     * 位置信息
     */
    private String location;

    /**
     * 网络状态
     */
    private Integer netState;

    /**
     * 时间段内通讯次数统计
     */
    private List<AppEmpDeviceStatNumDTO> numList;
}
