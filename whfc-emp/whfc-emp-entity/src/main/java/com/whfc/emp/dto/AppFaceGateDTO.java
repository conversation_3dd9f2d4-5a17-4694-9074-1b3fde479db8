package com.whfc.emp.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Schema(description = "闸机信息")
@Data
public class AppFaceGateDTO implements Serializable {

    @Schema(description =  "闸机id")
    private Integer faceGateId;

    @Schema(description =  "闸机序列号")
    private String deviceKey;

    @Schema(description =  "闸机名称")
    private String name;

    @Schema(description =  "型号")
    private String model;

    @Schema(description =  "组织机构id")
    private Integer deptId;

    @Schema(description =  "组织机构名称")
    private String deptName;

    @Schema(description =  "闸机方向  1-进门  2-出门")
    private Integer direction;

    @Schema(description =  "闸机平台")
    private String platform;

    @Schema(description =  "闸机平台名称")
    private String platformName;

    @Schema(description =  "工区ID")
    private Integer areaId;

    @Schema(description =  "工区")
    private String areaName;

    @Schema(description =  "经度")
    private Double lng;

    @Schema(description =  "纬度")
    private Double lat;

    @Schema(description =  "地址")
    private String address;

    @Schema(description =  "状态")
    private Integer state;

    @Schema(description =  "设备考勤更新时间")
    private Date time;

    @Schema(description =  "是否授权  0-未授权  1-已授权")
    private Integer isGrant;

    @Schema(description =  "授权类型")
    private Integer taskType;

    @Schema(description =  "授权信息")
    private String stateMessage;

    @Schema(description =  "授权人数")
    private Integer authEmpNum;

}
