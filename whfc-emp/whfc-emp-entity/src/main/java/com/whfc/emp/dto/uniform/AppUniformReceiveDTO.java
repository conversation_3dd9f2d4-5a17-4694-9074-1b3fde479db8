package com.whfc.emp.dto.uniform;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class AppUniformReceiveDTO implements Serializable {

    /**
     * 领用记录ID
     */
    private Integer receiveId;

    /**
     * 唯一编码
     */
    private String guid;

    /**
     * 人员ID
     */
    private Integer empId;

    /**
     * 人员姓名
     */
    private String empName;

    /**
     * 公司名称
     */
    private String company;

    /**
     * 证件号
     */
    private String workCardNo;

    /**
     * 工种
     */
    private String workTypeName;

    /**
     * 单位名称
     */
    private String corpName;

    /**
     * 班组名称
     */
    private String groupName;

    /**
     * 领用日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date date;

    /**
     * 创建人ID
     */
    private Integer createUserId;

    /**
     * 创建人
     */
    private String createUserName;

    /**
     * 扩展信息
     */
    @JsonIgnore
    private String ext;

    /**
     * 工服领用详情
     */
    private List<AppUniformReceiveDetailDTO> detailList;

    /**
     * 图片列表
     */
    private List<String> imgList;
}