package com.whfc.emp.dto.train;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 培训记录-试卷
 * <AUTHOR>
 * @version 1.0
 * @date 2021-08-26 14:24
 */
@Data
public class TrainPaperDTO implements Serializable {

    /**
     * 组织机构ID
     */
    private Integer deptId;
    /**
     * 主键Id
     */
    @JSONField(name = "ID")
    private String id;
    /**
     * 培训记录Id
     */
    @JSONField(name = "RecordId")
    private String recordId;
    /**
     * 试卷编号
     */
    @JSONField(name = "ExamNo")
    private String examNo;
    /**
     * 批次号
     */
    @JSONField(name = "GroupNo")
    private Integer groupNo;
    /**
     * 课程Id
     */
    @JSONField(name = "CourseID")
    private String courseId;
    /**
     * 顺序号
     */
    @JSONField(name = "COrder")
    private Integer cOrder;
    /**
     * 编码
     */
    @JSONField(name = "QsnCode")
    private String qsnCode;
    /**
     * 题库ID
     */
    @JSONField(name = "QsnId")
    private String qsnId;
    /**
     * 题库文件名称
     */
    @JSONField(name = "QsnFileName")
    private String qsnFileName;
    /**
     * 答案
     */
    @JSONField(name = "QsnAnswer")
    private String qsnAnswer;
    /**
     * 试题分类 1.文字题、2.多媒体题、3.图片题
     */
    @JSONField(name = "QsnCategory")
    private Integer qsnCategory;
    /**
     * 试题类型 (1:单选 2:多选 3:判断)
     */
    @JSONField(name = "QsnKind")
    private Integer qsnKind;
    /**
     * 重要程度 (0:容易 1:一般 2:困难)
     */
    @JSONField(name = "QsnImportant")
    private Integer qsnImportant;
    /**
     * 来源 0：系统；1：用户
     */
    @JSONField(name = "Source")
    private Integer source;
    /**
     * 版本
     */
    @JSONField(name = "Version")
    private Integer version;
    /**
     * 上传时间
     */
    @JSONField(name = "UploadTime", format = "yyyy-MM-dd'T'HH:mm:ss")
    private Date uploadTime;
    /**
     * 删除用户
     */
    @JSONField(name = "DeleteUser")
    private String deleteUser;
    /**
     * 删除时间
     */
    @JSONField(name = "DeleteDate", format = "yyyy-MM-dd'T'HH:mm:ss")
    private Date deleteDate;
    /**
     * 删除标记(true/false)
     */
    @JSONField(name = "DeleteTag")
    private Boolean deleteTag;
    /**
     * 内容Id
     */
    @JSONField(name = "ContentId")
    private String contentId;
    /**
     * 试题内容
     */
    @JSONField(name = "QsnContent")
    private String qsnContent;
    /**
     * 说明
     */
    @JSONField(name = "Description")
    private String description;
    /**
     * 试题解析
     */
    @JSONField(name = "Analysis")
    private String analysis;


}
