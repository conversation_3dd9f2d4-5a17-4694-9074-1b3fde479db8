package com.whfc.emp.entity;

import java.util.Date;

/**
    * 广播记录人员表
    */
public class AppBroadcastRecordEmp {
    /**
    * ID
    */
    private Integer id;

    /**
    * 广播记录ID
    */
    private Integer recordId;

    /**
    * 接收广播的人员ID
    */
    private Integer empId;

    /**
    * 接收广播的设备ID
    */
    private Integer deviceId;

    /**
    * 发送时间
    */
    private Date sendTime;

    /**
    * 重试次数
    */
    private Integer retryCnt;

    /**
    * 发送状态 0-未发送，1-已发送，2-已确认 ，3-已失效
    */
    private Integer state;

    /**
    * 删除标记(0-未删除 1-已删除)
    */
    private Integer delFlag;

    private Date updateTime;

    private Date createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getRecordId() {
        return recordId;
    }

    public void setRecordId(Integer recordId) {
        this.recordId = recordId;
    }

    public Integer getEmpId() {
        return empId;
    }

    public void setEmpId(Integer empId) {
        this.empId = empId;
    }

    public Integer getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(Integer deviceId) {
        this.deviceId = deviceId;
    }

    public Date getSendTime() {
        return sendTime;
    }

    public void setSendTime(Date sendTime) {
        this.sendTime = sendTime;
    }

    public Integer getRetryCnt() {
        return retryCnt;
    }

    public void setRetryCnt(Integer retryCnt) {
        this.retryCnt = retryCnt;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}