package com.whfc.emp.param;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

@Data
public class FaceGateEmpParam implements Serializable {


    private Integer id;

    @NotNull
    private Integer deptId;

    @NotNull
    private Integer projectId;

    private Integer cardType;

    private String workCardNo;

    private Date workCardEndDate;

    private String deptName;

    private Integer corpId;

    private String corpName;

    private Integer groupId;

    private String groupName;

    @NotNull
    private String empName;

    private String ename;

    private String phone;

    @NotEmpty
    private Integer gender;

    private Date birthday;

    private String headImg;

    private String avatar;

    @NotEmpty
    private String idCardNo;

    private String idCardFront;

    private String idCardBack;

    private String idCardGrantOrg;

    private Date idCardStartDate;

    private Date idCardEndDate;

    private String nation;

    private String province;

    private String city;

    private String area;

    private String address;

    private Integer education;

    private Integer degree;

    private String empCode;

    private Integer workRoleId;

    private String workRoleName;

    private Integer workTypeId;

    private String workTypeName;

    private Integer leaderFlag;

    private Integer postState;

    private Integer bindFlag;

    private Integer deviceId;

    private String sn;

    private Date enterTime;

    private Date outerTime;


}
