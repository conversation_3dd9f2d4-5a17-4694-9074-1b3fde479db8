package com.whfc.emp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

@Data
public class AppEmpDetailDTO implements Serializable {
	private Integer empId;
	private String empName;
	private String headImg;
	private String deptName;
	private String phone;
	private Integer gender;
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date recentEnterTime;
	/**
	 * ************************新需求增加***********************************
	 */
	private String workTypeName;
	private String corpName;
	private String workRoleName;
	private Integer leaderFlag;
	@JsonFormat(pattern = "yyyy-MM-dd")
	private Date birthday;
	private Double attendTimes;

	/**
	 * 班组id
	 */
	private Integer groupId;

	/**
	 * 班组名称
	 */
	private String groupName;
}
