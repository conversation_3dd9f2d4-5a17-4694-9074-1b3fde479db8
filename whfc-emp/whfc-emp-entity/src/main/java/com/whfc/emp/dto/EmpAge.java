package com.whfc.emp.dto;

import com.whfc.emp.enums.EmpAgeType;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 人员年龄信息
 * @author: xugcheng
 * @version: 1.0
 * @date: 2022-01-20 16:24
 */
@Data
public class EmpAge implements Serializable {

    /**
     * 出生日期
     */
    private Date birthday;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 年龄分类
     */
    private EmpAgeType ageType;

    /**
     * 性别
     */
    private Integer gender;

    /**
     * 数量
     */
    private Integer num;
}
