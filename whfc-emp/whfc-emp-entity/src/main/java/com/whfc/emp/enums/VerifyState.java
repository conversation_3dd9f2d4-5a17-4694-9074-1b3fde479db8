package com.whfc.emp.enums;

/**
 * @Description: 核验状态
 * @author: xug<PERSON>
 * @version: 1.0
 * @date: 2023/7/14 13:43
 */
public enum VerifyState {

    INIT(0, "未核验"),

    SUCCESS(1, "成功"),

    FAIL(2, "失败");

    private Integer value;

    private String name;

    VerifyState(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}
