package com.whfc.emp.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2020-01-16
 */
@Data
public class EmpMonthReportExportParam implements Serializable {
    @NotNull
    private Integer deptId;

    private Integer corpId;

    private Integer groupId;

    @NotNull
    @JsonFormat(pattern = "yyyy-MM")
    private Date month;

    private String keyword;
}
