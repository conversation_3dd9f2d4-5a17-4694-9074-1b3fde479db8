package com.whfc.emp.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> qzexing
 * @version : 1.0
 * @date : 2020/7/3 14:28
 */
@Data
public class AppBroadcastRecordEmpDTO implements Serializable {

    /**
     * ID
     */
    private Integer id;

    /**
     * 广播记录ID
     */
    private Integer recordId;

    /**
     * 接收广播的人员ID
     */
    private Integer empId;

    /**
     * 广播类型  1-人员广播，2-组织机构广播
     */
    private Integer type;

    /**
     * 接收广播的设备ID
     */
    private Integer deviceId;
    /**
     * 设备编码
     */
    private String code;
    /**
     * 广播内容
     */
    private String content;
    /**
     * 发送时间
     */
    private Date sendTime;

    /**
     * 重试次数
     */
    private Integer retryCnt;

    /**
     * 发送人姓名
     */
    private String sendUserName;

    /**
     * 发送状态 0-未发送，1-已发送，2-已确认 ，3-已失效
     */
    private Integer state;


}
