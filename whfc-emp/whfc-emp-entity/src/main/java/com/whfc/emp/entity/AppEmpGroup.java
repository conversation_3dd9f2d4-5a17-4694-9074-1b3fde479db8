package com.whfc.emp.entity;

import java.util.Date;

/**
 * 班组信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-09-03 16:01
 */
public class AppEmpGroup {
    private Integer id;

    /**
     * 组织机构id
     */
    private Integer deptId;

    /**
     * 合作单位id
     */
    private Integer corpId;

    /**
     * 合作单位编码
     */
    private String corpCode;

    /**
     * 合作单位名称
     */
    private String corpName;

    /**
     * 班组名称
     */
    private String groupName;

    /**
     * 班组编码
     */
    private String groupCode;

    /**
     * 责任人姓名
     */
    private String responsiblePersonName;

    /**
     * 责任人电话
     */
    private String responsiblePersonPhone;

    /**
     * 证件类型
     * (1-居民身份证 2-澳门永久居民证 3-澳门非永久居民证 4-澳门外地雇员身份识别证
     * 5-澳门特别逗留证)
     */
    private Integer responsibleIdcardType;

    /**
     * 责任人证件号码
     */
    private String responsibleIdcardNumber;

    /**
     * 备注
     */
    private String remark;

    /**
     * 进场日期
     */
    private Date entryTime;

    /**
     * 退场日期
     */
    private Date exitTime;

    /**
     * 监控设备ID
     */
    private Integer fvsDeviceId;

    /**
     * 监控设备名称
     */
    private String fvsDeviceName;

    /**
     * 删除标记(0-未删除 1-已删除)
     */
    private Integer delFlag;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getDeptId() {
        return deptId;
    }

    public void setDeptId(Integer deptId) {
        this.deptId = deptId;
    }

    public Integer getCorpId() {
        return corpId;
    }

    public void setCorpId(Integer corpId) {
        this.corpId = corpId;
    }

    public String getCorpCode() {
        return corpCode;
    }

    public void setCorpCode(String corpCode) {
        this.corpCode = corpCode;
    }

    public String getCorpName() {
        return corpName;
    }

    public void setCorpName(String corpName) {
        this.corpName = corpName;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }

    public String getResponsiblePersonName() {
        return responsiblePersonName;
    }

    public void setResponsiblePersonName(String responsiblePersonName) {
        this.responsiblePersonName = responsiblePersonName;
    }

    public String getResponsiblePersonPhone() {
        return responsiblePersonPhone;
    }

    public void setResponsiblePersonPhone(String responsiblePersonPhone) {
        this.responsiblePersonPhone = responsiblePersonPhone;
    }

    public Integer getResponsibleIdcardType() {
        return responsibleIdcardType;
    }

    public void setResponsibleIdcardType(Integer responsibleIdcardType) {
        this.responsibleIdcardType = responsibleIdcardType;
    }

    public String getResponsibleIdcardNumber() {
        return responsibleIdcardNumber;
    }

    public void setResponsibleIdcardNumber(String responsibleIdcardNumber) {
        this.responsibleIdcardNumber = responsibleIdcardNumber;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getEntryTime() {
        return entryTime;
    }

    public void setEntryTime(Date entryTime) {
        this.entryTime = entryTime;
    }

    public Date getExitTime() {
        return exitTime;
    }

    public void setExitTime(Date exitTime) {
        this.exitTime = exitTime;
    }

    public Integer getFvsDeviceId() {
        return fvsDeviceId;
    }

    public void setFvsDeviceId(Integer fvsDeviceId) {
        this.fvsDeviceId = fvsDeviceId;
    }

    public String getFvsDeviceName() {
        return fvsDeviceName;
    }

    public void setFvsDeviceName(String fvsDeviceName) {
        this.fvsDeviceName = fvsDeviceName;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}