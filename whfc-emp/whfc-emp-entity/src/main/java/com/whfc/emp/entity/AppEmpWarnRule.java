package com.whfc.emp.entity;

import lombok.Data;

import java.util.Date;

/**
 * 人员报警规则
 */
@Data
public class AppEmpWarnRule {
    private Integer id;

    /**
     * 报警规则名称
     */
    private String name;

    /**
     * 项目ID
     */
    private Integer deptId;

    /**
     * 报警规则类型: 101-跌落报警 102-摔倒报警, 103-脱帽报警 ,104-SOS报警
     */
    private Integer ruleType;

    /**
     * 报警类型 1-硬件报警 2-系统报警
     */
    private Integer warnType;

    /**
     * 报警报警参数
     */
    private String ruleParam;

    /**
     * 启用标记(0-未启用 1-已启用)
     */
    private Integer enableFlag;

    /**
     * 删除标记(0-未删除 1-已删除)
     */
    private Integer delFlag;

    private Date updateTime;

    private Date createTime;
}