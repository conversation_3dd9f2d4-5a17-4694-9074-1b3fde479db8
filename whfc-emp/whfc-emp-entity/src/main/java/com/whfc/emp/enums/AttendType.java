package com.whfc.emp.enums;

/**
 * @Description: 人员考勤方式
 * @author: likang
 * @version: 1.0
 * @date: 2020/2/27 16:43
 */
public enum AttendType {

    FENCE(1, "电子围栏"),

    FACEGATE(2, "闸机考勤"),

    MS(3, "后台打卡"),

    QR(4, "扫码打卡");

    private Integer value;

    private String desc;

    AttendType(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static AttendType parseByValue(Integer value) {
        AttendType[] attendTypes = AttendType.values();
        for (AttendType attendType : attendTypes) {
            if (attendType.value.equals(value)) {
                return attendType;
            }
        }
        return FACEGATE;
    }
}
