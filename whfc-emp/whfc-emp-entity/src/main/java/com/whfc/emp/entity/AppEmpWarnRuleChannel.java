package com.whfc.emp.entity;

import java.util.Date;

/**
    * 人员报警接收方式
    */
public class AppEmpWarnRuleChannel {
    private Integer id;

    /**
    * 人员报警规则id
    */
    private Integer ruleId;

    /**
    * 接收方式 1-小程序 2-公众号 3-后台 4-短信
    */
    private Integer msgChannel;

    /**
    * 删除标记 0-未删除 1-已删除
    */
    private Integer delFlag;

    private Date updateTime;

    private Date createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getRuleId() {
        return ruleId;
    }

    public void setRuleId(Integer ruleId) {
        this.ruleId = ruleId;
    }

    public Integer getMsgChannel() {
        return msgChannel;
    }

    public void setMsgChannel(Integer msgChannel) {
        this.msgChannel = msgChannel;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}