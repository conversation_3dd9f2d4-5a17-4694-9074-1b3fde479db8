package com.whfc.emp.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 人员信息DTO
 */
@Schema(description = "人员信息DTO")
@Data
public class AppEmpDTO implements Serializable {

    /**
     * 人员id
     */
    @Schema(description =  "人员id")
    private Integer empId;

    /**
     * 人员姓名
     */
    @Schema(description =  "人员姓名")
    @ColumnWidth(20)
    @ExcelProperty("姓名")
    private String empName;

    /**
     * 人员编码
     */
    @Schema(description =  "人员编码")
    private String empCode;

    /**
     * 姓名拼音
     */
    @Schema(description =  "姓名拼音")
    private String ename;

    /**
     * 电话
     */
    @Schema(description =  "电话")
    private String phone;

    /**
     * 性别 1-男 2-女
     */
    @Schema(description =  "性别 1-男 2-女")
    private Integer gender;

    /**
     * 性别
     */
    @Schema(description =  "性别")
    private String genderName;

    /**
     * 用户图像
     */
    @Schema(description =  "用户图像")
    private String avatar;

    /**
     * 生日
     */
    @Schema(description =  "生日")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date birthday;

    /**
     * 民族
     */
    @Schema(description =  "民族")
    private String nation;

    /**
     * 学历
     */
    @Schema(description =  "学历")
    private Integer education;

    /**
     * 学位
     */
    @Schema(description =  "学位")
    private Integer degree;

    /**
     * 组织机构
     */
    @Schema(description =  "组织机构")
    private Integer deptId;

    /**
     * 组织机构名称
     */
    @Schema(description =  "组织机构名称")
    private String deptName;

    /**
     * 合作单位
     */
    @Schema(description =  "合作单位")
    private Integer corpId;

    /**
     * 合作单位类型
     */
    @Schema(description =  "合作单位类型")
    private Integer corpType;

    /**
     * 合作单位名称
     */
    @Schema(description =  "合作单位名称")
    @ColumnWidth(20)
    @ExcelProperty("单位")
    private String corpName;

    /**
     * 班组id
     */
    @Schema(description =  "班组id")
    private Integer groupId;

    /**
     * 班组名称
     */
    @Schema(description =  "班组名称")
    @ColumnWidth(20)
    @ExcelProperty("班组")
    private String groupName;

    /**
     * 是否班组长
     */
    @Schema(description =  "是否班组长")
    private Integer leaderFlag;

    /**
     * 工人类型
     */
    @Schema(description =  "工人类型")
    private Integer workRoleId;

    /**
     * 工人类型名称
     */
    @Schema(description =  "工人类型名称")
    @ColumnWidth(20)
    @ExcelProperty("管理人员/建筑工人")
    private String workRoleName;

    /**
     * 工种
     */
    @Schema(description =  "工种")
    private Integer workTypeId;

    /**
     * 工种名称
     */
    @Schema(description =  "工种名称")
    @ColumnWidth(20)
    @ExcelProperty("工种")
    private String workTypeName;

    /**
     * 岗位状态 0-离岗 1-在岗
     */
    @Schema(description =  "岗位状态 0-离岗 1-在岗")
    private Integer postState;

    /**
     * 岗位状态
     */
    @Schema(description =  "岗位状态")
    private String postStateName;

    /**
     * 入场时间
     */
    @Schema(description =  "入场时间")
    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date enterTime;

    /**
     * 离场时间
     */
    @Schema(description =  "离场时间")
    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date outerTime;

    /**
     * 安全帽硬件平台
     */
    @Schema(description =  "安全帽硬件平台")
    private String platform;

    /**
     * 安全帽硬件
     */
    @Schema(description =  "安全帽硬件")
    private String sn;

    @Schema(description =  "硬件类型")
    private Integer deviceType;

    /**
     * 安全帽颜色
     */
    @Schema(description =  "安全帽颜色")
    private String color;

    /**
     * 绑定状态 0-未绑定 1-已绑定
     */
    @Schema(description =  "绑定状态 0-未绑定 1-已绑定")
    private Integer bindFlag;

    /**
     * 证件类型 (1-居民身份证 2-澳门永久居民证，3-澳门非永久居民证 4-澳门外地雇员身份识别证 5-澳门特别逗留证)
     */
    @Schema(description =  "证件类型 (1-居民身份证 2-澳门永久居民证，3-澳门非永久居民证 4-澳门外地雇员身份识别证 5-澳门特别逗留证)")
    private Integer cardType;

    /**
     * 工作证编号
     */
    @Schema(description =  "工作证编号")
    private String workCardNo;

    /**
     * 工作证有效期
     */
    @Schema(description =  "工作证有效期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date workCardEndDate;

    /**
     * 身份证-号码
     */
    @Schema(description =  "身份证-号码")
    private String idCardNo;

    /**
     * 身份证-正面
     */
    @Schema(description =  "身份证-正面")
    private String idCardFront;

    /**
     * 身份证-反面
     */
    @Schema(description =  "身份证-反面")
    private String idCardBack;

    /**
     * 身份证-发证机构
     */
    @Schema(description =  "身份证-发证机构")
    private String idCardGrantOrg;

    /**
     * 身份证-发证日期
     */
    @Schema(description =  "身份证-发证日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date idCardStartDate;

    /**
     * 身份证-有效期
     */
    @Schema(description =  "身份证-有效期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date idCardEndDate;

    /**
     * 身份证头像
     */
    @Schema(description =  "身份证头像")
    private String headImg;


    /**
     * 地址
     */
    @Schema(description =  "地址")
    private String address;

    /**
     * 出勤状态
     */
    @Schema(description =  "出勤状态")
    private Integer attendState;

    /**
     * 现场状态
     */
    @Schema(description =  "现场状态")
    private Integer localeState;

    /**
     * 现场时间
     */
    @Schema(description =  "现场时间")
    @ColumnWidth(50)
    @ExcelProperty("进场时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date localeTime;

    /**
     * 网络状态
     */
    @Schema(description =  "网络状态")
    private Integer netState;

    /**
     * 最近一次硬件通信时间
     */
    @Schema(description =  "最近一次硬件通信时间")
    private Date time;

    @Schema(description =  "GPS时间")
    private Date gpsTime;

    /**
     * 经度
     */
    @Schema(description =  "经度")
    private Double lng;

    /**
     * 纬度
     */
    @Schema(description =  "纬度")
    private Double lat;

    /**
     * 安全帽图标
     */
    @Schema(description =  "安全帽图标")
    private String icon;

    /**
     * 安全帽图标地址
     */
    @Schema(description =  "安全帽图标地址")
    private String iconUrl;

    /**
     * 年龄
     */
    @Schema(description =  "年龄")
    private Integer age;

    /**
     * 人员二维码信息
     */
    @Schema(description =  "人员二维码信息")
    private String qr;

    /**
     * 电量
     */
    @Schema(description =  "电量")
    private Double batteryPower;

    /**
     * 是否合格  0-不合格  1-合格
     */
    @Schema(description =  "是否合格  0-不合格  1-合格")
    private Integer passFlag;

    /**
     * 入职离职状态
     */
    @Schema(description =  "入职离职状态")
    private Integer type;

    /**
     * 关键岗位标记 0-否 1-是
     */
    @Schema(description =  "关键岗位标记 0-否 1-是")
    private Integer keyPositionFlag;

    /**
     * 关键岗位信息认证 0-否 1-是
     */
    @Schema(description =  "关键岗位信息认证 0-否 1-是")
    private Integer keyPositionAuth;

    /**
     * 是否参加入场三级教育（0-未参加  1-已参加）
     */
    @Schema(description =  "是否参加入场三级教育（0-未参加  1-已参加）")
    private Integer enterTrainFlag;

    /**
     * 人证核验状态
     */
    @Schema(description =  "人证核验状态")
    private Integer verifyState;

    /**
     * 同步状态
     */
    @Schema(description =  "同步状态")
    private Integer syncFlag;

    /**
     * 更新时间
     */
    @Schema(description =  "更新时间")
    private Date updateTime;

    /**
     * 创建时间
     */
    @Schema(description =  "创建时间")
    private Date createTime;
}
