package com.whfc.emp.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class AppTrainAddParam implements Serializable {
	/**
	 * 培训名称
	 */
	@NotEmpty
	private String name;
	/**
	 * 培训类型id
	 */
	@NotNull
	private Integer trainType;
	/**
	 * 组织机构id
	 */
	@NotNull
	private Integer deptId;
	/**
	 * 培训日期
	 */
	@JsonFormat(pattern="yyyy-MM-dd")
	private Date date;
	/**
	 * 培训时长
	 */
	private Double duration;
	/**
	 * 组织者Id
	 */
	private Integer organizerId;

	/**
	 * 组织者
	 */
	private String organizer;
	/**
	 * 培训人Id
	 */
	private String trainerId;
	/**
	 * 培训人
	 */
	private String trainer;
	/**
	 * 培训地址
	 */
	private String address;
	/**
	 * 培训内容
	 */
	private String content;

	/**
	 * 培训照片
	 */
	private List<String> trainPhoto;

	/**
	 * 附件
	 */
	private List<String> attachment;
}
