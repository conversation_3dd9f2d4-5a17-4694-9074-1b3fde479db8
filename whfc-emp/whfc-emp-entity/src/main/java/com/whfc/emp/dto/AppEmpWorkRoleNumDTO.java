package com.whfc.emp.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/19 10:32
 */
@Schema(description = "人员工人类型数量")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AppEmpWorkRoleNumDTO implements Serializable {

    @Schema(description =  "工人类型id")
    private Integer workRoleId;

    @Schema(description =  "工人类型名称")
    private String workRoleName;

    @Schema(description =  "数量")
    private Integer num;

    @Schema(description =  "出勤数量")
    private Integer attendNum;

    @Schema(description =  "在场数量")
    private Integer localeNum;

    public void accumulate(int num, int attendNum, int localeNum) {
        this.num += num;
        this.attendNum += attendNum;
        this.localeNum += localeNum;
    }

}
