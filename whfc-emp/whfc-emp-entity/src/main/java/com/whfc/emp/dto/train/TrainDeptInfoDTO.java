package com.whfc.emp.dto.train;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 部门信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-08-26 11:37
 */
@Data
public class TrainDeptInfoDTO implements Serializable {

    /**
     * 组织机构ID
     */
    private Integer deptId;
    /**
     * 主键Id
     */
    @JSONField(name = "ID")
    private String id;
    /**
     * 创建时间
     */
    @JSONField(name = "CreateDate", format = "yyyy-MM-dd'T'HH:mm:ss")
    private Date createDate;
    /**
     * 创建用户
     */
    @JSONField(name = "CreateUser")
    private String createUser;
    /**
     * 修改时间
     */
    @JSONField(name = "OperDate", format = "yyyy-MM-dd'T'HH:mm:ss")
    private Date operDate;
    /**
     * 修改用户
     */
    @JSONField(name = "OperUser")
    private String operUser;
    /**
     * 父级Id
     */
    @JSONField(name = "ParentID")
    private String parentId;
    /**
     * 单位Code
     */
    @JSONField(name = "DepartCode")
    private String departCode;
    /**
     * 父级Code
     */
    @JSONField(name = "ParentCode")
    private String parentCode;
    /**
     * 单位名称
     */
    @JSONField(name = "DepartName")
    private String departName;
    /**
     * 单位类型
     */
    @JSONField(name = "DepartType")
    private String departType;
    /**
     * 单位简称
     */
    @JSONField(name = "DepartShortName")
    private String departShortName;
    /**
     * 顺序号
     */
    @JSONField(name = "DepartOrder")
    private Integer departOrder;
    /**
     * 是否停用(0:否 1:是)
     */
    @JSONField(name = "State")
    private Integer state;
    /**
     * 单位级别 0：企业级 1：终端级 其它则为终端以下
     */
    @JSONField(name = "DepartSir")
    private String departSir;

    /**
     * 单位电话
     */
    @JSONField(name = "Phone")
    private String phone;
    /**
     * 单位负责人
     */
    @JSONField(name = "Charge")
    private String charge;

    /**
     * 备注
     */
    @JSONField(name = "Remark")
    private String remark;
    /**
     * 所属项目部Id
     */
    @JSONField(name = "OwnerDeptID")
    private String ownerDeptId;
    /**
     * 备用编码
     */
    @JSONField(name = "ProjectCode")
    private String projectCode;
    /**
     * 删除标记(true/false)
     */
    @JSONField(name = "DeleteTag")
    private Boolean deleteTag;


}
