package com.whfc.emp.param;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 闸机识别后待处理的参数
 * @date 2020-09-15
 */
@Data
public class FaceGateDataParam implements Serializable {
    /**
     * 项目id
     */
    private Integer projectId;
    /**
     * 人员id
     */
    private Integer empId;
    /**
     * 识别时间
     */
    private Date time;
    /**
     * 识别照片
     */
    private String picture;
    /**
     * 闸机方向
     */
    private Integer direction;
    /**
     * 闸机名称
     */
    private String name;
    /**
     * 闸机序列号
     */
    private String deviceKey;

}
