package com.whfc.emp.enums;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/29
 */
public enum HealthType {

    BODY_TEMP("bodyTemp", "体温"),
    HEART_RATE("heartRate", "心率"),
    BLOOD_PRESSURE("bloodPressure", "血压"),
    BLOOD_OXYGEN("bloodOxygen", "血氧"),
    BLOOD_SUGAR("bloodSugar", "血糖");
    private final String value;
    private final String desc;

    HealthType(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static List<String> getHealthColumns(String value) {
        List<String> list = new ArrayList<>();
        for (HealthType healthType : HealthType.values()) {
            // 血压特殊处理
            if ("bloodPressure".equals(healthType.getValue())) {
                list.add("diastolicPressure");
                list.add("systolicPressure");
                return list;
            } else if (healthType.getValue().equals(value)) {
                list.add(healthType.getValue());
                return list;
            }
        }
        return new ArrayList<>();
    }
}
