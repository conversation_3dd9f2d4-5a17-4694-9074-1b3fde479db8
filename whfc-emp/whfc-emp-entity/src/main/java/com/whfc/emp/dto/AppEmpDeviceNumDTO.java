package com.whfc.emp.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 人员管理-相关设备统计
 */
@Data
public class AppEmpDeviceNumDTO implements Serializable {

    /**
     * 设备总数量
     */
    private Integer deviceTotal;

    /**
     * 在线设备数量
     */
    private Integer deviceOnline;

    /**
     * 离线设备数量
     */
    private Integer deviceOffline;

    /**
     * 闸机总数量
     */
    private Integer faceGateTotal;

    /**
     * 闸机在线数量
     */
    private Integer faceGateOnline;

    /**
     * 基站总数量
     */
    private Integer stationTotal;

    /**
     * 基站在线数量
     */
    private Integer stationOnline;

    /**
     * 标签总数量
     */
    private Integer tagTotal;

    /**
     * 标签在线数量
     */
    private Integer tagOnline;

    /**
     * 安全帽总数量
     */
    private Integer helmetTotal;

    /**
     * 安全帽在线数量
     */
    private Integer helmetOnline;
}
