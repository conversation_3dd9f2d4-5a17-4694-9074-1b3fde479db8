package com.whfc.emp.entity;

import java.util.Date;
import lombok.Data;

/**
 * 人员硬件-每日统计
 */
@Data
public class AppEmpDay {
    private Integer id;

    /**
     * 组织机构ID
     */
    private Integer deptId;

    /**
     * 人员ID
     */
    private Integer empId;

    /**
     * 工人类型ID
     */
    private Integer workRoleId;

    /**
     * 日期
     */
    private Date date;

    /**
     * 报警次数
     */
    private Integer warnCnt;

    /**
     * 摔倒次数
     */
    private Integer fallCnt;

    /**
     * 跌落次数
     */
    private Integer dropCnt;

    /**
     * 脱帽次数
     */
    private Integer doffCnt;

    /**
     * 出勤状态(0-缺勤 1-出勤)
     */
    private Integer attendState;

    /**
     * 上班时间
     */
    private Date startTime;

    /**
     * 下班时间
     */
    private Date endTime;

    /**
     * 工作时长(秒)
     */
    private Integer workTimes;

    /**
     * 安全帽第一次在电子围栏内时间
     */
    private Date helmetStartTime;

    /**
     * 安全帽最后一次在电子围栏内时间
     */
    private Date helmetEndTime;

    /**
     * 安全帽出勤时长(s)
     */
    private Integer helmetTimes;

    /**
     * 闸机第一次进时间
     */
    private Date faceGateStartTime;

    /**
     * 闸机最后一次出时间
     */
    private Date faceGateEndTime;

    /**
     * 闸机出勤时长(s)
     */
    private Integer faceGateTimes;

    /**
     * 考勤方式
     */
    private Integer attendType;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;
}