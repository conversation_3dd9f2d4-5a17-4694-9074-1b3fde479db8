package com.whfc.emp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 安全帽通讯次数
 * @date 2021-02-02
 */
@Data
public class AppEmpDeviceStatNumDTO implements Serializable {
    /**
     * 开始时间
     */
    @JsonFormat(pattern = "HH:mm")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "HH:mm")
    private Date endTime;

    /**
     * 通讯次数
     */
    private Integer num;
}
