package com.whfc.emp.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-09-02 09:45
 */
public enum EmpHealthTestResultType {

    NEGATIVE(1, "阴性"),

    POSITIVE(2, "阳性");

    private final Integer value;

    private final String desc;

    EmpHealthTestResultType(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
