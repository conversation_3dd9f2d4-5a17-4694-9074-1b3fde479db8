package com.whfc.emp.param.indoor;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/3/4 16:46
 */
@Data
public class AppIndoorPositionMapAdd implements Serializable {

    @NotNull
    private Integer deptId;

    @NotEmpty
    @Length(max = 32)
    private String name;

    /**
     * 区域ID
     */
    private Integer areaId;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 地图url
     */
    private String imgUrl;

    /**
     * 地图-像素宽
     */
    private Double pixelWidth;

    /**
     * 地图-像素高
     */
    private Double pixelLength;

    /**
     * 地图-实际宽
     */
    private Double realWidth;

    /**
     * 地图-实际高
     */
    private Double realLength;
}
