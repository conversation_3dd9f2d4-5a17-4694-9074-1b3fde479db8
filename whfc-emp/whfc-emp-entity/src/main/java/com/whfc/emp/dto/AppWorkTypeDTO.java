package com.whfc.emp.dto;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description:
 * @date 2019年10月19日
 */
@Data
public class AppWorkTypeDTO implements Serializable {

    private Integer deptId;

    private String workTypeName;

    private Integer workTypeId;

    private Integer workRoleId;

    /**
     * 工种编码
     */
    private String workCode;

    private Integer spec;

    /**
     * 是否需要持证上岗 0-不需要 1-需要
     */
    private Integer needCert;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        AppWorkTypeDTO that = (AppWorkTypeDTO) o;
        return Objects.equals(workTypeId, that.workTypeId) &&
                Objects.equals(workRoleId, that.workRoleId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(workTypeId, workRoleId);
    }
}
