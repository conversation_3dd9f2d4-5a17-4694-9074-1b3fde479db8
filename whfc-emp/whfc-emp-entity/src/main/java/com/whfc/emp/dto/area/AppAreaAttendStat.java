package com.whfc.emp.dto.area;

import com.whfc.emp.dto.AppEmpDTO;
import com.whfc.emp.dto.AppEmpWorkRoleNumDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/8/2 11:07
 */
@Schema(description = "人员区域考勤统计")
@Data
public class AppAreaAttendStat implements Serializable {

    /**
     * 工区ID
     */
    @Schema(description =  "工区ID")
    private Integer areaId;

    /**
     * 工区名称
     */
    @Schema(description =  "工区名称")
    private String areaName;

    /**
     * 班组ID
     */
    @Schema(description =  "班组ID")
    private Integer groupId;

    /**
     * 班组名称
     */
    @Schema(description =  "班组名称")
    private String groupName;

    /**
     * 闸机数量
     */
    @Schema(description =  "闸机数量")
    private Integer faceGateNum;

    /**
     * 场内人数
     */
    @Schema(description =  "场内人数")
    private Integer localeNum;

    /**
     * 场内工人
     */
    @Deprecated
    @Schema(description =  "场内工人")
    private Integer localeWorkerNum;

    /**
     * 场内管理
     */
    @Deprecated
    @Schema(description =  "场内管理")
    private Integer localeManagerNum;

    /**
     * 场内各工人类型数量
     */
    private List<AppEmpWorkRoleNumDTO> workRoleNumList;

    /**
     * 出勤人数
     */
    @Schema(description =  "出勤人数")
    private Integer attendNum;

    /**
     * 出勤工人
     */
    @Deprecated
    @Schema(description =  "出勤工人")
    private Integer attendWorkerNum;

    /**
     * 出勤管理
     */
    @Deprecated
    @Schema(description =  "出勤管理")
    private Integer attendManagerNum;

    /**
     * 班组区域考勤
     */
    @Schema(description =  "班组区域考勤")
    private List<AppAreaAttendStat> groupList;

    /**
     * 管理人员列表
     */
    @Deprecated
    @Schema(description =  "管理人员列表")
    private List<AppEmpDTO> managerList;

    /**
     * 工人列表
     */
    @Deprecated
    @Schema(description =  "工人列表")
    private List<AppEmpDTO> workerList;

    @Schema(description =  "工人类型列表")
    private Map<String,List<AppEmpDTO>> workRoleMap;
}
