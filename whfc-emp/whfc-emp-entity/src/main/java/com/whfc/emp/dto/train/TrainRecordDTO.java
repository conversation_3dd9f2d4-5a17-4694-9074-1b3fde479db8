package com.whfc.emp.dto.train;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 培训记录信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-08-26 11:51
 */
@Data
public class TrainRecordDTO implements Serializable {

    /**
     * 组织机构ID
     */
    private Integer deptId;
    /**
     * 删除用户
     */
    @JSONField(name = "DeleteUser")
    private String deleteUser;
    /**
     * 删除时间
     */
    @JSONField(name = "DeleteDate", format = "yyyy-MM-dd'T'HH:mm:ss")
    private Date deleteDate;
    /**
     * 删除标记(true/false)
     */
    @JSONField(name = "DeleteTag")
    private Boolean deleteTag;
    /**
     * 培训记录名称
     */
    @JSONField(name = "RecordName")
    private String recordName;
    /**
     * 培训类型
     */
    @JSONField(name = "TrainType")
    private String trainType;
    /**
     * 出卷类型 0:使用原卷 1:重新出卷
     */
    @JSONField(name = "PaperMode")
    private Integer paperMode;
    /**
     * 培训方式 逗号分隔类型
     */
    @JSONField(name = "TrainMode")
    private String trainMode;
    /**
     * 培训负责人
     */
    @JSONField(name = "TrainPrincipal")
    private String trainPrincipal;
    /**
     * 培训开始时间
     */
    @JSONField(name = "TrainStartDate", format = "yyyy-MM-dd'T'HH:mm:ss")
    private Date trainStartDate;
    /**
     * 培训结束时间
     */
    @JSONField(name = "TrainEndDate", format = "yyyy-MM-dd'T'HH:mm:ss")
    private Date trainEndDate;
    /**
     * 培训内容
     */
    @JSONField(name = "TrainContent")
    private String trainContent;
    /**
     * 培训详细信息
     */
    @JSONField(name = "TrainDescript")
    private String trainDescript;
    /**
     * 培训学时
     */
    @JSONField(name = "TrainPeriod")
    private Integer trainPeriod;
    /**
     * 培训人员数量
     */
    @JSONField(name = "PersonCount")
    private Integer personCount;
    /**
     * 合格人员数量
     */
    @JSONField(name = "PassedCount")
    private Integer passedCount;
    /**
     * 封面图片
     */
    @JSONField(name = "CoverImg")
    private String coverImg;
    /**
     * 项目Id
     */
    @JSONField(name = "DemandID")
    private String demandId;
    /**
     * 课程数量
     */
    @JSONField(name = "CourseCount")
    private Integer courseCount;
    /**
     * 课程总时长 秒
     */
    @JSONField(name = "CourseDuration")
    private Integer courseDuration;
    /**
     * 来源 0:项目 1:课程
     */
    @JSONField(name = "Source")
    private Integer source;
    /**
     * 说明
     */
    @JSONField(name = "Description")
    private String description;
    /**
     * 设备编号
     */
    @JSONField(name = "DeviceNo")
    private String deviceNo;
    /**
     * 所属项目部Id
     */
    @JSONField(name = "OwnerDepartId")
    private String ownerDepartId;
    /**
     * 上传时间
     */
    @JSONField(name = "UploadTime", format = "yyyy-MM-dd'T'HH:mm:ss")
    private Date uploadTime;
    /**
     * 培训单位
     */
    @JSONField(name = "TrainDepart")
    private String trainDepart;
    /**
     * 主键Id
     */
    @JSONField(name = "ID")
    private String id;
    /**
     * 创建时间
     */
    @JSONField(name = "CreateDate", format = "yyyy-MM-dd'T'HH:mm:ss")
    private Date createDate;
    /**
     * 创建用户
     */
    @JSONField(name = "CreateUser")
    private String createUser;
    /**
     * 修改时间
     */
    @JSONField(name = "OperDate", format = "yyyy-MM-dd'T'HH:mm:ss")
    private Date operDate;
    /**
     * 修改用户
     */
    @JSONField(name = "OperUser")
    private String operUser;


}
