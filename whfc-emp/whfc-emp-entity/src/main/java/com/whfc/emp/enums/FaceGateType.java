package com.whfc.emp.enums;

/**
 * 闸机类型
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/2/27 16:43
 */
public enum FaceGateType {

    //WOTU(1, "沃土", "wotu", 1),

    //WHEATSUNSHINE(2, "小麦科技", "wheatsunshine", 1),

    WO(3, "沃", "wo", 1),

    YFOFFLINE(4, "宇泛智能(离线)", "yfoffline", 0),

    ZKTECO(5, "中控智慧(离线)", "zk", 0),

    SZYC(6, "深圳玉川(离线)", "fcr", 0),

    YUNQI(7, "云起智能(离线)", "rec", 0),

    DEBAO(8, "德宝智能(离线)", "debao", 0),

    MQTT_HQSX_ZJJ(9, "MQTT(海清视讯住建局版)", "mqtt-hqsx-zjj", 0),

    MQTT(10, "MQTT", "mqtt", 1),

    BRESEE(11, "博冠智能(无感考勤)", "bresee", 0),

    KYY(12, "快优易", "kyy", 1),

    MQTT_SZYC(13, "深圳玉川(MQTT)", "mqtt-szyc", 1),

    HIK(14, "海康", "hik", 0),

    XENSE(15, "XenseTech", "xense", 0),

    MQTT_RT(16, "睿瞳", "mqtt-rt", 1),

    OFFLINE(-1, "其他品牌(离线)", "offline", 0);

    /**
     * 闸机值
     */
    private final Integer value;

    /**
     * 闸机描述
     */
    private final String desc;

    /**
     * 闸机编码
     */
    private final String code;

    /**
     * 闸机状态  0-离线闸机 1-在线闸机
     */
    private final Integer state;

    FaceGateType(Integer value, String desc, String code, Integer state) {
        this.value = value;
        this.desc = desc;
        this.code = code;
        this.state = state;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public String getCode() {
        return code;
    }

    public Integer getState() {
        return state;
    }

    /**
     * 闸机是否在线
     *
     * @return
     */
    public boolean isOnline() {
        return state == 1;
    }

    /**
     * 闸机是否离线
     *
     * @return
     */
    public boolean isOffline() {
        return !isOnline();
    }

    /**
     * 是否支持批量处理
     *
     * @return 是否支持批量处理
     */
    public boolean isSupportBatch() {
        // 目前只有MQTT支持批量操作
        // 如果有新的闸机支持 需要修改此处代码
        if (MQTT.equals(this)) {
            return true;
        }
        return false;
    }

    /**
     * 解析闸机类型
     *
     * @param code
     * @return
     */
    public static FaceGateType parseCode(String code) {
        FaceGateType[] values = FaceGateType.values();
        for (FaceGateType type : values) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return OFFLINE;
    }

    public static void main(String[] args) {
        FaceGateType faceGateType = FaceGateType.parseCode("wo");
        System.out.println(faceGateType.isOnline());
    }
}
