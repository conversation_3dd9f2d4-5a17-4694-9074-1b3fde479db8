package com.whfc.emp.enums;

/**
 * 广播发送类型
 *
 * <AUTHOR> qzexing
 * @version : 1.0
 * @date : 2020/7/2 19:24
 */
public enum AppBroadcastType {

    EMP(1, "人员广播"),

    DEPT(2, "组织机构广播");

    private Integer value;

    private String desc;

    AppBroadcastType(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static AppBroadcastType parseValue(Integer value) {
        switch (value) {
            case 1:
                return EMP;
            case 2:
                return DEPT;
            default:
                return null;
        }
    }

}
