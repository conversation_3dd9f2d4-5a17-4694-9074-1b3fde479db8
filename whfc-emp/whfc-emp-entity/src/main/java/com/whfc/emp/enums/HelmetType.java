package com.whfc.emp.enums;

/**
 * @Description: 安全帽类型
 * @author: xug<PERSON>
 * @version: 1.0
 * @date: 2019/7/29 11:37
 */
public enum HelmetType {

    GPS(1, "定位安全帽"),

    VOICE(2, "语音播报安全帽");

    private Integer value;

    private String desc;

    HelmetType(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
