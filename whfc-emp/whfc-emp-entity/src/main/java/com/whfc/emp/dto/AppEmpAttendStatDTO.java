package com.whfc.emp.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/4
 */
@Data
@Schema(description = "人员出勤统计")
public class AppEmpAttendStatDTO implements Serializable {

    @Schema(description = "总人数")
    private Integer totalNum;

    @Schema(description = "出勤人员数量")
    private Integer attendNum;

    @Schema(description = "出勤率")
    private Double attendRate;

    @Schema(description = "在场人员数量")
    private Integer localeNum;

    @Schema(description = "离场人员数量")
    private Integer outLocaleNum;

    @Schema(description = "缺勤人员数量")
    private Integer absentNum;

    @Schema(description = "班组出勤情况")
    private List<AppEmpAttendGroupDTO> groupAttend;

}
