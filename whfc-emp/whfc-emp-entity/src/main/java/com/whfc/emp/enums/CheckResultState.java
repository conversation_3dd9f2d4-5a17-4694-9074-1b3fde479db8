package com.whfc.emp.enums;

/**
 * @ClasssName CheckResultState
 * @Description 审核状态
 * <AUTHOR>
 * @Date 2021/2/26 14:52
 * @Version 1.0
 */
public enum CheckResultState {

    SUCCESS(1,"已通过"),
    FAIL(2,"已拒绝");

    private Integer value;

    private String desc;

    CheckResultState(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

}
