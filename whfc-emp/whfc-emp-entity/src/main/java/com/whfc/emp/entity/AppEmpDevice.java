package com.whfc.emp.entity;

import java.util.Date;

import lombok.Data;

/**
 * 人员硬件信息
 */

@Data
public class AppEmpDevice {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * guid
     */
    private String guid;

    /**
     * 组织机构ID
     */
    private Integer deptId;

    /**
     * 硬件类型 2-智能安全帽 34-智能手环
     */
    private Integer deviceType;

    /**
     * 硬件平台
     */
    private String platform;

    /**
     * 硬件SN
     */
    private String sn;

    /**
     * 网络状态(0-离线 1-在线)
     */
    private Integer netState;

    /**
     * 电池电量(百分比)
     */
    private Integer batteryPower;

    /**
     * 最近通信时间
     */
    private Date time;

    /**
     * 颜色
     */
    private String color;

    /**
     * 绑定状态（0-未绑定 1-绑定）
     */

    private Integer bindFlag;

    /**
     * 人员ID
     */
    private Integer empId;

    /**
     * 设备ID
     */
    private Integer deviceId;

    /**
     * 绑定时间
     */
    private Date bindTime;

    /**
     * 绑定人员
     */
    private String bindUser;

    /**
     * 删除标记(0-未删除 1-已删除)
     */
    private Integer delFlag;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;

}