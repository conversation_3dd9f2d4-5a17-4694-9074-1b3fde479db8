package com.whfc.emp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 人员合同实体
 * @date 2020-08-13
 */
@Data
public class AppEmpContractDTO implements Serializable {

    /**
     * 合同id
     */
    private Integer id;

    /**
     * 合同状态 0-未签订 1-已签订
     */
    private Integer state;

    /**
     * 合同编号
     */
    private String contractNo;

    /**
     * 合同期限类型(0-固定期限 1-固定工作量)
     */
    private Integer contactType;

    /**
     * 合同开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startDate;

    /**
     * 合同结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endDate;

    /**
     * 工资方式 1-按小时 2-按天 3-按月
     */
    private Integer payType;

    private String payWay;

    /**
     * 单价
     */
    private Double salary;

    /**
     * 附件数
     */
    private Integer fileNo;

    /**
     * 附件
     */
    private List<AppAttachDTO> fileAttachList;

}
