package com.whfc.emp.dto;

import com.whfc.common.geometry.Point;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description 地图页人员聚合实体
 * @date 2021-03-31
 */
@Data
public class MapEmpPolyDTO implements Serializable {
    /**
     * 电子围栏id
     */
    private Integer id;
    /**
     * 电子围栏名称
     */
    private String name;

    /**
     * 电子围栏类型 1-多边形 2-圆形
     */
    private Integer type;

    /**
     * 多边形文本
     */
    private String polygon;

    /**
     * 多边形坐标
     */
    private List<Point> polygonPointList;

    /**
     * 圆形文本
     */
    private String center;

    /**
     * 圆形中心点坐标
     */
    private Point centerPoint;

    /**
     * 圆形半径
     */
    private Double radius;

    /**
     * 电子围栏中的人员列表
     */
    private List<AppEmpDTO> empList;

    /**
     * 电子围栏中的人数
     */
    private Integer empNum;
}
