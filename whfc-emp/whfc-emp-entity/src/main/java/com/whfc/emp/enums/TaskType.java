package com.whfc.emp.enums;

/**
 * @ClasssName TaskType
 * @Description 人员定时任务状态
 * <AUTHOR>
 * @Date 2021/1/22 17:51
 * @Version 1.0
 */
public enum TaskType {

    NOT_START(0, "未开始执行"),

    EMP_ADD(1, "人员注册完成"),

    EMP_ADD_ERROR(-1, "人员注册失败"),

    EMP_IMG_ADD(2, "人员照片注册完成"),

    EMP_IMG_ADD_ERROR(-2, "人员照片注册失败"),

    EMP_AUTH(3, "人员授权完成"),

    EMP_AUTH_ERROR(-3, "人员授权失败"),

    EMP_UN_AUTH(11, "人员取消授权");

    private final Integer value;

    private final String desc;

    TaskType(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static TaskType parseByValue(Integer taskType) {
        TaskType[] types = TaskType.values();
        for (TaskType type : types) {
            if (type.getValue().equals(taskType)) {
                return type;
            }
        }
        return NOT_START;
    }

}
