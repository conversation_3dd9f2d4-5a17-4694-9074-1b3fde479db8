package com.whfc.emp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 人员防疫信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-09-02 10:53
 */
@Data
public class AppEmpHealthReportDTO implements Serializable {

    /**
     * 主键ID
     */
    private Integer healthReportId;

    /**
     * healthCode-健康码  journeyCard-行程卡 testRecord-核算记录  vaccination-疫苗接种
     *
     * @see com.whfc.emp.enums.EmpHealthInfoType
     */
    private String type;

    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date date;

    /**
     * 检测状态  1-阴性  2-阳性
     *
     * @see com.whfc.emp.enums.EmpHealthTestResultType
     */
    private Integer testResult;

    /**
     * 健康码状态 green-绿码  red-红码  yellow-黄码  orange-橙码
     *
     * @see com.whfc.emp.enums.EmpHealthCodeSate
     */
    private String codeState;

    /**
     * 行程
     */
    private String journey;

    /**
     * 失效日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date expDate;

    /**
     * 第一次接种时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date firstTime;

    /**
     * 第二次接种时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date secondTime;

    /**
     * 图片列表
     */
    private List<String> imgList;

}