package com.whfc.emp.enums;

/**
 * @Description: 劳动合同类型
 * @author: xug<PERSON>
 * @version: 1.0
 * @date: 2023/6/30 16:43
 */
public enum ContractType {

    FIXED(0, "固定期限"),

    NOT_FIXED(1, "无固定期限");

    private Integer value;

    private String name;

    ContractType(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}
