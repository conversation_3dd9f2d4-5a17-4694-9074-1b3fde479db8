package com.whfc.emp.param;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Description 校验用户是否需要申请开门
 * <AUTHOR>
 * @Date 2021-06-08 15:42
 * @Version 1.0
 */
@Data
public class AppFaceGateVisitorCheckAddParam implements Serializable {

    /**
     * 闸机id
     */
    @NotNull
    private Integer faceGateId;

    /**
     * 微信code
     */
    private String code;

    /**
     * 用户微信id
     */
    private String openid;
}
