package com.whfc.emp.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClasssName AppEmpLogDTO
 * @Description 人员日志
 * <AUTHOR>
 * @Date 2021/3/17 14:55
 * @Version 1.0
 */
@Schema(description = "人员日志")
@Data
public class AppEmpLogDTO implements Serializable {

    /**
     * 在职人员
     */
    @Schema(description =  "在职人员")
    private Integer empTotal;

    /**
     * 管理人员出勤人数
     */
    @Deprecated
    @Schema(description =  "管理人员出勤人数")
    private Integer attendManagerNum;

    /**
     * 管理人员数量
     */
    @Deprecated
    @Schema(description =  "在职人员")
    private Integer managerNum;

    /**
     * 工人出勤人数
     */
    @Deprecated
    @Schema(description =  "在职人员")
    private Integer attendWorkerNum;

    /**
     * 工人数量
     */
    @Deprecated
    @Schema(description =  "在职人员")
    private Integer workerNum;

    /**
     * 工人类型数量列表
     */
    @Schema(description =  "工人类型数量列表")
    private List<AppEmpWorkRoleNumDTO> workRoleNumList;

    /**
     * 进场列表
     */
    @Schema(description =  "在职人员")
    private List<AppEmpDTO> enterList;

    /**
     * 出场列表
     */
    @Schema(description =  "在职人员")
    private List<AppEmpDTO> outerList;

}
