package com.whfc.emp.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 添加奖惩记录参数实体
 * @date 2020-08-12
 */
@Data
public class AppEmpRewardAddParam implements Serializable {
    @NotNull
    private Integer empId;
    @NotEmpty
    private String empName;
    @NotNull
    private Integer type;
    @NotNull
    private Integer item;
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date date;
    @NotEmpty
    @Length(max = 200)
    private String description;
}
