package com.whfc.emp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 人员管理-日出勤人员
 *
 * <AUTHOR>
 * @Description:
 * @date 2019年9月3日
 */
@Data
public class AttendDayEmpDTO implements Serializable {

    /**
     * 人员id
     */
    private Integer empId;

    /**
     * 人员姓名
     */
    private String empName;

    /**
     * 人员电话
     */
    private String empPhone;

    /**
     * 组织机构名称
     */
    private String deptName;

    /**
     * 工人类型名称
     */
    private String workRoleName;

    /**
     * 工种名称
     */
    private String workTypeName;

    /**
     * 首次打卡时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 末次打卡时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 出勤时长
     */
    private Double workTimes;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date date;

    /**
     * 是否出勤 0-缺勤 1-出勤
     */
    private Integer attendState;

    /**
     * 班组id
     */
    private Integer groupId;

    /**
     * 班组名称
     */
    private String groupName;

    /**
     * 合作单位ID
     */
    private Integer corpId;

    /**
     * 合作单位名称
     */
    private String corpName;
}
