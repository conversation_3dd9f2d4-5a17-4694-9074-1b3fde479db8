package com.whfc.emp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.whfc.common.util.DateUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description 人员申请信息
 * <AUTHOR>
 * @Date 2021-08-06 9:58
 * @Version 1.0
 */
@Data
public class AppEmpApplyDTO implements Serializable {

    /**
     * 人员id
     */
    private Integer empId;
    /**
     * 人员名称
     */
    private String empName;
    /**
     * 生日
     */
    @JsonFormat(pattern = DateUtil.DATE_FORMAT)
    private Date birthday;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 身份证图像
     */
    private String headImg;

    /**
     * 身份证号
     */
    private String idCardNo;

    /**
     * 身份证正面
     */
    private String idCardFront;

    /**
     * 身份证反面
     */
    private String idCardBack;

    /**
     * 身份证颁发机构
     */
    private String idCardGrantOrg;

    /**
     * 有效期开始时间
     */
    @JsonFormat(pattern = DateUtil.DATE_FORMAT)
    private Date idCardStartDate;

    /**
     * 有效期结束时间
     */
    @JsonFormat(pattern = DateUtil.DATE_FORMAT)
    private Date idCardEndDate;

    /**
     * 民族
     */
    private String nation;

    /**
     * 班组Id
     */
    private Integer groupId;

    /**
     * 班组名称
     */
    private String groupName;

    /**
     * 工人类型Id
     */
    private Integer workTypeId;

    /**
     * 工种名称
     */
    private String workTypeName;

    /**
     * 工人种类id
     */
    private Integer workRoleId;

    /**
     * 工人种类
     */
    private String workRoleName;

    /**
     * address
     */
    private String address;

    /**
     * 性别（1-男 2-女）
     */
    private Integer gender;
    /**
     * 用户头像
     */
    private String avatar;

    @JsonFormat(pattern = DateUtil.DATE_TIME_FORMAT)
    private Date enterTime;

    /**
     * 审批人
     */
    private String checkName;
    /**
     * 审批时间
     */
    private Date checkTime;
    /**
     * 审批状态(0-待审批，1-已通过，2-已拒绝)
     */
    private Integer checkResult;

    /**
     * 申请时间
     */
    private Date createTime;
}
