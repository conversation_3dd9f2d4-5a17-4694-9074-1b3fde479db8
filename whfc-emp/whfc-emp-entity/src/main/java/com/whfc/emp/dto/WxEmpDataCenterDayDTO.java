package com.whfc.emp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 微信人员数据中心每天数据详情
 *
 * <AUTHOR> qzexing
 * @version : 1.0
 * @date : 2020-08-27 10:01
 */
@Data
public class WxEmpDataCenterDayDTO implements Serializable {

    /**
     * 日期(yyyy-MM-dd)
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date date;

    /**
     * 出勤人数
     */
    private Integer attendEmpNum;

    /**
     * 缺勤人数
     */
    private Integer absentEmpNum;

    /**
     * 日均出勤时长
     */
    private Double avgAttendTimes;


}
