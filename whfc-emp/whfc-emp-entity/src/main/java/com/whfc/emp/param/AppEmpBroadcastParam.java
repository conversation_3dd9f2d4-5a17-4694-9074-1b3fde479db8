package com.whfc.emp.param;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR> qzexing
 * @version : 1.0
 * @date : 2020/7/2 17:42
 */
@Data
public class AppEmpBroadcastParam implements Serializable {

    /**
     * 合作单位id
     */
    private Integer corpId;

    /**
     * 班组id
     */
    private Integer groupId;

    /**
     * 组织机构id
     */
    private Integer deptId;

    /**
     * 人员ID
     */
    private Integer empId;

    /**
     * 广播内容
     */
    @NotEmpty
    private String content;

    /**
     * 发送广播人员ID
     */
    private Integer sendUserId;

    /**
     * 发送广播人员姓名
     */
    private String sendUserName;
}
