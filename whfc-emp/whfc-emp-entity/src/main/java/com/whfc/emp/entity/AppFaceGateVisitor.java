package com.whfc.emp.entity;

import lombok.Data;

import java.util.Date;

/**
 * 闸机访客审批
 */
@Data
public class AppFaceGateVisitor {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 微信唯一id
     */
    private String openId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 照片地址
     */
    private String pictureUrl;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 身份证号码
     */
    private String idCardNo;

    /**
     * 闸机id
     */
    private Integer faceGateId;

    /**
     * 访客类型（1-普通访客，2-临时工）
     */
    private Integer visitorsType;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 审批状态(0-待审批，1-已通过，2-已拒绝)
     */
    private Integer state;

    /**
     * 审批结果(1-已通过，2-已拒绝)
     */
    private Integer checkResult;

    /**
     * 审批人
     */
    private String checkName;

    /**
     * 审批时间
     */
    private Date checkTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标记(0-未删除 1-已删除)
     */
    private Integer delFlag;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;
}