package com.whfc.emp.entity;

import java.util.Date;

/**
 * 风险告知书关联人员
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/28 18:15
 */
public class AppEmpRiskEmp {
    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 风险告知书ID
     */
    private Integer riskId;

    /**
     * 人员ID
     */
    private Integer empId;

    /**
     * 签名时间
     */
    private Date signTime;

    /**
     * 签名图片
     */
    private String signImgUrl;

    /**
     * 删除标记（0-未删除 1-已删除）
     */
    private Integer delFlag;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getRiskId() {
        return riskId;
    }

    public void setRiskId(Integer riskId) {
        this.riskId = riskId;
    }

    public Integer getEmpId() {
        return empId;
    }

    public void setEmpId(Integer empId) {
        this.empId = empId;
    }

    public Date getSignTime() {
        return signTime;
    }

    public void setSignTime(Date signTime) {
        this.signTime = signTime;
    }

    public String getSignImgUrl() {
        return signImgUrl;
    }

    public void setSignImgUrl(String signImgUrl) {
        this.signImgUrl = signImgUrl;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}