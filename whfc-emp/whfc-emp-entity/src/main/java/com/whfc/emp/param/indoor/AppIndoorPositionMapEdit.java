package com.whfc.emp.param.indoor;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/3/4 16:46
 */
@Data
public class AppIndoorPositionMapEdit implements Serializable {

    @NotEmpty
    private String guid;

    @NotEmpty
    @Length(max = 32)
    private String name;

    /**
     * 区域ID
     */
    private Integer areaId;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 地图url
     */
    private String imgUrl;

    private Double pixelWidth;

    private Double pixelLength;

    private Double realWidth;

    private Double realLength;
}
