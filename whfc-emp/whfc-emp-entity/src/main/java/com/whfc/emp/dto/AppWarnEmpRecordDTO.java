package com.whfc.emp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2019-11-28
 */
@Data
public class AppWarnEmpRecordDTO implements Serializable {
    /**
     * 报警id
     */
    private Integer warnId;

    /**
     * 报警时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date triggerTime;

    private String triggerParam;

    private String triggerKey;

    /**
     * 人员姓名
     */
    private String empName;
    /**
     * 工种
     */
    private String workTypeName;

    /**
     * 报警类型
     */
    private Integer ruleType;
    /**
     * 组织机构id
     */
    private Integer deptId;
    /**
     * 组织机构名称
     */
    private String deptName;

    /**
     * 处理时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date handleTime;

    /**
     * 处理人
     */
    private String handleUserName;

    private Integer state;

    private Integer empId;

    private Double lat;

    private Double lng;

    private String detail;

    private String groupName;

    private String icon;

}
