package com.whfc.emp.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 导出刷脸记录参数
 * @date 2020-08-26
 */
@Data
public class FaceGateRecordExportParam implements Serializable {
    @NotNull
    private Integer deptId;
    private String keyword;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
}
