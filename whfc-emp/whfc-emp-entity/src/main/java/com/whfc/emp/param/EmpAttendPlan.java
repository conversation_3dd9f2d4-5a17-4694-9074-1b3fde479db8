package com.whfc.emp.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description:
 * @author: xug<PERSON>
 * @version: 1.0
 * @date: 2022-01-19 10:10
 */
@Data
public class EmpAttendPlan implements Serializable {

    /**
     * 项目ID
     */
    @NotNull
    private Integer deptId;

    /**
     * 日期
     */
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date date;

    /**
     * 计划出勤人数
     */
    @NotNull
    private Integer planAttendNum;
}
