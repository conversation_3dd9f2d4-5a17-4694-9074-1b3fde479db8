package com.whfc.emp.entity;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;
import java.util.Date;

public class AppIndoorPositionMap implements Serializable {
    private Integer id;

    private Integer deptId;

    private String name;

    private String guid;

    private Integer areaId;

    private String areaName;

    @JSONField(serialize = false)
    private Double pixelWidth;

    @JSONField(serialize = false)
    private Double pixelLength;

    @JSONField(serialize = false)
    private Double realWidth;

    @JSONField(serialize = false)
    private Double realLength;

    @JSONField(serialize = false)
    private Double rotation;

    @JSONField(serialize = false)
    private String imgUrl;

    @JSONField(serialize = false)
    private Integer delFlag;

    @J<PERSON>NField(serialize = false)
    private Date updateTime;

    @JSONField(serialize = false)
    private Date createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getDeptId() {
        return deptId;
    }

    public void setDeptId(Integer deptId) {
        this.deptId = deptId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getGuid() {
        return guid;
    }

    public Integer getAreaId() {
        return areaId;
    }

    public void setAreaId(Integer areaId) {
        this.areaId = areaId;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public void setGuid(String guid) {
        this.guid = guid == null ? null : guid.trim();
    }

    public Double getPixelWidth() {
        return pixelWidth;
    }

    public void setPixelWidth(Double pixelWidth) {
        this.pixelWidth = pixelWidth;
    }

    public Double getPixelLength() {
        return pixelLength;
    }

    public void setPixelLength(Double pixelLength) {
        this.pixelLength = pixelLength;
    }

    public Double getRealWidth() {
        return realWidth;
    }

    public void setRealWidth(Double realWidth) {
        this.realWidth = realWidth;
    }

    public Double getRealLength() {
        return realLength;
    }

    public void setRealLength(Double realLength) {
        this.realLength = realLength;
    }

    public Double getRotation() {
        return rotation;
    }

    public void setRotation(Double rotation) {
        this.rotation = rotation;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl == null ? null : imgUrl.trim();
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}