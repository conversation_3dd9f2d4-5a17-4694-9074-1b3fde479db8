package com.whfc.emp.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2019/8/27 17:17
 */
@Setter
@Getter
@ToString
public class WxEmpCurveMapDTO implements Serializable {

    /**
     * 总人数
     */
    private Integer totalEmpNum;

    /**
     * 出勤人数
     */
    private Integer attendEmpNum;

    /**
     * 缺勤人数
     */
    private Integer absenceEmpNum;

    /**
     * 在线人数
     */
    private Integer onlineEmpNum;

    /**
     * 离线人数
     */
    private Integer offlineEmpNum;

    /**
     * 日均出勤人数
     */
    private Double avgDayAttendEmpNum;

    /**
     * 日均工作时长
     */
    private Double avgDayWorkTimes;

    /**
     * 日均报警次数
     */
    private Double avgDayWarnCnt;

    /**
     * 月均工作天数
     */
    private Double avgMonthDays;

    /**
     * 网络状态
     */
    private Integer netState;

    /**
     * 出勤状态
     */
    private Integer attendState;

    /**
     * 出勤率
     */
    private Double attendRate;

    /**
     * 最近7天数据
     */
    private List<WxEmpCurveMapDayDTO> dayList;

    /**
     * 最近3个月数据
     */
    private List<WxEmpCurveMapMonthDTO> monthList;

    /**
     * 在场人数
     */
    private Integer localeEmpNum;

    /**
     * 离场人数
     */
    private Integer outLocaleEmpNum;
}
