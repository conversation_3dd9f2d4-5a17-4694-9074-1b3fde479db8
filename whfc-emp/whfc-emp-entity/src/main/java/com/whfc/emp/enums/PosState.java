package com.whfc.emp.enums;

/**
 * @Description: 定位状态
 * @author: xug<PERSON>
 * @version: 1.0
 * @date: 2019/7/29 11:37
 */
public enum PosState {

    INVALID(0, "无效"),

    VALID(1, "有效");

    private Integer value;

    private String desc;

    PosState(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
