package com.whfc.emp.entity;

import lombok.Data;

import java.util.Date;

/**
 * 人员-证书
 */
@Data
public class AppEmpCert {
    private Integer id;

    /**
     * 项目ID
     */
    private Integer deptId;

    /**
     * 人员ID
     */
    private Integer empId;

    /**
     * 证书类型ID
     */
    private Integer certTypeId;

    /**
     * 证书类型名称
     */
    private String certTypeName;

    /**
     * 操作项目ID
     */
    private Integer operationItemId;

    /**
     * 操作项目名称
     */
    private String operationItemName;

    /**
     * 证书名称
     */
    private String certName;

    /**
     * 证书编号
     */
    private String certCode;

    /**
     * 技能等级
     */
    private String level;

    /**
     * 发证日期
     */
    private Date certStartDate;

    /**
     * 证件有效期
     */
    private Date certExpireDate;

    /**
     * 发证机关
     */
    private String certGrantOrg;

    /**
     * 证书附件
     */
    private String certFile;

    /**
     * 删除标记(0-未删除 1-已删除)
     */
    private Integer delFlag;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;
}