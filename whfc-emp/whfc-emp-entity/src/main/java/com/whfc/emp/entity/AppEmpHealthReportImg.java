package com.whfc.emp.entity;

import java.util.Date;

/**
 * 人员防疫信息图片
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-09-02 11:27
 */
public class AppEmpHealthReportImg {
    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 防疫信息ID
     */
    private Integer healthReportId;

    /**
     * 图片地址
     */
    private String imgUrl;

    /**
     * 删除标记(0-未删除 1-已删除)
     */
    private Integer delFlag;

    private Date updateTime;

    private Date createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getHealthReportId() {
        return healthReportId;
    }

    public void setHealthReportId(Integer healthReportId) {
        this.healthReportId = healthReportId;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}