package com.whfc.emp.enums;

/**
 * @ClasssName GenderCoe
 * @Description 人员性别
 * <AUTHOR>
 * @Date 2021/3/3 16:26
 * @Version 1.0
 */
public enum GenderCode {

    unknown(0, "未知"),

    man(1, "男"),

    woman(2, "女");

    private Integer value;

    private String desc;

    GenderCode(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static String parseByValue(Integer value) {
        if (man.value.equals(value)) {
            return man.desc;
        } else if (woman.value.equals(value)) {
            return woman.desc;
        }
        return unknown.desc;
    }
}
