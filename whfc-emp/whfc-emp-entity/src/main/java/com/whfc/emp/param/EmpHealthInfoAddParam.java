package com.whfc.emp.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-09-02 11:06
 */
@Data
public class EmpHealthInfoAddParam implements Serializable {

    /**
     * 人员ID
     */
    @NotNull
    private Integer empId;
    /**
     * healthCode-健康码  journeyCard-行程卡 testRecord-核算记录  vaccination-疫苗接种
     */
    @NotEmpty
    private String type;
    /**
     * 检测日期（yyyy-MM-dd）
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date date;
    /**
     * 核酸结果 1-阴性 2-阳性
     */
    private Integer testResult;
    /**
     * 过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date expDate;
    /**
     * 健康码状态 green-绿码  red-红码  yellow-黄码  orange-橙码
     */
    private String codeState;
    /**
     * 行程
     */
    private String journey;
    /**
     * 第一次接种时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date firstTime;
    /**
     * 第二次接种时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date secondTime;
    /**
     * 图片
     */
    private List<String> imgList;


}
