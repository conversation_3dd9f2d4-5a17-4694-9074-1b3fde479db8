package com.whfc.emp.entity;

import lombok.Data;

import java.util.Date;

/**
 * 报警规则电子围栏
 *
 * <AUTHOR>
 */
@Data
public class AppEmpWarnRuleFence {
    private Integer id;

    /**
     * 项目id
     */
    private Integer deptId;

    /**
     * 报警规则id
     */
    private Integer ruleId;

    /**
     * 电子围栏类型(1-多边形 2-圆形)
     */
    private Integer type;

    /**
     * 多边形坐标
     */
    private Object polygon;

    /**
     * 中心点坐标
     */
    private Integer center;

    /**
     * 半径(米)
     */
    private Double radius;

    /**
     * 删除标记 0-未删除 1-已删除
     */
    private Integer delFlag;

    private Date updateTime;

    private Date createTime;
}