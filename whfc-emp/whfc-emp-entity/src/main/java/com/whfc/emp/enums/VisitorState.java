package com.whfc.emp.enums;

/**
 * @ClasssName VisitorState
 * @Description 人员审批状态
 * <AUTHOR>
 * @Date 2021/3/1 14:59
 * @Version 1.0
 */
public enum VisitorState {

    pending(0,"待审批"),
    success(1,"审批通过"),
    fai(2,"已拒绝");


    private Integer value;


    private String desc;

    VisitorState(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static String parseByValue(Integer value) {
        if (pending.value.equals(value)) {
            return pending.getDesc();
        }
        if (success.value.equals(value)) {
            return success.getDesc();
        }
        if (fai.value.equals(value)) {
            return fai.getDesc();
        }
        return null;
    }

}
