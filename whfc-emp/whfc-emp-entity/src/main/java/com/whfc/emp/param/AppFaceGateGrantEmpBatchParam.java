package com.whfc.emp.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Schema(description = "闸机人员授权参数")
@Data
public class AppFaceGateGrantEmpBatchParam implements Serializable {

    /**
     * 人员ID
     */
    @Schema(description = "人员ID")
    @NotNull(message = "人员ID不能为空")
    private Integer empId;

    /**
     * 闸机ID列表
     */
    @Schema(description =  "闸机ID列表", example = "[1]")
    @NotEmpty(message = "闸机ID列表不能为空")
    private List<Integer> faceGateIdList;

    /**
     * 授权操作 0-取消授权 1-授权
     */
    @Schema(description =  "授权操作 0-取消授权 1-授权")
    @NotNull(message = "授权操作不能为空")
    private Integer grantType;
}
