package com.whfc.emp.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 扫码（或后台手动）录入考勤数据参数
 * @date 2020-08-04
 */
@Data
public class AppEmpInputDataAddParam implements Serializable {
    /**
     * 组织机构id
     */
    @NotNull
    private Integer deptId;
    /**
     * 人员姓名
     */
    @NotEmpty
    private String empName;
    /**
     * 电话
     */
    private String phone;
    /**
     * 方向 0-出 1-进
     */
    @NotNull
    private Integer direction;
    /**
     * 时间
     */
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date time;

    /**
     * 方式 3-后台 4-扫码
     */
    private Integer type;

    private String photo;

    private Double lat;

    private Double lng;

    private String location;
}
