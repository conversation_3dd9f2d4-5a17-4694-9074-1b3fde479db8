package com.whfc.emp.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClasssName AppEmpAgeDTO
 * @Description 人员年龄统计
 * <AUTHOR>
 * @Date 2021/1/13 14:57
 * @Version 1.0
 */
@Data
public class AppEmpAgeDTO implements Serializable {

    /**
     * 年龄分段
     */
    private Integer type;

    /**
     * 年龄分段名称
     */
    private String typeName;

    /**
     * 年龄段总数量
     */
    private Integer ageNum;

    /**
     * 男性员工数量
     */
    private Integer manNum;

    /**
     * 男性员工百分比
     */
    private Double manPercent;

    /**
     * 女性员工数量
     */
    private Integer womanNum;

    /**
     * 女性员工百分比
     */
    private Double womanPercent;

    /**
     * 其他
     */
    private Integer otherNum;

    /**
     * 其他百分比
     */
    private Double otherPercent;

    public AppEmpAgeDTO(Integer type) {
        this.type = type;
        this.ageNum = 0;
        this.manNum = 0;
        this.womanNum = 0;
        this.otherNum = 0;
    }

}
