package com.whfc.emp.dto.uniform;

import com.alibaba.excel.metadata.data.WriteCellData;
import lombok.Data;

import java.io.Serializable;

@Data
public class AppUniformReceiveExportDTO implements Serializable {

    /**
     * 领用记录ID
     */
    private Integer receiveId;

    /**
     * 日期
     */
    private String date;

    /**
     * 单位ID
     */
    private Integer corpId;

    /**
     * 单位名称
     */
    private String corpName;

    /**
     * 人员ID
     */
    private Integer empId;

    /**
     * 人员姓名
     */
    private String empName;

    /**
     * 物品ID
     */
    private Integer itemId;

    /**
     * 物品名称
     */
    private String itemName;

    /**
     * 物品数量
     */
    private Integer itemAmount;

    /********数据统计转换*********/

    /**
     * Short sleeve shirt
     */
    private Integer amount1;

    /**
     * Long sleeve shirt
     */
    private Integer amount2;

    /**
     * Safety helmet
     */
    private Integer amount3;

    /**
     * Ear protector
     */
    private Integer amount4;

    /**
     * Respirator / Dust mask
     */
    private Integer amount5;

    /**
     * Safety harness
     */
    private Integer amount6;

    /**
     * Eye protector
     */
    private Integer amount7;

    /**
     * Safety boots
     */
    private Integer amount8;

    /**
     * Safety rain boots
     */
    private Integer amount9;

    /**
     * Reflective belt / vest
     */
    private Integer amount10;

    /**
     * 图片1
     */
    private WriteCellData<Void> img1;

    /**
     * 图片2
     */
    private WriteCellData<Void> img2;

    /**
     * 图片3
     */
    private WriteCellData<Void> img3;
}