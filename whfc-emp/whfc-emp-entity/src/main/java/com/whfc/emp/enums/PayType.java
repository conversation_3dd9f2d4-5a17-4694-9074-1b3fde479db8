package com.whfc.emp.enums;

/**
 * @Description: 工资核定方式
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/7/3 10:05
 */
public enum PayType {

    HOUR(1, "按小时"),

    DAY(2, "按日"),

    MONTH(3, "按月");

    private Integer value;

    private String desc;

    PayType(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 按值解析
     *
     * @param value
     * @return
     */
    public static PayType parseByValue(Integer value) {
        PayType[] types = PayType.values();
        for (PayType type : types) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return MONTH;
    }
}
