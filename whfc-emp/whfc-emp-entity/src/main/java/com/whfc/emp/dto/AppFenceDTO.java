package com.whfc.emp.dto;

import com.whfc.common.geometry.Point;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AppFenceDTO implements Serializable {

    /**
     * 电子围栏id
     */
    private Integer id;

    /**
     * 电子围栏名称
     */
    private String name;

    /**
     * 电子围栏类型 1-多边形 2-圆形
     */
    private Integer type;

    /**
     * 多边形文本
     */
    private String polygon;

    /**
     * 多边形坐标
     */
    private List<Point> polygonPointList;

    private String multiPolygon;

    private List<List<Point>> multiPolygonPointList;

    /**
     * 圆形文本
     */
    private String center;

    /**
     * 圆形中心点坐标
     */
    private Point centerPoint;

    /**
     * 圆形半径
     */
    private Double radius;

    /**
     * 组织机构名称
     */
    private String deptName;

    /**
     * 组织机构id
     */
    private Integer deptId;

    private Integer ruleId;


}
