package com.whfc.emp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class AppEmpAnaWeekDataDTO implements Serializable {

    /**
     * 时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date date;

    /**
     * 进场人数
     */
    private Integer enterNum;

    /**
     * 出场人数
     */
    private Integer outerNum;

    /**
     * 出勤人数
     */
    private Integer attendNum;

    public AppEmpAnaWeekDataDTO() {
    }

    public AppEmpAnaWeekDataDTO(Date date, Integer enterNum, Integer outerNum) {
        this.date = date;
        this.enterNum = enterNum;
        this.outerNum = outerNum;
    }

    public AppEmpAnaWeekDataDTO(Date date, Integer enterNum, Integer outerNum, Integer attendNum) {
        this.date = date;
        this.enterNum = enterNum;
        this.outerNum = outerNum;
        this.attendNum = attendNum;
    }
}
