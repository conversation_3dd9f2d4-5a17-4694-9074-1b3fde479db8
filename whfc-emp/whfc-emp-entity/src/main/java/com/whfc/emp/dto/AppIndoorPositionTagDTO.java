package com.whfc.emp.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Schema(description = "室内定位标签")
@Data
public class AppIndoorPositionTagDTO implements Serializable {

    @Schema(description = "标签ID")
    private Integer tagId;

    @Schema(description = "唯一编码")
    private String guid;

    @Schema(description = "标签编码")
    private String code;

    @Schema(description = "标签名称")
    private String name;

    @Schema(description = "绑定标记:0-未绑定 1-已绑定")
    private Integer bindFlag;

    @Schema(description = "绑定类型 1-人员 2-设备")
    private Integer bindType;

    @Schema(description = "班组ID")
    private Integer groupId;

    @Schema(description = "班组名称")
    private String groupName;

    @Schema(description = "合作单位ID")
    private Integer corpId;

    @Schema(description = "合作单位名称")
    private String corpName;

    @Schema(description = "人员ID")
    private Integer empId;

    @Schema(description = "人员名称")
    private String empName;

    @Schema(description = "设备唯一标识")
    private String deviceCode;

    @Schema(description = "状态:0-离线 1-在线")
    private Integer status;

    @Schema(description = "定位时间")
    private Date time;

    @Schema(description = "地图ID")
    private Integer mapId;

    @Schema(description = "基站ID")
    private Integer stationId;

    @Schema(description = "基站名称")
    private String stationName;

    @Schema(description = "x坐标")
    private Double x;

    @Schema(description = "y坐标")
    private Double y;

    @Schema(description = "z坐标")
    private Double z;

    @Schema(description = "经度")
    private Double lng;

    @Schema(description = "纬度")
    private Double lat;

    @Schema(description = "距离")
    private Integer distance;

    @Schema(description = "距离1")
    private Integer distance1;

    @Schema(description = "电池电量")
    private Integer batteryPower;

    @Schema(description = "标签状态")
    private Integer state;

    @Schema(description = "SOS标志")
    private Integer sos;
}