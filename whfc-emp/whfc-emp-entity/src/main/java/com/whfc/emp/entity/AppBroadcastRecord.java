package com.whfc.emp.entity;

import java.util.Date;

/**
    * 广播记录表
    */
public class AppBroadcastRecord {
    /**
    * ID
    */
    private Integer id;

    /**
    * 组织机构ID
    */
    private Integer deptId;

    /**
    * 广播内容
    */
    private String content;

    /**
    * 广播类型  1-人员广播，2-组织机构广播
    */
    private Integer type;

    /**
    * 发送广播的用户ID
    */
    private Integer sendUserId;

    /**
    * 发送广播的用户姓名
    */
    private String sendUserName;

    /**
    * 删除标记(0-未删除 1-已删除)
    */
    private Integer delFlag;

    private Date updateTime;

    private Date createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getDeptId() {
        return deptId;
    }

    public void setDeptId(Integer deptId) {
        this.deptId = deptId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getSendUserId() {
        return sendUserId;
    }

    public void setSendUserId(Integer sendUserId) {
        this.sendUserId = sendUserId;
    }

    public String getSendUserName() {
        return sendUserName;
    }

    public void setSendUserName(String sendUserName) {
        this.sendUserName = sendUserName;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}