package com.whfc.emp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 手动录入实体
 * @date 2020-08-03
 */
@Data
public class AppEmpInputDataDTO implements Serializable {
    /**
     * 记录id
     */
    private Integer attendRecordId;

    /**
     * 人员ID
     */
    private Integer empId;

    /**
     * 人员姓名
     */
    private String empName;
    /**
     * 电话
     */
    private String phone;

    /**
     * 组织机构ID
     */
    private Integer deptId;
    /**
     * 组织机构名称
     */
    private String deptName;
    /**
     * 来源 3-后台 4-扫描
     */
    private Integer type;
    /**
     * 方向 0-出 1-进
     */
    private Integer direction;
    /**
     * 时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date time;
    /**
     * 合作单位名称
     */
    private String corpName;
    /**
     * 班组ID
     */
    private Integer groupId;
    /**
     * 班组名称
     */
    private String groupName;
    /**
     * 工种名称
     */
    private String workTypeName;
    /**
     * 头像
     */
    private String photo;

    /**
     * 设备名称
     */
    private String deviceName;
}
