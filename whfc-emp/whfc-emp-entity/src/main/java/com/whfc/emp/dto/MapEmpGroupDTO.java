package com.whfc.emp.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description 地图页人员聚合实体
 * @date 2021-03-31
 */
@Data
public class MapEmpGroupDTO implements Serializable {

    /**
     * 班组ID
     */
    private Integer groupId;

    /**
     * 班组名称
     */
    private String groupName;

    /**
     * 视频监控ID
     */
    private Integer fvsDeviceId;

    /**
     * 视频监控名称
     */
    private String fvsDeviceName;

    /**
     * 经度
     */
    private Double lng;

    /**
     * 纬度
     */
    private Double lat;

    /**
     * 监控设备状态  0-离线 1-在线
     */
    private Integer deviceStatus;

    /**
     * 播放流模式 1-标清  2-高清
     */
    private Integer streamMode;

    /**
     * 电子围栏中的人员列表
     */
    private List<AppEmpDTO> empList;

    /**
     * 电子围栏中的人数
     */
    private Integer empNum;
}
