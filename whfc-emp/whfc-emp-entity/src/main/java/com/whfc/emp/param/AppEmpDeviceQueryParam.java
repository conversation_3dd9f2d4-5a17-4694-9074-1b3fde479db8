package com.whfc.emp.param;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/27
 */
@Data
@Schema(description = "人员设备查询参数")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AppEmpDeviceQueryParam implements Serializable {

    @Hidden
    @Schema(description = "部门ID")
    private Integer deptId;

    @Schema(description = "页码")
    private Integer pageNum;

    @Schema(description = "每页条数")
    private Integer pageSize;

    @Schema(description = "合作为单位ID")
    private Integer corpId;

    @Schema(description = "分组ID")
    private Integer groupId;

    @Schema(description = "关键字")
    private String keyword;

    @Schema(description = "绑定状态")
    private Integer bindFlag;

    @Schema(description = "网络状态")
    private Integer netState;


}
