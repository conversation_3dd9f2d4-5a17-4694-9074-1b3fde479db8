package com.whfc.emp.param.uniform;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/1/25 15:21
 */
@Data
public class AppUniformReceiveQuery implements Serializable {

    /**
     * 项目ID
     */
    @NotNull
    private Integer deptId;

    /**
     * 公司名称
     */
    private String corpName;

    /**
     * 人员姓名
     */
    private String empName;

    /**
     * 物品名称
     */
    @Length(max = 32)
    private String itemName;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    /**
     * 领用ID列表
     */
    private List<Integer> receiveIds;
}
