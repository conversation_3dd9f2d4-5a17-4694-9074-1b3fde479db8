package com.whfc.emp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class TrainEmpDTO implements Serializable {

    /**
     * 培训人员ID
     */
    private Integer trainEmpId;

    /**
     * 人员id
     */
    private Integer empId;

    /**
     * 人员姓名
     */
    private String empName;

    /**
     * 身份证号
     */
    private String idCardNo;

    /**
     * 人员电话号
     */
    private String empPhone;

    /**
     * 组织机构名称
     */
    private String deptName;

    /**
     * 工人类型
     */
    private String workRoleName;

    /**
     * 工种
     */
    private String workTypeName;

    /**
     * 进场日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date enterTime;

    /**
     * 分数
     */
    private Double score;

    /**
     * 是否合格  0-不合格  1-合格
     */
    private Integer passFlag;

    /**
     * 班组名称
     */
    private String groupName;

    /**
     * 培训学时
     */
    private Integer trainPeriod;

    /**
     * 总分
     */
    private Integer totalScore;

    /**
     * 及格分
     */
    private Integer passScore;

    /**
     * 试卷编号
     */
    private String examNo;

    /**
     * 考试次数
     */
    private Integer examCount;

    /**
     * 签名图片
     */
    private String signImgUrl;

    /**
     * 成绩图片
     */
    private List<String> imgUrlList;


}
