package com.whfc.emp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class AppAnaEmpDTO implements Serializable {
	/**
	 * 姓名
	 */
	private String name;
	/**
	 * 头像
	 */
	private String avatar;
	/**
	 * 出勤时间
	 */
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date time;
	
	/**
	 * 进场日期
	 */
	@JsonFormat(pattern="yyyy-MM-dd")
	private Date date;
}
