package com.whfc.emp.entity;

import java.util.Date;
import lombok.Data;

/**
 * @author: hw
 * @date: 2021-10-21 17:40
 * @description: //todo
 */

/**
 * 人员工资发放记录
 */
@Data
public class AppPayrollEmp {
    /**
     * 自增主键
     */
    private Integer id;

    /**
     * 工资发放记录id
     */
    private Integer payrollId;

    /**
     * 人员id
     */
    private Integer empId;

    /**
     * 出勤天数
     */
    private Integer attendDays;

    /**
     * 实际出勤天数
     */
    private Integer realAttendDays;

    /**
     * 单价
     */
    private Double unitPrice;

    /**
     * 工程量
     */
    private Integer workAmount;

    /**
     * 薪水总额（元）
     */
    private Double salaryTotal;

    /**
     * 借款（元）
     */
    private Double salaryBorrow;

    /**
     * 扣款
     */
    private Double salaryDeduct;

    /**
     * 应发工资（元）
     */
    private Double salaryShould;

    /**
     * 实发工资（元）
     */
    private Double salaryReal;

    /**
     * 发放日期
     */
    private Date date;

    /**
     * 备注
     */
    private String remark;

    private Integer delFlag;

    private Date createTime;

    private Date updateTime;
}