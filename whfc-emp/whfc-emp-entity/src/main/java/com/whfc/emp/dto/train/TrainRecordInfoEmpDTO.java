package com.whfc.emp.dto.train;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 培训记录人员信息与培训信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-08-26 14:33
 */
@Data
public class TrainRecordInfoEmpDTO implements Serializable {
    /**
     * 组织机构ID
     */
    private Integer deptId;
    /**
     * 主键Id
     */
    @JSONField(name = "ID")
    private String id;
    /**
     * 人员姓名
     */
    @JSONField(name = "EmpName")
    private String empName;
    /**
     * 人员证件编号
     */
    @JSONField(name = "IdentifyId")
    private String identifyId;
    /**
     * 工种名称
     */
    @JSONField(name = "CategoryName")
    private String categoryName;
    /**
     * 培训记录Id
     */
    @JSONField(name = "RecordId")
    private String recordId;
    /**
     * 单位Id
     */
    @JSONField(name = "DepartId")
    private String departId;
    /**
     * 单位名称
     */
    @JSONField(name = "DepartName")
    private String departName;
    /**
     * 学时
     */
    @JSONField(name = "TrainPeriod")
    private Integer trainPeriod;
    /**
     * 总分
     */
    @JSONField(name = "TotalScore")
    private Integer totalScore;
    /**
     * 及格分
     */
    @JSONField(name = "PassScore")
    private Integer passScore;
    /**
     * 成绩(-1代表该次培训无考试,否则为实际成绩)
     */
    @JSONField(name = "Score")
    private Integer score;
    /**
     * 是否合格(0:否 1:是)
     */
    @JSONField(name = "IsPass")
    private Integer isPass;
    /**
     * 批次号
     */
    @JSONField(name = "GroupNo")
    private Integer groupNo;
    /**
     * 试卷编号
     */
    @JSONField(name = "ExamNo")
    private String examNo;
    /**
     * 考试次数
     */
    @JSONField(name = "ExamCount")
    private Integer examCount;
    /**
     * 设备编号
     */
    @JSONField(name = "DeviceNo")
    private String deviceNo;
    /**
     * 所属项目部Id
     */
    @JSONField(name = "OwnerDepartId")
    private String ownerDepartId;
    /**
     * 答案
     */
    @JSONField(name = "Answers")
    private String answers;
    /**
     * 培训记录名称
     */
    @JSONField(name = "RecordName")
    private String recordName;
    /**
     * 培训类型
     */
    @JSONField(name = "TrainType")
    private String trainType;
    /**
     * 出卷类型 0:使用原卷 1:重新出卷
     */
    @JSONField(name = "PaperMode")
    private Integer paperMode;
    /**
     * 培训方式(逗号分隔类型)
     */
    @JSONField(name = "TrainMode")
    private String trainMode;
    /**
     * 培训负责人
     */
    @JSONField(name = "TrainPrincipal")
    private String trainPrincipal;
    /**
     * 培训开始时间
     */
    @JSONField(name = "TrainStartDate", format = "yyyy-MM-dd'T'HH:mm:ss")
    private Date trainStartDate;
    /**
     * 培训结束时间
     */
    @JSONField(name = "TrainEndDate", format = "yyyy-MM-dd'T'HH:mm:ss")
    private Date trainEndDate;
    /**
     * 培训内容
     */
    @JSONField(name = "TrainContent")
    private String trainContent;
    /**
     * 培训详细信息
     */
    @JSONField(name = "TrainDescript")
    private String trainDescript;
    /**
     * 培训单位
     */
    @JSONField(name = "TrainDepart")
    private String trainDepart;
    /**
     * 上传时间
     */
    @JSONField(name = "UploadTime", format = "yyyy-MM-dd'T'HH:mm:ss")
    private Date uploadTime;
    /**
     * 删除用户
     */
    @JSONField(name = "DeleteUser")
    private String deleteUser;
    /**
     * 删除时间
     */
    @JSONField(name = "DeleteDate", format = "yyyy-MM-dd'T'HH:mm:ss")
    private String deleteDate;
    /**
     * 删除标记(true/false)
     */
    @JSONField(name = "DeleteTag")
    private Boolean deleteTag;


}
