package com.whfc.emp.entity;

import java.util.Date;

/**
 * 机构-人脸识别闸机-人员授权
 */
public class AppFaceGatePerson {

    private Integer id;

    /**
     * 组织机构id
     */
    private Integer deptId;

    /**
     * 闸机ID
     */
    private Integer faceGateId;

    /**
     * 闸机序列号
     */
    private String deviceKey;

    /**
     * 人员ID
     */
    private Integer empId;

    /**
     * 人员guid
     */
    private String personGuid;

    /**
     * 定时任务状态，0-未开始执行，1-人员注册完成，2-人员照片注册完成，3-人员授权完成
     */
    private Integer taskType;

    /**
     * 添加考勤人员返回信息
     */
    private String message;

    /**
     * 删除标记(0-未删除 1-已删除)
     */
    private Integer delFlag;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getDeptId() {
        return deptId;
    }

    public void setDeptId(Integer deptId) {
        this.deptId = deptId;
    }

    public Integer getFaceGateId() {
        return faceGateId;
    }

    public void setFaceGateId(Integer faceGateId) {
        this.faceGateId = faceGateId;
    }

    public String getDeviceKey() {
        return deviceKey;
    }

    public void setDeviceKey(String deviceKey) {
        this.deviceKey = deviceKey;
    }

    public Integer getEmpId() {
        return empId;
    }

    public void setEmpId(Integer empId) {
        this.empId = empId;
    }

    public String getPersonGuid() {
        return personGuid;
    }

    public void setPersonGuid(String personGuid) {
        this.personGuid = personGuid;
    }

    public Integer getTaskType() {
        return taskType;
    }

    public void setTaskType(Integer taskType) {
        this.taskType = taskType;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}