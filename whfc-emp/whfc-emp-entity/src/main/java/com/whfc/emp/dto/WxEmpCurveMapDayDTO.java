package com.whfc.emp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2019/8/27 17:23
 */
@Data
public class WxEmpCurveMapDayDTO implements Serializable {

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date date;

    /**
     * 工作时长
     */
    private Double workTimes;

    /**
     * 报警次数
     */
    private Double warnCnt;

    /**
     * 出勤人数
     */
    private Integer attendEmpNum;
    
    /**
           * 在岗总人数
     */
    private Integer totalEmpNum;
}
