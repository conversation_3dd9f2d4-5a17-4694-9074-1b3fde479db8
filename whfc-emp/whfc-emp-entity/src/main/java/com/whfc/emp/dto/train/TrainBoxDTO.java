package com.whfc.emp.dto.train;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 培训箱数据
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-08-26 10:41
 */
@Data
public class TrainBoxDTO implements Serializable {

    /**
     * 消息类型
     */
    @JSONField(name = "Type")
    private Integer type;
    /**
     * 对应类型的数据集合
     */
    @JSONField(name = "Data")
    private String data;
    /**
     * 队列Id
     */
    @JSONField(name = "Code")
    private String code;
    /**
     * 队列Id
     */
    @JSONField(name = "DepartId")
    private String departId;
    /**
     * 队列Id
     */
    @JSONField(name = "OtherDepartId")
    private String otherDepartId;
    /**
     * 推送时间
     */
    @JSONField(name = "SendDate")
    private Date sendDate;

    /**
     * 部门Id
     */
    private Integer deptId;
}
