package com.whfc.emp.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 人员报警检测实体
 * @date 2020-06-08
 */
@Data
public class EmpWarnCheckDTO implements Serializable {

    /**
     * 项目ID
     */
    private Integer deptId;

    /**
     * 人员ID
     */
    private Integer empId;

    /**
     * 人员姓名
     */
    private String empName;

    /**
     * 设备类型
     */
    private Integer deviceType;

    /**
     * 工种ID
     */
    private Integer workTypeId;

    /**
     * 工种名称
     */
    private String workTypeName;

    /**
     * 触发时间
     */
    private Date triggerTime;

    /**
     * 报警值SOS
     */
    private Integer alarmSos;

    /**
     * 报警值
     */
    private Integer alarmDrop;

    /**
     * 报警值
     */
    private Integer alarmDoff;

    /**
     * 报警值
     */
    private Integer alarmCrash;

    /**
     * 报警值
     */
    private Integer alarmStill;

    /**
     * 经度
     */
    private Double lng;

    /**
     * 纬度
     */
    private Double lat;

    /**
     * 电池电量
     */
    private Integer batteryPower;

    /**
     * 体温
     */
    private Double bodyTemp;

    /**
     * 心率
     */
    private Integer heartRate;


    private Integer ruleType;

    private Integer ruleId;

    private List<Integer> ruleTypeList;

    /**
     * 触发报警key
     */
    private String triggerKey;


}
