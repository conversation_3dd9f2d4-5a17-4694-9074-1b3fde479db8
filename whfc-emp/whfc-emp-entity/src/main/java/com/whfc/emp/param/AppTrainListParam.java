package com.whfc.emp.param;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class AppTrainListParam implements Serializable {
	@NotNull
	private Integer pageNum;
	@NotNull
	private Integer pageSize;
	@NotNull
	private Integer deptId;

	/**
	 * 培训类型id
	 */
	private Integer trainType;
	/**
	 * 状态  0-未完成  1-已完成
	 */
	private Integer state;
	
	private String keyword;
	/**
	 * 开始日期
	 */
	
	private Date startTime;
	/**
	 * 结束日期
	 */
	private Date endTime;
	
}
