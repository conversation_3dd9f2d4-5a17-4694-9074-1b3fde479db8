package com.whfc.emp.param;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 编辑班组参数
 * @date 2020-08-03
 */
@Data
public class AppEmpGroupEditParam implements Serializable {

    /**
     * 合作单位ID
     */
    @NotNull
    private Integer corpId;

    /**
     * 合作单位名称
     */
    @Length(max = 32)
    private String corpName;

    /**
     * 班组id
     */
    @NotNull
    private Integer groupId;

    /**
     * 班组名称
     */
    private String groupName;

    /**
     * 负责人姓名
     */
    private String responsiblePersonName;

    /**
     * 负责人电话
     */
    private String responsiblePersonPhone;

    /**
     * 负责人证件类型 1-居民身份证 2-澳门永久居民证 3-澳门非永久居民证 4-澳门外地雇员身份识别证 5-澳门特别逗留证
     */
    private Integer responsibleIdcardType;

    /**
     * 负责人身份证号
     */
    private String responsibleIdcardNumber;

    /**
     * 班组编码
     */
    private String groupCode;

    /**
     * 监控设备ID
     */
    private Integer fvsDeviceId;

    /**
     * 监控设备名称
     */
    private String fvsDeviceName;
}
