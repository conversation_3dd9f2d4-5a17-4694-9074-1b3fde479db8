package com.whfc.emp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description: 工资发放详情人员集合
 * @author: hw
 * @version: 1.0
 * @date: 2020-11-25 18:53
 */
@Data
public class AppPayrollEmpDTO implements Serializable {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 工资单id
     */
    private Integer payrollId;

    /**
     * 人员id
     */
    private Integer empId;

    /**
     * 人员名称
     */
    private String empName;

    /**
     * 人员证件号
     */
    private String idCardNo;

    /**
     * 工种ID
     */
    private Integer workTypeId;

    /**
     * 工种名称
     */
    private String workTypeName;

    /**
     * 工人类型
     */
    private String workRoleName;

    /**
     * 系统出勤天数
     */
    private Integer attendDays;

    /**
     * 实际出勤天数
     */
    private Integer realAttendDays;

    /**
     * 工程量
     */
    private Integer workAmount;

    /**
     * 单价
     */
    private Double unitPrice;

    /**
     * 工资总额
     */
    private Double salaryTotal;

    /**
     * 借款
     */
    private Double salaryBorrow;

    /**
     * 扣款
     */
    private Double salaryDeduct;

    /**
     * 应发工资
     */
    private Double salaryShould;

    /**
     * 实发工资
     */
    private Double salaryReal;

    /**
     * 发放日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date date;

    /**
     * 备注
     */
    private String remark;

    /**
     * 银行卡号
     */
    private String bankNumber;
    /**
     * 人员工资图片
     */
    private List<String> imgUrlList;
}
