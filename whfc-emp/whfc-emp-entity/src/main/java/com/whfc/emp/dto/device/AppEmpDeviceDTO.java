package com.whfc.emp.dto.device;

import com.whfc.emp.dto.AppEmpDeviceStatNumDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 人员硬件信息
 */
@Schema(description = "人员硬件信息")
@Data
public class AppEmpDeviceDTO implements Serializable {

    @Schema(description =  "guid")
    private String guid;

    @Schema(description =  "组织机构ID")
    private Integer deptId;

    @Schema(description =  "硬件类型 2-智能安全帽 34-智能手环")
    private Integer deviceType;

    @Schema(description =  "硬件平台")
    private String platform;

    @Schema(description =  "硬件SN")
    private String sn;

    @Schema(description =  "网络状态(0-离线 1-在线)")
    private Integer netState;

    @Schema(description =  "电池电量(百分比)")
    private Integer batteryPower;

    @Schema(description =  "上次通信时间")
    private Date time;

    @Schema(description =  "颜色")
    private String color;

    @Schema(description =  "绑定状态（0-未绑定 1-绑定）")
    private Integer bindFlag;


    @Schema(description =  "人员ID")
    private Integer empId;

    @Schema(description =  "人员组名称")
    private String groupName;

    @Schema(description =  "人员名称")
    private String empName;

    @Schema(description =  "性别（1-男 2-女）")
    private Integer gender;

    @Deprecated
    private Integer deviceId;

    @Schema(description =  "绑定时间")
    private Date bindTime;

    @Schema(description =  "时间段内通讯次数统计")
    private List<AppEmpDeviceStatNumDTO> numList;


}