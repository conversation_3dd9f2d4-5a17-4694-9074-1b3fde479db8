package com.whfc.emp.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClasssName WxEmpGroupDTO
 * @Description 小程序人员-列表(不分页)请求类
 * <AUTHOR>
 * @Date 2020/10/20 17:05
 * @Version 1.0
 */
@Data
public class WxEmpGroupDTO implements Serializable {

    /**
     * 班组id
     */
    @JsonIgnore
    private Integer groupId;

    /**
     * 班组名称
     */
    private String groupName;

    /**
     * 在线人员数量
     */
    private Integer onlineNum;

    /**
     * 在岗人员数量
     */
    private Integer enterNum;

    /**
     * 出勤人员数量
     */
    private Integer attendNum;

    /**
     * 在场人员数量
     */
    private Integer localeNum;

    /**
     * 离场人员数量
     */
    private Integer outLocaleNum;

    /**
     * 缺勤人员数量
     */
    private Integer absentNum;

    /**
     * 绑定人员数量
     */
    private Integer bindNum;

    /**
     * 未绑定人员数量
     */
    private Integer unbindNum;

    /**
     * 人员列表
     */
    private List<AppEmpDTO> dataList;

    public WxEmpGroupDTO() {
    }

    public WxEmpGroupDTO(Integer groupId, String groupName) {
        this.groupId = groupId;
        this.groupName = groupName;
    }
}
