package com.whfc.emp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 电子围栏时间
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/20 15:06
 */
@Data
public class AppFenceTimeDTO implements Serializable {

    /**
     * 电子围栏ID
     */
    private Integer fenceId;
    /**
     * 电子围栏名称
     */
    private String fenceName;
    /**
     * 开始时间
     */
    @JsonFormat(pattern = "HH:mm")
    private Date startTime;
    /**
     * 结束时间
     */
    @JsonFormat(pattern = "HH:mm")
    private Date endTime;
}
