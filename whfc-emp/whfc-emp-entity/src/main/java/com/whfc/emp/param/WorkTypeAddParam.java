package com.whfc.emp.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 增加工种的参数
 * @date 2020-10-29
 */
@Schema(description = "增加工种参数")
@Data
public class WorkTypeAddParam implements Serializable {

    @Schema(description = "部门id")
    @NotNull
    private Integer deptId;

    @Schema(description = "工种名称")
    @NotEmpty
    private String workTypeName;

    /**
     * 工种编码
     */
    @Schema(description = "工种编码")
    @Length(max = 64)
    private String workCode;

    /**
     * 是否特殊工种
     */
    @Schema(description = "是否特殊工种")
    private Integer spec;

    /**
     * 是否需要持证上岗 0-不需要 1-需要
     */
    @Schema(description = "是否需要持证上岗 0-不需要 1-需要")
    private Integer needCert;
}
