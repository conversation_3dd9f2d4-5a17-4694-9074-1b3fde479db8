package com.whfc.emp.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.whfc.common.util.DateUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description 身份证识别返回类
 * <AUTHOR>
 * @Date 2021-08-05 11:39
 * @Version 1.0
 */
@Data
public class AppEmpIdentifyDTO implements Serializable {

    /**
     * 姓名
     */
    private String name;

    /**
     * 性别
     */
    private String sex;

    /**
     * 民族
     */
    private String nation;

    /**
     * 生日
     */
    @JsonFormat(pattern = DateUtil.DATE_FORMAT)
    private Date birth;

    /**
     * 地址
     */
    private String address;

    /**
     * 证件号码
     */
    private String idNum;

    /**
     * 发证机关
     */
    private String authority;

    /**
     * 证件有效期
     */
    private String validDate;

    /**
     * 扩展信息
     */
    private String advancedInfo;
}
