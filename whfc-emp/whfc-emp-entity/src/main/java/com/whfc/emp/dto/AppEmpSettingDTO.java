package com.whfc.emp.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2020-06-03
 */
@Data
public class AppEmpSettingDTO implements Serializable {

    /**
     * 考勤类型
     *
     * @see com.whfc.emp.enums.AttendType
     */
    private Integer attendType;

    /**
     * 考勤类型启用标记 (0-关闭 1-启用)
     */
    private Integer attendTypeFlag;

    /**
     * 童工年龄
     */
    private Integer childLabourAge;

    /**
     * 童工年龄启用标记
     */
    private Integer childLabourAgeFlag;

    /**
     * 男工退休年龄
     */
    private Integer maleRetireAge;

    /**
     * 男工退休年龄启用标记
     */
    private Integer maleRetireAgeFlag;

    /**
     * 女工退休年龄
     */
    private Integer femaleRetireAge;

    /**
     * 女工退休年龄启用标记
     */
    private Integer femaleRetireAgeFlag;

    /**
     * 黑名单列表启用标记
     */
    private Integer empBlackListFlag;

    /**
     * 二维码进出启用标记
     */
    private Integer qrFlag;

    /**
     * 二维码
     */
    private String qr;

    /**
     * 二维码人员录入标记
     */
    private Integer empQrFlag;

    /**
     * 安全帽离线时间
     */
    private Integer minutes;

    /**
     * 人证核验标记
     */
    private String idCardVerify;

}
