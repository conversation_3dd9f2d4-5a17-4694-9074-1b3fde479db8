package com.whfc.emp.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

@Data
public class AppEmpEditParam implements Serializable {

	@NotNull
	private Integer empId;

	/**
	 * 项目id
	 */
	@NotNull
	private Integer projectId;

	/**
	 * 组织机构id
	 */
	private Integer deptId;

	/**
	 * 组织机构名称
	 */
	private String deptName;

	/**
	 * 姓名
	 */
	@NotEmpty
	private String empName;

	/**
	 * 性别  1-男 2-女
	 */
	private Integer gender;

	/**
	 * 生日
	 */
	@JsonFormat(pattern = "yyyy-MM-dd")
	private Date birthday;

	/**
	 * 电话
	 */
	private String phone;

	/**
	 * 身份证头像
	 */
	private String headImg;

	/**
	 * 身份证号
	 */
	private String idCardNo;

	/**
	 * 身份证正面
	 */
	private String idCardFront;

	/**
	 * 身份证反面
	 */
	private String idCardBack;

	/**
	 * 发证机关
	 */
	private String idCardGrantOrg;

	/**
	 * 身份证有效期开始时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd")
	private Date idCardStartDate;

    /**
     * 身份证结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date idCardEndDate;

	/**
	 * 年龄
	 */
	private Integer age;

	/**
	 * 民族
	 */
	private String nation;

	/**
	 * 住址
	 */
	private String address;

	/**
	 * 学历
	 */
	private Integer education;

	/**
	 * 学位
	 */
	private Integer degree;

	/**
	 * 合作单位id
	 */
	private Integer corpId;

	/**
	 * 合作公司名称
	 */
	private String corpName;

	/**
	 * 班组id
	 */
	private Integer groupId;

	/**
	 * 班组名称
	 */
	private String groupName;

	/**
	 * 用户头像
	 */
	private String avatar;

	/**
	 * 工人类型id
	 */
	@NotNull
	private Integer workRoleId;

	/**
	 * 工人类型名称
	 */
	@NotEmpty
	private String workRoleName;

	/**
	 * 工种id
	 */
	private Integer workTypeId;

	/**
	 * 工种名称
	 */
	private String workTypeName;

	/**
	 * 是否是班组长（0-否  1-是）
	 */
	private Integer leaderFlag;

	/**
	 * 证件类型 (1-居民身份证 2-澳门永久居民证，3-澳门非永久居民证 4-澳门外地雇员身份识别证 5-澳门特别逗留证)
	 */
	private Integer cardType;

	/**
	 * 工作证编号
	 */
	private String workCardNo;

	/**
	 * 工作证有效期
	 */
	@JsonFormat(pattern = "yyyy-MM-dd")
	private Date workCardEndDate;

	/**
	 * 入职时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd")
	private Date enterTime;

	/**
	 * 在职/离职
	 */
	private Integer postState;

	/**
	 * 人员编号
	 */
	private String empCode;

	/**
	 * 关键岗位标记
	 */
	private Integer keyPositionFlag;

	/**
	 * 关键岗位认证
	 */
	private Integer keyPositionAuth;
}
