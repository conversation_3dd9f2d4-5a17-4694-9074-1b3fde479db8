package com.whfc.emp.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-08-28 15:02
 */
public enum TrainSource {

    MS("ms", "后台"),

    MP("mp", "小程序"),

    TRAIN_BOX("train_box", "培训箱"),

    OTHER("other", "其它");

    private final String code;

    private final String desc;

    TrainSource(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public TrainSource parseCode(String code) {
        TrainSource[] values = TrainSource.values();
        for (TrainSource source : values) {
            if (source.code.equals(code)) {
                return source;
            }
        }
        return null;
    }

}
