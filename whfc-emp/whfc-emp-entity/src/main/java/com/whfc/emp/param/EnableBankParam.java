package com.whfc.emp.param;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 启用禁用银行卡参数
 * @date 2020-08-08
 */
@Data
public class EnableBankParam implements Serializable {
    /**
     * 银行账号id
     */
    @NotNull
    private Integer bankId;
    /**
     * 启用标记 0-未启用 1-启用
     */
    @NotNull
    private Integer enableFlag;
}
