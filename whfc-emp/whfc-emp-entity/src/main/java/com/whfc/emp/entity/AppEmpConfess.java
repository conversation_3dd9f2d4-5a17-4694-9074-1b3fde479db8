package com.whfc.emp.entity;

import java.util.Date;

/**
 * 人员安全交底
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-09-02 15:59
 */
public class AppEmpConfess {
    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 人员ID
     */
    private Integer empId;

    /**
     * 工程名称
     */
    private String projectName;

    /**
     * 交底时间
     */
    private Date date;

    /**
     * 施工单位ID
     */
    private Integer corpId;

    /**
     * 施工单位
     */
    private String corpName;

    /**
     * 分项工程
     */
    private String projectPart;

    /**
     * 负责人ID
     */
    private Integer principalId;

    /**
     * 负责人名称
     */
    private String principalName;

    /**
     * 交底人ID
     */
    private Integer confidantId;

    /**
     * 交底人名称
     */
    private String confidantName;

    /**
     * 被交底人ID
     */
    private Integer beConfidantId;

    /**
     * 被交底人名称
     */
    private String beConfidantName;

    /**
     * 签名图片
     */
    private String signImgUrl;

    /**
     * 删除标记（0-未删除 1-已删除）
     */
    private Integer delFlag;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getEmpId() {
        return empId;
    }

    public void setEmpId(Integer empId) {
        this.empId = empId;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public Integer getCorpId() {
        return corpId;
    }

    public void setCorpId(Integer corpId) {
        this.corpId = corpId;
    }

    public String getCorpName() {
        return corpName;
    }

    public void setCorpName(String corpName) {
        this.corpName = corpName;
    }

    public String getProjectPart() {
        return projectPart;
    }

    public void setProjectPart(String projectPart) {
        this.projectPart = projectPart;
    }

    public Integer getPrincipalId() {
        return principalId;
    }

    public void setPrincipalId(Integer principalId) {
        this.principalId = principalId;
    }

    public String getPrincipalName() {
        return principalName;
    }

    public void setPrincipalName(String principalName) {
        this.principalName = principalName;
    }

    public Integer getConfidantId() {
        return confidantId;
    }

    public void setConfidantId(Integer confidantId) {
        this.confidantId = confidantId;
    }

    public String getConfidantName() {
        return confidantName;
    }

    public void setConfidantName(String confidantName) {
        this.confidantName = confidantName;
    }

    public Integer getBeConfidantId() {
        return beConfidantId;
    }

    public void setBeConfidantId(Integer beConfidantId) {
        this.beConfidantId = beConfidantId;
    }

    public String getBeConfidantName() {
        return beConfidantName;
    }

    public void setBeConfidantName(String beConfidantName) {
        this.beConfidantName = beConfidantName;
    }

    public String getSignImgUrl() {
        return signImgUrl;
    }

    public void setSignImgUrl(String signImgUrl) {
        this.signImgUrl = signImgUrl;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}