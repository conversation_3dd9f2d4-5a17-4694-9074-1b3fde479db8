package com.whfc.emp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 员工每天的工时
 *
 * <AUTHOR>
 * @Description:
 * @date 2019年9月3日
 */
@Data
public class AppEmpDayWorkTimesDTO implements Serializable {

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date date;

    /**
     * 上班时间
     */
    private Date startTime;

    /**
     * 下班时间
     */
    private Date endTime;

    /**
     * 工作时长(秒)
     */
    private Integer times;

    /**
     * 工作时长(小时)
     */
    private Double workTimes;
}
