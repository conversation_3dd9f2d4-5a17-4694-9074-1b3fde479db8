package com.whfc.emp.param;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 关键岗位人员认证参数
 * @date 2020-12-30
 */
@Data
public class KeyPositionAuthConfirmParam implements Serializable {

    /**
     * 项目ID
     */
    private Integer deptId;

    /**
     * 人员id
     */
    @NotNull
    private Integer empId;

    /**
     * 认证  0-未使用 1-使用
     */
    @NotNull
    private Integer keyPositionAuth;
}
