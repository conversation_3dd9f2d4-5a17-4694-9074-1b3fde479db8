package com.whfc.emp.dto.device;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.whfc.emp.dto.AppEmpDeviceStatNumDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/28
 */
@Data
public class AppEmpDeviceLogBaseExportDTO implements Serializable {

    /**
     * 时间段内通讯次数统计
     */
    @ExcelIgnore
    private List<AppEmpDeviceStatNumDTO> numList;

    @ColumnWidth(20)
    @ExcelProperty(value = "00:00-02:00", index = 3)
    private String time2;

    @ColumnWidth(20)
    @ExcelProperty(value = "02:00-04:00", index = 4)
    private String time4;

    @ColumnWidth(20)
    @ExcelProperty(value = "04:00-06:00", index = 5)
    private String time6;

    @ColumnWidth(20)
    @ExcelProperty(value = "06:00-08:00", index = 6)
    private String time8;

    @ColumnWidth(20)
    @ExcelProperty(value = "08:00-10:00", index = 7)
    private String time10;

    @ColumnWidth(20)
    @ExcelProperty(value = "10:00-12:00", index = 8)
    private String time12;

    @ColumnWidth(20)
    @ExcelProperty(value = "12:00-14:00", index = 9)
    private String time14;

    @ColumnWidth(20)
    @ExcelProperty(value = "14:00-16:00", index = 10)
    private String time16;

    @ColumnWidth(20)
    @ExcelProperty(value = "16:00-18:00", index = 11)
    private String time18;

    @ColumnWidth(20)
    @ExcelProperty(value = "18:00-20:00", index = 12)
    private String time20;

    @ColumnWidth(20)
    @ExcelProperty(value = "20:00-22:00", index = 13)
    private String time22;

    @ColumnWidth(20)
    @ExcelProperty(value = "22:00-24:00", index = 14)
    private String time24;

}
