package com.whfc.emp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 安全交底DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-09-02 15:55
 */
@Data
public class AppEmpConfessDTO implements Serializable {

    /**
     * 安全交底ID
     */
    private Integer confessId;

    /**
     * 人员ID
     */
    private Integer empId;

    /**
     * 工程名称
     */
    private String projectName;

    /**
     * 交底时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date date;

    /**
     * 施工单位ID
     */
    private Integer corpId;

    /**
     * 施工单位
     */
    private String corpName;

    /**
     * 分项工程
     */
    @Deprecated
    private String projectPart;

    /**
     * 负责人ID
     */
    @Deprecated
    private Integer principalId;

    /**
     * 负责人名称
     */
    @Deprecated
    private String principalName;

    /**
     * 交底人ID
     */
    private Integer confidantId;

    /**
     * 交底人名称
     */
    private String confidantName;

    /**
     * 被交底人ID
     */
    private Integer beConfidantId;

    /**
     * 被交底人名称
     */
    private String beConfidantName;

    /**
     * 签名图片
     */
    private String signImgUrl;

    /**
     * 安全交底图片
     */
    private List<String> imgUrlList;
}
