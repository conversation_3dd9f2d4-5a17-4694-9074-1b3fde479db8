package com.whfc.emp.entity;

import java.util.Date;

/**
    * 人员-惩罚记录
    */
public class AppEmpRewardPunishment {
    private Integer id;

    /**
    * 人员ID
    */
    private Integer empId;

    /**
    * 人员姓名
    */
    private String empName;

    /**
    * 类型 1-奖励 2-惩罚
    */
    private Integer type;

    /**
    * 类目 1-火电气 2-气瓶 3-特种作业证 4-悬空作业 5-高处作业 6-翻斗车 7-安全帽 8-安全带 9-国家级 10-省级 11-市级
    */
    private Integer item;

    /**
    * 描述
    */
    private String description;

    /**
    * 发生日期
    */
    private Date date;

    /**
    * 删除标记(0-未删除 1-已删除)
    */
    private Integer delFlag;

    /**
    * 更新时间
    */
    private Date updateTime;

    /**
    * 创建时间
    */
    private Date createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getEmpId() {
        return empId;
    }

    public void setEmpId(Integer empId) {
        this.empId = empId;
    }

    public String getEmpName() {
        return empName;
    }

    public void setEmpName(String empName) {
        this.empName = empName;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getItem() {
        return item;
    }

    public void setItem(Integer item) {
        this.item = item;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}