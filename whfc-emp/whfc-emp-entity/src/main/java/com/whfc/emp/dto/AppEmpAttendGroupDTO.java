package com.whfc.emp.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description 今日出勤班组统计
 * <AUTHOR>
 * @Date 2021/1/13 14:50
 * @Version 1.0
 */
@Schema(description = "今日出勤班组统计")
@Data
public class AppEmpAttendGroupDTO implements Serializable {

    @JsonIgnore
    private Integer id;

    @Schema(description = "班组名称")
    private String name;

    @Schema(description = "班组ID")
    private Integer groupId;

    @Schema(description = "合作单位ID")
    private Integer corpId;

    @Schema(description = "合作单位名称")
    private String corpName;

    @Schema(description = "工区ID")
    private Integer areaId;

    @Schema(description = "工区名称")
    private String areaName;

    @Schema(description = "人员总数")
    private Integer total;

    @Schema(description = "闸机数量")
    private Integer faceGateNum;

    @Schema(description = "出勤人数")
    private Integer num;

    @Schema(description = "出勤率")
    private Double rate;

    @Schema(description = "是否展开")
    private Boolean expand = false;

    @Schema(description = "人员列表")
    private List<AppEmpDTO> empList;

}
