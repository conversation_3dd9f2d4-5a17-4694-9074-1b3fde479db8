package com.whfc.emp.enums;

/**
 * @Description 附件类型
 * <AUTHOR>
 * @Date 2021-06-10 10:50
 * @Version 1.0
 */
public enum AppAttachmentType {
    PHOTO(1, "照片"),
    ATTACHMENT(2, "附件");

    private Integer value;

    private String desc;

    AppAttachmentType(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
