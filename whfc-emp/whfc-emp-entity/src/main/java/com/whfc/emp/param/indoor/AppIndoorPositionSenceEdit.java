package com.whfc.emp.param.indoor;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/3/4 16:46
 */
@Data
public class AppIndoorPositionSenceEdit implements Serializable {

    /**
     * 地图guid
     */
    @NotEmpty(message = "地图guid不能为空")
    private String guid;

    /**
     * 比例
     */
    private Double ratio;

    /**
     * 基站列表
     */
    @NotEmpty
    @Valid
    private List<AppIndoorPositionSenceItem> stationList;
}
