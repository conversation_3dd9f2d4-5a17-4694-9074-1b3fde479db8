package com.whfc.emp.param;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 人员管理-人员列表的参数
 *
 * <AUTHOR>
 * @Description:
 * @date 2019年8月27日
 */
@Data
public class AppEmpListParam implements Serializable {

    /**
     * 分页页码
     */
    private Integer pageNum;

    /**
     * 分页大小
     */
    private Integer pageSize;

    /**
     * 组织机构ID
     */
    @NotNull
    private Integer deptId;

    /**
     * 关键词
     */
    private String keyword;

    /**
     * 岗位状态
     */
    private Integer postState;

    /**
     * 岗位类型
     */
    private Integer workTypeId;

    /**
     * 岗位/工种
     */
    private Integer workRoleId;

    /**
     * 性别
     */
    private Integer gender;

    /**
     * 合作单位
     */
    private Integer corpId;

    /**
     * 硬件绑定状态
     */
    private Integer bindFlag;

    /**
     * 班组id
     */
    private Integer groupId;
}
