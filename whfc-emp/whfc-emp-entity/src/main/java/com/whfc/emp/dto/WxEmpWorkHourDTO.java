package com.whfc.emp.dto;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2019/8/27 17:43
 */
@Data
public class WxEmpWorkHourDTO implements Serializable {

    private Integer empId;

    private Date date;

    /**
     * 报警次数
     */
    private Integer warnCnt;

    /**
     * 报警数据数组
     */
    private List<WxEmpWorkWarnDTO> warnArr;

    /**
     * 工作时长
     */
    private Double workTimes;

    /**
     * 工作时段数组
     */
    private List<WxEmpWorkTimesDTO> workTimesArr;

    /**
     * 进出电子围栏记录
     */
    private List<WxEmpLocaleStateDTO> localeStateArr;

    /**
     * 出勤状态
     */
    private Integer attendState;

    private Integer localeState;
}
