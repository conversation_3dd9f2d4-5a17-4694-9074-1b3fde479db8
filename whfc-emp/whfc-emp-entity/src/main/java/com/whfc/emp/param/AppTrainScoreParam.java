package com.whfc.emp.param;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class AppTrainScoreParam implements Serializable {
    /**
     * 培训id
     */
    @NotNull
    private Integer trainId;
    /**
     * 人员id
     */
    @NotNull
    private Integer empId;
    /**
     * 分数
     */
    @NotNull
    private Double score;
    /**
     * 是否合格 0-不合格  1-合格
     */
    @NotNull
    private Integer passFlag;

    /**
     * 成绩图片列表
     */
    private List<String> imgUrlList;

}
