package com.whfc.emp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 打卡记录实体
 * @date 2020-06-05
 */
@Data
public class AppEmpAttendRecordDTO implements Serializable {

    /**
     * 人员ID
     */
    private Integer empId;

    /**
     * 时间
     */
    @JsonFormat(pattern = "HH:mm:ss")
    private Date time;

    /**
     * 方向
     */
    private Integer direction;

    /**
     * 数据来源(1-电子围栏 2-闸机 3-后台录入 4.扫码录入)
     */
    private Integer type;

    /**
     * 名称(闸机名称 或者 电子围栏名称)
     */
    private String name;

    /**
     * 状态 (方向)
     */
    private Integer state;

    /**
     * 图片
     */
    private String photo;
}
