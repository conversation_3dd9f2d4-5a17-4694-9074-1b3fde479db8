package com.whfc.emp.param.indoor;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/3/4 16:46
 */
@Data
public class AppIndoorPositionStationAdd implements Serializable {

    @NotNull
    private Integer deptId;

    @NotEmpty
    @Length(max = 32)
    private String code;

    @NotEmpty
    @Length(max = 32)
    private String name;

    private Integer mapId;

    private Double x;

    private Double y;

    private Double lng;

    private Double lat;

    @Length(max = 64)
    private String ext1;

    @Length(max = 64)
    private String ext2;

    @Length(max = 64)
    private String ext3;
}
