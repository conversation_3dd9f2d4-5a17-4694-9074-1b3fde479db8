package com.whfc.emp.entity;

import java.util.Date;

/**
 * 平台闸机-品牌库
 *
* <AUTHOR>
* @version 1.0
* @date 2021-08-17 11:31
*/
public class AppFaceGatePlatform {
    /**
    * 主键ID
    */
    private Integer id;

    /**
    * 平台编码
    */
    private String platform;

    /**
    * 平台名称
    */
    private String name;

    /**
    * 厂家信息
    */
    private String factory;

    /**
    * 备注
    */
    private String remark;

    /**
    * 闸机状态  0-离线闸机  1-在线闸机
    */
    private Integer state;

    /**
    * 删除标记（0-未删除 1-已删除）
    */
    private Integer delFlag;

    /**
    * 更新时间
    */
    private Date updateTime;

    /**
    * 创建时间
    */
    private Date createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getFactory() {
        return factory;
    }

    public void setFactory(String factory) {
        this.factory = factory;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}