package com.whfc.emp.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 人员批量离职参数
 */
@Data
public class AppEmpOuterBatchParam implements Serializable {

    /**
     * 人员ID
     */
    @NotEmpty
    private List<Integer> empIds;

    /**
     * 日期
     */
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date date;
}
