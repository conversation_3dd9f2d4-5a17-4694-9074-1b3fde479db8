package com.whfc.emp.enums;

/**
 * 广播发送类型
 *
 * <AUTHOR> qzexing
 * @version : 1.0
 * @date : 2020/7/2 19:24
 */
public enum AppBroadcastSendState {

    UNSENT(0, "未发送"),

    SEND(1, "已发送"),

    CONFIRM(2,"已确认"),

    EXPIRED(3,"已失效");


    private Integer value;

    private String desc;

    AppBroadcastSendState(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static AppBroadcastSendState parseValue(Integer value) {
        switch (value) {
            case 0:
                return UNSENT;
            case 1:
                return SEND;
            case 2:
                return CONFIRM;
            case 3:
                return EXPIRED;
            default:
                return null;
        }
    }

}
