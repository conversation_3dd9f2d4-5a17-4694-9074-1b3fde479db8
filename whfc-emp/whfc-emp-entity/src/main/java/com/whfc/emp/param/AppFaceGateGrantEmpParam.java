package com.whfc.emp.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AppFaceGateGrantEmpParam implements Serializable {
	@NotNull
	private Integer faceGateId;

	private String deviceKey;

	private String personGuid;

	@NotEmpty
	private List<Integer>empIdList;
}
