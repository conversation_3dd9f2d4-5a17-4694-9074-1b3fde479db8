package com.whfc.emp.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 人员信息同步
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-08-05 11:51
 */
@Getter
@Setter
@ToString(exclude = {"avatar"})
public class EmpInfoSyncDataParam implements Serializable {

    /**
     * 同步类型
     *
     * @see com.whfc.emp.enums.FaceGateSyncOp
     */
    private String syncType;

    /**
     * 闸机平台
     */
    private String platform;

    /**
     * 组织机构ID
     */
    private Integer deptId;

    /**
     * 人员姓名
     */
    private String empName;

    /**
     * 人员唯一凭证
     */
    private String empCode;

    /**
     * 性别 1-男 2-女
     */
    private Integer gender;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 电话
     */
    private String phone;

    /**
     * 身份证号
     */
    private String idCardNo;

    /**
     * 住址
     */
    private String address;

    /**
     * 民族
     */
    private String nation;

    /**
     * 所在单位ID
     */
    private Integer corpId;

    /**
     * 所在单位编号
     */
    private String corpCode;

    /**
     * 所在单位名称
     */
    private String corpName;

    /**
     * 班组ID
     */
    private Integer groupId;

    /**
     * 班组编号
     */
    private String groupCode;

    /**
     * 班组名称
     */
    private String groupName;

    /**
     * 工种id
     */
    private String workTypeName;

    /**
     * 工人类型ID
     */
    private Integer workRoleId;

    /**
     * 工人类型名称
     */
    private String workRoleName;

    /**
     * 进场时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date enterTime;

    /**
     * 图片类型
     * 1-url  2-base64
     *
     * @see com.whfc.emp.enums.EmpSyncImgType
     */
    private Integer imgType;
}
