package com.whfc.emp.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-09-02 09:45
 */
public enum EmpHealthCodeSate {

    GREEN("GRE<PERSON>", "绿码"),

    RED("RED", "红码"),

    YELLOW("<PERSON><PERSON><PERSON><PERSON>", "黄码"),

    ORANGE("ORANGE", "橙码");


    private final String value;

    private final String desc;

    EmpHealthCodeSate(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

}
