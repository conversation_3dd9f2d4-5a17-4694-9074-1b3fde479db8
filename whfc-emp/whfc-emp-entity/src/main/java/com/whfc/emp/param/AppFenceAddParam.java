package com.whfc.emp.param;

import com.whfc.common.geometry.Point;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class AppFenceAddParam implements Serializable {

    /**
     * 电子围栏ID
     */
    private Integer id;

    /**
     * 组织机构id
     */
    @NotNull
    private Integer deptId;

    /**
     * 电子围栏名称
     */
    @NotEmpty
    private String name;

    /**
     * 电子围栏类型 1-多边形 2-圆形
     */
    @NotNull
    private Integer type;

    /**
     * 多边形坐标 格式  polygon((1 2,1 3,1 4,1 2))  最后一个点和第一个点相同
     */
    private String polygon;

    /**
     * 圆形中心点坐标 格式  point(1 2)
     */
    private String center;

    /**
     * 圆形半径
     */
    private Double radius;

    /**
     * 接收多边形点左边
     */
    private List<Point> polygonPointList;

    /**
     * 接收圆形中心点坐标
     */
    private Point centerPoint;


}
