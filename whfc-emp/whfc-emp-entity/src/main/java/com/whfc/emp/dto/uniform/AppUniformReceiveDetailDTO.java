package com.whfc.emp.dto.uniform;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class AppUniformReceiveDetailDTO implements Serializable {

    private Integer deptId;

    private Integer receiveId;

    /**
     * 物品ID
     */
    private Integer itemId;

    /**
     * 物品名称
     */
    @NotEmpty
    @Length(max = 32)
    private String itemName;

    /**
     * 物品数量
     */
    @NotNull
    private Integer itemAmount;
}