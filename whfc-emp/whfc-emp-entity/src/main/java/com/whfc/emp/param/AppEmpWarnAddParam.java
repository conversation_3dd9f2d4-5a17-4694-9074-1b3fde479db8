package com.whfc.emp.param;

import com.whfc.common.geometry.Point;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description 添加人员报警规则的参数
 * @date 2021-05-18
 */
@Data
public class AppEmpWarnAddParam implements Serializable {
    /**
     * 规则id
     */
    private Integer ruleId;

    @NotNull
    private Integer deptId;

    @NotNull
    private Integer ruleType;


    private String ruleParam;

    @NotEmpty
    private String name;

    /**
     * 电子围栏类型 1-多边形 2-圆形
     */
    private Integer type;

    /**
     * 多边形文本
     */
    private String polygon;

    /**
     * 多边形坐标
     */
    private List<Point> polygonPointList;

    /**
     * 圆形文本
     */
    private String center;

    /**
     * 圆形中心点坐标
     */
    private Point centerPoint;

    /**
     * 圆形半径
     */
    private Double radius;
}
