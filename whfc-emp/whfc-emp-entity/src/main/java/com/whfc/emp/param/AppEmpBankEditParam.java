package com.whfc.emp.param;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 编辑银行账户参数
 * @date 2020-08-08
 */
@Data
public class AppEmpBankEditParam implements Serializable {
    /**
     * 银行账号id
     */
    @NotNull
    private Integer bankId;
    /**
     * 银行代码
     */
    @NotEmpty
    private String bankCode;
    /**
     * 银行名称
     */
    @NotEmpty
    private String bankName;
    /**
     * 银行号码
     */
    @NotEmpty
    private String bankNumber;
}
