package com.whfc.emp.dto.device;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 人员硬件信息 导出中文
 */
@Data
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
public class AppEmpDeviceExportHkDTO implements Serializable {

    /**
     * 硬件SN
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "硬件編號", index = 0)
    private String sn;

    /**
     * 人员名称
     */
    @ColumnWidth(15)
    @ExcelProperty(value = "人員名稱", index = 1)
    private String empName;

    /**
     * 所属班组
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "所屬班組", index = 2)
    private String groupName;

    /**
     * 上次通信时间
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "上次通信時間", index = 3)
    private Date time;


}