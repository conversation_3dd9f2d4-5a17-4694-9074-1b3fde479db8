package com.whfc.emp.enums;

/**
 * @Description: 网络状态
 * @author: xug<PERSON>
 * @version: 1.0
 * @date: 2019/7/29 11:37
 */
public enum NetState {

    OFFLINE(0, "离线"),

    ONLINE(1, "在线");

    private Integer value;

    private String desc;

    NetState(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
