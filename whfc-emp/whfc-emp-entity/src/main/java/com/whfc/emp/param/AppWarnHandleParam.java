package com.whfc.emp.param;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Description: 报警记录处理-参数对象
 * @author: xug<PERSON>
 * @version: 1.0
 * @date: 2019/11/30 9:04
 */
@Data
public class AppWarnHandleParam implements Serializable {

    /**
     * 报警记录ID
     */
    @NotNull
    private Integer warnId;

    /**
     * 处理结果
     */
    @NotEmpty
    @Length(max = 100)
    private String handleResult;

    /**
     * 备注
     */
    @Length(max = 100)
    private String handleRemark;

    /**
     * 处理人
     */
    private Integer userId;

    /**
     * 处理人姓名
     */
    private String userName;

    /**
     * 处理人手机号
     */
    private String phone;
}
