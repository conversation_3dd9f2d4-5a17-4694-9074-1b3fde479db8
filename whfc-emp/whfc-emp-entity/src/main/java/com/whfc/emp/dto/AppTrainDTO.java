package com.whfc.emp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class AppTrainDTO implements Serializable {

    /**
     * 培训id
     */
    private Integer trainId;

    /**
     * 培训名称
     */
    private String name;

    /**
     * 培训类型id
     */
    private Integer trainType;

    /**
     * 培训类型编码
     */
    private String trainTypeCode;

    /**
     * 培训类型名称
     */
    private String trainTypeName;

    /**
     * 组织机构id
     */
    private Integer deptId;

    /**
     * 组织机构名称
     */
    private String deptName;

    /**
     * 状态 0-未完成 1-已完成
     */
    private Integer state;

    /**
     * 培训内容
     */
    private String content;

    /**
     * 组织者Id
     */
    private Integer organizerId;

    /**
     * 组织者
     */
    private String organizer;

    /**
     * 培训人Id
     */
    private String trainerId;

    /**
     * 培训人
     */
    private String trainer;

    /**
     * 培训地点
     */
    private String address;

    /**
     * 培训日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date date;

    /**
     * 培训时长
     */
    private Double duration;

    /**
     * 培训照片
     */
    private List<String> trainPhoto;

    /**
     * 附件
     */
    private List<String> attachment;

    /**
     * 来源
     *
     * @see com.whfc.emp.enums.TrainSource
     */
    private String source;

    /**
     * 同步标记
     */
    private Integer syncFlag;
}
