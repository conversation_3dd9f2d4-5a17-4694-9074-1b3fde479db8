package com.whfc.emp.enums;

/**
 * @Description: 进出方向
 * @author: xugcheng
 * @version: 1.0
 * @date: 2019/7/29 11:37
 */
public enum Direction {

    IN(1, "进"),

    OUT(0, "出");

    private Integer value;

    private String desc;

    Direction(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static Direction parseValue(Integer value) {
        switch (value) {
            case 1:
                return IN;
            case 0:
                return OUT;
            default:
                return IN;
        }
    }

    public static Direction parseDesc(String desc) {
        switch (desc) {
            case "进":
                return IN;
            case "出":
                return OUT;
            default:
                return IN;
        }
    }
}
