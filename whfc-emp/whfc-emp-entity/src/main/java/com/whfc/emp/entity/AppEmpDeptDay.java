package com.whfc.emp.entity;

import java.util.Date;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2022-01-14 11:06
 */
public class AppEmpDeptDay {

    private Integer id;

    private Integer deptId;

    private Date date;

    private Integer totalEmpNum;

    private Integer managerNum;

    private Integer workerNum;

    private Integer planAttendNum;

    private Integer actualAttendNum;

    private Date updateTime;

    private Date createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getDeptId() {
        return deptId;
    }

    public void setDeptId(Integer deptId) {
        this.deptId = deptId;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public Integer getTotalEmpNum() {
        return totalEmpNum;
    }

    public void setTotalEmpNum(Integer totalEmpNum) {
        this.totalEmpNum = totalEmpNum;
    }

    public Integer getManagerNum() {
        return managerNum;
    }

    public void setManagerNum(Integer managerNum) {
        this.managerNum = managerNum;
    }

    public Integer getWorkerNum() {
        return workerNum;
    }

    public void setWorkerNum(Integer workerNum) {
        this.workerNum = workerNum;
    }

    public Integer getPlanAttendNum() {
        return planAttendNum;
    }

    public void setPlanAttendNum(Integer planAttendNum) {
        this.planAttendNum = planAttendNum;
    }

    public Integer getActualAttendNum() {
        return actualAttendNum;
    }

    public void setActualAttendNum(Integer actualAttendNum) {
        this.actualAttendNum = actualAttendNum;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
