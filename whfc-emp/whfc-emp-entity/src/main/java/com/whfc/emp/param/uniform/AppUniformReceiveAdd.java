package com.whfc.emp.param.uniform;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.whfc.emp.dto.uniform.AppUniformReceiveDetailDTO;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/1/25 15:21
 */
@Data
public class AppUniformReceiveAdd implements Serializable {

    /**
     * 项目ID
     */
    @NotNull
    private Integer deptId;

    /**
     * 人员ID
     */
    private Integer empId;

    /**
     * 人员姓名
     */
    @NotEmpty
    private String empName;

    /**
     * 公司
     */
    private String company;

    /**
     * 领用日期
     */
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date date;

    /**
     * 创建人ID
     */
    private Integer createUserId;

    /**
     * 创建人
     */
    private String createUserName;

    /**
     * 工服领用详情
     */
    @NotEmpty
    @Valid
    private List<AppUniformReceiveDetailDTO> detailList;

    /**
     * 图片列表
     */
    private List<String> imgList;
}
