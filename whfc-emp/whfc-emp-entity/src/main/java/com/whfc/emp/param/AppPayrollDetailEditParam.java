package com.whfc.emp.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description: 修改工资记录
 * @author: hw
 * @version: 1.0
 * @date: 2020-11-25 18:53
 */
@Data
public class AppPayrollDetailEditParam implements Serializable {

    /**
     * 工资单id
     */
    @NotNull
    private Integer payrollId;

    /**
     * 员工id
     */
    @NotNull
    private Integer empId;

    /**
     * 系统出勤天数
     */
    private Integer attendDays;

    /**
     * 工程量
     */
    private Integer workAmount;

    /**
     * 单价
     */
    private Double unitPrice;

    /**
     * 工资总额
     */
    private Double salaryTotal;

    /**
     * 应发工资
     */
    private Double salaryShould;

    /**
     * 扣款
     */
    private Double salaryDeduct;

    /**
     * 借款
     */
    private Double salaryBorrow;

    /**
     * 实发工资
     */
    private Double salaryReal;
    /**
     * 工资发放日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date date;
    /**
     * 备注
     */
    @Length(max = 50)
    private String remark;

    /**
     * 员工工资照片
     */
    private List<String> imgUrlList;
}
