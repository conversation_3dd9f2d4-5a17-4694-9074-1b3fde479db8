package com.whfc.emp.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/2/27
 */
@Data
public class AppEmpDeviceParam implements Serializable {

    @Schema(description =  "guid")
    private String guid;

    /**
     * 硬件类型 2-智能安全帽 34-智能手环
     */
    @Schema(description =  "硬件类型 2-智能安全帽 34-智能手环")
    private Integer deviceType;

    /**
     * 硬件平台
     */
    @Schema(description =  "硬件平台")
    private String platform;

    /**
     * 硬件SN
     */
    @Schema(description =  "硬件SN")
    private String sn;

    /**
     * 颜色
     */
    @Schema(description =  "颜色")
    private String color;


}
