package com.whfc.emp.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 硬件-工牌数据日志
 */
@Data
public class AppDeviceCardLog implements Serializable {
    private Integer id;

    /**
     * 设备序列号
     */
    private String deviceCode;

    /**
     * 劳务ID
     */
    private Integer empId;

    /**
     * 硬件ID(工牌)
     */
    private Integer deviceId;

    /**
     * 设备类型
     */
    private Integer deviceType;

    /**
     * 人员硬件ID
     */
    private Integer empDeviceId;

    /**
     * 时间
     */
    private Date time;

    /**
     * 厂商
     */
    private String platform;

    private Double lngWgs84;

    private Double latWgs84;

    private Double lng;

    private Double lat;

    /**
     * 坐标地址
     */
    private String location;

    /**
     * 速度
     */
    private Double speed;

    /**
     * 角度
     */
    private Double rotation;

    /**
     * 版本号
     */
    private String version;

    /**
     * 卫星数量
     */
    private Integer geoNum;

    /**
     * 水平因子
     */
    private Double levelFactor;

    /**
     * 信号强度
     */
    private Integer rssi;

    /**
     * 报警值
     */
    private Integer alarmValue;

    /**
     * 报到提醒(0-无效 1-有效)
     */
    private Integer alarmReport;

    /**
     * sos提醒(0-无效 1-有效)
     */
    private Integer alarmSos;

    /**
     * 跌落报警(0-无效 1-有效)
     */
    private Integer alarmDrop;

    /**
     * 脱帽报警(0-无效 1-有效)
     */
    private Integer alarmDoff;

    /**
     * 长时间静止报警(0-无效 1-有效)
     */
    private Integer alarmStill;

    /**
     * 碰撞报警 0-无效 1-有效
     */
    private Integer alarmCrash;

    /**
     * 状态值
     */
    private Integer cardStateValue;

    /**
     * 定位类型(0:基站 1-卫星)
     */
    private Integer posType;

    /**
     * 定位状态(0-无效,1-有效)
     */
    private Integer posState;

    /**
     * 定位方式(0-GPS 1-北斗 2-GPS北斗双模)
     */
    private Integer posMode;

    /**
     * 电压
     */
    private Double batterVolt;

    /**
     * 电量
     */
    private Integer batteryPower;

    /**
     * 加速度x
     */
    private Double accelerationX;

    /**
     * 加速度y
     */
    private Double accelerationY;

    /**
     * 加速度z
     */
    private Double accelerationZ;

    /**
     * 体温
     */
    private Double bodyTemp;

    /**
     * 心率
     */
    private Integer heartRate;

    /**
     * 血压
     */
    @Deprecated
    private String bloodPressure;

    /**
     * 低压
     */
    private Integer diastolicPressure;

    /**
     * 高压
     */
    private Integer systolicPressure;

    /**
     * 血氧
     */
    private Integer bloodOxygen;

    /**
     * 血糖
     */
    private Double bloodSugar;

    /**
     * 删除标记
     */
    private Integer delFlag;

    private Date updateTime;

    private Date createTime;


}