package com.whfc.emp.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/8/10 17:09
 */
public enum FaceGateSyncOp {

    EMP_ADD_OR_UPDATE("emp_add_or_update", "新增/修改人员"),

    EMP_DEL("emp_del", "删除人员"),

    DEVICE_ADD_OR_UPDATE("device_add_or_update", "新增/修改设备"),

    DEVICE_DEL("device_del", "删除设备");


    private final String value;

    private final String desc;

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    FaceGateSyncOp(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static FaceGateSyncOp parseValue(String value) {
        FaceGateSyncOp[] values = FaceGateSyncOp.values();
        for (FaceGateSyncOp faceGateSyncOp : values) {
            if (faceGateSyncOp.getValue().equals(value)) {
                return faceGateSyncOp;
            }
        }
        return null;
    }

}
