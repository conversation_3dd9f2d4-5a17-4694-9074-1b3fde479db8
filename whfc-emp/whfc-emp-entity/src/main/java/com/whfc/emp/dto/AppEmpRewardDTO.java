package com.whfc.emp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 奖惩记录实体
 * @date 2020-08-12
 */
@Data
public class AppEmpRewardDTO implements Serializable {
    private Integer id;
    /**
     * 人员姓名
     */
    private String empName;
    /**
     * 类型 1-奖励 2-惩罚
     */
    private Integer type;
    /**
     * 类目 1-火电气 2-气瓶 3-特种作业证 4-悬空作业 5-高处作业 6-翻斗车 7-安全帽 8-安全带 9-国家级 10-省级 11-市级
     */
    private Integer item;
    /**
     * 描述
     */
    private String description;
    /**
     * 发生日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date date;

}
