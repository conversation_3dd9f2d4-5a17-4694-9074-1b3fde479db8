package com.whfc.emp.entity;

import lombok.Data;

import java.util.Date;

/**
 * 人员硬件-最新数据
 */
@Data
public class AppEmpData {
    private Integer id;

    private Integer deptId;

    /**
     * 人员ID
     */
    private Integer empId;

    /**
     * 网络状态(0-离线 1-在线)
     */
    private Integer netState;

    /**
     * 现场状态(0-不在现场 1-在现场)
     */
    private Integer localeState;

    /**
     * 现场时间
     */
    private Date localeTime;

    /**
     * 工区ID
     */
    private Integer areaId;

    /**
     * 工区名称
     */
    private String areaName;

    /**
     * 考勤状态(0-缺勤 1-出勤)
     */
    private Integer attendState;

    /**
     * 最近通信时间
     */
    private Date time;

    /**
     * E-东经/W-西经
     */
    private String lngFlag;

    /**
     * N-北纬/S-南纬
     */
    private String latFlag;

    /**
     * 经度
     */
    private Double lng;

    /**
     * 纬度
     */
    private Double lat;

    /**
     * 坐标时间
     */
    private Date gpsTime;

    private Double lngWgs84;

    private Double latWgs84;

    /**
     * 电池状态(1-在充电 2-未充电)
     */
    private Integer batteryState;

    /**
     * 电池电量(百分比)
     */
    private Integer batteryPower;

    /**
     * 血压
     */
    @Deprecated
    private String bloodPressure;

    private Double bodyTemp;

    private Integer heartRate;

    private Integer bloodOxygen;

    private Integer diastolicPressure;

    private Integer systolicPressure;

    private Double bloodSugar;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;
}