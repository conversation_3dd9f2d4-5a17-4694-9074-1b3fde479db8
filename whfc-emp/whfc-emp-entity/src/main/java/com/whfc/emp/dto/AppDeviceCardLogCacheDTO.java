package com.whfc.emp.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 安全帽硬件数据缓存对象
 * @author: xugcheng
 * @version: 1.0
 * @date: 2020/6/2 9:47
 */
@Data
public class AppDeviceCardLogCacheDTO implements Serializable, Comparable<AppDeviceCardLogCacheDTO> {

    /**
     * 时间
     */
    private Date time;

    /**
     * 经度
     */
    private Double lng;

    /**
     * 纬度
     */
    private Double lat;

    /**
     * 位置信息
     */
    private String location;

    public AppDeviceCardLogCacheDTO() {
    }

    public AppDeviceCardLogCacheDTO(Date time, Double lng, Double lat, String location) {
        this.time = time;
        this.lng = lng;
        this.lat = lat;
        this.location = location;
    }

    @Override
    public int compareTo(AppDeviceCardLogCacheDTO o) {
        return this.getTime().compareTo(o.getTime());
    }
}
