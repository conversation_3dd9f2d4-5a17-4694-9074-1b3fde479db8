package com.whfc.emp.enums;


public enum AppWorkRole {

    WORKER(2, "建筑工人"),

    MANAGER(1, "管理人员");

    private Integer value;

    private String desc;

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    AppWorkRole(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer value() {
        return value;
    }

    public static AppWorkRole parseValue(Integer value) {
        switch (value) {
            case 1:
                return MANAGER;
            case 2:
                return WORKER;
            default:
                return MANAGER;
        }
    }


}
