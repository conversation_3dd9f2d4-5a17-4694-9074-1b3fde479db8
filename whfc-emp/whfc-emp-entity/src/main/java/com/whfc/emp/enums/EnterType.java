package com.whfc.emp.enums;

/**
 * @ClasssName EnterType
 * @Description 在职离职状态
 * <AUTHOR>
 * @Date 2021/3/17 17:52
 * @Version 1.0
 */
public enum EnterType {
    enter(1, "入职"),
    outer(2,"离职");

    private Integer value;

    private String desc;

    EnterType(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    }
