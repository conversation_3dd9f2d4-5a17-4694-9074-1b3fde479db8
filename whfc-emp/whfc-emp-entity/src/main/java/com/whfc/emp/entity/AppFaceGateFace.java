package com.whfc.emp.entity;

import java.util.Date;

/**
    * 机构-人脸识别闸机-人脸
    */
public class AppFaceGateFace {
    private Integer id;

    /**
     * 组织机构id
     */
    private Integer deptId;

    /**
    * 闸机iD
    */
    private Integer faceGateId;

    /**
    * 闸机序列号
    */
    private String deviceKey;

    /**
    * 人员ID
    */
    private Integer empId;

    /**
    * 人员guid
    */
    private String personGuid;

    /**
    * 1：普通 RGB 照片，默认；2：红外照片， 特定设备型号使用
    */
    private Integer type;

    /**
    * 人脸guid
    */
    private String faceGuid;

    /**
    * 人脸图片url
    */
    private String faceUrl;

    /**
    * 校验等级
    */
    private Integer validLevel;

    /**
    * 照片注册状态(1:上传中 2:注册中 3:注册成功 4:注册失败)
    */
    private Integer state;

    /**
    * 删除标记(0-未删除 1-已删除)
    */
    private Integer delFlag;

    /**
    * 更新时间
    */
    private Date updateTime;

    /**
    * 创建时间
    */
    private Date createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getDeptId() {
        return deptId;
    }

    public void setDeptId(Integer deptId) {
        this.deptId = deptId;
    }

    public Integer getFaceGateId() {
        return faceGateId;
    }

    public void setFaceGateId(Integer faceGateId) {
        this.faceGateId = faceGateId;
    }

    public String getDeviceKey() {
        return deviceKey;
    }

    public void setDeviceKey(String deviceKey) {
        this.deviceKey = deviceKey;
    }

    public Integer getEmpId() {
        return empId;
    }

    public void setEmpId(Integer empId) {
        this.empId = empId;
    }

    public String getPersonGuid() {
        return personGuid;
    }

    public void setPersonGuid(String personGuid) {
        this.personGuid = personGuid;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getFaceGuid() {
        return faceGuid;
    }

    public void setFaceGuid(String faceGuid) {
        this.faceGuid = faceGuid;
    }

    public String getFaceUrl() {
        return faceUrl;
    }

    public void setFaceUrl(String faceUrl) {
        this.faceUrl = faceUrl;
    }

    public Integer getValidLevel() {
        return validLevel;
    }

    public void setValidLevel(Integer validLevel) {
        this.validLevel = validLevel;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}