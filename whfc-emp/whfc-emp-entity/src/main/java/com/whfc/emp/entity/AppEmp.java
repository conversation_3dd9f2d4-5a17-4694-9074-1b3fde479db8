package com.whfc.emp.entity;

import lombok.Data;

import java.util.Date;

/**
 * 人员信息-对应机构
 */
@Data
public class AppEmp {
    private Integer id;

    /**
     * 部门ID
     */
    private Integer deptId;

    /**
     * 机构名称
     */
    private String deptName;

    /**
     * 合作单位ID
     */
    private Integer corpId;

    /**
     * 合作单位名称
     */
    private String corpName;

    /**
     * 班组id
     */
    private Integer groupId;

    /**
     * 班组名称
     */
    private String groupName;

    /**
     * 人员姓名
     */
    private String empName;

    /**
     * 姓名拼音
     */
    private String ename;

    /**
     * 电话
     */
    private String phone;

    /**
     * 性别（1-男 2-女）
     */
    private Integer gender;

    /**
     * 生日
     */
    private Date birthday;

    /**
     * 身份证号
     */
    private String idCardNo;

    /**
     * 身份证头像
     */
    private String headImg;

    /**
     * 用户头像
     */
    private String avatar;

    /**
     * 证件类型 (1-居民身份证 2-澳门永久居民证 3-澳门非永久居民证 4-澳门外地雇员身份识别证 5-澳门特别逗留证)
     */
    private Integer cardType;

    /**
     * 工作证编号
     */
    private String workCardNo;

    /**
     * 工作证有效期
     */
    private Date workCardEndDate;

    /**
     * 身份证正面
     */
    private String idCardFront;

    /**
     * 身份证反面
     */
    private String idCardBack;

    /**
     * 发证机关
     */
    private String idCardGrantOrg;

    /**
     * 证件有效期开始时间
     */
    private Date idCardStartDate;

    /**
     * 证件有效期结束时间
     */
    private Date idCardEndDate;

    /**
     * 名族
     */
    private String nation;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 地址
     */
    private String address;

    /**
     * 学历
     */
    private Integer education;

    /**
     * 学位
     */
    private Integer degree;

    /**
     * 人员编号
     */
    private String empCode;

    /**
     * 工人类型ID
     */
    private Integer workRoleId;

    /**
     * 工人类型名称
     */
    private String workRoleName;

    /**
     * 工种ID
     */
    private Integer workTypeId;

    /**
     * 工种名称
     */
    private String workTypeName;

    /**
     * 班组长标记(1-是 0-否)
     */
    private Integer leaderFlag;

    /**
     * 岗位状态(1-在岗 2-退场)
     */
    private Integer postState;

    /**
     * 绑定状态（0-未绑定 1-绑定）
     */
    @Deprecated
    private Integer bindFlag;

    /**
     * 绑定的硬件id
     */
    @Deprecated
    private Integer deviceId;

    /**
     * 绑定的硬件平台
     */
    @Deprecated
    private String platform;

    /**
     * 绑定的硬件sn码
     */
    @Deprecated
    private String sn;

    /**
     * 绑定的硬件颜色
     */
    @Deprecated
    private String color;

    /**
     * 二维码地址
     */
    private String qr;

    /**
     * 入场时间
     */
    private Date enterTime;

    /**
     * 退场时间
     */
    private Date outerTime;

    /**
     * 关键岗位标记 0-否 1-是
     */
    private Integer keyPositionFlag;

    /**
     * 关键岗位信息认证 0-否 1-是
     */
    private Integer keyPositionAuth;

    /**
     * 是否参加入场三级教育（0-未参加  1-已参加）
     */
    private Integer enterTrainFlag;

    /**
     * 人证核验状态
     */
    private Integer verifyState;

    /**
     * 同步标记
     */
    private Integer syncFlag;

    /**
     * 删除标记(0-未删除 1-已删除)
     */
    private Integer delFlag;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;

}