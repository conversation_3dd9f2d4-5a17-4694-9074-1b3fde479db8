package com.whfc.emp.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2020-06-17
 */
@Schema(description = "人员数量统计看板")
@Data
public class AppBoardEmpNumDTO implements Serializable {
    /**
     * 合作单位名称
     */
    @Schema(description =  "合作单位名称")
    private String corpName;
    /**
     * 人员总数
     */
    @Schema(description =  "人员总数")
    private Integer num;
    /**
     * 管理人员总数
     */
    @Deprecated
    @Schema(description =  "管理人员总数")
    private Integer managerNum;
    /**
     * 管理人员在场数
     */
    @Deprecated
    @Schema(description =  "管理人员在场数")
    private Integer managerLocaleNum;
    /**
     * 管理人员离场数
     */
    @Deprecated
    @Schema(description =  "管理人员离场数")
    private Integer managerOffLocaleNum;
    /**
     * 管理人员缺勤数
     */
    @Deprecated
    @Schema(description =  "管理人员缺勤数")
    private Integer managerAbsenceNum;
    /**
     * 管理人员出勤数
     */
    @Deprecated
    @Schema(description =  "管理人员出勤数")
    private Integer managerAttendNum;
    /**
     * 建筑工人总数
     */
    @Deprecated
    @Schema(description =  "建筑工人总数")
    private Integer workerNum;
    /**
     * 建筑工人在场数
     */
    @Deprecated
    @Schema(description =  "建筑工人在场数")
    private Integer workerLocaleNum;
    /**
     * 建筑工人离场数
     */
    @Deprecated
    @Schema(description =  "建筑工人离场数")
    private Integer workerOffLocaleNum;
    /**
     * 建筑工人缺勤数
     */
    @Deprecated
    @Schema(description =  "建筑工人缺勤数")
    private Integer workerAbsenceNum;
    /**
     * 建筑工人出勤数
     */
    @Deprecated
    @Schema(description =  "建筑工人出勤数")
    private Integer workerAttendNum;
    /**
     * 在岗工种统计
     */
    @Schema(description =  "在岗工种统计")
    private List<AppEmpAnaNumDTO> workTypeEmpList;

    /**
     * 工人类型数量统计
     */
    @Schema(description =  "工人类型数量统计")
    private List<AppEmpWorkRoleNumDTO> workRoleNumList;

    /**
     * 不同工种出勤情况统计
     */
    @Schema(description =  "不同工种出勤情况统计")
    private List<AppEmpAnaNumDTO> attendStateEmpList;


}
