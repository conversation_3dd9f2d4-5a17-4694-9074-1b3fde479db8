package com.whfc.emp.enums;

/**
 * @Description 访客类型
 * <AUTHOR>
 * @Date 2021-06-09 14:21
 * @Version 1.0
 */
public enum  AppVisitorsType {

    VISITORS(1,"普通访客"),
    DAYLABORER(2,"临时工");

    private Integer value;

    private String desc;

    AppVisitorsType(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
