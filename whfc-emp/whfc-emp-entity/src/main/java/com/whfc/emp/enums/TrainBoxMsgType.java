package com.whfc.emp.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-08-26 10:54
 */
public enum TrainBoxMsgType {

    TRAIN_EMP_INFO(0, "人员信息"),

    TRAIN_DEPT_INFO(1, "单位信息"),

    TRAIN_RECORD(2, "培训记录-培训信息"),

    TRAIN_RECORD_EMP(3, "培训记录-培训人员信息"),

    TRAIN_RECORD_PAPER(4, "培训记录-试卷信息"),

    TRAIN_RECORD_INFO_EMP(5, "人员培训信息(培训信息 + 培训人员信息)"),

    TRAIN_RESERVED(6, "预留"),

    TRAIN_RECORD_FILE(7, "培训记录附件"),

    TRAIN_VIOLATION_INFO(8, "违章信息");


    private final Integer value;

    private final String desc;

    TrainBoxMsgType(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static TrainBoxMsgType parseValue(Integer value) {
        TrainBoxMsgType[] values = TrainBoxMsgType.values();
        for (TrainBoxMsgType type : values) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }
}
