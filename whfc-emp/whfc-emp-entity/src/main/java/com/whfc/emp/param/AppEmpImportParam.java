package com.whfc.emp.param;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/21 14:31
 */
@Data
public class AppEmpImportParam implements Serializable {

    @ExcelProperty("姓名")
    private String empName;

    @ExcelProperty("性别")
    private String gender;

    @ExcelProperty("民族")
    private String nation;

    @ExcelProperty("地址")
    private String address;

    @ExcelProperty("证件类型")
    private String cardType;

    @ExcelProperty("证件号码")
    private String idCardNo;

    @ExcelProperty("电话号码")
    private String phone;

    @ExcelProperty("劳务分包单位")
    private String corpName;

    @ExcelProperty("班组")
    private String groupName;

    @ExcelProperty("工种")
    private String workTypeName;

    @ExcelProperty("工人类型")
    private String workRoleName;

    @ExcelProperty("进场时间")
    private String enterTime;
}
