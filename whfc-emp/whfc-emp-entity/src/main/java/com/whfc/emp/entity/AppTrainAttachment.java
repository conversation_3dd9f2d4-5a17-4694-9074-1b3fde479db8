package com.whfc.emp.entity;

import java.util.Date;
import lombok.Data;

/**
    * 人员培训附件
    */
@Data
public class AppTrainAttachment {
    /**
    * 主键
    */
    private Integer id;

    /**
    * 培训id
    */
    private Integer trainId;

    /**
    * 附件类型：1-培训照片；2-附件
    */
    private Integer type;

    /**
    * 附件地址
    */
    private String url;

    /**
    * 删除标记（0-未删除  1-已删除）
    */
    private Integer delFlag;

    /**
    * 更新时间
    */
    private Date updateTime;

    /**
    * 创建时间
    */
    private Date createTime;
}