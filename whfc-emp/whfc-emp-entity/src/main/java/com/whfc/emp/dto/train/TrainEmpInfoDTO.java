package com.whfc.emp.dto.train;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 人员信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-08-26 11:08
 */
@Data
public class TrainEmpInfoDTO implements Serializable {

    /**
     * 组织机构ID
     */
    private Integer deptId;

    /**
     * 姓名
     */
    @JSONField(name = "Name")
    private String name;
    /**
     * 性别
     */
    @JSONField(name = "Sex")
    private Integer sex;
    /**
     * 出生日期  yyyy-MM-dd'T'HH:mm:ss
     */
    @JSONField(name = "BirthDay", format = "yyyy-MM-dd'T'HH:mm:ss")
    private Date birthDay;
    /**
     * 地址
     */
    @JSONField(name = "Address")
    private String address;
    /**
     * 民族
     */
    @JSONField(name = "Nation")
    private Integer nation;
    /**
     * 证件编号
     */
    @JSONField(name = "IdentifyID")
    private String identifyId;
    /**
     * 证件类型(0：身份证 1：护照 2：军官证 -1：其他)
     */
    @JSONField(name = "Kind")
    private Integer kind;
    /**
     * 证件照片.
     * 使用以下url+Photo进行拼接显示
     * http://dmt.bosafe.com/STBMS_DHY/Upfile/PersonPhoto/
     */
    @JSONField(name = "photo")
    private String photo;
    /**
     * 发证机关
     */
    @JSONField(name = "Police")
    private String police;
    /**
     * 有效期开始
     * yyyy-MM-dd
     */
    @JSONField(name = "ValidPeriodStart", format = "yyyy-MM-dd")
    private Date validPeriodStart;
    /**
     * 有效期结束
     * yyyy-MM-dd
     */
    @JSONField(name = "ValidPeriodEnd", format = "yyyy-MM-dd")
    private Date validPeriodEnd;
    /**
     * 学历
     */
    @JSONField(name = "Education")
    private String education;
    /**
     * 籍贯
     */
    @JSONField(name = "Native")
    private Integer nativePlace;
    /**
     * 电话
     */
    @JSONField(name = "Telephone")
    private String telephone;
    /**
     * 婚姻状况 1:未婚 2:已婚
     */
    @JSONField(name = "MaritalStatus")
    private Integer maritalStatus;
    /**
     * 血型 -1:其它 1:A型 2:B型 3:AB型 4:O型
     */
    @JSONField(name = "BloodGroup")
    private Integer bloodGroup;
    /**
     * 户口性质 0:农业 1:非农业
     */
    @JSONField(name = "RegisteredType")
    private Integer registeredType;
    /**
     * 年龄
     */
    @JSONField(name = "Age")
    private Integer age;
    /**
     * 登记机关
     */
    @JSONField(name = "RegisterOrgan")
    private String registerOrgan;
    /**
     * 有效时间
     */
    @JSONField(name = "EffectDate")
    private String effectDate;
    /**
     * 过期时间
     */
    @JSONField(name = "ValidDate")
    private String validDate;
    /**
     * 删除用户
     */
    @JSONField(name = "DeleteUser")
    private String deleteUser;
    /**
     * 删除时间
     */
    @JSONField(name = "DeleteDate")
    private String deleteDate;
    /**
     * 删除标记
     */
    @JSONField(name = "DeleteTag")
    private Boolean deleteTag;
    /**
     * 工号
     */
    @JSONField(name = "JobNumber")
    private String jobNumber;
    /**
     * 门禁卡号
     */
    @JSONField(name = "CardContent")
    private String cardContent;
    /**
     * 联系电话
     */
    @JSONField(name = "ContactTel")
    private String contactTel;
    /**
     * 第二联系人
     */
    @JSONField(name = "SecondContacts")
    private String secondContacts;
    /**
     * 第二联系人电话
     */
    @JSONField(name = "SecondContactsTel")
    private String secondContactsTel;
    /**
     * 现住址
     */
    @JSONField(name = "NewAddress")
    private String newAddress;
    /**
     * 健康状况(0:合格 1:不合格)
     */
    @JSONField(name = "HealthCondition")
    private Integer healthCondition;
    /**
     * 健康状况证明(附件名称)
     */
    @JSONField(name = "HealthFile")
    private String healthFile;
    /**
     * 施工区域
     */
    @JSONField(name = "BuildArea")
    private String buildArea;
    /**
     * 培训负责人
     */
    @JSONField(name = "TraPrincipal")
    private String traPrincipal;
    /**
     * 登记时间
     * yyyy-MM-dd'T'HH:mm:ss
     */
    @JSONField(name = "RegisterDate", format = "yyyy-MM-dd'T'HH:mm:ss")
    private Date registerDate;
    /**
     * 工种类型
     */
    @JSONField(name = "CategoryType")
    private String categoryType;
    /**
     * 岗位
     */
    @JSONField(name = "Station")
    private String station;
    /**
     * 工种
     */
    @JSONField(name = "Category")
    private Integer category;
    /**
     * 专业等级
     */
    @JSONField(name = "CategoryLevel")
    private String categoryLevel;
    /**
     * 入场时间
     * yyyy-MM-dd'T'HH:mm:ss
     */
    @JSONField(name = "EntranceDate", format = "yyyy-MM-dd'T'HH:mm:ss")
    private Date entranceDate;
    /**
     * 是否离场(0:否 1:是)
     */
    @JSONField(name = "IsOut")
    private Integer isOut;
    /**
     * 离场时间(可空)
     * yyyy-MM-dd'T'HH:mm:ss
     */
    @JSONField(name = "LeaveDate", format = "yyyy-MM-dd'T'HH:mm:ss")
    private Date leaveDate;
    /**
     * 是否黑名单(0:否 1:是)
     */
    @JSONField(name = "IsBlackList")
    private Integer isBlackList;
    /**
     * 合同编号
     */
    @JSONField(name = "AgreementId")
    private String agreementId;
    /**
     * 是否购买保险 0:否 1:是
     */
    @JSONField(name = "HasInsurance")
    private Integer hasInsurance;
    /**
     * 设备编号
     */
    @JSONField(name = "DeviceNumber")
    private String deviceNumber;
    /**
     * 是否活动 0:历史 1:正在使用
     */
    @JSONField(name = "IsActive")
    private Integer isActive;
    /**
     * 所属单位Id
     */
    @JSONField(name = "DepartId")
    private String departId;
    /**
     * 所属单位名称
     */
    @JSONField(name = "DepartName")
    private String departName;
    /**
     * 所属项目部Id
     */
    @JSONField(name = "OwnerDepartId")
    private String ownerDepartId;
    /**
     * 安全帽编号
     */
    @JSONField(name = "SafetyHatCode")
    private String safetyHatCode;
    /**
     * 上传时间
     * yyyy-MM-dd'T'HH:mm:ss
     */
    @JSONField(name = "UploadTime", format = "yyyy-MM-dd'T'HH:mm:ss")
    private Date uploadTime;
    /**
     * 籍贯名称
     */
    @JSONField(name = "NativeName")
    private String nativeName;
    /**
     * 民族名称
     */
    @JSONField(name = "NationName")
    private String nationName;
    /**
     * 婚姻状态名称
     */
    @JSONField(name = "MaritalStatusName")
    private String maritalStatusName;
    /**
     * 血型名称
     */
    @JSONField(name = "BloodGroupName")
    private String bloodGroupName;
    /**
     * 学历名称
     */
    @JSONField(name = "EducationName")
    private String educationName;
    /**
     * 工种名称
     */
    @JSONField(name = "CategoryName")
    private String categoryName;
    /**
     * 主键Id
     */
    @JSONField(name = "ID")
    private String id;
    /**
     * 创建时间
     * yyyy-MM-dd'T'HH:mm:ss
     */
    @JSONField(name = "CreateDate", format = "yyyy-MM-dd'T'HH:mm:ss")
    private Date createDate;
    /**
     * 创建用户
     */
    @JSONField(name = "CreateUser")
    private String createUser;
    /**
     * 修改时间
     * yyyy-MM-dd'T'HH:mm:ss
     */
    @JSONField(name = "OperDate", format = "yyyy-MM-dd'T'HH:mm:ss")
    private Date operDate;
    /**
     * 修改用户
     */
    @JSONField(name = "OperUser")
    private String operUser;


}
