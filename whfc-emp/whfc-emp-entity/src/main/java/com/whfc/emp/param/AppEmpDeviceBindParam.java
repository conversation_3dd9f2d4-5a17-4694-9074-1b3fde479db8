package com.whfc.emp.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Schema(description = "人员硬件绑定参数")
@Data
public class AppEmpDeviceBindParam implements Serializable {

	@Schema(description =  "设备SN")
	private String sn;


	@Schema(description =  "人员ID")
	@NotNull
	private Integer empId;

	@Schema(description =  "guid")
	private String guid;

}
