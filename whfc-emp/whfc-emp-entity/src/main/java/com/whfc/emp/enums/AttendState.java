package com.whfc.emp.enums;

/**
 * @Description: 网络状态
 * @author: xug<PERSON>
 * @version: 1.0
 * @date: 2019/7/29 11:37
 */
public enum AttendState {

    ABSENCE(0, "缺勤"),

    ATTEND(1, "出勤");

    private Integer value;

    private String desc;

    AttendState(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
