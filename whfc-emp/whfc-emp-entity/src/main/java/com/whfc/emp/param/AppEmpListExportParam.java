package com.whfc.emp.param;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 导出人员列表参数
 * @date 2020-08-26
 */
@Data
public class AppEmpListExportParam implements Serializable {
    private String keyword;
    /**
     * 是否在岗 1-是 2-否
     */
    private Integer postState;
    /**
     * 工人类型id
     */
    private Integer workRoleId;
    /**
     * 岗位/工种id
     */
    private Integer workTypeId;
    /**
     * 合作单位id
     */
    private Integer corpId;
    /**
     * 绑定标记 0-未绑定 1-已绑定
     */
    private Integer bindFlag;
    /**
     * 班组id
     */
    private Integer groupId;
    /**
     * 组织机构id
     */
    @NotNull
    private Integer deptId;
}
