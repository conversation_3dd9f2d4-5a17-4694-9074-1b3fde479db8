package com.whfc.emp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Schema(description = "人员分析数据")
@Data
public class AppEmpAnaNumDTO implements Serializable {
    /**
     * 工人类型
     */
    @Schema(description =  "工人类型")
    private Integer workRoleId;
    /**
     * 数量/出勤人数
     */
    @Schema(description =  "数量/出勤人数")
    private Integer num;
    /**
     * 名称对应的id
     */
    @Schema(description =  "名称对应的id")
    private Integer id;
    /**
     * 名称
     */
    @Schema(description =  "名称")
    private String name;

    /**
     * 在岗人员/总出勤人数
     */
    @Schema(description =  "在岗人员/总出勤人数")
    private Integer empTotal;

    /**
     * 计划出勤人数
     */
    @Schema(description =  "计划出勤人数")
    private Integer planAttendNum;

    /**
     * 管理人员/管理人员出勤人数
     */
    @Deprecated
    @Schema(description =  "管理人员/管理人员出勤人数")
    private Integer managerNum;

    /**
     * 管理人员出勤率
     */
    @Deprecated
    @Schema(description =  "管理人员出勤率")
    private Double managerRate;

    /**
     * 普通工人/工人出勤人数
     */
    @Deprecated
    @Schema(description =  "普通工人/工人出勤人数")
    private Integer workerNum;

    /**
     * 工人出勤率
     */
    @Deprecated
    @Schema(description =  "工人出勤率")
    private Double workerRate;

    @Schema(description =  "工人类型数量列表")
    private List<AppEmpWorkRoleNumDTO> workRoleNumList;

    /**
     * 男性数量
     */
    @Schema(description =  "男性数量")
    private Integer male;

    /**
     * 男性比例
     */
    @Schema(description =  "男性比例")
    private Integer maleRate;

    /**
     * 女性数量
     */
    @Schema(description =  "女性数量")
    private Integer female;

    /**
     * 女性比例
     */
    @Schema(description =  "女性比例")
    private Integer femaleRate;

    /**
     * 出勤率
     */
    @Schema(description =  "出勤率")
    private Double rate;

    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(description =  "日期")
    private Date date;

    /**
     * 出勤人数
     */
    @Schema(description =  "出勤人数")
    private Integer attendNum;

    /**
     * 在场数
     */
    @Schema(description =  "在场数")
    private Integer localeNum;
    /**
     * 离场数
     */
    @Schema(description =  "离场数")
    private Integer offLocaleNum;
    /**
     * 缺勤数
     */
    @Schema(description =  "缺勤数")
    private Integer absenceNum;

}
