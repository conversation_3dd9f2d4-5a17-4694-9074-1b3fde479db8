package com.whfc.emp.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 添加安全帽数据参数（内部接口使用）
 *
 * <AUTHOR>
 * @date 2021-08-25
 */
@Data
public class AddEmpDataParam implements Serializable {
    /**
     * 人员id的集合
     */
    @NotEmpty
    private List<Integer> empIds;

    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date time;
}
