package com.whfc.emp.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 室内定位统计
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/10/13 11:16
 */
@Schema(description = "室内定位统计")
@Data
public class AppIndoorPositionStatDTO implements Serializable {

    /**
     * 区域id
     */
    @Schema(description =  "区域id")
    private Integer areaId;

    /**
     * 区域名称
     */
    @Schema(description =  "区域名称")
    private String areaName;

    /**
     * 地图ID
     */
    @Schema(description =  "地图ID")
    private Integer mapId;

    /**
     * 地图名称
     */
    @Schema(description =  "地图名称")
    private String mapName;

    /**
     * 基站ID
     */
    @Schema(description =  "基站ID")
    private Integer stationId;

    /**
     * 基站名称
     */
    @Schema(description =  "基站名称")
    private String stationName;

    /**
     * 定位人员数量
     */
    @Schema(description =  "定位人员数量")
    private Integer positionNum;

    /**
     * 定位人员数量-工人
     */
    @Schema(description =  "定位人员数量-工人")
    @Deprecated
    private Integer positionWorkerNum;

    /**
     * 定位人员数量-管理
     */
    @Schema(description =  "定位人员数量-管理")
    @Deprecated
    private Integer positionManagerNum;

    /**
     * 人员类型数量统计
     */
    @Schema(description = "人员类型数量统计")
    private List<AppEmpWorkRoleNumDTO> workRoleNumList;


    @Schema(description =  "x坐标")
    private Double x;

    @Schema(description =  "y坐标")
    private Double y;

    @Schema(description =  "经度")
    private Double lng;

    @Schema(description =  "维度")
    private Double lat;

    @Schema(description =  "最大距离")
    private Integer maxDistance;

    @Schema(description =  "最大距离1")
    private Integer maxDistance1;
}
