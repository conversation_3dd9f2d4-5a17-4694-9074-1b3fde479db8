package com.whfc.emp.param;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @ClasssName AppFaceGateVisitorCheckParam
 * @Description 审核
 * <AUTHOR>
 * @Date 2021/2/26 14:36
 * @Version 1.0
 */
@Data
public class AppFaceGateVisitorCheckParam implements Serializable {

    /**
     * 申请id
     */
    @NotNull
    private Integer visitorId;

    /**
     * 审批结果
     */
    @NotNull
    private Integer checkResult;

    /**
     * 访客类型（1-普通访客，2-临时工）
     */
    private Integer visitorsType;

    /**
     * 审批人
     */
    private String checkName;

}
