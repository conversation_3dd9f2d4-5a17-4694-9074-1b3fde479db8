package com.whfc.emp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClasssName AppFaceGateVisitorDTO
 * @Description 闸机人员列表返回类
 * <AUTHOR>
 * @Date 2021/2/26 10:58
 * @Version 1.0
 */
@Data
public class AppFaceGateVisitorDTO implements Serializable {

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 身份证号码
     */
    private String idCardNo;

    /**
     * 访客类型（1-普通访客，2-临时工）
     */
    private Integer visitorsType;

    /**
     * 访客图片
     */
    private String pictureUrl;

    /**
     * 闸机名称
     */
    private String faceGateName;

    /**
     * 申请说明
     */
    private String remark;

    /**
     * 申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 状态0-待审批，1-已通过，2-已拒绝
     */
    private Integer state;

    /**
     * 审批人
     */
    private String checkName;

    /**
     * 审批时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkTime;

    /**
     * 主键
     */
    private Integer visitorId;

}
