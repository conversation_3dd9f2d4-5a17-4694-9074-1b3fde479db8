package com.whfc.emp.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 工资发放详情
 * @author: xugcheng
 * @version: 1.0
 * @date: 2020-11-25 18:53
 */
@Data
public class AppPayrollDetailDTO implements Serializable {
    private Integer deptId;
    private Integer payrollId;
    private String corpName;
    private Integer corpId;
    /**
     * 状态  0-未完成  1-已完成
     */
    private Integer state;
    private Integer year;
    private Integer month;

    /**
     * 结算方式；1-工时，2-工程量
     */
    private Integer clearingForm;

    /**
     * 附件集合
     */
    private List<AppPayrollAttachDTO> attachList;
    /**
     * 人员集合
     * （单独创建接口）
     */
    @Deprecated
    private List<AppPayrollEmpDTO> empList;
}
