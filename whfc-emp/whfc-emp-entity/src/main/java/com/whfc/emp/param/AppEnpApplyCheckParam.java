package com.whfc.emp.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.whfc.common.util.DateUtil;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description 人员审核
 * <AUTHOR>
 * @Date 2021-08-05 9:52
 * @Version 1.0
 */
@Data
public class AppEnpApplyCheckParam implements Serializable {

    /**
     * 人员id
     */
    @NotNull
    private Integer empId;

    /**
     * 班组id
     */
    @NotNull
    private Integer groupId;

    /**
     * 班组名称
     */
    @NotEmpty
    private String groupName;

    /**
     * 工人类型id
     */
    @NotNull
    private Integer workTypeId;

    /**
     * 工人类型名称
     */
    @NotEmpty
    private String workTypeName;

    /**
     * 工种id
     */
    @NotNull
    private Integer workRoleId;

    /**
     * 工种名称
     */
    @NotEmpty
    private String workRoleName;

    /**
     * 入职时间
     */
    @JsonFormat(pattern = DateUtil.DATE_FORMAT)
    private Date enterTime;

    /**
     * 审批结果(1-已通过，2-已拒绝)
     */
    @NotNull
    private Integer checkResult;

    /**
     * 审批人呢
     */
    private String checkName;

}
