package com.whfc.emp.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-09-02 15:55
 */
@Data
public class AppEmpConfessAddParam implements Serializable {

    /**
     * 人员ID
     */
    @NotNull
    private Integer empId;

    /**
     * 工程名称
     */
    @NotEmpty
    private String projectName;

    /**
     * 交底时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date date;

    /**
     * 施工单位ID
     */
    private Integer corpId;

    /**
     * 施工单位
     */
    private String corpName;

    /**
     * 分项工程
     */
    @Deprecated
    private String projectPart;

    /**
     * 负责人ID
     */
    @Deprecated
    private Integer principalId;

    /**
     * 负责人名称
     */
    @Deprecated
    private String principalName;

    /**
     * 交底人ID
     */
    private Integer confidantId;

    /**
     * 交底人名称
     */
    private String confidantName;

    /**
     * 被交底人ID
     */
    private Integer beConfidantId;

    /**
     * 被交底人名称
     */
    private String beConfidantName;

    /**
     * 安全交底图片
     */
    private List<String> imgUrlList;
}
