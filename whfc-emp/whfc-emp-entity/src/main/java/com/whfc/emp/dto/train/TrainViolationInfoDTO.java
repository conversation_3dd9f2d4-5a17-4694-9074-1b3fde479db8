package com.whfc.emp.dto.train;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 违章信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-08-26 14:44
 */
@Data
public class TrainViolationInfoDTO implements Serializable {
    /**
     * 组织机构ID
     */
    private Integer deptId;
    /**
     * 人员Id
     */
    @JSONField(name = "UserID")
    private String userId;
    /**
     * 描述
     */
    @JSONField(name = "Describe")
    private String describe;
    /**
     * 扣分
     */
    @JSONField(name = "Grade")
    private Integer grade;
    /**
     * 检查单位
     */
    @JSONField(name = "Depart")
    private String depart;
    /**
     * 检查人
     */
    @JSONField(name = "CheckPerson")
    private String checkPerson;
    /**
     * 检查时间
     */
    @JSONField(name = "CheckDate")
    private Date checkDate;
    /**
     * 相关图片(多张以逗号分隔)
     */
    @JSONField(name = "Photo")
    private String photo;
    /**
     * 证件编号
     */
    @JSONField(name = "IdentifyID")
    private String identifyId;
    /**
     * 违章类型（0：无意义，1：一般违章，2：严重违章）
     */
    @JSONField(name = "RegulationType")
    private Integer regulationType;
    /**
     * 项目部Id
     */
    @JSONField(name = "OwnerDepartId")
    private String ownerDepartId;
    /**
     * 主键Id
     */
    @JSONField(name = "ID")
    private String id;
    /**
     * 创建时间
     */
    @JSONField(name = "CreateDate")
    private Date createDate;
    /**
     * 创建用户
     */
    @JSONField(name = "CreateUser")
    private String createUser;
    /**
     * 修改时间
     */
    @JSONField(name = "OperDate")
    private Date operDate;
    /**
     * 修改用户
     */
    @JSONField(name = "OperUser")
    private String operUser;
    /**
     * 删除用户
     */
    @JSONField(name = "DeleteUser")
    private String deleteUser;
    /**
     * 删除时间
     */
    @JSONField(name = "DeleteDate")
    private Date deleteDate;
    /**
     * 删除标记(true/false)
     */
    @JSONField(name = "DeleteTag")
    private Boolean deleteTag;

}
