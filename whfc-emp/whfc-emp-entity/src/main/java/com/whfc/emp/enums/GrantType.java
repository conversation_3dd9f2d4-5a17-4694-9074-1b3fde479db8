package com.whfc.emp.enums;

/**
 * @Description: 授权类型
 * @author: xug<PERSON>
 * @version: 1.0
 * @date: 2019/7/29 11:37
 */
public enum GrantType {

    UN_GRANT(0, "取消授权"),

    GRANT(1, "授权");

    private final Integer value;

    private final String desc;

    GrantType(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

}
