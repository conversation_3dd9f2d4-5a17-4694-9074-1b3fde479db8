package com.whfc.emp.entity;

import java.util.Date;
import lombok.Data;

/**
 * 人员信息扫码录入
 */
@Data
public class AppEmpApply {
    private Integer id;

    /**
     * 部门ID
     */
    private Integer deptId;

    /**
     * 班组id
     */
    private Integer groupId;

    /**
     * 班组名称
     */
    private String groupName;

    /**
     * 合作单位ID
     */
    private Integer corpId;

    /**
     * 合作单位名称
     */
    private String corpName;

    /**
     * 人员姓名
     */
    private String empName;

    /**
     * 电话
     */
    private String phone;

    /**
     * 性别（1-男 2-女）
     */
    private Integer gender;

    /**
     * 生日
     */
    private Date birthday;

    /**
     * 身份证号
     */
    private String idCardNo;

    /**
     * 身份证头像
     */
    private String headImg;

    /**
     * 用户头像
     */
    private String avatar;

    /**
     * 证件类型 (1-居民身份证 2-澳门永久居民证 3-澳门非永久居民证 4-澳门外地雇员身份识别证 5-澳门特别逗留证)
     */
    private Integer cardType;

    /**
     * 身份证正面
     */
    private String idCardFront;

    /**
     * 身份证反面
     */
    private String idCardBack;

    /**
     * 发证机关
     */
    private String idCardGrantOrg;

    /**
     * 证件有效期开始时间
     */
    private Date idCardStartDate;

    /**
     * 证件有效期结束时间
     */
    private Date idCardEndDate;

    /**
     * 名族
     */
    private String nation;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 地址
     */
    private String address;

    /**
     * 工人类型ID
     */
    private Integer workRoleId;

    /**
     * 工人类型名称
     */
    private String workRoleName;

    /**
     * 工种ID
     */
    private Integer workTypeId;

    /**
     * 工种名称
     */
    private String workTypeName;

    /**
     * 班组长标记(1-是 0-否)
     */
    private Integer leaderFlag;

    /**
     * 入场时间
     */
    private Date enterTime;

    /**
     * 审批状态(0-待审批，1-已通过，2-已拒绝)
     */
    private Integer checkResult;

    /**
     * 审批人
     */
    private String checkName;

    /**
     * 审批时间
     */
    private Date checkTime;

    /**
     * 删除标记(0-未删除 1-已删除)
     */
    private Integer delFlag;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;
}