package com.whfc.emp.param.indoor;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Schema(description = "室内定位标签编辑")
@Data
public class AppIndoorPositionTagEdit implements Serializable {

    @NotNull
    @Schema(description = "标签ID")
    private Integer tagId;

    @Schema(description = "guid")
    private String guid;

    @Schema(description = "班组ID")
    private Integer groupId;

    @Schema(description = "班组名称")
    private String groupName;

    @Schema(description = "人员ID")
    private Integer empId;

    @Schema(description = "人员姓名")
    private String empName;

    @Schema(description = "设备编码")
    private String deviceCode;
}