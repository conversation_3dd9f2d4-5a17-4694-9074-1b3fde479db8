package com.whfc.emp.entity;

import java.util.Date;

/**
    * 人员-银行账户
    */
public class AppEmpBank {
    private Integer id;

    /**
    * 人员ID
    */
    private Integer empId;

    /**
    * 银行代码 见枚举BankCode
    */
    private String bankCode;

    /**
    * 银行名称
    */
    private String bankName;

    /**
    * 银行卡号
    */
    private String bankNumber;

    /**
    * 启用标记(0-未启用 1-已启用)
    */
    private Integer enableFlag;

    /**
    * 删除标记(0-未删除 1-已删除)
    */
    private Integer delFlag;

    /**
    * 更新时间
    */
    private Date updateTime;

    /**
    * 创建时间
    */
    private Date createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getEmpId() {
        return empId;
    }

    public void setEmpId(Integer empId) {
        this.empId = empId;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankNumber() {
        return bankNumber;
    }

    public void setBankNumber(String bankNumber) {
        this.bankNumber = bankNumber;
    }

    public Integer getEnableFlag() {
        return enableFlag;
    }

    public void setEnableFlag(Integer enableFlag) {
        this.enableFlag = enableFlag;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}