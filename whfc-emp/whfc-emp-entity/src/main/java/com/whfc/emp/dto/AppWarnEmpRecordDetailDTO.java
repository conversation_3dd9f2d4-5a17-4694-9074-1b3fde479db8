package com.whfc.emp.dto;

import com.whfc.common.geometry.Point;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2019-11-28
 */
@Data
public class AppWarnEmpRecordDetailDTO extends AppWarnEmpRecordDTO implements Serializable {

    private Double lat;

    private Double lng;

    private String handleRemark;

    private String handleResult;

    private Integer handleState;

    List<AppWarnTimeDTO> timeList;

    /**
     * 电子围栏类型 1-多边形 2-圆形
     */
    private Integer type;

    /**
     * 多边形坐标
     */
    private List<Point> polygonPointList;

    /**
     * 圆形中心点坐标
     */
    private Point centerPoint;

    /**
     * 圆形半径
     */
    private Double radius;

    /**
     * 规则参数
     */
    private String ruleParam;

    /**
     * 触发参数
     */
    private String triggerKey;


}
