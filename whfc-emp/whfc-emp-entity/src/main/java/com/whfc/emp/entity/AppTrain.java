package com.whfc.emp.entity;

import lombok.Data;

import java.util.Date;

/**
 * @author: hw
 * @date: 2021-10-22 15:24
 * @description: //todo
 */

/**
 * 组织机构-培训记录
 */
@Data
public class AppTrain {
    private Integer id;

    /**
     * 培训记录guid
     */
    private String guid;

    /**
     * 机构ID
     */
    private Integer deptId;

    /**
     * 培训类型
     */
    private Integer trainType;

    /**
     * 培训类型编码
     */
    private String trainTypeCode;

    /**
     * 培训类型名称
     */
    private String trainTypeName;

    /**
     * 培训名称
     */
    private String name;

    /**
     * 开始日期
     */
    private Date date;

    /**
     * 培训时长(小时)
     */
    private Double duration;

    /**
     * 组织者Id
     */
    private Integer organizerId;

    /**
     * 组织者
     */
    private String organizer;

    /**
     * 培训人Id
     */
    private Integer trainerId;

    /**
     * 培训人
     */
    private String trainer;

    /**
     * 培训地点
     */
    private String address;

    /**
     * 内容
     */
    private String content;

    /**
     * 状态（0-未完成  1-完成）
     */
    private Integer state;

    /**
     * 来源（ms-后台  mp-小程序 train_box-培训箱 other-其它）
     */
    private String source;

    /**
     * 同步标记
     */
    private Integer syncFlag;

    /**
     * 删除标记（0-未删除  1-已删除）
     */
    private Integer delFlag;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;
}