package com.whfc.emp.entity;

import java.util.Date;

/**
 * 人员工资图片
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/11 11:34
 */
public class AppPayrollEmpImg {
    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 工资人员关联表ID
     */
    private Integer payrollEmpId;

    /**
     * 图片地址
     */
    private String imgUrl;

    /**
     * 删除标记（0-未删除 1-已删除）
     */
    private Integer delFlag;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getPayrollEmpId() {
        return payrollEmpId;
    }

    public void setPayrollEmpId(Integer payrollEmpId) {
        this.payrollEmpId = payrollEmpId;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}