package com.whfc.emp.entity;

import lombok.Data;

import java.util.Date;

/**
 * 人员工种表
 */
@Data
public class AppEmpWorkType {
    private Integer id;

    /**
     * 项目ID
     */
    private Integer deptId;

    /**
     * 工种名称
     */
    private String name;

    /**
     * 工种编码
     */
    private String workCode;

    /**
     * 是否特殊工种
     */
    private Integer spec;

    /**
     * 是否需要持证上岗 0-不需要 1-需要
     */
    private Integer needCert;

    /**
     * 删除标记,0-未删除 1-已删除
     */
    private Integer delFlag;

    private Date updateTime;

    private Date createTime;

}