package com.whfc.emp.dto;

import com.whfc.emp.constant.IndoorMeasurement;
import lombok.Data;
import org.influxdb.annotation.Column;
import org.influxdb.annotation.Measurement;

import java.io.Serializable;
import java.time.Instant;
import java.util.Date;
import java.util.concurrent.TimeUnit;

@Data
@Measurement(name = IndoorMeasurement.MEASUREMENT, database = IndoorMeasurement.DATABASE, retentionPolicy = IndoorMeasurement.RETENTION_POLICY, timeUnit = TimeUnit.SECONDS)
public class AppIndoorPositionTagLogDTO implements Serializable {

    /**
     * 硬件时间
     */
    private Date time;

    /**
     * 服务器时间
     */
    private Date createTime;

    /**
     * 硬件时间
     */
    @Column(name = "time")
    private Instant deviceTime;

    /**
     * 服务器时间
     */
    @Column(name = "serverTime")
    private Long serverTime;

    @Column(name = "tagId", tag = true)
    private Integer tagId;

    @Column(name = "mapId")
    private Integer mapId;

    @Column(name = "stationId")
    private Integer stationId;

    private String stationName;

    @Column(name = "x")
    private Double x;

    @Column(name = "y")
    private Double y;

    @Column(name = "lng")
    private Double lng;

    @Column(name = "lat")
    private Double lat;

    @Column(name = "distance")
    private Integer distance;

    @Column(name = "batteryPower")
    private Integer batteryPower;
}