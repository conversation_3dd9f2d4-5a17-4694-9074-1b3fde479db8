package com.whfc.emp.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-08-06 17:33
 */
public enum EmpSyncImgType {

    URL(1, "url地址"),

    BASE64(2, "base64"),

    FILE(3, "图片文件");

    private final Integer value;

    private final String desc;

    EmpSyncImgType(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
