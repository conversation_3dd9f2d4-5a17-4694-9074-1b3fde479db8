package com.whfc.emp.entity;

import java.util.Date;

/**
 * 培训试卷
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-08-26 18:41
 */
public class AppTrainPaper {
    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 培训记录ID
     */
    private Integer trainId;

    /**
     * 试卷编号
     */
    private String examNo;

    /**
     * 批次号
     */
    private Integer groupNo;

    /**
     * 课程id
     */
    private String courseId;

    /**
     * 顺序号
     */
    private Integer order;

    /**
     * 题库编码
     */
    private String qsnCode;

    /**
     * 题库文件名称
     */
    private String qsnFileName;

    /**
     * 题库答案
     */
    private String qsnAnswer;

    /**
     * 题库类型（1.文字题、2.多媒体题、3.图片题）
     */
    private Integer qsnCategory;

    /**
     * 试题类型（(1:单选 2:多选 3:判断）
     */
    private Integer qsnKind;

    /**
     * 重要程度 (0:容易 1:一般 2:困难)
     */
    private Integer qsnImportant;

    /**
     * 来源 0：系统；1：用户
     */
    private Integer source;

    /**
     * 版本
     */
    private Integer version;

    /**
     * 上传时间
     */
    private Date uploadTime;

    /**
     * 试题id
     */
    private Integer contentId;

    /**
     * 试题内容
     */
    private String qsnContent;

    /**
     * 说明
     */
    private String desc;

    /**
     * 试题解析
     */
    private String analysis;

    /**
     * 删除标记(0-未删除 1-已删除)
     */
    private Integer delFlag;

    private Date updateTime;

    private Date createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getTrainId() {
        return trainId;
    }

    public void setTrainId(Integer trainId) {
        this.trainId = trainId;
    }

    public String getExamNo() {
        return examNo;
    }

    public void setExamNo(String examNo) {
        this.examNo = examNo;
    }

    public Integer getGroupNo() {
        return groupNo;
    }

    public void setGroupNo(Integer groupNo) {
        this.groupNo = groupNo;
    }

    public String getCourseId() {
        return courseId;
    }

    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public String getQsnCode() {
        return qsnCode;
    }

    public void setQsnCode(String qsnCode) {
        this.qsnCode = qsnCode;
    }

    public String getQsnFileName() {
        return qsnFileName;
    }

    public void setQsnFileName(String qsnFileName) {
        this.qsnFileName = qsnFileName;
    }

    public String getQsnAnswer() {
        return qsnAnswer;
    }

    public void setQsnAnswer(String qsnAnswer) {
        this.qsnAnswer = qsnAnswer;
    }

    public Integer getQsnCategory() {
        return qsnCategory;
    }

    public void setQsnCategory(Integer qsnCategory) {
        this.qsnCategory = qsnCategory;
    }

    public Integer getQsnKind() {
        return qsnKind;
    }

    public void setQsnKind(Integer qsnKind) {
        this.qsnKind = qsnKind;
    }

    public Integer getQsnImportant() {
        return qsnImportant;
    }

    public void setQsnImportant(Integer qsnImportant) {
        this.qsnImportant = qsnImportant;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Date getUploadTime() {
        return uploadTime;
    }

    public void setUploadTime(Date uploadTime) {
        this.uploadTime = uploadTime;
    }

    public Integer getContentId() {
        return contentId;
    }

    public void setContentId(Integer contentId) {
        this.contentId = contentId;
    }

    public String getQsnContent() {
        return qsnContent;
    }

    public void setQsnContent(String qsnContent) {
        this.qsnContent = qsnContent;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getAnalysis() {
        return analysis;
    }

    public void setAnalysis(String analysis) {
        this.analysis = analysis;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}