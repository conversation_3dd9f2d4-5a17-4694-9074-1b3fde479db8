package com.whfc.emp.param;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Description: 添加工资记录
 * @author: hw
 * @version: 1.0
 * @date: 2020-11-25 18:53
 */
@Data
public class AppPayrollAddParam implements Serializable {
    @NotNull
    private Integer groupId;
    @NotEmpty
    private String groupName;
    @NotNull
    private Integer year;
    @NotNull
    private Integer month;
    @NotNull
    private Integer deptId;

    /**
     * 结算方式；1-工时，2-工程量
     */
    private Integer clearingForm;
}
