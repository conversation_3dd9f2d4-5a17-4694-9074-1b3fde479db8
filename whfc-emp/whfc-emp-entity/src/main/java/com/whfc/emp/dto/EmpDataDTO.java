package com.whfc.emp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Schema(description = "人员数据")
@Data
public class EmpDataDTO implements Serializable {

    /**
     * 项目ID
     */
    @Schema(description = "项目ID")
    private Integer deptId;

    /**
     * 项目名称
     */
    @Schema(description = "项目名称")
    private String deptName;

    /**
     * 月份
     */
    @Schema(description = "月份")
    private String month;

    /**
     * 周
     */
    @Schema(description = "周")
    private String week;

    /**
     * 日期
     */
    @Schema(description = "日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date date;

    @Schema(description = "工人类型ID")
    private Integer workRoleId;

    @Schema(description = "工人类型名称")
    private String workRoleName;

    @Schema(description = "工人类型出勤情况")
    private List<AppEmpWorkRoleNumDTO> workRoleNumList;

    /**
     * 人员总数
     */
    @Schema(description = "人员总数")
    private Integer empTotal;

    /**
     * 管理人员数
     */
    @Deprecated
    @Schema(description = "管理人员数")
    private Integer managerNum;

    /**
     * 务工人员数
     */
    @Deprecated
    @Schema(description = "务工人员数")
    private Integer workerNum;

    /**
     * 在线人数
     */
    @Schema(description = "在线人数")
    private Integer onlineNum;

    /**
     * 离线人数
     */
    @Schema(description = "离线人数")
    private Integer offlineNum;

    /**
     * 出勤人数
     */
    @Schema(description = "出勤人数")
    private Integer attendNum;

    /**
     * 缺勤人数
     */
    @Schema(description = "缺勤人数")
    private Integer absentNum;

    /**
     * 场内人数
     */
    @Schema(description = "场内人数")
    private Integer localeNum;

    /**
     * 日均时长
     */
    @Schema(description = "日均时长")
    private Double workTimes;

    /**
     * 计划出勤人数
     */
    @Schema(description = "计划出勤人数")
    private Integer planAttendNum;

    /**
     * 出勤率
     */
    @Schema(description = "出勤率")
    private Double rate;
}
