package com.whfc.emp.entity;

import java.util.Date;
import lombok.Data;

/**
 * @author: hw
 * @date: 2021-10-21 11:23
 * @description: //todo
 */

/**
 * 人员工资发放记录
 */
@Data
public class AppPayroll {
    private Integer id;

    /**
     * 组织机构ID
     */
    private Integer deptId;

    /**
     * 合作单位ID
     */
    private Integer corpId;

    /**
     * 合作单位名称
     */
    private String corpName;

    /**
     * 结算方式；1-工时，2-工程量
     */
    private Integer clearingForm;

    /**
     * 班组id
     */
    private Integer groupId;

    /**
     * 班组名称
     */
    private String groupName;

    private Integer year;

    private Integer month;

    private Date startDate;

    private Date endDate;

    /**
     * 状态（0-未完成  1-已完成）
     */
    private Integer state;

    /**
     * 删除标记(0-未删除 1-已删除)
     */
    private Integer delFlag;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;
}