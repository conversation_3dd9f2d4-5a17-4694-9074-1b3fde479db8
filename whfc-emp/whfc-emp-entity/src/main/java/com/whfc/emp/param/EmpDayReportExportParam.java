package com.whfc.emp.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2020-01-16
 */
@Data
public class EmpDayReportExportParam implements Serializable {

    /**
     * 项目ID
     */
    @NotNull
    private Integer deptId;

    /**
     * 日期
     */
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date date;

    /**
     * 合作单位ID
     */
    private Integer corpId;

    /**
     * 班组ID
     */
    private Integer groupId;

    /**
     * 考勤状态
     */
    private Integer attendState;

    /**
     * 关键词
     */
    private String keyword;
}
