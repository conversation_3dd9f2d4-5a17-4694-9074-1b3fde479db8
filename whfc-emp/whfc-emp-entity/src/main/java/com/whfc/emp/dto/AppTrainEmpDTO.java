package com.whfc.emp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class AppTrainEmpDTO implements Serializable {

    /**
     * 培训人员ID
     */
    private Integer trainEmpId;

    /**
     * 培训id
     */
    private Integer trainId;

    /**
     * 培训名称
     */
    private String name;

    /**
     * 培训日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date date;

    /**
     * 培训类型名称
     */
    private String trainTypeName;

    /**
     * 培训类型
     */
    private Integer trainType;

    /**
     * 组织者
     */
    private String organizer;

    /**
     * 培训地点
     */
    private String address;

    /**
     * 状态  0-未完成  1-已完成
     */
    private Integer state;

    /**
     * 参加培训人数
     */
    private Integer empNum;

    /**
     * 合格率
     */
    private Double qualifiedRate;

    /**
     * 培训人员的集合
     */
    private List<TrainEmpDTO> empList;

    /**
     * 分数
     */
    private Integer score;

    /**
     * 是否合格
     */
    private Integer passFlag;

    /**
     * 人员培训图片
     */
    private List<String> imgUrlList;

}
