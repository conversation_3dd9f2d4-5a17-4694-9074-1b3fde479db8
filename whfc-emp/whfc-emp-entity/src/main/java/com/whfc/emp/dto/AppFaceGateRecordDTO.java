package com.whfc.emp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 人脸识别记录
 *
 * <AUTHOR>
 * @Description:
 * @date 2019年9月3日
 */
@Data
public class AppFaceGateRecordDTO implements Serializable {

    /**
     * 人员ID
     */
    private Integer empId;

    /**
     * 人员姓名
     */
    private String empName;

    /**
     * 闸机序列号
     */
    private String deviceKey;

    /**
     * 闸机ID
     */
    private Integer faceGateId;

    /**
     * 闸机名称
     */
    private String name;

    /**
     * 人员GUID
     */
    private String personGuid;

    /**
     * 方向 1-进门 2-出门
     */
    private Integer direction;

    /**
     * 识别模式，1:刷脸，2:刷卡，3:双重认证， 4:人证比对
     */
    private Integer recMode;

    /**
     * 识别出的人员类型，1：员工，2：访客，3：陌生人
     */
    private Integer type;

    /**
     * 人脸照片
     */
    private String photoUrl;

    /**
     * 识别时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date showTime;

    /**
     * 班组名称
     */
    private String groupName;

    /**
     * 工种
     */
    private String workTypeName;

    /**
     * 温度
     */
    private Double temperature;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 性别
     */
    private Integer gender;

    /**
     * 生日
     */
    private Date birthday;

    /**
     * 证件号
     */
    private String idCardNo;

    /**
     * 进时间
     */
    private Date inTime;

    /**
     * 进图片
     */
    private String inImgUrl;

    /**
     * 出时间
     */
    private Date outTime;

    /**
     * 出图片
     */
    private String outImgUrl;

}
