package com.whfc.emp.dto;

import com.whfc.common.geometry.Point;
import com.whfc.entity.dto.msg.AppMsgToUserDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2019-11-29
 */
@Data
public class AppWarnRuleDTO implements Serializable {

    /**
     * 报警规则id
     */
    private Integer ruleId;

    /**
     * 报警规则名称
     */
    private String name;

    /**
     * 报警规则类型: 101-设备出电子围栏预警 102-油位报警, 103-电量报警 ,104-怠机报警 ,105-停机报警 106-工作报警
     */
    private Integer ruleType;

    /**
     * 报警类型
     */
    private Integer warnType;

    /**
     * 报警参数
     */
    private String ruleParam;

    /**
     * 触发上限
     */
    private String ruleMaxValue;

    /**
     * 触发下限
     */
    private String ruleMinValue;

    /**
     * 电子围栏类型 1-多边形 2-圆形
     */
    private Integer type;

    /**
     * 多边形坐标
     */
    private List<Point> polygonPointList;

    /**
     * 圆形中心点坐标
     */
    private Point centerPoint;

    /**
     * 圆形半径
     */
    private Double radius;

    private String center;

    private String polygon;

    private List<AppWarnTimeDTO> timeList;

    /**
     * 是否启用 0-否 1-是
     */
    private Integer enableFlag;

    /**
     * 报警接收人
     */
    private List<AppMsgToUserDTO> userList;

    /**
     * 报警接收方式
     */
    private List<Integer> msgChannelList;
}
