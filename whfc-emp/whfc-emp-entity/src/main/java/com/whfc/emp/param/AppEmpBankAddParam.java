package com.whfc.emp.param;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 添加银行账户参数
 * @date 2020-08-08
 */
@Data
public class AppEmpBankAddParam implements Serializable {
    /**
     * 人员id
     */
    @NotNull
    private Integer empId;
    /**
     * 银行代码
     */
    @NotEmpty
    private String bankCode;
    /**
     * 银行名称
     */
    @NotEmpty
    private String bankName;
    /**
     * 银行号码
     */
    @NotEmpty
    private String bankNumber;
}
