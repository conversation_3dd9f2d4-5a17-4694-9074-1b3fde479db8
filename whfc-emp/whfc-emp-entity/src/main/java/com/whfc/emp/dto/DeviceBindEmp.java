package com.whfc.emp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2019/8/6 20:20
 */
@Data
public class DeviceBindEmp implements Serializable {

    /**
     * 硬件SN
     */
    @NotEmpty
    protected String sn;

    /**
     * 人员编号
     */
    private String empCode;

    /**
     * 人员姓名
     */
    @NotEmpty
    private String empName;

    /**
     * 人员性别
     */
    @NotNull
    private Integer gender;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 入场时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date enterTime;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 身份证号码
     */
    private String idCardNo;

    /**
     * 身份证正面
     */

    private String idCardFront;

    /**
     * 身份证反面
     */

    private String idCardBack;

    /**
     * 持有方机构ID
     */
    @NotNull
    protected Integer deptId;

    /**
     * 项目ID
     */
    @NotNull
    private Integer projectId;

    /**
     * 组织机构名称
     */
    private String deptName;

    /**
     * 工人类型
     */
    @NotNull
    private Integer workRoleId;

    /**
     * 工人类型名称
     */
    @NotEmpty
    private String workRoleName;

    /**
     * 岗位工种ID
     */
    @NotNull
    private Integer workTypeId;

    /**
     * 岗位工种名称
     */
    @NotEmpty
    private String workTypeName;

    /**
     * 合作单位ID
     */
    private Integer corpId;

    /**
     * 合作单位名称
     */
    private String corpName;

    /**
     * 班组id
     */
    @NotNull
    private Integer groupId;

    /**
     * 班组名称
     */
    @NotEmpty
    private String groupName;

    /**
     * 是否是班组长
     */
    private Integer leaderFlag;
}
