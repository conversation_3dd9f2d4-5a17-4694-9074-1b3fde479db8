package com.whfc.emp.param;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @Description: 工资列表请求
 * @author: hw
 * @version: 1.0
 * @date: 2020-11-25 18:53
 */
@Data
public class AppPayrollListParam implements Serializable {
	@NotNull
	private Integer pageNum;
	@NotNull
	private Integer pageSize;
	/**
	 * 年份
	 */
	private Integer year;
	/**
	 * 月份
	 */
	private Integer month;
	/**
	 * 组织机构id
	 */
	@NotNull
	private Integer deptId;
	/**
	 * 合作单位名称
	 */
	private Integer corpId;
	
	private String keyword;
}
