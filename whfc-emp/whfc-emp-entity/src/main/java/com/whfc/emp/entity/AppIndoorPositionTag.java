package com.whfc.emp.entity;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

@Data
public class AppIndoorPositionTag {
    private Integer id;

    private Integer deptId;

    private String guid;

    @JSONField(serialize = false)
    private String code;

    @JSONField(serialize = false)
    private String name;

    private Integer bindFlag;

    private Integer bindType;

    @JSO<PERSON>ield(serialize = false)
    private Integer groupId;

    @JSONField(serialize = false)
    private String groupName;

    private Integer empId;

    private String empName;

    private String deviceCode;

    private Integer status;

    private Date time;

    @JSONField(serialize = false)
    private Integer mapId;

    @JSONField(serialize = false)
    private Integer stationId;

    private Double x;

    private Double y;

    private Double z;

    private Double lng;

    private Double lat;

    private Integer distance;

    private Integer distance1;

    private Integer batteryPower;

    private Integer state;

    private Integer sos;

    @JSONField(serialize = false)
    private Integer delFlag;

    @J<PERSON>NField(serialize = false)
    private Date updateTime;

    @J<PERSON><PERSON>ield(serialize = false)
    private Date createTime;

    private Double stationX;

    private Double stationY;

    private Double stationZ;

}