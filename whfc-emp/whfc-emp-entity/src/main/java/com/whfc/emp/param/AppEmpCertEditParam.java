package com.whfc.emp.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.whfc.emp.dto.AppAttachDTO;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2020-05-26
 */
@Data
public class AppEmpCertEditParam implements Serializable {

    /**
     * 证书ID
     */
    @NotNull
    private Integer certId;

    /**
     * 证书名称
     */
    @NotEmpty
    @Length(max = 32)
    private String certName;

    /**
     * 证书编码
     */
    @NotEmpty
    @Length(max = 32)
    private String certCode;

    /**
     * 证书类型ID
     */
    private Integer certTypeId;

    /**
     * 证书类型名称
     */
    @Length(max = 32)
    private String certTypeName;

    /**
     * 操作项目ID
     */
    private Integer operationItemId;

    /**
     * 操作项目名称
     */
    @Length(max = 32)
    private String operationItemName;

    /**
     * 证书等级
     */
    private String level;

    /**
     * 签发日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date certStartDate;

    /**
     * 证书有效期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date certExpireDate;

    /**
     * 证书签发单位
     */
    @Length(max = 32)
    private String certGrantOrg;

    /**
     * 证书附件
     */
    @Valid
    private List<AppAttachDTO> fileAttachList;
}
