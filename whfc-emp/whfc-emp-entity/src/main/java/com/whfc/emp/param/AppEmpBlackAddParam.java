package com.whfc.emp.param;

import com.whfc.emp.dto.AppEmpDTO;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2020-06-03
 */
@Data
public class AppEmpBlackAddParam implements Serializable {
    @NotEmpty
    private List<AppEmpDTO> empList;

    @NotEmpty
    @Length(max = 20)
    private String reason;

    private Integer userId;

    private String userName;
}
