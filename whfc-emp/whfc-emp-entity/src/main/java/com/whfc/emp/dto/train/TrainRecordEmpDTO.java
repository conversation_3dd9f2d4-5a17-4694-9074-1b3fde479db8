package com.whfc.emp.dto.train;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 培训人员
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-08-26 12:06
 */
@Data
public class TrainRecordEmpDTO implements Serializable {

    /**
     * 组织机构ID
     */
    private Integer deptId;
    /**
     * 删除用户
     */
    @JSONField(name = "DeleteUser")
    private String deleteUser;
    /**
     * 删除时间
     */
    @JSONField(name = "DeleteDate", format = "yyyy-MM-dd'T'HH:mm:ss")
    private Date deleteDate;
    /**
     * 删除标记(true/false)
     */
    @JSONField(name = "DeleteTag")
    private Boolean deleteTag;
    /**
     * 人员姓名
     */
    @JSONField(name = "EmpName")
    private String empName;
    /**
     * 人员证件编号
     */
    @JSONField(name = "IdentifyId")
    private String identifyId;
    /**
     * 人员照片
     */
    @JSONField(name = "Photo")
    private String photo;
    /**
     * 岗位
     */
    @JSONField(name = "Station")
    private String station;
    /**
     * 工种
     */
    @JSONField(name = "Category")
    private String category;
    /**
     * 工种名称
     */
    @JSONField(name = "CategoryName")
    private String categoryName;
    /**
     * 培训记录Id
     */
    @JSONField(name = "RecordId")
    private String recordId;
    /**
     * 签名照片名称
     */
    @JSONField(name = "SignName")
    private String signName;
    /**
     * 所属单位Id
     */
    @JSONField(name = "DepartId")
    private String departId;
    /**
     * 所属单位名称
     */
    @JSONField(name = "DepartName")
    private String departName;
    /**
     * 签到时间(v5.10以上支持)
     */
    @JSONField(name = "SignInDate", format = "yyyy-MM-dd'T'HH:mm:ss")
    private String signInDate;
    /**
     * 签到方式(0:身份证 1:ICOrID 2:指纹 3:预留 4:刷脸 5:输入身份证 6:其他) (v5.10以上支持)
     */
    @JSONField(name = "SignInType")
    private Integer signInType;
    /**
     * 培训学时
     */
    @JSONField(name = "TrainPeriod")
    private Integer trainPeriod;
    /**
     * 总分
     */
    @JSONField(name = "TotalScore")
    private Integer totalScore;
    /**
     * 及格分
     */
    @JSONField(name = "PassScore")
    private Integer passScore;
    /**
     * 成绩(-1代表该次培训无考试,否则为实际成绩)
     */
    @JSONField(name = "Score")
    private Integer score;
    /**
     * 是否合格(0:否 1:是)
     */
    @JSONField(name = "IsPass")
    private Integer isPass;
    /**
     * 批次号
     */
    @JSONField(name = "GroupNo")
    private Integer groupNo;
    /**
     * 试卷编号
     */
    @JSONField(name = "ExamNo")
    private String examNo;
    /**
     * 考试次数
     */
    @JSONField(name = "ExamCount")
    private Integer examCount;
    /**
     * 设备编号
     */
    @JSONField(name = "DeviceNo")
    private String deviceNo;
    /**
     * 所属项目部Id
     */
    @JSONField(name = "OwnerDepartId")
    private String ownerDepartId;
    /**
     * 上传时间
     */
    @JSONField(name = "UploadTime", format = "yyyy-MM-dd'T'HH:mm:ss")
    private Date uploadTime;
    /**
     * 答案
     */
    @JSONField(name = "Answers")
    private String answers;
    /**
     * 主键Id
     */
    @JSONField(name = "ID")
    private String id;
    /**
     * 创建时间
     */
    @JSONField(name = "CreateDate", format = "yyyy-MM-dd'T'HH:mm:ss")
    private Date createDate;
    /**
     * 创建用户
     */
    @JSONField(name = "CreateUser")
    private String createUser;
    /**
     * 修改时间
     */
    @JSONField(name = "OperDate", format = "yyyy-MM-dd'T'HH:mm:ss")
    private Date operDate;
    /**
     * 修改用户
     */
    @JSONField(name = "OperUser")
    private String operUser;

}
