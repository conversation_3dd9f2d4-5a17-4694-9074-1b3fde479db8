package com.whfc.emp.dto;

import com.whfc.common.geometry.Point;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-07-30 17:51
 */
@Data
public class EmpValidFenceDTO implements Serializable {

    /**
     * 编码
     */
    private String code;
    /**
     * 多边形电子围栏
     */
    private List<Point> polygonPointList;

    /**
     * 多个多边形电子围栏
     */
    private List<List<Point>> multiPolygonPointList;


}
