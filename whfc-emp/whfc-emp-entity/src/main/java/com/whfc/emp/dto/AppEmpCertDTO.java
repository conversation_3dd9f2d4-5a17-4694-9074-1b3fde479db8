package com.whfc.emp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2020-05-26
 */
@Data
public class AppEmpCertDTO implements Serializable {

    /**
     * 项目ID
     */
    private Integer deptId;

    /**
     * 证书id
     */
    private Integer certId;

    /**
     * 证书名称
     */
    private String certName;

    /**
     * 证书编号
     */
    private String certCode;

    /**
     * 证书等级
     */
    private String level;

    /**
     * 签发日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date certStartDate;

    /**
     * 证书有效期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date certExpireDate;

    /**
     * 签发机关
     */
    private String certGrantOrg;

    /**
     * 证书类型ID
     */
    private Integer certTypeId;

    /**
     * 证书类型名称
     */
    private String certTypeName;

    /**
     * 操作项目ID
     */
    private Integer operationItemId;

    /**
     * 操作项目名称
     */
    private String operationItemName;

    /**
     * 附件数
     */
    private Integer fileNo;

    /**
     * 证书附件
     */
    private List<AppAttachDTO> fileAttachList;
}
