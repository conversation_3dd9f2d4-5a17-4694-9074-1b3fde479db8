package com.whfc.emp.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-09-02 09:45
 */
public enum EmpHealthInfoType {

    HEALTH_CODE("healthCode", "健康码"),

    JOURNEY_CARD("journeyCard", "行程卡"),

    TEST_RECORD("testRecord", "核酸记录"),

    VACCINATION("vaccination", "疫苗接种");


    private final String value;

    private final String desc;

    EmpHealthInfoType(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static EmpHealthInfoType parseValue(String value) {
        EmpHealthInfoType[] types = EmpHealthInfoType.values();
        for (EmpHealthInfoType type : types) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }


}
