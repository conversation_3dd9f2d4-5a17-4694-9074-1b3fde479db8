package com.whfc.emp.dto.train;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-08-26 14:41
 */
@Data
public class TrainRecordFileDTO implements Serializable {

    /**
     * 组织机构ID
     */
    private Integer deptId;
    /**
     * 培训记录Id
     */
    @JSONField(name = "RecordId")
    private String recordId;
    /**
     * 文件url路径
     */
    @JSONField(name = "FileContent")
    private String fileContent;
    /**
     * 文件名称
     */
    @JSONField(name = "FileName")
    private String fileName;


}
