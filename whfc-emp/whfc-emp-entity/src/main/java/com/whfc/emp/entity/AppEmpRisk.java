package com.whfc.emp.entity;

import java.util.Date;

/**
 * 人员风险告知书
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/28 18:15
 */
public class AppEmpRisk {
    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 组织机构ID
     */
    private Integer deptId;

    /**
     * 风险告知书名称
     */
    private String name;

    /**
     * 风险告知书地址
     */
    private String fileUrl;

    /**
     * 删除标记（0-未删除 1-已删除）
     */
    private Integer delFlag;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getDeptId() {
        return deptId;
    }

    public void setDeptId(Integer deptId) {
        this.deptId = deptId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}