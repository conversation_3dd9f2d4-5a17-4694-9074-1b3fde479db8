package com.whfc.emp.enums;

/**
 * 签字屏 上传图片类型
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/28 14:22
 */
public enum SignImgType {

    TRAIN_EMP("train_emp", "培训人员"),

    CONFESS("confess", "安全交底"),

    RISK("risk", "风险告知书");

    private final String code;

    private final String desc;

    SignImgType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static SignImgType parseCode(String code) {
        SignImgType[] values = SignImgType.values();
        for (SignImgType type : values) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
