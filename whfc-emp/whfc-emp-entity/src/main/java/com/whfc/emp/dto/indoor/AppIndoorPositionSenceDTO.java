package com.whfc.emp.dto.indoor;

import com.whfc.emp.dto.AppIndoorPositionStationDTO;
import com.whfc.emp.param.indoor.AppIndoorPositionSenceItem;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/3/4 16:46
 */
@Data
public class AppIndoorPositionSenceDTO implements Serializable {

    /**
     * 地图guid
     */
    private String guid;

    /**
     * 比例
     */
    private Double ratio;

    /**
     * 基站列表
     */
    List<AppIndoorPositionStationDTO> list;
}
