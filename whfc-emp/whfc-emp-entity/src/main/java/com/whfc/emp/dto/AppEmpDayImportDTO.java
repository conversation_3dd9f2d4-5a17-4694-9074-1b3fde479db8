package com.whfc.emp.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class AppEmpDayImportDTO implements Serializable {

    @ExcelProperty("emp_code")
    private String empCode;

    @ExcelProperty("emp_name")
    private String empName;

    @ExcelProperty("date")
    private String date;

    @ExcelProperty("work_role_id")
    private Integer workRoleId;

    @ExcelProperty("attend_type")
    private Integer attendType;

    @ExcelProperty("attend_state")
    private Integer attendState;

    @ExcelProperty("start_time")
    private String startTime;

    @ExcelProperty("end_time")
    private String endTime;
}