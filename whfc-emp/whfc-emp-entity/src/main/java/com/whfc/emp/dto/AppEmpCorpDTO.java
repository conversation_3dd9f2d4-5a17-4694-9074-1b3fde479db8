package com.whfc.emp.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 合作单位实体
 * @date 2020-08-03
 */
@Data
public class AppEmpCorpDTO implements Serializable {
    /**
     * 合作单位id
     */
    private Integer corpId;

    /**
     * 合作单位名称
     */
    private String corpName;

    /**
     * 该合作单位下的班组
     */
    private List<AppGroupDTO> groupList;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AppEmpCorpDTO that = (AppEmpCorpDTO) o;
        return Objects.equals(corpId, that.corpId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(corpId);
    }
}
