package com.whfc.emp.entity;

import lombok.Data;

import java.util.Date;

/**
    * 人员报警
    */
@Data
public class AppEmpWarn {

    private Integer id;

    /**
    * 项目ID
    */
    private Integer deptId;

    /**
    * 规则ID
    */
    private Integer ruleId;

    /**
    * 规则类型
    */
    private Integer ruleType;

    /**
     * 触发参数
     */
    private String triggerParam;

    /**
     * 触发报警key
     */
    private String triggerKey;

    /**
    * 触发时间
    */
    private Date triggerTime;

    /**
    * 触发报警的人员id
    */
    private String triggerObjectId;

    /**
    * 经度
    */
    private Double lat;

    /**
    * 纬度
    */
    private Double lng;

    /**
    * 报警记录状态(0-未处理 1-已处理)
    */
    private Integer state;

    /**
    * 处理时间
    */
    private Date handleTime;

    /**
    * 处理结果
    */
    private String handleResult;

    /**
    * 处理结果备注
    */
    private String handleRemark;

    /**
    * 处理人
    */
    private Integer handleUserId;

    /**
    * 处理人姓名
    */
    private String handleUserName;

    /**
    * 处理人手机号
    */
    private String handleUserPhone;

    /**
    * 删除标记(0-未删除,1-已删除)
    */
    private Integer delFlag;

    private Date updateTime;

    private Date createTime;

}