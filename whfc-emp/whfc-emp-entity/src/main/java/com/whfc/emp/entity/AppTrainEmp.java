package com.whfc.emp.entity;

import java.util.Date;

/**
 * 组织机构-培训人员
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-09-02 18:29
 */
public class AppTrainEmp {
    private Integer id;

    /**
     * 培训ID
     */
    private Integer trainId;

    /**
     * 人员ID
     */
    private Integer empId;

    /**
     * 人员姓名(冗余)
     */
    private String empName;

    /**
     * 通过标记(1-通过 0-未通过)
     */
    private Integer passFlag;

    /**
     * 试卷编号
     */
    private String examNo;

    /**
     * 考试次数
     */
    private Integer examCount;

    /**
     * 签名图片
     */
    private String signImgUrl;

    /**
     * 分数
     */
    private Double score;

    /**
     * 培训学时
     */
    private Integer trainPeriod;

    /**
     * 总分
     */
    private Integer totalScore;

    /**
     * 及格分
     */
    private Integer passScore;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getTrainId() {
        return trainId;
    }

    public void setTrainId(Integer trainId) {
        this.trainId = trainId;
    }

    public Integer getEmpId() {
        return empId;
    }

    public void setEmpId(Integer empId) {
        this.empId = empId;
    }

    public String getEmpName() {
        return empName;
    }

    public void setEmpName(String empName) {
        this.empName = empName;
    }

    public Integer getPassFlag() {
        return passFlag;
    }

    public void setPassFlag(Integer passFlag) {
        this.passFlag = passFlag;
    }

    public String getExamNo() {
        return examNo;
    }

    public void setExamNo(String examNo) {
        this.examNo = examNo;
    }

    public Integer getExamCount() {
        return examCount;
    }

    public void setExamCount(Integer examCount) {
        this.examCount = examCount;
    }

    public String getSignImgUrl() {
        return signImgUrl;
    }

    public void setSignImgUrl(String signImgUrl) {
        this.signImgUrl = signImgUrl;
    }

    public Double getScore() {
        return score;
    }

    public void setScore(Double score) {
        this.score = score;
    }

    public Integer getTrainPeriod() {
        return trainPeriod;
    }

    public void setTrainPeriod(Integer trainPeriod) {
        this.trainPeriod = trainPeriod;
    }

    public Integer getTotalScore() {
        return totalScore;
    }

    public void setTotalScore(Integer totalScore) {
        this.totalScore = totalScore;
    }

    public Integer getPassScore() {
        return passScore;
    }

    public void setPassScore(Integer passScore) {
        this.passScore = passScore;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}