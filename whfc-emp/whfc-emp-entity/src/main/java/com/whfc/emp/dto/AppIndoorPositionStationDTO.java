package com.whfc.emp.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Schema(description = "室内定位基站")
@Data
public class AppIndoorPositionStationDTO implements Serializable {

    @Schema(description =  "基站ID")
    private Integer stationId;

    @Schema(description =  "基站名称")
    private String stationName;

    @Schema(description =  "唯一编码")
    private String guid;

    @Schema(description =  "基站名称")
    private String name;

    @Schema(description =  "基站编码")
    private String code;

    @Schema(description =  "地图ID")
    private Integer mapId;

    @Schema(description =  "x轴坐标")
    private Double x;

    @Schema(description =  "y轴坐标")
    private Double y;

    @Schema(description =  "z轴坐标")
    private Double z;

    @Schema(description =  "经度")
    private Double lng;

    @Schema(description =  "纬度")
    private Double lat;

    @Schema(description =  "扩展字段1")
    private String ext1;

    @Schema(description =  "扩展字段2")
    private String ext2;

    @Schema(description =  "扩展字段3")
    private String ext3;

    @Schema(description =  "状态:0-离线 1-在线")
    private Integer status;

    @Schema(description =  "定位标签数量")
    private Integer positionNum;

    @Schema(description =  "是否展开")
    private Boolean expand = false;

    @Schema(description =  "定位标签列表")
    private List<AppIndoorPositionTagDTO> tagList;
}