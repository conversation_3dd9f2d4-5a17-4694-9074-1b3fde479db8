package com.whfc.emp.entity;

import java.util.Date;

/**
    * 电子围栏
    */
public class AppFence {
    private Integer id;

    /**
    * 机构ID
    */
    private Integer deptId;

    /**
    * 报警规则id（废弃）
    */
    private Integer ruleId;

    /**
    * 业务模块（1-项目的电子围栏 2-设备报警区域）（废弃）
    */
    private Integer moduleType;

    /**
    * 电子围栏类型(1-多边形 2-圆形)
    */
    private Integer type;

    /**
    * 考勤区域名称
    */
    private String name;

    /**
    * 多边形坐标
    */
    private Object polygon;

    /**
    * 中心点坐标
    */
    private Object center;

    /**
    * 半径(米)
    */
    private Double radius;

    /**
    * 删除标记(0-未删除 1-已删除)
    */
    private Integer delFlag;

    /**
    * 更新时间
    */
    private Date updateTime;

    /**
    * 创建时间
    */
    private Date createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getDeptId() {
        return deptId;
    }

    public void setDeptId(Integer deptId) {
        this.deptId = deptId;
    }

    public Integer getRuleId() {
        return ruleId;
    }

    public void setRuleId(Integer ruleId) {
        this.ruleId = ruleId;
    }

    public Integer getModuleType() {
        return moduleType;
    }

    public void setModuleType(Integer moduleType) {
        this.moduleType = moduleType;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Object getPolygon() {
        return polygon;
    }

    public void setPolygon(Object polygon) {
        this.polygon = polygon;
    }

    public Object getCenter() {
        return center;
    }

    public void setCenter(Object center) {
        this.center = center;
    }

    public Double getRadius() {
        return radius;
    }

    public void setRadius(Double radius) {
        this.radius = radius;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}