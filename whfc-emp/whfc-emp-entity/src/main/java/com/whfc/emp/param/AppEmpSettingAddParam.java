package com.whfc.emp.param;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.lang.annotation.Native;

/**
 * <AUTHOR>
 * @description
 * @date 2020-06-03
 */
@Data
public class AppEmpSettingAddParam implements Serializable {
    @NotNull
    private Integer deptId;
    private Integer attendType;
    private Integer attendTypeFlag;
    private Integer childLabourAge;
    private Integer childLabourAgeFlag;
    private Integer maleRetireAge;
    private Integer maleRetireAgeFlag;
    private Integer femaleRetireAge;
    private Integer femaleRetireAgeFlag;
    private Integer empBlackListFlag;
    private Integer idCardVerify;
    /**
     * 考勤二维码地址
     */
    private String qr;
    /**
     * 是否启用
     */
    private Integer qrFlag;

    /**
     * 移动端实名制是否启用
     */
    private Integer empQrFlag;

    @Native
    private Integer minutes;
}
