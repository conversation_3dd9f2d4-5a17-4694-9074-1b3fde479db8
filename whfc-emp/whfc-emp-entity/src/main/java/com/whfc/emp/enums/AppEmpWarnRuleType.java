package com.whfc.emp.enums;

/**
 * @Description: 人员报警规则类型
 * @author: sunguodong
 * @version: 1.0
 * @date: 2019/7/22 11:47
 */
public enum AppEmpWarnRuleType {

    DROP(101, "人员跌落报警", "可能从高空跌落", "跌落"),
    FALL(102, "人员摔倒报警", "可能摔倒在地", "摔倒"),
    DOFF(103, "人员脱帽报警", "脱下安全帽", "脱帽"),
    SOS(104, "人员SOS报警", "进行SOS呼叫", "SOS"),
    CRASH(105, "人员碰撞报警", "可能发生碰撞", "碰撞"),
    STILL(106, "人员静止报警", "原地静止不动", "静止"),
    FENCE(201, "人员出区域报警", "离开了范围区域", "出区"),
    BATTERY(202, "低电量报警", "设备电量过低", "低电"),
    HEART_RATE(203, "人员心率异常报警", "心率异常", "心率"),
    TEMP(204, "人员体温异常报警", "体温异常", "体温")



    ;


    private Integer value;

    private String tittle;

    private String detail;

    private String pic;

    AppEmpWarnRuleType(Integer value, String tittle, String detail, String pic) {
        this.value = value;
        this.tittle = tittle;
        this.detail = detail;
        this.pic = pic;
    }

    public Integer value() {
        return value;
    }

    public String tittle() {
        return tittle;
    }

    public String detail() {
        return detail;
    }

    public String pic() {
        return pic;
    }


    public static AppEmpWarnRuleType parseByValue(Integer value) {
        AppEmpWarnRuleType[] types = AppEmpWarnRuleType.values();
        for (AppEmpWarnRuleType type : types) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        return DROP;
    }


    public static AppEmpWarnRuleType parseByTittle(String tittle) {
        AppEmpWarnRuleType[] types = AppEmpWarnRuleType.values();
        for (AppEmpWarnRuleType type : types) {
            if (type.tittle.equals(tittle)) {
                return type;
            }
        }
        return DROP;
    }


}
