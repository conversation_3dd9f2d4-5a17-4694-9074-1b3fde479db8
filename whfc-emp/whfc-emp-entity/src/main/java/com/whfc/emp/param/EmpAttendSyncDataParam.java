package com.whfc.emp.param;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-08-05 11:51
 */
@Data
public class EmpAttendSyncDataParam implements Serializable {

    /**
     * 组织机构ID
     */
    private Integer deptId;

    /**
     * 闸机平台
     */
    private String platform;

    /**
     * 闸机序列号
     */
    private String deviceKey;

    /**
     * 闸机名称
     */
    private String deviceName;

    /**
     * 人员唯一编号
     */
    private String personGuid;

    /**
     * 人员身份证号（兼容玉川闸机 玉川闸机没有 empCode）
     */
    private String idCardNo;

    /**
     * 人员ID
     */
    private Integer empId;

    /**
     * 人员名称
     */
    private String empName;

    /**
     * 识别时间
     */
    private Date showTime;

    /**
     * 闸机ID
     */
    private Integer faceGateId;

    /**
     * 闸机方向
     */
    private Integer direction;

    /**
     * 识别模式，
     *
     * @see com.whfc.common.enums.RecMode
     */
    private Integer recMode;

    /**
     * 温度
     */
    private Double temperature;

    /********图片******************/

    /**
     * 图片类型
     * 1-url  2-base64
     *
     * @see com.whfc.emp.enums.EmpSyncImgType
     */
    private Integer imgType;

    /**
     * 识别照片
     */
    private String picture;

    /**
     * 经度
     */
    private Double lng;

    /**
     * 纬度
     */
    private Double lat;

    /**
     * 工区ID
     */
    private Integer areaId;

    /**
     * 工区名称
     */
    private String areaName;
}
