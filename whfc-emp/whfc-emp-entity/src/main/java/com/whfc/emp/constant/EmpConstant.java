package com.whfc.emp.constant;

import com.whfc.emp.dto.AppEmpSettingDTO;

/**
 * 人员常量
 */
public class EmpConstant {

    /**
     * 考勤方式
     */
    public static final String EMP_ATTEND_CODE = "attend_type";

    /**
     * 人证对比-配置code
     */
    public static final String EMP_IDCARD_VERIFY_CODE = "id_card_verify";

    /**
     * 人证对比-开启值
     */
    public static final String EMP_IDCARD_VERIFY_YES = "1";

    /**
     * 童工年龄设置
     */
    public static final String CHILD_LABOUR_AGE = "child_labour_age";

    /**
     * 男工退休年龄设置
     */
    public static final String MALE_RETIRE_AGE = "male_retire_age";

    /**
     * 女工退休年龄设置
     */
    public static final String FEMALE_RETIRE_AGE = "female_retire_age";

    /**
     * 人员黑名单
     */
    public static final String EMP_BLACK_LIST = "emp_black_list";

    /**
     * 考勤二维码
     */
    public static final String ATTEND_QR = "attend_qr";

    /**
     * 移动端实名制二维码
     */
    public static final String EMP_QR = "emp_qr";

    /**
     * 地图设备离线时间
     */
    public static final String TIME_OUT = "time_out";

    /**
     * 默认时间120分钟
     */
    public static final Integer DEFAULT_TIME_OUT = 120;

    /**
     * 人员电子围栏设置
     */
    public static final String FENCE = "fence";

    /**
     * 休息区域电子围栏设置
     */
    public static final String REST_AREA_FENCE = "rest_area_fence";

    /**
     * 人员首页班组监控
     */
    public static final String EMP_INDEX_FVS = "emp_index_fvs";

    /**
     * 插入人员配置数据
     *
     * @param code
     * @param empSettingDTO
     * @param value
     * @param enableFlag
     */
    public static void setEmpSettingDTOValue(String code, AppEmpSettingDTO empSettingDTO, String value, Integer enableFlag) {
        switch (code) {
            case EmpConstant.CHILD_LABOUR_AGE:
                empSettingDTO.setChildLabourAge(Integer.valueOf(value));
                empSettingDTO.setChildLabourAgeFlag(enableFlag);
                break;
            case EmpConstant.MALE_RETIRE_AGE:
                empSettingDTO.setMaleRetireAge(Integer.valueOf(value));
                empSettingDTO.setMaleRetireAgeFlag(enableFlag);
                break;
            case EmpConstant.FEMALE_RETIRE_AGE:
                empSettingDTO.setFemaleRetireAge(Integer.valueOf(value));
                empSettingDTO.setFemaleRetireAgeFlag(enableFlag);
                break;
            case EmpConstant.EMP_BLACK_LIST:
                empSettingDTO.setEmpBlackListFlag(enableFlag);
                break;
            case EmpConstant.EMP_ATTEND_CODE:
                empSettingDTO.setAttendType(Integer.valueOf(value));
                empSettingDTO.setAttendTypeFlag(enableFlag);
                break;
            case EmpConstant.ATTEND_QR:
                empSettingDTO.setQr(value);
                empSettingDTO.setQrFlag(enableFlag);
                break;
            case EmpConstant.TIME_OUT:
                empSettingDTO.setMinutes(Integer.valueOf(value));
                break;
            case EmpConstant.EMP_QR:
                empSettingDTO.setEmpQrFlag(enableFlag);
            case EMP_IDCARD_VERIFY_CODE:
                empSettingDTO.setIdCardVerify(value);
            default:
                break;
        }
    }
}
