package com.whfc.emp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class AppEmpGpsDTO implements Serializable {
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date time;
	/**
	 * 经度
	 */
	private Double lng;
	/**
	 * 纬度
	 */
	private Double lat;

	private String location;
	/**
	 * 是否在电子围栏内  0-否  1-是
	 */
	private Integer localeState;
}
