package com.whfc.emp.param;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description 批量处理报警记录参数
 * @date 2021-04-12
 */
@Data
public class WarnBatchHandleParam implements Serializable {
    /**
     * 报警记录ID
     */
    @NotEmpty
    private List<Integer> warnIdList;

    /**
     * 处理结果
     */
    @NotEmpty
    @Length(max = 100)
    private String handleResult;

    /**
     * 备注
     */
    @Length(max = 100)
    private String handleRemark;

    /**
     * 处理人
     */
    private Integer userId;

    /**
     * 处理人姓名
     */
    private String userName;

    /**
     * 处理人手机号
     */
    private String phone;
}
