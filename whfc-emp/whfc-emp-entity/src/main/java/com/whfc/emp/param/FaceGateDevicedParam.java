package com.whfc.emp.param;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class FaceGateDevicedParam implements Serializable {


    @NotNull
    private Integer Id;

    @NotNull
    private Integer deptId;

    @NotEmpty
    private String deviceKey;

    @NotEmpty
    private String name;

    private Double lat;

    private Double lng;

    private String address;

    @NotNull
    private Integer direction;

    private String tag;

    private String sceneGuid;

    private String userGuid;

    private Integer state;

    private Integer status;

    private String versionNo;

    private String systemVersionNo;

    private String appId;

    private Integer regNum;

    private Integer needUpgradeApp;

    private Integer needUpgradeSystem;

    private Integer needUpgrade;

    private Integer expired;


}
