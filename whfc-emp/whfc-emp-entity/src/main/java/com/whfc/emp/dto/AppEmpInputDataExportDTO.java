package com.whfc.emp.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/6 10:24
 */
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@ContentRowHeight(18)
@Data
public class AppEmpInputDataExportDTO implements Serializable {

    /**
     * 人员姓名
     */
    @ColumnWidth(15)
    @ExcelProperty(value = "人员姓名")
    private String empName;
    /**
     * 电话
     */
    @ColumnWidth(15)
    @ExcelProperty(value = "人员电话")
    private String phone;
    /**
     * 合作单位名称
     */
    @ColumnWidth(35)
    @ExcelProperty(value = "合作单位")
    private String corpName;
    /**
     * 班组名称
     */
    @ColumnWidth(30)
    @ExcelProperty(value = "班组")
    private String groupName;
    /**
     * 工种名称
     */
    @ColumnWidth(15)
    @ExcelProperty(value = "工种")
    private String workTypeName;
    /**
     * 来源 3-后台 4-扫描
     */
    @ExcelProperty(value = "来源")
    private String type;

    @ColumnWidth(25)
    @ExcelProperty(value = "设备名称")
    private String deviceName;
    /**
     * 方向 0-出 1-进
     */
    @ExcelProperty(value = "方向")
    private String direction;
    /**
     * 时间
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "打卡时间")
    private String time;


}
