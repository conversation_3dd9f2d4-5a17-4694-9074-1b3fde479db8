package com.whfc.emp.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class AppFaceGateEmpDTO implements Serializable {
    /**
     * 人员id
     */
    private Integer empId;
    /**
     * 人员姓名
     */
    private String empName;
    /**
     * 人员手机号
     */
    private String phone;
    /**
     * 人员识别图片
     */
    private String avatar;
    /**
     * 班组名称
     */
    private String groupName;
    /**
     * 工种名称
     */
    private String workTypeName;

    private Integer keyPositionFlag;

    private Integer keyPositionAuth;
    /**
     * 闸机ID
     */
    private Integer faceGateId;
    /**
     * 闸机序列号
     */
    private String deviceKey;
    /**
     * 人员guid
     */
    private String personGuid;
    /**
     * 定时任务状态
     */
    private Integer taskType;
    /**
     * 人员授权id
     */
    private Integer personId;

    /**
     * 刷脸权限
     */
    private String stateMessage;
}
