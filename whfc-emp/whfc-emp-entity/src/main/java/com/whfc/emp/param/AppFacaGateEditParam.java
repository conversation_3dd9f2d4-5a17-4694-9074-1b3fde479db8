package com.whfc.emp.param;

import com.whfc.common.geometry.Point;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class AppFacaGateEditParam implements Serializable {

    /**
     * 闸机id
     */
    @NotNull
    private Integer faceGateId;

    /**
     * 组织机构id
     */
    private Integer deptId;

    /**
     * 闸机名称
     */
    @Length(max = 32)
    private String name;

    /**
     * 方向
     */
    @NotNull
    private Integer direction;

    /**
     * 型号
     */
    @Length(max = 32)
    private String model;

    /**
     * 定位
     */
    private Point point;

    private Integer areaId;

    private String areaName;
}
