package com.whfc.emp.param;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@ToString(exclude = {"data"})
public class FaceGateRecordParam implements Serializable {

    private Integer id;

    /**
     * 项目id
     */
    private Integer deptId;

    /**
     * 机器信息在数据库中主键Id
     */
    private Integer faceGateId;

    /**
     * 闸机唯一标识
     */
    private String deviceKey;

    /**
     * 闸机名称
     */
    private String deviceName;

    /**
     * 闸机方向  1-进门  2-出门
     */
    private Integer direction;

    /**
     * 人员唯一标识
     */
    private String personGuid;

    /**
     * 人员id
     */
    private Integer empId;

    /**
     * 人员名称
     */
    private String empName;

    /**
     * 人员证件号
     */
    private String idCardNo;

    /**
     * 过闸温度
     */
    private Double temperature;

    /**
     * 过闸抓拍图片
     */
    @JsonIgnore
    private String picture;

    /**
     * 过闸抓拍图片
     */
    private String photoUrl;

    /**
     * 过闸时间
     */
    private Date showTime;

    /**
     * 过闸模式
     */
    private Integer recMode;

    private Integer type;

    private String data;

    private String idCardInfo;

    /**
     * 经度
     */
    private Double lng;

    /**
     * 纬度
     */
    private Double lat;


}
