package com.whfc.emp.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 班组信息实体
 * @date 2020-08-01
 */
@Data
public class AppGroupDTO implements Serializable {

    /**
     * 班组id
     */
    private Integer groupId;

    /**
     * 班组名称
     */
    private String groupName;

    /**
     * 合作单位id
     */
    private Integer corpId;

    /**
     * 合作单位名称
     */
    private String corpName;

    /**
     * 合作单位编码
     */
    private String corpCode;

    /**
     * 负责人名称
     */
    private String responsiblePersonName;

    /**
     * 负责人电话
     */
    private String responsiblePersonPhone;

    /**
     * 负责人证件类型 1-居民身份证 2-澳门永久居民证 3-澳门非永久居民证 4-澳门外地雇员身份识别证 5-澳门特别逗留证
     */
    private Integer responsibleIdcardType;

    /**
     * 负责人身份证号
     */
    private String responsibleIdcardNumber;

    /**
     * 班组编号
     */
    private Integer teamSysNo;

    /**
     * 班组编码
     */
    private String groupCode;

    /**
     * 监控设备ID
     */
    private Integer fvsDeviceId;

    /**
     * 监控设备名称
     */
    private String fvsDeviceName;
}
