package com.whfc.emp.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 人员风险告知书
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/28 18:15
 */
@Data
public class AppEmpRiskDTO implements Serializable {

    /**
     * 主键ID
     */
    private Integer riskId;

    /**
     * 组织机构ID
     */
    private Integer deptId;

    /**
     * 风险告知书名称
     */
    private String name;

    /**
     * 风险告知书地址
     */
    private String fileUrl;

    /**
     * 人员ID
     */
    private Integer empId;

    /**
     * 签名时间
     */
    private Date signTime;

    /**
     * 签名图片
     */
    private String signImgUrl;
}