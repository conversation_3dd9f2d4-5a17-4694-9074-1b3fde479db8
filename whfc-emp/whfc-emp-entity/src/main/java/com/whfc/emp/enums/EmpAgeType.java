package com.whfc.emp.enums;

/**
 * @Description: 年龄分类枚举
 * @author: xugcheng
 * @version: 1.0
 * @date: 2022-01-20 16:25
 */
public enum EmpAgeType {

    type1(1, "18岁以下", 0, 18),
    type2(2, "19-30岁", 19, 30),
    type3(3, "31-40岁", 31, 40),
    type4(4, "41-50岁", 41, 50),
    type5(5, "51-60岁", 51, 60),
    type6(6, "60岁以上", 61, 150),
    type7(7, "其他", Integer.MIN_VALUE, -1);

    /**
     * 分类
     */
    private Integer type;

    /**
     * 描述
     */
    private String desc;

    /**
     * 最小
     */
    private Integer min;

    /**
     * 最大
     */
    private Integer max;

    EmpAgeType(Integer type, String desc, Integer min, Integer max) {
        this.type = type;
        this.desc = desc;
        this.min = min;
        this.max = max;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public Integer getMin() {
        return min;
    }

    public Integer getMax() {
        return max;
    }

    /**
     * 根据年龄分类
     *
     * @param age
     * @return
     */
    public static EmpAgeType parseByAge(Integer age) {
        EmpAgeType[] ageTypes = EmpAgeType.values();
        for (EmpAgeType ageType : ageTypes) {
            if (age != null && ageType.getMin() != null && ageType.getMax() != null
                    && ageType.getMin() <= age && age <= ageType.getMax()) {
                return ageType;
            }
        }
        return type7;
    }

    public static void main(String[] args) {
        for (int i = 0; i <= 200; i++) {
            System.out.println(i + " --- " + parseByAge(i));
        }
    }
}
