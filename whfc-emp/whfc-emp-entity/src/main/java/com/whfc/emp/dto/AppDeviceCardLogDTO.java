package com.whfc.emp.dto;

import com.whfc.emp.constant.EmpMeasurement;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.influxdb.annotation.Column;
import org.influxdb.annotation.Measurement;

import java.io.Serializable;
import java.time.Instant;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * @Description: 智能设备数据
 * @author: xugcheng
 * @version: 1.0
 * @date: 2021-01-28 10:14
 */
@Schema(description = "智能设备数据")
@Data
@Measurement(name = EmpMeasurement.MEASUREMENT, database = EmpMeasurement.DATABASE, retentionPolicy = EmpMeasurement.RETENTION_POLICY, timeUnit = TimeUnit.SECONDS)
public class AppDeviceCardLogDTO implements Serializable {

    /**
     * 硬件时间
     */
    @Schema(description =  "硬件时间")
    private Date time;

    /**
     * 服务器时间
     */
    @Schema(description =  "服务器时间")
    private Date createTime;

    /**
     * 硬件时间
     */
    @Schema(description =  "硬件时间")
    @Column(name = "time")
    private Instant deviceTime;

    /**
     * 服务器时间
     */
    @Schema(description =  "服务器时间")
    @Column(name = "serverTime")
    private Long serverTime;

    /**
     * 人员ID
     */
    @Schema(description =  "人员ID")
    @Column(name = "empId", tag = true)
    private String empId;

    /**
     * 硬件ID(安全帽)
     */
    @Schema(description =  "硬件ID(安全帽)")
    @Column(name = "deviceId", tag = true)
    private String deviceId;

    /**
     * 人员硬件ID
     */
    @Schema(description =  "人员硬件ID")
    @Column(name = "empDeviceId", tag = true)
    private String empDeviceId;

    /**
     * 经度
     */
    @Schema(description =  "经度")
    @Column(name = "lng")
    private Double lng;

    /**
     * 纬度
     */
    @Schema(description =  "纬度")
    @Column(name = "lat")
    private Double lat;

    /**
     * 经度
     */
    @Schema(description =  "经度")
    @Column(name = "lngWgs84")
    private Double lngWgs84;

    /**
     * 纬度
     */
    @Schema(description =  "纬度")
    @Column(name = "latWgs84")
    private Double latWgs84;

    /**
     * 地址
     */
    @Schema(description =  "地址")
    @Column(name = "location")
    private String location;

    /**
     * 速度
     */
    @Schema(description =  "速度")
    @Column(name = "speed")
    private Double speed;

    /**
     * 角度
     */
    @Schema(description =  "角度")
    @Column(name = "rotation")
    private Double rotation;

    /**
     * 版本号
     */
    @Schema(description =  "版本号")
    @Column(name = "version")
    private String version;

    /**
     * 卫星数量
     */
    @Schema(description =  "卫星数量")
    @Column(name = "geoNum")
    private Integer geoNum;

    /**
     * 水平因子
     */
    @Schema(description =  "水平因子")
    @Column(name = "levelFactor")
    private Double levelFactor;

    /**
     * 信号强度
     */
    @Schema(description =  "信号强度")
    @Column(name = "rssi")
    private Integer rssi;

    /**
     * 报警值
     */
    @Schema(description =  "报警值")
    @Column(name = "alarmValue")
    private Integer alarmValue;

    /**
     * 报到提醒(0-无效 1-有效)
     */
    @Schema(description =  "報到提醒(0-無效 1-有效)")
    @Column(name = "alarmReport")
    private Integer alarmReport;

    /**
     * sos提醒(0-无效 1-有效)
     */
    @Schema(description =  "sos提醒(0-無效 1-有效)")
    @Column(name = "alarmSos")
    private Integer alarmSos;

    /**
     * 跌落报警(0-无效 1-有效)
     */
    @Schema(description =  "跌落报警(0-無效 1-有效)")
    @Column(name = "alarmDrop")
    private Integer alarmDrop;

    /**
     * 脱帽报警(0-无效 1-有效)
     */
    @Schema(description =  "脱帽报警(0-無效 1-有效)")
    @Column(name = "alarmDoff")
    private Integer alarmDoff;

    /**
     * 长时间静止报警(0-无效 1-有效)
     */
    @Schema(description =  "长时间静止报警(0-无效 1-有效)")
    @Column(name = "alarmStill")
    private Integer alarmStill;

    /**
     * 碰撞报警 0-无效 1-有效
     */
    @Schema(description =  "碰撞报警 0-无效 1-有效")
    @Column(name = "alarmCrash")
    private Integer alarmCrash;

    /**
     * 状态值
     */
    @Schema(description =  "状态值")
    @Column(name = "cardStateValue")
    private Integer cardStateValue;

    /**
     * 定位类型(0:基站 1-卫星)
     */
    @Schema(description =  "定位类型(0:基站 1-卫星)")
    @Column(name = "posType")
    private Integer posType;

    /**
     * 定位状态(0-无效,1-有效)
     */
    @Schema(description =  "定位状态(0-无效,1-有效)")
    @Column(name = "posState")
    private Integer posState;

    /**
     * 定位方式(0-GPS 1-北斗 2-GPS北斗双模)
     */
    @Schema(description =  "定位方式(0-GPS 1-北斗 2-GPS北斗双模)")
    @Column(name = "posMode")
    private Integer posMode;

    /**
     * 电压
     */
    @Schema(description =  "电压")
    @Column(name = "batterVolt")
    private Double batterVolt;

    /**
     * 电量
     */
    @Schema(description =  "电量")
    @Column(name = "batteryPower")
    private Integer batteryPower;

    /**
     * 加速度x
     */
    @Schema(description =  "加速度x")
    @Column(name = "accelerationX")
    private Double accelerationX;

    /**
     * 加速度y
     */
    @Schema(description =  "加速度y")
    @Column(name = "accelerationY")
    private Double accelerationY;

    /**
     * 加速度z
     */
    @Schema(description =  "加速度z")
    @Column(name = "accelerationZ")
    private Double accelerationZ;

    /**
     * 体温
     */
    @Schema(description =  "体温")
    @Column(name = "bodyTemp")
    private Double bodyTemp;

    /**
     * 心率
     */
    @Schema(description =  "心率")
    @Column(name = "heartRate")
    private Integer heartRate;

    /**
     * 血压
     */
    @Schema(description =  "血压")
    @Column(name = "bloodPressure")
    private String bloodPressure;

    /**
     * 低压
     */
    @Schema(description =  "低压")
    @Column(name = "diastolicPressure")
    private String diastolicPressure;

    /**
     * 高压
     */
    @Schema(description =  "高压")
    @Column(name = "systolicPressure")
    private String systolicPressure;

    /**
     * 血氧
     */
    @Schema(description =  "血氧")
    @Column(name = "bloodOxygen")
    private Integer bloodOxygen;

    /**
     * 血糖
     */
    @Schema(description =  "血糖")
    @Column(name = "bloodSugar")
    private Double bloodSugar;

    @Schema(description =  "数量")
    @Column(name = "count")
    private Integer count;
}
