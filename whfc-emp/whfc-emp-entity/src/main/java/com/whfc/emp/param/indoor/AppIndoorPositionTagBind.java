package com.whfc.emp.param.indoor;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Schema(description = "标签绑定参数")
@Data
public class AppIndoorPositionTagBind implements Serializable {

    @NotNull
    @Schema(description = "项目ID")
    private Integer deptId;

    @Schema(description = "标签code")
    @NotEmpty
    private String guid;

    @Schema(description = "绑定人员ID")
    private Integer empId;

    @Schema(description = "绑定人员姓名")
    private String empName;

    @Schema(description = "设备唯一编码")
    private String deviceCode;

    @Schema(description = "绑定类型 1-人员 2-设备")
    private Integer bindType;

}