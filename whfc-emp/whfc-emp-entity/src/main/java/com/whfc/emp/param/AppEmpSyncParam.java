package com.whfc.emp.param;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description 同步参数
 * @date 2020-08-05
 */
@Data
public class AppEmpSyncParam implements Serializable {
    @NotEmpty
    private List<AppObjectSyncParam> objectList;

    @NotNull
    private Integer projectId;

    private Integer groupId;

    private Integer year;

    private Integer month;

    private Integer platform;

}
