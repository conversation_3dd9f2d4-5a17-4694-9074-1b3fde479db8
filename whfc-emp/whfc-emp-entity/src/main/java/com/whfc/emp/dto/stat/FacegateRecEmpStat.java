package com.whfc.emp.dto.stat;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 闸机识别记录-按人员和时间统计
 * @author: xugcheng
 * @version: 1.0
 * @date: 2021-08-06 11:36
 */
@Data
public class FacegateRecEmpStat implements Serializable {

    /**
     * 人员ID
     */
    private Integer empId;

    /**
     * 识别时间
     */
    private Date showTime;

    /**
     * 统计次数
     */
    private Integer cnt;
}
