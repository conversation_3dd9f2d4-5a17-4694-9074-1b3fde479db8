package com.whfc.emp.param;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @DESCRIPTION
 * @AUTHOR GuoDong_Sun
 * @DATE 2020/3/17
 */
@Data
public class AppFenceEmpAddParam implements Serializable {
    @NotNull
    private Integer fenceId;
    @NotEmpty
    private List<Integer> empList;
}
