package com.whfc.emp.param;

import com.whfc.common.geometry.Point;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class AppFaceGateAddParam implements Serializable {

    @NotNull
    private Integer deptId;

    @NotEmpty
    @Length(max = 32)
    private String platform;

    @NotEmpty
    @Length(max = 64)
    private String deviceKey;

    /**
     * 闸机名称
     */
    @NotEmpty
    @Length(max = 32)
    private String name;

    /**
     * 方向
     */
    @NotNull
    private Integer direction;

    /**
     * 型号
     */
    @Length(max = 32)
    private String model;

    /**
     * 定位
     */
    private Point point;
}
