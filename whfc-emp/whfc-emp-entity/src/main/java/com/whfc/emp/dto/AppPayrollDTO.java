package com.whfc.emp.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class AppPayrollDTO implements Serializable {


    /**
     * 工资发放记录id
     */
    private Integer payrollId;

    /**
     * 项目ID
     */
    private Integer deptId;

    /**
     * 组织机构名称
     */
    private String deptName;

    /**
     * 班组名称
     */
    private String groupName;

    /**
     * 合作单位
     */
    private String corpName;

    /**
     * 结算方式；1-工时，2-工程量
     */
    private Integer clearingForm;

    /**
     * 年份
     */
    private Integer year;

    /**
     * 月份
     */
    private Integer month;

    /**
     * 开始日期
     */
    private Date startDate;

    /**
     * 结束日期
     */
    private Date endDate;

    /**
     * 附件数
     */
    private Integer attachCnt;

    /**
     * 状态 0-未完成  1-已完成
     */
    private Integer state;
}
