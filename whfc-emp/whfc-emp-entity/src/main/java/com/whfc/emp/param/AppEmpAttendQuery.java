package com.whfc.emp.param;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 打卡记录查询
 *
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/7/12 14:45
 */
@Data
public class AppEmpAttendQuery implements Serializable {

    /**
     * 项目ID
     */
    private Integer deptId;

    /**
     * 参建单位ID
     */
    private Integer corpId;

    /**
     * 班组ID
     */
    private Integer groupId;

    /**
     * 工种ID
     */
    private Integer workTypeId;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 关键词
     */
    private String keyword;

    /**
     * 考勤类型
     */
    private List<Integer> attendTypeList;
}
