package com.whfc.emp.entity;

import java.util.Date;

/**
 * 人员防疫信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-09-02 10:53
 */
public class AppEmpHealthReport {
    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 人员ID
     */
    private Integer empId;

    /**
     * healthCode-健康码  journeyCard-行程卡 testRecord-核算记录  vaccination-疫苗接种
     */
    private String type;

    /**
     * 日期
     */
    private Date date;

    /**
     * 检测状态  1-阴性  2-阳性
     */
    private Integer testResult;

    /**
     * 健康码状态 green-绿码  red-红码  yellow-黄码  orange-橙码
     */
    private String codeState;

    /**
     * 行程
     */
    private String journey;

    /**
     * 失效日期
     */
    private Date expDate;

    /**
     * 第一次接种时间
     */
    private Date firstTime;

    /**
     * 第二次接种时间
     */
    private Date secondTime;

    /**
     * 删除标记(0-未删除 1-已删除)
     */
    private Integer delFlag;

    private Date updateTime;

    private Date createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getEmpId() {
        return empId;
    }

    public void setEmpId(Integer empId) {
        this.empId = empId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public Integer getTestResult() {
        return testResult;
    }

    public void setTestResult(Integer testResult) {
        this.testResult = testResult;
    }

    public String getCodeState() {
        return codeState;
    }

    public void setCodeState(String codeState) {
        this.codeState = codeState;
    }

    public String getJourney() {
        return journey;
    }

    public void setJourney(String journey) {
        this.journey = journey;
    }

    public Date getExpDate() {
        return expDate;
    }

    public void setExpDate(Date expDate) {
        this.expDate = expDate;
    }

    public Date getFirstTime() {
        return firstTime;
    }

    public void setFirstTime(Date firstTime) {
        this.firstTime = firstTime;
    }

    public Date getSecondTime() {
        return secondTime;
    }

    public void setSecondTime(Date secondTime) {
        this.secondTime = secondTime;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}