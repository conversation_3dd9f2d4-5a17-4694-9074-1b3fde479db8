package com.whfc.emp.param;

import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * @ClasssName AppFaceGateVisitorAddParam
 * @Description 闸机访客生气请求
 * <AUTHOR>
 * @Date 2021/2/26 14:20
 * @Version 1.0
 */
@Data
@ToString(exclude = {"pictureData"})
public class AppFaceGateVisitorAddParam implements Serializable {

    /**
     *
     */
    @NotEmpty
    private String pictureData;

    /**
     * 头像照片
     */
    private String pictureUrl;

    /**
     * 微信id
     */
    @NotEmpty
    private String openId;

    /**
     * 申请人姓名
     */
    @NotEmpty
    @Size(min = 1, max = 32, message = "姓名长度最大为32位")
    private String name;

    /**
     * 申请人手机号
     */
    @Size(min = 1, max = 11, message = "手机号长度最大为11位")
    private String phone;


    /**
     * 身份证
     */
    @NotEmpty
    @Size(min = 1, max = 18, message = "身份证号长度最大为18位")
    private String idCardNo;

    /**
     * 闸机id
     */
    @NotNull
    private Integer faceGateId;

    /**
     * 申请说明
     */
    @Size(min = 1, max = 200, message = "申请说明长度最大为200位")
    private String remark;
}
