package com.whfc.emp.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.whfc.emp.dto.AppAttachDTO;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 添加合同参数
 * @date 2020-08-13
 */
@Data
public class AppContractAddParam implements Serializable {

    /**
     * 人员id
     */
    @NotNull
    private Integer empId;

    /**
     * 合同状态 0-未签订 1-已签订
     */
    @NotNull
    private Integer state;

    /**
     * 合同编号
     */
    @NotEmpty
    @Length(max = 50)
    private String contractNo;

    /**
     * 合同期限类型(0-固定期限 1-固定工作量)
     */
    @NotNull
    private Integer contactType;

    /**
     * 合同开始时间
     */
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startDate;

    /**
     * 合同结束时间
     */
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endDate;

    /**
     * 工资方式 1-按小时 2-按天 3-按月
     */
    private Integer payType;

    /**
     * 工资发放形式
     */
    @Length(max = 32)
    private String payWay;

    /**
     * 单价
     */
    private Double salary;

    /**
     * 附件
     */
    private List<AppAttachDTO> fileAttachList;
}
