package com.whfc.wp.service;

import com.whfc.common.exception.BizException;
import com.whfc.common.result.PageData;
import com.whfc.wp.dto.WorkPermitConfigDTO;
import com.whfc.wp.dto.WorkPermitDTO;
import com.whfc.wp.param.*;

import java.util.List;

/**
 * 作业票服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/31
 */
public interface WorkPermitService {


    /**
     * 获取作业票配置列表
     *
     * @param deptId
     * @param workTypeId
     * @return
     * @throws BizException
     */
    List<WorkPermitConfigDTO> getPermitConfigList(Integer deptId, String workTypeId) throws BizException;

    /**
     * 分页查询作业票列表
     *
     * @param query 查询参数
     * @return 分页数据
     * @throws BizException 业务异常
     */
    PageData<WorkPermitDTO> page(WorkPermitQueryParam query) throws BizException;

    /**
     * 查询作业票列表
     *
     * @param query 查询参数
     * @return 作业票列表
     * @throws BizException 业务异常
     */
    List<WorkPermitDTO> list(WorkPermitQueryParam query) throws BizException;

    /**
     * 根据GUID获取作业票详情
     *
     * @param userId
     * @param guid   GUID
     * @return 作业票详情
     * @throws BizException 业务异常
     */
    WorkPermitDTO getWorkPermitDetail(Integer userId, String guid) throws BizException;

    /**
     * 新增作业票
     *
     * @param param 新增参数
     * @throws BizException 业务异常
     */
    void addWorkPermit(WorkPermitParam param) throws BizException;

    /**
     * 编辑作业票
     *
     * @param param 编辑参数
     * @throws BizException 业务异常
     */
    void editWorkPermit(WorkPermitParam param) throws BizException;

    /**
     * 删除作业票
     *
     * @param guid GUID
     * @throws BizException 业务异常
     */
    void deleteWorkPermit(String guid) throws BizException;

    /**
     * 提交作业票（草稿 -> 待签发）
     *
     * @param param 状态转换参数
     * @throws BizException 业务异常
     */
    void submitWorkPermit(WorkPermitParam param) throws BizException;

    /**
     * 签发作业票（待签发 -> 作业中/ 代签发-> 已打回）
     *
     * @param param 状态转换参数
     * @throws BizException 业务异常
     */
    void issueWorkPermit(WorkPermitIssueParam param) throws BizException;

    /**
     * 关闭作业票（作业中 -> 关闭中）
     *
     * @param param 状态转换参数
     * @throws BizException 业务异常
     */
    void closeWorkPermit(WorkPermitCloseParam param) throws BizException;

    /**
     * 作业票关闭确认（关闭中 -> 已结束）
     *
     * @param param 状态转换参数
     * @throws BizException 业务异常
     */
    void closeConfirmWorkPermit(WorkPermitCloseConfirmParam param) throws BizException;

    /**
     * 作业票绑定巡查
     *
     * @param param
     */
    void bindInspection(WorkPermitBindParam param) throws BizException;

    /**
     * 作业票导出
     *
     * @param deptId
     * @param param
     * @throws BizException
     */
    void exportWorkPermit(Integer deptId, WorkPermitExportParam param) throws BizException;
}
