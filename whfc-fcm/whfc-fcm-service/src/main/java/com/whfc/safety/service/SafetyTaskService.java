package com.whfc.safety.service;

import com.whfc.common.exception.BizException;
import com.whfc.common.result.PageData;
import com.whfc.safety.dto.*;
import com.whfc.safety.param.*;

import java.util.Date;
import java.util.List;

/**
 * @Description 检查任务服务类
 * <AUTHOR>
 * @Date 2021/3/5 10:01
 * @Version 1.0
 */
public interface SafetyTaskService {

    /**
     * 添加检查任务
     *
     * @param param
     * @throws BizException
     */
    void taskAdd(SafetyTaskAddParam param) throws BizException;

    /**
     * 修改检查任务
     *
     * @param param
     * @throws BizException
     */
    void taskEdit(SafetyTaskEditParam param) throws BizException;

    /**
     * 删除检查任务
     *
     * @param taskId
     * @throws BizException
     */
    void delTask(Integer taskId) throws BizException;

    /**
     * 检查任务列表
     *
     * @param deptId
     * @param title
     * @param overdue
     * @param state
     * @param startTime
     * @param endTime
     * @param pageNum
     * @param pageSize
     * @return
     * @throws BizException
     */
    PageData<SafetyTaskDTO> taskList(Integer deptId, String title, Integer overdue, Integer state,
                                     Date startTime, Date endTime, Integer pageNum, Integer pageSize) throws BizException;

    /**
     * 检查任务统计
     *
     * @param deptId
     * @param title
     * @param overdue
     * @param startTime
     * @param endTime
     * @return
     * @throws BizException
     */
    SafetyExecNumDTO taskNum(Integer deptId, String title, Integer overdue, Date startTime, Date endTime) throws BizException;

    /**
     * 检查任务详情（不分页）
     *
     * @param taskId
     * @return
     * @throws BizException
     */
    List<SafetyTaskDetailsDTO> taskDetails(Integer taskId) throws BizException;

    /**
     * 检查任务详情（分页）
     *
     * @param taskId
     * @param pageNum
     * @param pageSize
     * @return
     * @throws BizException
     */
    PageData<SafetyTaskDetailsDTO> taskDetails(Integer taskId, Integer pageNum, Integer pageSize) throws BizException;

    /**
     * 检查信息详情
     *
     * @param taskItemId
     * @param pageNum
     * @param pageSize
     * @return
     * @throws BizException
     */
    PageData<SafetyExecDetailsDTO> execDetailsList(Integer taskItemId, Integer pageNum, Integer pageSize) throws BizException;

    /**
     * 检查信息数据
     *
     * @param taskItemId
     * @return
     * @throws BizException
     */
    SafetyExecNumDTO execNum(Integer taskItemId) throws BizException;

    /**
     * 我的任务
     *
     * @param userId
     * @param deptId
     * @param keyword
     * @param overdue
     * @param partId
     * @param state
     * @param startTime
     * @param endTime
     * @param pageNum
     * @param pageSize
     * @return
     * @throws BizException
     */
    PageData<SafetyTaskExecDTO> taskExecList(Integer userId,
                                             Integer deptId,
                                             String keyword,
                                             Integer overdue,
                                             Integer partId,
                                             Integer state,
                                             Date startTime,
                                             Date endTime,
                                             Integer pageNum,
                                             Integer pageSize) throws BizException;


    /**
     * 我的任务
     *
     * @param userId
     * @param deptId
     * @param keyword
     * @param overdue
     * @param state
     * @param startTime
     * @param endTime
     * @return
     * @throws BizException
     */
    List<SafetyTaskExecDTO> taskExecList(Integer userId,
                                         Integer deptId,
                                         String keyword,
                                         Integer overdue,
                                         Integer state,
                                         Date startTime,
                                         Date endTime) throws BizException;

    /**
     * 我的任务统计
     *
     * @param userId
     * @param deptId
     * @param keyword
     * @param overdue
     * @param startTime
     * @param endTime
     * @return
     * @throws BizException
     */
    SafetyExecNumDTO execNum(Integer userId,
                             Integer deptId,
                             String keyword,
                             Integer overdue,
                             Date startTime,
                             Date endTime) throws BizException;

    /**
     * 检查记录列表（分页）
     *
     * @param deptId
     * @param partId
     * @param startTime
     * @param endTime
     * @param pageNum
     * @param pageSize
     * @return
     * @throws BizException
     */
    PageData<SafetyTaskExecDTO> taskExecList(Integer deptId,
                                             Integer partId,
                                             Date startTime,
                                             Date endTime,
                                             Integer pageNum,
                                             Integer pageSize) throws BizException;

    /**
     * 检查记录列表（不分页）
     *
     * @param deptId
     * @param partId
     * @param startTime
     * @param endTime
     * @return
     * @throws BizException
     */
    List<SafetyTaskExecDTO> taskExecList(Integer deptId,
                                         Integer partId,
                                         Date startTime,
                                         Date endTime) throws BizException;

    /**
     * 检查记录列表（分页）
     *
     * @param deptId
     * @param partId
     * @param startTime
     * @param endTime
     * @param pageNum
     * @param pageSize
     * @return
     * @throws BizException
     */
    PageData<SafetyTaskExecDTO> taskExecList(Integer userId,
                                             Integer deptId,
                                             Integer partId,
                                             Date startTime,
                                             Date endTime,
                                             Integer pageNum,
                                             Integer pageSize) throws BizException;

    /**
     * 检查记录列表（不分页）
     *
     * @param deptId
     * @param partId
     * @param startTime
     * @param endTime
     * @return
     * @throws BizException
     */
    List<SafetyTaskExecDTO> taskExecList(Integer userId,
                                         Integer deptId,
                                         Integer partId,
                                         Date startTime,
                                         Date endTime) throws BizException;


    /**
     * 删除检查任务
     *
     * @param execId
     * @throws BizException
     */
    void execDel(Integer execId) throws BizException;

    /**
     * 排查上报
     *
     * @param param
     * @throws BizException
     */
    Integer report(SafetyTaskReportParam param) throws BizException;

    /**
     * 发布
     *
     * @param param
     * @throws BizException
     */
    void taskRelease(SafetyTaskReleaseParam param) throws BizException;

    /**
     * 完成任务
     *
     * @param param
     * @throws BizException
     */
    void complete(SafetyTaskCompleteParam param) throws BizException;


    /**
     * 检查记录统计
     *
     * @param userId
     * @param deptId
     * @param partId
     * @param startTime
     * @param endTime
     * @return
     * @throws BizException
     */
    SafetyExecNumDTO execNum(Integer userId,
                             Integer deptId,
                             Integer partId,
                             Date startTime,
                             Date endTime) throws BizException;

    /**
     * 导出检查记录
     *
     * @param execIds
     * @throws BizException
     */
    void excExport(List<Integer> execIds, Integer deptId) throws BizException;
}
