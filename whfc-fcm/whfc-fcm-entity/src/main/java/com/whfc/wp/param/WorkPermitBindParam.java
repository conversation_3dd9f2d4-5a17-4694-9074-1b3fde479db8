package com.whfc.wp.param;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 作业票绑定巡查
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/31
 */
@Schema(description = "作业票状态转换参数")
@Data
public class WorkPermitBindParam implements Serializable {

    @Schema(description = "作业票GUID")
    private String guid;

    @Schema(description = "巡查类型  1-安全巡查  2-隐患提交")
    private Integer type;

    @Schema(description = "巡查ID")
    private Integer inspectionId;

    @Schema(description = "检查说明")
    private String remark;

    @Hidden
    @Schema(description = "当前用户ID")
    private Integer currentUserId;

    @Hidden
    @Schema(description = "当前用户姓名")
    private String currentUserName;

}
