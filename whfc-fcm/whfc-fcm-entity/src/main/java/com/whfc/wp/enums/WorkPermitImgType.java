package com.whfc.wp.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/8
 */
public enum WorkPermitImgType {

    APPLY(1, "申请图片"),

    BEFORE(2, "班前交底图片"),

    PROCESSING(3, "作业中图片"),

    AFTER(4, "作业后图片"),

    ;
    private final Integer value;
    private final String desc;

    WorkPermitImgType(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

}
