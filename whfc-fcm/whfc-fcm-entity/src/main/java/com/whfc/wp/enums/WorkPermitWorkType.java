package com.whfc.wp.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/8
 */
public enum WorkPermitWorkType {

    // wp_gczy	高处作业
    // wp_dzzy	吊装作业
    // wp_dhzy	动火作业
    // wp_lsydzy	临时用电作业
    // wp_yxkjzy	有限空间作业
    // wp_bpzy	爆破作业
    // wp_cczy	拆除作业
    // wp_dtzy	动土作业
    // wp_dlzy	断路作业
    // wp_mbcdzy	盲板抽堵作业
    // wp_gxqzy	轨行区作业票
    // wp_ybzy	一般作业

    GCZY("wp_gczy", "高处作业"),
    DZZY("wp_dzzy", "吊装作业"),
    DHZY("wp_dhzy", "动火作业"),
    LSYDZY("wp_lsydzy", "临时用电作业"),
    YXKJZY("wp_yxkjzy", "有限空间作业"),
    BPZY("wp_bpzy", "爆破作业"),
    CCZY("wp_cczy", "拆除作业"),
    DTZY("wp_dtzy", "动土作业"),
    DLZY("wp_dlzy", "断路作业"),
    MBCDZY("wp_mbcdzy", "盲板抽堵作业"),
    GXQZY("wp_gxqzy", "轨行区作业票"),
    YBZY("wp_ybzy", "一般作业");

    private final String code;
    private final String desc;

    WorkPermitWorkType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }



}
