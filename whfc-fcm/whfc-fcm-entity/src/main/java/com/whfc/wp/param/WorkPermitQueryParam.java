package com.whfc.wp.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 作业票查询参数
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/31
 */
@Schema(description = "作业票查询参数")
@Data
public class WorkPermitQueryParam implements Serializable {

    /**
     * 项目ID
     */
    @Schema(description = "项目ID")
    @Hidden
    private Integer deptId;

    /**
     * 作业类型ID
     */
    @Schema(description = "作业类型ID")
    private Integer workTypeId;
    /**
     * 状态
     */
    @Schema(description = "状态")
    private Integer state;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 关键字搜索（编号、作业内容）
     */
    @Schema(description = "关键字搜索（编号、作业内容）")
    private String keyword;

    /**
     * 类型
     */
    @Schema(description = "类型 1-我的作业票  2-我提交的作业票  3-我的待处理  4-作业中  5-未通过  6-已结束")
    private Integer type;

    /**
     * 当前用户ID
     */
    @Hidden
    @Schema(name = "当前用户ID")
    private Integer currUserId;

    /**
     * 页码
     */
    @Schema(description = "页码")
    private Integer pageNum;

    /**
     * 页大小
     */
    @Schema(description = "页大小")
    private Integer pageSize;

    @Hidden
    @Schema(description = "作业票ID列表")
    private List<Integer> workPermitIdList;
}
