package com.whfc.wp.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 作业票-照片
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/31
 */
@Schema(description = "作业票-照片")
@Data
public class WorkPermitImgDTO implements Serializable {
    /**
     * 类型
     */
    @Schema(description = "类型 1-申请图片  2-班前交底图片  3-作业中图片 4-作业后图片")
    private Integer type;

    /**
     * 图片
     */
    @Schema(description = "图片")
    private String imgUrl;

}