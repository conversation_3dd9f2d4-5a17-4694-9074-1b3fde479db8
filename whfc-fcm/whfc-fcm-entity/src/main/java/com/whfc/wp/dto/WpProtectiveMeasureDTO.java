package com.whfc.wp.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 作业票保护措施
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/5
 */
@Data
public class WpProtectiveMeasureDTO implements Serializable {

    /**
     * 保护措施选项
     */
    private List<WorkPermitConfigDetailDTO> protectiveMeasure;

    /**
     * 申请关闭选项
     */
    private WorkPermitConfigDetailDTO toClose;

    /**
     * 关闭确认选项
     */
    private WorkPermitConfigDetailDTO closeConfirm;


}
