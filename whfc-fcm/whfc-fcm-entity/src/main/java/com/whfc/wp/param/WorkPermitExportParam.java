package com.whfc.wp.param;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 作业票导出参数
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/31
 */
@Schema(description = "作业票导出参数")
@Data
public class WorkPermitExportParam implements Serializable {

    @NotEmpty
    @Schema(description = "作业票GUID列表")
    private List<String> guids;

    @Hidden
    @Schema(description = "当前用户ID")
    private Integer currUserId;


}
