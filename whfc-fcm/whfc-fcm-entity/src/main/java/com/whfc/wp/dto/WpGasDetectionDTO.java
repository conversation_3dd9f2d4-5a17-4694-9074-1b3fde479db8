package com.whfc.wp.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/8
 */
@Data
public class WpGasDetectionDTO implements Serializable {

    private String checkUser;
    private String checkTime;
    private String workStartTime;
    private List<GasCheckData> checkData;

    @Data
    public static class GasCheckData {
        private String type;
        private String name;
        private String operator;
        private String value;
        private String unit;
    }

}
