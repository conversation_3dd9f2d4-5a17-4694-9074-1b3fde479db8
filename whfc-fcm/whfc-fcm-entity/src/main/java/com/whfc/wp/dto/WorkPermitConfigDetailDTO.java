package com.whfc.wp.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 作业票配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/31
 */
@Schema(description = "作业票配置")
@Data
public class WorkPermitConfigDetailDTO implements Serializable {

    @Schema(description = "序号")
    private Integer index;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "是否选中")
    private Boolean flag;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "是否为输入项")
    private Boolean isInput;

}