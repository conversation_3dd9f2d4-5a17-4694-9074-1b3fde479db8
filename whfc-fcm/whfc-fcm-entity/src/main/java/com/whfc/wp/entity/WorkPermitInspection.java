package com.whfc.wp.entity;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

import lombok.Data;

/**
 * 作业票巡查
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/2
 */
@Schema(description = "作业票巡查")
@Data
public class WorkPermitInspection {
    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Integer id;

    /**
     * 作业票ID
     */
    @Schema(description = "作业票ID")
    private Integer permitId;

    /**
     * 巡查类型  1-安全巡查  2-隐患提交
     */
    @Schema(description = "巡查类型  1-安全巡查  2-隐患提交")
    private Integer type;

    /**
     * 巡查ID
     */
    @Schema(description = "巡查ID")
    private Integer inspectionId;

    /**
     * 检查说明
     */
    @Schema(description = "检查说明")
    private String remark;

    /**
     * 删除标记:0-未删除 1-已删除
     */
    @Schema(description = "删除标记:0-未删除 1-已删除")
    private Integer delFlag;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;
}