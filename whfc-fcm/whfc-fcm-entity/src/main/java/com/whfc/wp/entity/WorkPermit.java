package com.whfc.wp.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 作业票
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/31
 */
@Schema(description = "作业票")
@Data
public class WorkPermit {
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Integer id;

    /**
     * 项目ID
     */
    @Schema(description = "项目ID")
    private Integer deptId;

    /**
     * GUID
     */
    @Schema(description = "GUID")
    private String guid;

    /**
     * 编号
     */
    @Schema(description = "编号")
    private String code;

    /**
     * 安全等级ID
     */
    @Schema(description = "安全等级ID")
    private Integer levelId;

    /**
     * 安全等级
     */
    @Schema(description = "安全等级")
    private String levelName;

    /**
     * 作业类型ID
     */
    @Schema(description = "作业类型ID")
    private Integer workTypeId;

    /**
     * 作业类型名称
     */
    @Schema(description = "作业类型名称")
    private String workTypeName;

    /**
     * 作业开始时间
     */
    @Schema(description = "作业开始时间")
    private Date workStartTime;

    /**
     * 作业截止时间
     */
    @Schema(description = "作业截止时间")
    private Date workEndTime;

    /**
     * 休息时间段
     */
    @Schema(description = "休息时间段")
    private String restPeriod;

    /**
     * 作业单位ID
     */
    @Schema(description = "作业单位ID")
    private Integer workUnitId;

    /**
     * 作业单位名称
     */
    @Schema(description = "作业单位名称")
    private String workUnitName;

    /**
     * 作业内容
     */
    @Schema(description = "作业内容")
    private String workContent;

    /**
     * 作业区域iD
     */
    @Schema(description = "作业区域iD")
    private Integer workAreaId;

    /**
     * 作业区域
     */
    @Schema(description = "作业区域")
    private String workAreaName;

    /**
     * 作业人数
     */
    @Schema(description = "作业人数")
    private Integer workerNum;

    /**
     * 经度
     */
    @Schema(description = "经度")
    private Double lng;

    /**
     * 纬度
     */
    @Schema(description = "纬度")
    private Double lat;

    /**
     * 地址
     */
    @Schema(description = "地址")
    private String address;

    /**
     * 工器具
     */
    @Schema(description = "工器具")
    private String tool;

    /**
     * 特种作业人员
     */
    @Schema(description = "特种作业人员")
    private String specialOperationPerson;

    /**
     * 监护人姓名
     */
    @Schema(description = "监护人姓名")
    private String guardianName;

    /**
     * 监护人联系方式
     */
    @Schema(description = "监护人联系方式")
    private String guardianContact;

    /**
     * 申请人ID
     */
    @Schema(description = "申请人ID")
    private Integer applyUserId;

    /**
     * 申请人姓名
     */
    @Schema(description = "申请人姓名")
    private String applyUserName;

    /**
     * 申请人联系方式
     */
    @Schema(description = "申请人联系方式")
    private String applyUserContact;

    /**
     * 申请人签名
     */
    @Schema(description = "申请人签名")
    private String applyUserSign;

    /**
     * 申请时间
     */
    @Schema(description = "申请时间")
    private Date applyTime;

    /**
     * 签发人ID
     */
    @Schema(description = "签发人ID")
    private Integer issueUserId;

    /**
     * 签发人姓名
     */
    @Schema(description = "签发人姓名")
    private String issueUserName;

    /**
     * 签发人联系方式
     */
    @Schema(description = "签发人联系方式")
    private String issueUserContact;

    /**
     * 签发人签名
     */
    @Schema(description = "签发人签名")
    private String issueUserSign;

    /**
     * 签发时间
     */
    @Schema(description = "签发时间")
    private Date issueTime;

    /**
     * 现场管理人员ID
     */
    @Schema(description = "现场管理人员ID")
    private Integer siteUserId;

    /**
     * 现场管理人员姓名
     */
    @Schema(description = "现场管理人员姓名")
    private String siteUserName;

    /**
     * 现场管理人员签名
     */
    @Schema(description = "现场管理人员签名")
    private String siteUserSign;

    /**
     * 安全监督人员ID
     */
    @Schema(description = "安全监督人员ID")
    private Integer safetyUserId;

    /**
     * 安全监督人员姓名
     */
    @Schema(description = "安全监督人员姓名")
    private String safetyUserName;

    /**
     * 安全监督人员签名
     */
    @Schema(description = "安全监督人员签名")
    private String safetyUserSign;

    /**
     * 气体检测
     */
    @Schema(description = "气体检测")
    private String gasDetection;

    /**
     * 风险识别
     */
    @Schema(description = "风险识别")
    private String riskRecognition;

    /**
     * 管控措施
     */
    @Schema(description = "管控措施")
    private String controlMeasure;

    /**
     * 班前培训
     */
    @Schema(description = "班前培训")
    private String preWorkTrain;

    /**
     * 作业检查
     */
    @Schema(description = "作业检查")
    private String inWorkInspect;

    /**
     * 保护措施
     */
    @Schema(description = "保护措施")
    private String protectiveMeasure;

    /**
     * 状态  0-草稿 10-待签发 11-已打回 20-作业中 90-关闭中 100-已结束
     */
    @Schema(description = "状态  0-草稿 10-待签发 11-已打回 20-作业中 90-关闭中 100-已结束")
    private Integer state;

    /**
     * 删除标记:0-未删除 1-已删除
     */
    @Schema(description = "删除标记:0-未删除 1-已删除")
    private Integer delFlag;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;
}