<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd">
    <property name="autoIncrement" value="true" dbms="mysql"/>

    <changeSet id="1" author="xuguocheng">
        <comment>质量管理SQL迁移</comment>
        <sql>
            CREATE TABLE `quality_acceptance`  (
               `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
               `dept_id` int(11) NULL DEFAULT NULL COMMENT '组织机构id',
               `construction_id` int(11) NULL DEFAULT NULL COMMENT '施工单位ID',
               `construction_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '施工单位',
               `labour_id` int(11) NULL DEFAULT NULL COMMENT '劳务单位ID',
               `labour_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '劳务单位',
               `regulatory_id` int(11) NULL DEFAULT NULL COMMENT '监理单位ID',
               `regulatory_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '监理单位',
               `conclusions` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '结论',
               `part_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '检查部位',
               `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
               `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
               `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
               `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
               PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 32 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '验收情况' ROW_FORMAT = Dynamic;


            CREATE TABLE `quality_acceptance_img`  (
               `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
               `deptId` int(11) NULL DEFAULT NULL COMMENT '组织机构id',
               `acceptance_id` int(11) NOT NULL COMMENT '施工记录id',
               `img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '图片地址',
               `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记（0-未删除 1-已删除）',
               `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
               `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
               PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 81 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '验收情况图片' ROW_FORMAT = Dynamic;


            CREATE TABLE `quality_check`  (
              `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
              `dept_id` int(11) NOT NULL COMMENT '组织机构ID',
              `part_id` int(11) NULL DEFAULT NULL COMMENT '检查部位ID',
              `part_name` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '检查部位名称',
              `issue_id` bigint(20) NULL DEFAULT NULL COMMENT '问题明细ID',
              `issue_type_id` bigint(20) NULL DEFAULT NULL COMMENT '问题类别ID',
              `issue_type_name` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问题类型名称',
              `issue_content` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问题明细',
              `urgency` int(11) NOT NULL DEFAULT 1 COMMENT '紧急程度ID',
              `urgency_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '紧急程度名称',
              `check_time` datetime(0) NOT NULL COMMENT '检查时间',
              `check_desc` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '检查说明',
              `check_user_id` int(11) NULL DEFAULT NULL COMMENT '检查人',
              `check_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '检查人姓名',
              `rectify_require` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '整改要求',
              `rectify_duration` int(11) NULL DEFAULT NULL COMMENT '整改期限 （天）',
              `rectify_start_time` datetime(0) NOT NULL COMMENT '整改开始时间',
              `rectify_end_time` datetime(0) NOT NULL COMMENT '整改截止时间',
              `lng` double NOT NULL COMMENT '经度',
              `lat` double NOT NULL COMMENT '纬度',
              `address` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '位置信息',
              `overdue` int(11) NOT NULL DEFAULT 0 COMMENT '是否超期  0-未超期  1-超期',
              `state` int(11) NOT NULL COMMENT '状态   10-待整改 11-待整改(复查不合格) 12-待整改(核验不合格) 20-待复查 30-待核验 100-已完成',
              `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
              `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
              `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
              PRIMARY KEY (`id`) USING BTREE,
              INDEX `idx_deptId_checkTime`(`dept_id`, `check_time`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1010 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '质量问题记录表' ROW_FORMAT = Dynamic;


            CREATE TABLE `quality_check_img`  (
              `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
              `check_id` int(11) NOT NULL COMMENT '问题记录ID',
              `log_id` int(11) NULL DEFAULT NULL COMMENT '问题处理流程ID',
              `img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '图片地址',
              `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
              `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
              `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
              PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1099 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '质量问题记录图片表' ROW_FORMAT = Dynamic;


            CREATE TABLE `quality_check_log`  (
              `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
              `check_id` int(11) NOT NULL COMMENT '问题记录ID',
              `op_type` int(11) NOT NULL COMMENT '操作类型 1-上报 2-整改 3-复查 4-核验',
              `op_user_id` int(11) NOT NULL COMMENT '处理用户ID',
              `op_user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '处理用户名称',
              `op_time` datetime(0) NULL DEFAULT NULL COMMENT '整改时间',
              `op_desc` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审核说明',
              `op_result` int(11) NULL DEFAULT NULL COMMENT '操作结果  1-合格  2-不合格',
              `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
              `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
              `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
              PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1730 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '质量问题处理流程表' ROW_FORMAT = Dynamic;


            CREATE TABLE `quality_check_statistics_day`  (
             `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
             `dept_id` int(11) NOT NULL COMMENT '组织机构ID',
             `date` date NOT NULL COMMENT '日期',
             `new_issue_num` int(11) NOT NULL COMMENT '新问题数量',
             `todo_issue_num` int(11) NOT NULL COMMENT '待整改数量',
             `complete_issue_num` int(11) NOT NULL COMMENT '完成整改数量',
             PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 17277 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '质量检查统计-天' ROW_FORMAT = Dynamic;


            CREATE TABLE `quality_check_user`  (
               `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
               `check_id` int(11) NOT NULL COMMENT '问题记录ID',
               `type` int(11) NOT NULL COMMENT '人员类型 1-上报人  2-整改人  3-复查人  4-核验人  5-抄送人',
               `user_id` int(11) NOT NULL COMMENT '人员ID',
               `user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '人员姓名',
               `user_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '人员手机号',
               `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
               `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
               `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
               PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 3961 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '质量问题记录干系人' ROW_FORMAT = Dynamic;


            CREATE TABLE `quality_dict`  (
                 `id` int(11) NOT NULL AUTO_INCREMENT,
                 `dept_id` int(11) NOT NULL COMMENT '项目ID',
                 `pid` int(11) NOT NULL DEFAULT 0 COMMENT '父级ID',
                 `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码',
                 `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
                 `ext1` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '扩展字段1',
                 `ext2` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '扩展字段2',
                 `param` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '其他信息',
                 `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
                 `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                 `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                 PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 19 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '质量管理-数据字典' ROW_FORMAT = Dynamic;


            CREATE TABLE `quality_fine`  (
             `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
             `dept_id` int(11) NULL DEFAULT NULL COMMENT '组织机构id',
             `corp_id` int(11) NULL DEFAULT NULL COMMENT '罚款单位id',
             `corp_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '罚款单位名称',
             `corp_user_id` int(11) NULL DEFAULT NULL COMMENT '单位负责人id',
             `corp_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '单位负责人名称',
             `time` datetime(0) NULL DEFAULT NULL COMMENT '罚款日期',
             `money` double NULL DEFAULT NULL COMMENT '罚款金额',
             `handle_opinion` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '处理意见',
             `remark` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
             `user_id` int(11) NULL DEFAULT NULL COMMENT '提交用户id',
             `user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提交用户名称',
             `state` int(11) NULL DEFAULT 0 COMMENT '状态 0-待审核 10-待批准 11-审批已驳回 20-已通过 21-批准已驳回',
             `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
             `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
             `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
             PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 55 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '质量罚款单' ROW_FORMAT = Dynamic;


            CREATE TABLE `quality_fine_img`  (
                 `id` int(11) NOT NULL AUTO_INCREMENT,
                 `fine_id` int(11) NULL DEFAULT NULL COMMENT '罚款id',
                 `url` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件地址',
                 `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
                 `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                 `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                 PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '质量罚款单-图片' ROW_FORMAT = Dynamic;


            CREATE TABLE `quality_fine_user`  (
              `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
              `fine_id` int(11) NOT NULL COMMENT '质量罚款单ID',
              `type` int(11) NOT NULL COMMENT '人员类型 1-上报人  2-审核人  3-批准人',
              `user_id` int(11) NOT NULL COMMENT '人员ID',
              `user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '人员姓名',
              `time` datetime(0) NOT NULL COMMENT '审批时间',
              `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
              `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
              `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
              PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 58 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '质量罚款单记录干系人' ROW_FORMAT = Dynamic;


            CREATE TABLE `quality_inspection_lot`  (
               `id` int(11) NOT NULL AUTO_INCREMENT,
               `dept_id` int(11) NOT NULL COMMENT '项目ID',
               `lot_type` int(11) NOT NULL COMMENT '检验批类型',
               `lot_type_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '检验批名称',
               `unit` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '单位工程',
               `subsection` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分部工程',
               `subitem` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分项工程',
               `lot_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '检验批名称',
               `check_place` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '检验部位',
               `commit_time` datetime(0) NULL DEFAULT NULL COMMENT '提交时间',
               `commit_user_id` int(11) NULL DEFAULT NULL COMMENT '提交人',
               `commit_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提交人姓名',
               `file_guid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件GUID',
               `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
               `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
               `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
               PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 22 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '质量管理-检验批' ROW_FORMAT = Dynamic;


            CREATE TABLE `quality_inspection_lot_detail`  (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `dept_id` int(11) NOT NULL COMMENT '项目ID',
              `lot_id` int(11) NOT NULL COMMENT '检验批ID',
              `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '检查项目序号',
              `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '检查项目名称',
              `check_place` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '检查位置',
              `check_desc` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '检查情况',
              `check_result` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '检查结果',
              `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
              `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
              `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
              PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 180 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '质量管理-检验批详情' ROW_FORMAT = Dynamic;


            CREATE TABLE `quality_issue`  (
              `issue_type_id` bigint(20) NOT NULL COMMENT 'issue_type_id',
              `id` bigint(20) NOT NULL COMMENT 'ID',
              `dept_id` int(11) NOT NULL COMMENT '组织机构ID',
              `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码',
              `content` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '检查内容',
              `require` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
              `duration` int(11) NOT NULL COMMENT '整改期限 （天）',
              `enable_flag` int(11) NOT NULL DEFAULT 1 COMMENT '状态   0-已禁用  1-启用',
              `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
              `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
              `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
              `issue_type_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
              PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '质量问题库表' ROW_FORMAT = Dynamic;


            CREATE TABLE `quality_issue_type`  (
               `pid` bigint(20) NOT NULL COMMENT 'pid',
               `id` bigint(20) NOT NULL COMMENT 'ID',
               `dept_id` int(11) NOT NULL COMMENT '组织机构ID',
               `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '编码',
               `level` int(11) NOT NULL COMMENT '层级',
               `name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '类别名称',
               `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
               `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
               `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
               PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '质量问题类型表' ROW_FORMAT = Dynamic;


            CREATE TABLE `quality_part`  (
                 `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
                 `dept_id` int(11) NOT NULL COMMENT '组织机构ID',
                 `pid` int(11) NOT NULL COMMENT '父节点ID',
                 `code` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '编码',
                 `name` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '部位名称',
                 `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
                 `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
                 `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                 PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1013 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '质量检测部位表' ROW_FORMAT = Dynamic;


            CREATE TABLE `quality_rectify_details`  (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `rectify_id` int(11) NULL DEFAULT NULL COMMENT '整改通知单id',
                `check_id` int(11) NULL DEFAULT NULL COMMENT '问题id',
                `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
                `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 41 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '整改通知单详情' ROW_FORMAT = Dynamic;


            CREATE TABLE `quality_rectify_info`  (
                 `id` int(11) NOT NULL AUTO_INCREMENT,
                 `dept_id` int(11) NULL DEFAULT NULL COMMENT '组织机构id',
                 `corp_id` int(11) NULL DEFAULT NULL COMMENT '合作单位id',
                 `corp_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合作单位名称',
                 `question_num` int(11) NULL DEFAULT NULL COMMENT '问题数量',
                 `create_user_id` int(11) NULL DEFAULT NULL COMMENT '创建人id',
                 `create_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
                 `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
                 `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                 `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                 PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 17 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '整改通知单' ROW_FORMAT = Dynamic;


            CREATE TABLE `quality_task`  (
                 `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                 `dept_id` int(11) NOT NULL COMMENT '组织机构id',
                 `title` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务名称',
                 `content` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '检查说明',
                 `state` int(11) NOT NULL DEFAULT 0 COMMENT '状态；0-未发布，1-未开始，2-进行中，3-已完成',
                 `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标识，0-未删除，1-已删除',
                 `publish_user_id` int(11) NULL DEFAULT NULL COMMENT '发布认id',
                 `publish_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发布人姓名',
                 `publish_time` datetime(0) NULL DEFAULT NULL COMMENT '发布时间',
                 `update_user_id` int(11) NULL DEFAULT NULL COMMENT '更新人id',
                 `update_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人姓名',
                 `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                 `create_user_id` int(11) NOT NULL COMMENT '创建人id',
                 `create_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人姓名',
                 `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                 PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 37 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '检查任务' ROW_FORMAT = Dynamic;


            CREATE TABLE `quality_task_item`  (
              `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
              `task_id` int(11) NOT NULL COMMENT '检查任务id',
              `check_user_id` int(11) NOT NULL COMMENT '检查人id',
              `check_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '检查人姓名',
              `check_type` int(11) NOT NULL COMMENT '检查类型；1-单次检查；2-每日检查',
              `state` int(11) NOT NULL DEFAULT 0 COMMENT '状态；0-未开始；1-进行中；2-已完成',
              `overdue` int(11) NOT NULL DEFAULT 0 COMMENT '是否超期；0-未超期，1-超期',
              `progress` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '进度',
              `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标识；0-未删除；1-已删除',
              `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
              `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
              PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 51 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '检查任务项' ROW_FORMAT = Dynamic;


            CREATE TABLE `quality_task_item_exec`  (
               `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
               `dept_id` int(11) NOT NULL COMMENT '组织机构ID',
               `task_id` int(11) NULL DEFAULT NULL COMMENT '检查任务id',
               `task_item_id` int(11) NULL DEFAULT NULL COMMENT '检查任务项id',
               `part_id` int(11) NOT NULL COMMENT '部位id',
               `part_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '部位名称',
               `require_start_time` datetime(0) NULL DEFAULT NULL COMMENT '任务开始时间',
               `require_end_time` datetime(0) NULL DEFAULT NULL COMMENT '任务结束时间',
               `check_time` datetime(0) NULL DEFAULT NULL COMMENT '检查时间',
               `check_user_id` int(11) NULL DEFAULT NULL COMMENT '检查人id',
               `check_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '检查人姓名',
               `check_result` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '检查结果',
               `overdue` int(11) NOT NULL DEFAULT 0 COMMENT '是否超期；0-未超期，1-超期',
               `task_type` int(11) NOT NULL DEFAULT 0 COMMENT '检查类型,0-任务下发，1-日常上报',
               `lng` double NULL DEFAULT NULL COMMENT '经度',
               `lat` double NULL DEFAULT NULL COMMENT '纬度',
               `location` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地址',
               `state` int(11) NOT NULL DEFAULT 0 COMMENT '状态；0-未开始，1-进行中，2-已完成',
               `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标识；0-未删除；1-已删除',
               `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
               `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
               PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 812 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '检查信息' ROW_FORMAT = Dynamic;


            CREATE TABLE `quality_task_item_img`  (
              `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
              `exec_id` int(11) NOT NULL COMMENT '问题记录ID',
              `img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '图片地址',
              `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
              `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
              `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
              PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 734 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '质量问题记录图片表' ROW_FORMAT = Dynamic;


            CREATE TABLE `quality_task_item_part`  (
               `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
               `task_id` int(11) NOT NULL COMMENT '检查任务id',
               `task_item_id` int(11) NOT NULL COMMENT '检查任务项id',
               `part_id` int(11) NOT NULL COMMENT '部位id',
               `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标识；0-未删除；1-已删除',
               `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
               `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
               PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 77 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '检查任务项-部位' ROW_FORMAT = Dynamic;


            CREATE TABLE `quality_task_item_time`  (
               `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
               `task_id` int(11) NOT NULL COMMENT '检查任务id',
               `task_item_id` int(11) NOT NULL COMMENT '检查任务项id',
               `start_date` datetime(0) NOT NULL COMMENT '开始时间',
               `end_date` datetime(0) NOT NULL COMMENT '结束时间',
               `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标识；0-未删除；1-已删除',
               `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
               `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
               PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 128 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '检查任务项-时间段' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="2" author="xuguocheng">
        <comment>安全管理SQL迁移</comment>
        <sql>

            CREATE TABLE `safety_area`  (
                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                `dept_id` int(11) NULL DEFAULT NULL COMMENT '组织机构id',
                `pid` int(11) NULL DEFAULT NULL COMMENT '上级id',
                `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '区域名称',
                `rectify_user_id` int(11) NULL DEFAULT NULL COMMENT '隐患整改人id',
                `rectify_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '隐患整改人姓名',
                `notice_user_id` int(11) NULL DEFAULT NULL COMMENT '隐患通知人id',
                `notice_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '隐患通知人姓名',
                `responsible_user_id` int(11) NULL DEFAULT NULL COMMENT '风险责任人id',
                `responsible_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '风险责任人姓名',
                `corp_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合作单位',
                `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
                `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '责任区域' ROW_FORMAT = Dynamic;


            CREATE TABLE `safety_check`  (
             `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
             `dept_id` int(11) NOT NULL COMMENT '组织机构ID',
             `part_id` int(11) NULL DEFAULT NULL COMMENT '检查部位ID',
             `part_name` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '检查部位名称',
             `issue_id` bigint(20) NULL DEFAULT NULL COMMENT 'issue_id',
             `issue_type_id` bigint(20) NULL DEFAULT NULL COMMENT 'issue_type_id',
             `issue_type_name` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问题类型名称',
             `issue_content` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问题明细',
             `urgency` int(11) NOT NULL DEFAULT 1 COMMENT '紧急程度ID',
             `urgency_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '紧急程度名称',
             `check_time` datetime(0) NOT NULL COMMENT '检查时间',
             `check_desc` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '检查说明',
             `check_user_id` int(11) NULL DEFAULT NULL COMMENT '检查人ID',
             `check_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '检查人姓名',
             `rectify_require` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '整改要求',
             `rectify_duration` int(11) NULL DEFAULT NULL COMMENT '整改期限 （天）',
             `rectify_start_time` datetime(0) NOT NULL COMMENT '整改开始时间',
             `rectify_end_time` datetime(0) NOT NULL COMMENT '整改截止时间',
             `lng` double NOT NULL COMMENT '经度',
             `lat` double NOT NULL COMMENT '纬度',
             `address` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '位置信息',
             `overdue` int(11) NOT NULL DEFAULT 0 COMMENT '是否超期  0-未超期  1-超期',
             `state` int(11) NOT NULL COMMENT '状态   10-待整改 11-待整改(复查不合格) 12-待整改(核验不合格) 20-待复查 30-待核验 100-已完成',
             `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
             `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
             `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
             PRIMARY KEY (`id`) USING BTREE,
             INDEX `idx_deptId_checkTime`(`dept_id`, `check_time`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1562 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '安全问题记录表' ROW_FORMAT = Dynamic;


            CREATE TABLE `safety_check_ext`  (
                                                 `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                                 `check_id` int(11) NOT NULL COMMENT '问题上报id',
                                                 `obj_id` int(11) NOT NULL COMMENT '关联id',
                                                 `area_id` int(11) NULL DEFAULT NULL COMMENT '责任区域',
                                                 `check_type` int(11) NOT NULL COMMENT '检查类型（1-日常检查，2-选择检查类型，3-周检查，4-月检查，5-经常性检查，6-定期检查，7-节假日前后，8-专项检查，9-重大节气，10-突发性事件，11-主要负责人带班检查，12-多人检查）',
                                                 `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
                                                 `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
                                                 `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                                                 PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '问题上报扩展表' ROW_FORMAT = Dynamic;


            CREATE TABLE `safety_check_img`  (
                                                 `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                                 `check_id` int(11) NOT NULL COMMENT '问题记录ID',
                                                 `log_id` int(11) NULL DEFAULT NULL COMMENT '问题处理流程ID',
                                                 `img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '图片地址',
                                                 `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
                                                 `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
                                                 `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                                                 PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 2321 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '安全问题记录图片表' ROW_FORMAT = Dynamic;


            CREATE TABLE `safety_check_log`  (
                                                 `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                                 `check_id` int(11) NOT NULL COMMENT '问题记录ID',
                                                 `op_type` int(11) NOT NULL COMMENT '操作类型 1-上报 2-整改 3-复查 4-核验',
                                                 `op_user_id` int(11) NOT NULL COMMENT '处理用户ID',
                                                 `op_user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '处理用户名称',
                                                 `op_time` datetime(0) NULL DEFAULT NULL COMMENT '整改时间',
                                                 `op_desc` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审核说明',
                                                 `op_result` int(11) NULL DEFAULT NULL COMMENT '操作结果  1-合格  2-不合格',
                                                 `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
                                                 `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
                                                 `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                                                 PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 3734 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '安全问题处理流程表' ROW_FORMAT = Dynamic;


            CREATE TABLE `safety_check_statistics_day`  (
                                                            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                                            `dept_id` int(11) NOT NULL COMMENT '组织机构ID',
                                                            `date` date NOT NULL COMMENT '日期',
                                                            `new_issue_num` int(11) NOT NULL COMMENT '新问题数量',
                                                            `todo_issue_num` int(11) NOT NULL COMMENT '待整改数量',
                                                            `complete_issue_num` int(11) NOT NULL COMMENT '完成整改数量',
                                                            PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 18523 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '安全检查统计-天' ROW_FORMAT = Dynamic;


            CREATE TABLE `safety_check_user`  (
                                                  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                                  `check_id` int(11) NOT NULL COMMENT '问题记录ID',
                                                  `type` int(11) NOT NULL COMMENT '人员类型 1-上报人  2-整改人  3-复查人  4-核验人  5-抄送人',
                                                  `user_id` int(11) NOT NULL COMMENT '人员ID',
                                                  `user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '人员姓名',
                                                  `user_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '人员手机号',
                                                  `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
                                                  `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
                                                  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                                                  PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 6417 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '安全问题记录干系人' ROW_FORMAT = Dynamic;


            CREATE TABLE `safety_clue`  (
                                            `id` int(11) NOT NULL AUTO_INCREMENT,
                                            `dept_id` int(11) NOT NULL COMMENT '项目ID',
                                            `title` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标题',
                                            `content` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '内容',
                                            `ext` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '其他信息',
                                            `time` datetime(0) NULL DEFAULT NULL COMMENT '时间',
                                            `user` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '姓名',
                                            `phone` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机号',
                                            `file_guid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件GUID',
                                            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
                                            `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                                            `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                            PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '安全管理-安全线索' ROW_FORMAT = Dynamic;


            CREATE TABLE `safety_dict`  (
                                            `id` int(11) NOT NULL AUTO_INCREMENT,
                                            `dept_id` int(11) NOT NULL COMMENT '项目ID',
                                            `pid` int(11) NOT NULL DEFAULT 0 COMMENT '父级ID',
                                            `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码',
                                            `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
                                            `ext1` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '扩展字段1',
                                            `ext2` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '扩展字段2',
                                            `param` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '其他信息',
                                            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
                                            `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                                            `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                            PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '安全管理-数据字典' ROW_FORMAT = Dynamic;


            CREATE TABLE `safety_fine`  (
                                            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                            `dept_id` int(11) NULL DEFAULT NULL COMMENT '组织机构id',
                                            `corp_id` int(11) NULL DEFAULT NULL COMMENT '罚款单位id',
                                            `corp_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '罚款单位名称',
                                            `corp_user_id` int(11) NULL DEFAULT NULL COMMENT '单位负责人id',
                                            `corp_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '单位负责人名称',
                                            `time` datetime(0) NULL DEFAULT NULL COMMENT '罚款日期',
                                            `money` double NULL DEFAULT NULL COMMENT '罚款金额',
                                            `handle_opinion` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '处理意见',
                                            `remark` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
                                            `user_id` int(11) NULL DEFAULT NULL COMMENT '提交用户id',
                                            `user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提交用户名称',
                                            `state` int(11) NULL DEFAULT 0 COMMENT '状态 0-待审核 10-待批准 11-审批已驳回 20-已通过 21-批准已驳回',
                                            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
                                            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                                            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                            PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 191 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '质量罚款单' ROW_FORMAT = Dynamic;


            CREATE TABLE `safety_fine_img`  (
                                                `id` int(11) NOT NULL AUTO_INCREMENT,
                                                `fine_id` int(11) NULL DEFAULT NULL COMMENT '罚款id',
                                                `url` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件地址',
                                                `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
                                                `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                                                `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 120 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '质量罚款单-图片' ROW_FORMAT = Dynamic;


            CREATE TABLE `safety_fine_user`  (
                                                 `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                                 `fine_id` int(11) NOT NULL COMMENT '质量罚款单ID',
                                                 `type` int(11) NOT NULL COMMENT '人员类型 1-上报人  2-审核人  3-批准人',
                                                 `user_id` int(11) NOT NULL COMMENT '人员ID',
                                                 `user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '人员姓名',
                                                 `time` datetime(0) NOT NULL COMMENT '审批时间',
                                                 `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
                                                 `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                                                 `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                                                 PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 287 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '质量罚款单记录干系人' ROW_FORMAT = Dynamic;


            CREATE TABLE `safety_inspect`  (
                                               `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                               `dept_id` int(11) NOT NULL COMMENT '项目ID',
                                               `guid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '唯一编码',
                                               `type` int(11) NOT NULL COMMENT '类型',
                                               `around` int(11) NOT NULL COMMENT '前后',
                                               `location` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地址',
                                               `rainstorm` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '暴风雨',
                                               `typhoon` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '台风信号',
                                               `check_time` datetime(0) NULL DEFAULT NULL COMMENT '检查时间',
                                               `check_user_id` int(11) NULL DEFAULT NULL COMMENT '检查人ID',
                                               `check_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '检查人',
                                               `position` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '职位',
                                               `sign` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '签名',
                                               `state` int(11) NOT NULL DEFAULT 0 COMMENT '状态',
                                               `export_url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '导出地址',
                                               `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
                                               `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                                               `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                               PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '安全管理-专项检查' ROW_FORMAT = Dynamic;


            CREATE TABLE `safety_inspect_image`  (
                                                     `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                                     `dept_id` int(11) NOT NULL COMMENT '项目ID',
                                                     `inspect_id` int(11) NOT NULL COMMENT '检查ID',
                                                     `image` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图片',
                                                     `remark` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
                                                     `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
                                                     `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                                                     `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                                     PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 44 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '安全管理-专项检查-现场图片' ROW_FORMAT = Dynamic;


            CREATE TABLE `safety_inspect_item`  (
                                                    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                                    `dept_id` int(11) NOT NULL COMMENT '项目ID',
                                                    `inspect_id` int(11) NOT NULL COMMENT '检查ID',
                                                    `type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类型',
                                                    `code` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码',
                                                    `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
                                                    `ename` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '英文',
                                                    `result` int(11) NULL DEFAULT NULL COMMENT '结果',
                                                    `remark` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
                                                    `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
                                                    `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                                                    `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                                    PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 427 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '安全管理-专项检查-检查项' ROW_FORMAT = Dynamic;


            CREATE TABLE `safety_issue`  (
                                             `issue_type_id` bigint(20) NOT NULL COMMENT 'issue_type_id',
                                             `id` bigint(20) NOT NULL COMMENT 'ID',
                                             `dept_id` int(11) NOT NULL COMMENT '组织机构ID',
                                             `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '编码',
                                             `content` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '检查内容',
                                             `require` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                             `duration` int(11) NOT NULL COMMENT '整改期限 （天）',
                                             `enable_flag` int(11) NOT NULL DEFAULT 1 COMMENT '状态   0-已禁用  1-启用',
                                             `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
                                             `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
                                             `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                                             `issue_type_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                             PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '安全问题库表' ROW_FORMAT = Dynamic;


            CREATE TABLE `safety_issue_type`  (
                                                  `pid` bigint(20) NOT NULL COMMENT 'pid',
                                                  `id` bigint(20) NOT NULL COMMENT 'ID',
                                                  `dept_id` int(11) NOT NULL COMMENT '组织机构ID',
                                                  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '编码',
                                                  `level` int(11) NOT NULL COMMENT '层级',
                                                  `name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '类别名称',
                                                  `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
                                                  `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
                                                  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                                                  PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '安全问题类型表' ROW_FORMAT = Dynamic;


            CREATE TABLE `safety_part`  (
                                            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                            `dept_id` int(11) NOT NULL COMMENT '组织机构ID',
                                            `pid` int(11) NOT NULL COMMENT '父节点ID',
                                            `code` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '编码',
                                            `name` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '部位名称',
                                            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
                                            `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
                                            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                                            PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 615 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '安全检测部位表' ROW_FORMAT = Dynamic;


            CREATE TABLE `safety_rectify_details`  (
                                                       `id` int(11) NOT NULL AUTO_INCREMENT,
                                                       `rectify_id` int(11) NULL DEFAULT NULL COMMENT '整改通知单id',
                                                       `check_id` int(11) NULL DEFAULT NULL COMMENT '问题id',
                                                       `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
                                                       `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                                                       `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                                       PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 28 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '整改通知单详情' ROW_FORMAT = Dynamic;


            CREATE TABLE `safety_rectify_info`  (
                                                    `id` int(11) NOT NULL AUTO_INCREMENT,
                                                    `dept_id` int(11) NULL DEFAULT NULL COMMENT '组织机构id',
                                                    `corp_id` int(11) NULL DEFAULT NULL COMMENT '合作单位id',
                                                    `corp_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合作单位名称',
                                                    `question_num` int(11) NULL DEFAULT NULL COMMENT '问题数量',
                                                    `create_user_id` int(11) NULL DEFAULT NULL COMMENT '创建人id',
                                                    `create_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
                                                    `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
                                                    `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                                                    `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                                    PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '整改通知单' ROW_FORMAT = Dynamic;


            CREATE TABLE `safety_task`  (
                                            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                            `dept_id` int(11) NOT NULL COMMENT '组织机构id',
                                            `title` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务名称',
                                            `content` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '检查说明',
                                            `state` int(11) NOT NULL DEFAULT 0 COMMENT '状态；0-未发布，1-未开始，2-进行中，3-已完成',
                                            `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标识，0-未删除，1-已删除',
                                            `publish_user_id` int(11) NULL DEFAULT NULL COMMENT '发布认id',
                                            `publish_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发布人姓名',
                                            `publish_time` datetime(0) NULL DEFAULT NULL COMMENT '发布时间',
                                            `update_user_id` int(11) NULL DEFAULT NULL COMMENT '更新人id',
                                            `update_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人姓名',
                                            `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                                            `create_user_id` int(11) NOT NULL COMMENT '创建人id',
                                            `create_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人姓名',
                                            `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                            PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '检查任务' ROW_FORMAT = Dynamic;


            CREATE TABLE `safety_task_item`  (
                                                 `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                                 `task_id` int(11) NOT NULL COMMENT '检查任务id',
                                                 `check_user_id` int(11) NOT NULL COMMENT '检查人id',
                                                 `check_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '检查人姓名',
                                                 `check_type` int(11) NOT NULL COMMENT '检查类型；1-单次检查；2-每日检查',
                                                 `state` int(11) NOT NULL DEFAULT 0 COMMENT '状态；0-未开始；1-进行中；2-已完成',
                                                 `overdue` int(11) NOT NULL DEFAULT 0 COMMENT '是否超期；0-未超期，1-超期',
                                                 `progress` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '进度',
                                                 `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标识；0-未删除；1-已删除',
                                                 `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                                                 `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                                 PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '检查任务项' ROW_FORMAT = Dynamic;


            CREATE TABLE `safety_task_item_exec`  (
                                                      `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                                      `dept_id` int(11) NOT NULL COMMENT '组织机构ID',
                                                      `task_id` int(11) NULL DEFAULT NULL COMMENT '检查任务id',
                                                      `task_item_id` int(11) NULL DEFAULT NULL COMMENT '检查任务项id',
                                                      `part_id` int(11) NOT NULL COMMENT '部位id',
                                                      `part_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '部位名称',
                                                      `require_start_time` datetime(0) NULL DEFAULT NULL COMMENT '任务开始时间',
                                                      `require_end_time` datetime(0) NULL DEFAULT NULL COMMENT '任务结束时间',
                                                      `check_time` datetime(0) NULL DEFAULT NULL COMMENT '检查时间',
                                                      `check_user_id` int(11) NULL DEFAULT NULL COMMENT '检查人id',
                                                      `check_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '检查人姓名',
                                                      `check_result` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '检查结果',
                                                      `overdue` int(11) NOT NULL DEFAULT 0 COMMENT '是否超期；0-未超期，1-超期',
                                                      `task_type` int(11) NOT NULL DEFAULT 0 COMMENT '检查类型,0-任务下发，1-日常上报',
                                                      `lng` double NULL DEFAULT NULL COMMENT '经度',
                                                      `lat` double NULL DEFAULT NULL COMMENT '纬度',
                                                      `location` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地址',
                                                      `state` int(11) NOT NULL DEFAULT 0 COMMENT '状态；0-未开始，1-进行中，2-已完成',
                                                      `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标识；0-未删除；1-已删除',
                                                      `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                                                      `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                                      PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 771 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '检查信息' ROW_FORMAT = Dynamic;


            CREATE TABLE `safety_task_item_img`  (
                                                     `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                                     `exec_id` int(11) NOT NULL COMMENT '问题记录ID',
                                                     `img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '图片地址',
                                                     `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标记(0-未删除 1-已删除)',
                                                     `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
                                                     `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
                                                     PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 628 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '检查信息-图片' ROW_FORMAT = Dynamic;


            CREATE TABLE `safety_task_item_part`  (
                                                      `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                                      `task_id` int(11) NOT NULL COMMENT '检查任务id',
                                                      `task_item_id` int(11) NOT NULL COMMENT '检查任务项id',
                                                      `part_id` int(11) NOT NULL COMMENT '部位id',
                                                      `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标识；0-未删除；1-已删除',
                                                      `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
                                                      `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                                      PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '检查任务项-部位' ROW_FORMAT = Dynamic;


            CREATE TABLE `safety_task_item_time`  (
                                                      `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                                      `task_id` int(11) NOT NULL COMMENT '检查任务id',
                                                      `task_item_id` int(11) NOT NULL COMMENT '检查任务项id',
                                                      `start_date` datetime(0) NOT NULL COMMENT '开始时间',
                                                      `end_date` datetime(0) NOT NULL COMMENT '结束时间',
                                                      `del_flag` int(11) NOT NULL DEFAULT 0 COMMENT '删除标识；0-未删除；1-已删除',
                                                      `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                                                      `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                                      PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '检查任务项-时间段' ROW_FORMAT = Dynamic;

        </sql>
    </changeSet>

    <changeSet id="3" author="qzexing">
        <sql>
            CREATE TABLE `work_permit`
            (
                `id`                       int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                `dept_id`                  int(11) NOT NULL COMMENT '项目ID',
                `guid`                     varchar(32)  NOT NULL COMMENT 'GUID',
                `code`                     varchar(32) NULL COMMENT '编号',
                `level_id`                 int(11) NULL DEFAULT NULL COMMENT '安全等级ID',
                `level_name`               varchar(32) NULL COMMENT '安全等级',
                `work_type_id`             int(11) NULL DEFAULT NULL COMMENT '作业类型ID',
                `work_type_name`           varchar(64) NULL COMMENT '作业类型名称',
                `work_start_time`          datetime(0) NULL DEFAULT NULL COMMENT '作业开始时间',
                `work_end_time`            datetime(0) NULL DEFAULT NULL COMMENT '作业截止时间',
                `rest_period`              varchar(64) NULL COMMENT '休息时间段',
                `work_unit_id`             int(11) NULL DEFAULT NULL COMMENT '作业单位ID',
                `work_unit_name`           varchar(128) NULL COMMENT '作业单位名称',
                `work_content`             varchar(64) NULL COMMENT '作业内容',
                `work_area_id`             int(11) NULL DEFAULT NULL COMMENT '作业区域iD',
                `work_area_name`           varchar(64) NULL COMMENT '作业区域',
                `worker_num`               int(11) NULL DEFAULT NULL COMMENT '作业人数',
                `lng`                      double(11, 6) NULL DEFAULT NULL COMMENT '经度',
                `lat`                      double(11, 6) NULL DEFAULT NULL COMMENT '纬度',
                `address`                  varchar(100) NULL COMMENT '地址',
                `tool`                     varchar(64) NULL COMMENT '工器具',
                `special_operation_person` varchar(64) NULL COMMENT '特种作业人员',
                `guardian_name`            varchar(32) NULL COMMENT '监护人姓名',
                `guardian_contact`         varchar(16) NULL COMMENT '监护人联系方式',
                `apply_user_id`            int(11) NULL DEFAULT NULL COMMENT '申请人ID',
                `apply_user_name`          varchar(32) NULL COMMENT '申请人姓名',
                `apply_user_contact`       varchar(16) NULL COMMENT '申请人联系方式',
                `apply_user_sign`          varchar(200) NULL COMMENT '申请人签名',
                `apply_time`               datetime(0) NULL DEFAULT NULL COMMENT '申请时间',
                `issue_user_id`            int(11) NULL DEFAULT NULL COMMENT '签发人ID',
                `issue_user_name`          varchar(32) NULL COMMENT '签发人姓名',
                `issue_user_contact`       varchar(16) NULL COMMENT '签发人联系方式',
                `issue_user_sign`          varchar(200) NULL COMMENT '签发人签名',
                `issue_time`               datetime(0) NULL DEFAULT NULL COMMENT '签发时间',
                `site_user_id`             int(11) NULL DEFAULT NULL COMMENT '现场管理人员ID',
                `site_user_name`           varchar(32) NULL COMMENT '现场管理人员姓名',
                `safety_user_id`           int(11) NULL DEFAULT NULL COMMENT '安全监督人员ID',
                `safety_user_name`         varchar(32) NULL COMMENT '安全监督人员姓名',
                `gas_detection`            text NULL COMMENT '气体检测',
                `risk_recognition`         text NULL COMMENT '风险识别',
                `control_measure`          text NULL COMMENT '管控措施',
                `pre_work_train`           text NULL COMMENT '班前培训',
                `in_work_inspect`          text NULL COMMENT '作业检查',
                `protective_measure`       text NULL COMMENT '保护措施',
                `state`                    int(11) NULL DEFAULT 0 COMMENT '状态  0-草稿 10-待签发 11-已打回 20-作业中 90-关闭中 100-已结束',
                `del_flag`                 int(11) NOT NULL DEFAULT 0 COMMENT '删除标记:0-未删除 1-已删除',
                `update_time`              timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                `create_time`              timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 COMMENT = '作业票' ROW_FORMAT = Dynamic;

            CREATE TABLE `work_permit_img`
            (
                `id`          int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                `dept_id`     int(11) NOT NULL COMMENT '项目ID',
                `permit_id`   int(11) NOT NULL COMMENT '许可证ID',
                `type`        int(11) NULL DEFAULT NULL COMMENT '类型',
                `img_url`     varchar(200) NULL COMMENT '图片',
                `del_flag`    int(11) NOT NULL DEFAULT 0 COMMENT '删除标记:0-未删除 1-已删除',
                `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 COMMENT = '作业票-照片' ROW_FORMAT = Dynamic;


            CREATE TABLE `work_permit_op_log`
            (
                `id`           int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                `dept_id`      int(11) NOT NULL COMMENT '项目ID',
                `obj_type`     int(11) NOT NULL COMMENT '业务对象类型',
                `obj_id`       int(11) NOT NULL COMMENT '业务对象ID',
                `type`         int(11) NOT NULL COMMENT '操作类型',
                `name`         varchar(32) NULL COMMENT '操作名称',
                `result`       int(11) NOT NULL COMMENT '操作结果:1-通过 2-不通过',
                `op_state`     int(11) NOT NULL DEFAULT 0 COMMENT '操作状态:0-未操作 1-已操作',
                `op_time`      datetime(0) NULL DEFAULT NULL COMMENT '操作时间',
                `op_user_id`   int(11) NULL DEFAULT NULL COMMENT '操作用户',
                `op_user_name` varchar(32) NULL COMMENT '操作用户名称',
                `remark`       varchar(128) NULL COMMENT '操作备注',
                `position`     varchar(100) NULL COMMENT '职位',
                `sign`         varchar(255) NULL COMMENT '签名图片',
                `del_flag`     int(11) NOT NULL DEFAULT 0 COMMENT '删除标记',
                `update_time`  datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                `create_time`  datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 714 COMMENT = '作业票-操作日志' ROW_FORMAT = Dynamic;

            CREATE TABLE `work_permit_user`
            (
                `id`          int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                `dept_id`     int(11) NOT NULL COMMENT '项目ID',
                `permit_id`   int(11) NOT NULL COMMENT '许可证ID',
                `type`        int(11) NULL DEFAULT NULL COMMENT '类型',
                `user_id`     int(11) NULL DEFAULT NULL COMMENT '用户ID',
                `user_name`   varchar(32) NULL COMMENT '用户姓名',
                `del_flag`    int(11) NOT NULL DEFAULT 0 COMMENT '删除标记:0-未删除 1-已删除',
                `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 COMMENT = '作业票-参与用户' ROW_FORMAT = Dynamic;


            CREATE TABLE `work_permit_config`
            (
                `id`                 int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                `dept_id`            int(11) NOT NULL COMMENT '组织机构ID',
                `work_type_id`       varchar(30)  NOT NULL COMMENT '作业票类型',
                `work_type_name`     varchar(50) NULL COMMENT '类型名称',
                `gas_detection`      text NULL COMMENT '气体检测',
                `risk_recognition`   text NULL COMMENT '风险识别',
                `control_measure`    text NULL COMMENT '管控措施',
                `pre_work_train`     text NULL COMMENT '班前培训',
                `in_work_inspect`    text NULL COMMENT '作业检查',
                `protective_measure` text NULL COMMENT '保护措施',
                `del_flag`           int(11) NOT NULL DEFAULT 0 COMMENT '删除标记:0-未删除 1-已删除',
                `update_time`        timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                `create_time`        timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 COMMENT = '作业票配置' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="4" author="qzexing">
        <comment>新建作业票绑定巡查</comment>
        <sql>
            CREATE TABLE `work_permit_inspection`
            (
                `id`            int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                `permit_id`     int(11) NOT NULL COMMENT '作业票ID',
                `type`          int(11) NOT NULL COMMENT '巡查类型  1-现场巡查  2-安全巡查',
                `inspection_id` int(11) NOT NULL COMMENT '巡查ID',
                `remark`        varchar(255) NULL COMMENT '检查说明',
                `del_flag`      int(11) NOT NULL DEFAULT 0 COMMENT '删除标记:0-未删除 1-已删除',
                `update_time`   timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                `create_time`   timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 COMMENT = '作业票巡查' ROW_FORMAT = Dynamic;
        </sql>
    </changeSet>

    <changeSet id="5" author="qzexing">
        <comment>修改字段注释</comment>
        <sql>
            ALTER TABLE `work_permit`
                ADD COLUMN `site_user_sign` varchar(200) NULL COMMENT '现场管理人员签名' AFTER `site_user_name`,
                ADD COLUMN `safety_user_sign` varchar(200) NULL COMMENT '安全监督人员签名' AFTER `safety_user_name`;

            ALTER TABLE `work_permit_inspection`
                MODIFY COLUMN `type` int(11) NOT NULL COMMENT '巡查类型  1-安全巡查  2-隐患提交' AFTER `permit_id`;
        </sql>
    </changeSet>

    <changeSet id="6" author="qzexing">
        <comment>修改休息时间段字段类型</comment>
        <sql>
            ALTER TABLE `work_permit`
                MODIFY COLUMN `rest_period` VARCHAR(1000) NULL COMMENT '休息时间段' AFTER `work_end_time`;
        </sql>
    </changeSet>

</databaseChangeLog>
