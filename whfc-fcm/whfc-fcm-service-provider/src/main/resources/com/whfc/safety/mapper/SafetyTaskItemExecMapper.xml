<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.safety.dao.SafetyTaskItemExecMapper">
  <resultMap id="BaseResultMap" type="com.whfc.safety.entity.SafetyTaskItemExec">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="task_id" jdbcType="INTEGER" property="taskId" />
    <result column="task_item_id" jdbcType="INTEGER" property="taskItemId" />
    <result column="part_id" jdbcType="INTEGER" property="partId" />
    <result column="part_name" jdbcType="VARCHAR" property="partName" />
    <result column="require_start_time" jdbcType="TIMESTAMP" property="requireStartTime" />
    <result column="require_end_time" jdbcType="TIMESTAMP" property="requireEndTime" />
    <result column="check_time" jdbcType="TIMESTAMP" property="checkTime" />
    <result column="check_user_id" jdbcType="INTEGER" property="checkUserId" />
    <result column="check_user_name" jdbcType="VARCHAR" property="checkUserName" />
    <result column="check_result" jdbcType="VARCHAR" property="checkResult" />
    <result column="overdue" jdbcType="INTEGER" property="overdue" />
    <result column="task_type" jdbcType="INTEGER" property="taskType" />
    <result column="lng" jdbcType="DOUBLE" property="lng" />
    <result column="lat" jdbcType="DOUBLE" property="lat" />
    <result column="location" jdbcType="VARCHAR" property="location" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, dept_id, task_id, task_item_id, part_id, part_name, require_start_time, require_end_time, 
    check_time, check_user_id, check_user_name, check_result, overdue, task_type, lng, 
    lat, location, state, del_flag, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from safety_task_item_exec
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from safety_task_item_exec
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.whfc.safety.entity.SafetyTaskItemExec">
    insert into safety_task_item_exec (id, dept_id, task_id, 
      task_item_id, part_id, part_name, 
      require_start_time, require_end_time, check_time, 
      check_user_id, check_user_name, check_result, 
      overdue, task_type, lng, 
      lat, location, state, 
      del_flag, update_time, create_time
      )
    values (#{id,jdbcType=INTEGER}, #{deptId,jdbcType=INTEGER}, #{taskId,jdbcType=INTEGER}, 
      #{taskItemId,jdbcType=INTEGER}, #{partId,jdbcType=INTEGER}, #{partName,jdbcType=VARCHAR}, 
      #{requireStartTime,jdbcType=TIMESTAMP}, #{requireEndTime,jdbcType=TIMESTAMP}, #{checkTime,jdbcType=TIMESTAMP}, 
      #{checkUserId,jdbcType=INTEGER}, #{checkUserName,jdbcType=VARCHAR}, #{checkResult,jdbcType=VARCHAR}, 
      #{overdue,jdbcType=INTEGER}, #{taskType,jdbcType=INTEGER}, #{lng,jdbcType=DOUBLE}, 
      #{lat,jdbcType=DOUBLE}, #{location,jdbcType=VARCHAR}, #{state,jdbcType=INTEGER}, 
      #{delFlag,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.whfc.safety.entity.SafetyTaskItemExec" keyProperty="id" useGeneratedKeys="true">
    insert into safety_task_item_exec
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="taskId != null">
        task_id,
      </if>
      <if test="taskItemId != null">
        task_item_id,
      </if>
      <if test="partId != null">
        part_id,
      </if>
      <if test="partName != null">
        part_name,
      </if>
      <if test="requireStartTime != null">
        require_start_time,
      </if>
      <if test="requireEndTime != null">
        require_end_time,
      </if>
      <if test="checkTime != null">
        check_time,
      </if>
      <if test="checkUserId != null">
        check_user_id,
      </if>
      <if test="checkUserName != null">
        check_user_name,
      </if>
      <if test="checkResult != null">
        check_result,
      </if>
      <if test="overdue != null">
        overdue,
      </if>
      <if test="taskType != null">
        task_type,
      </if>
      <if test="lng != null">
        lng,
      </if>
      <if test="lat != null">
        lat,
      </if>
      <if test="location != null">
        location,
      </if>
      <if test="state != null">
        state,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=INTEGER},
      </if>
      <if test="taskItemId != null">
        #{taskItemId,jdbcType=INTEGER},
      </if>
      <if test="partId != null">
        #{partId,jdbcType=INTEGER},
      </if>
      <if test="partName != null">
        #{partName,jdbcType=VARCHAR},
      </if>
      <if test="requireStartTime != null">
        #{requireStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="requireEndTime != null">
        #{requireEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="checkTime != null">
        #{checkTime,jdbcType=TIMESTAMP},
      </if>
      <if test="checkUserId != null">
        #{checkUserId,jdbcType=INTEGER},
      </if>
      <if test="checkUserName != null">
        #{checkUserName,jdbcType=VARCHAR},
      </if>
      <if test="checkResult != null">
        #{checkResult,jdbcType=VARCHAR},
      </if>
      <if test="overdue != null">
        #{overdue,jdbcType=INTEGER},
      </if>
      <if test="taskType != null">
        #{taskType,jdbcType=INTEGER},
      </if>
      <if test="lng != null">
        #{lng,jdbcType=DOUBLE},
      </if>
      <if test="lat != null">
        #{lat,jdbcType=DOUBLE},
      </if>
      <if test="location != null">
        #{location,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        #{state,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.whfc.safety.entity.SafetyTaskItemExec">
    update safety_task_item_exec
    <set>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=INTEGER},
      </if>
      <if test="taskItemId != null">
        task_item_id = #{taskItemId,jdbcType=INTEGER},
      </if>
      <if test="partId != null">
        part_id = #{partId,jdbcType=INTEGER},
      </if>
      <if test="partName != null">
        part_name = #{partName,jdbcType=VARCHAR},
      </if>
      <if test="requireStartTime != null">
        require_start_time = #{requireStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="requireEndTime != null">
        require_end_time = #{requireEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="checkTime != null">
        check_time = #{checkTime,jdbcType=TIMESTAMP},
      </if>
      <if test="checkUserId != null">
        check_user_id = #{checkUserId,jdbcType=INTEGER},
      </if>
      <if test="checkUserName != null">
        check_user_name = #{checkUserName,jdbcType=VARCHAR},
      </if>
      <if test="checkResult != null">
        check_result = #{checkResult,jdbcType=VARCHAR},
      </if>
      <if test="overdue != null">
        overdue = #{overdue,jdbcType=INTEGER},
      </if>
      <if test="taskType != null">
        task_type = #{taskType,jdbcType=INTEGER},
      </if>
      <if test="lng != null">
        lng = #{lng,jdbcType=DOUBLE},
      </if>
      <if test="lat != null">
        lat = #{lat,jdbcType=DOUBLE},
      </if>
      <if test="location != null">
        location = #{location,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        state = #{state,jdbcType=INTEGER},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.whfc.safety.entity.SafetyTaskItemExec">
    update safety_task_item_exec
    set dept_id = #{deptId,jdbcType=INTEGER},
      task_id = #{taskId,jdbcType=INTEGER},
      task_item_id = #{taskItemId,jdbcType=INTEGER},
      part_id = #{partId,jdbcType=INTEGER},
      part_name = #{partName,jdbcType=VARCHAR},
      require_start_time = #{requireStartTime,jdbcType=TIMESTAMP},
      require_end_time = #{requireEndTime,jdbcType=TIMESTAMP},
      check_time = #{checkTime,jdbcType=TIMESTAMP},
      check_user_id = #{checkUserId,jdbcType=INTEGER},
      check_user_name = #{checkUserName,jdbcType=VARCHAR},
      check_result = #{checkResult,jdbcType=VARCHAR},
      overdue = #{overdue,jdbcType=INTEGER},
      task_type = #{taskType,jdbcType=INTEGER},
      lng = #{lng,jdbcType=DOUBLE},
      lat = #{lat,jdbcType=DOUBLE},
      location = #{location,jdbcType=VARCHAR},
      state = #{state,jdbcType=INTEGER},
      del_flag = #{delFlag,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectUserNum" resultType="com.whfc.safety.dto.SafetyUserNumDTO">
    SELECT check_user_name AS userName,
            COUNT( 0 ) AS num
    FROM  safety_task_item_exec
    WHERE state = 2
      AND dept_id = #{deptId}
      AND date( check_time ) = date( #{date} )
    ORDER BY check_user_id
  </select>

  <insert id="insertAll">
    INSERT INTO safety_task_item_exec
    (
    dept_id,
    task_id,
    task_item_id,
    part_id,
    part_name,
    task_type,
    check_user_id,
    check_user_name,
    require_start_time,
    require_end_time
    ) VALUES
    <foreach collection="itemParts" item="part" separator=",">
      <foreach collection="itemTimes" item="time" separator=",">
        (
        #{deptId},
        #{taskId},
        #{taskItem.id},
        #{part.partId},
        #{part.partName},
        #{taskItem.checkType},
        #{taskItem.checkUserId},
        #{taskItem.checkUserName},
        #{time.startDate},
        #{time.endDate})
      </foreach>
    </foreach>
  </insert>

  <select id="selectByDeptId" resultType="com.whfc.safety.dto.SafetyTaskExecDTO">
    SELECT  qtie.id as execId,
            qtie.task_id,
            qt.title,
            qtie.check_time,
            qtie.part_name,
            qtie.part_id,
            qtie.check_user_name as checkName,
            qtie.task_type,
            qt.content,
            qt.publish_time,
            qtie.check_result,
            qtie.require_start_time as startTime,
            qtie.require_end_time as endTime,
            qtie.overdue,
            qtie.lat,
            qtie.lng,
            qtie.location,
            qtie.state
    FROM safety_task_item_exec qtie
    INNER JOIN safety_task qt on qt.id = qtie.task_id
    where qtie.del_flag = 0
    and qt.dept_id = #{deptId}
    <if test="userId != null">
      and qtie.check_user_id = #{userId}
    </if>
    <if test="keyword != null">
      and qt.title like concat('%',#{keyword},'%')
    </if>
    <if test="overdue != null">
      and qtie.overdue = #{overdue}
    </if>
    <if test="partId != null">
      and qtie.part_id = #{partId}
    </if>
    <if test="state != null">
      and qtie.state = #{state}
    </if>
    <if test="startTime != null and endTime != null">
      AND qtie.check_time BETWEEN #{startTime} and #{endTime}
    </if>
  </select>

  <select id="selectYesterday" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM safety_task_item_exec
    WHERE require_end_time &lt;= DATE_ADD( str_to_date( DATE_FORMAT( NOW(), '%Y-%m-%d' ), '%Y-%m-%d 23:59:59' ), INTERVAL
    - 1 SECOND )
    AND state = 1
    AND del_flag = 0
  </select>

  <update id="updateOverdue">
    UPDATE safety_task_item_exec
    SET overdue = 1
    WHERE id in
    <foreach close=")" collection="list" item="id" open="(" separator=",">
      #{id}
    </foreach>
  </update>

  <select id="selectToday" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM safety_task_item_exec
    WHERE
    require_start_time &lt;=DATE_ADD( DATE_ADD(str_to_date( DATE_FORMAT( NOW(), '%Y-%m-%d' ), '%Y-%m-%d 59:59:59'
    ),INTERVAL 1 day), INTERVAL - 1 SECOND )
    AND state = 0
    AND del_flag = 0
  </select>

  <update id="updateStateByExecIds">
    UPDATE safety_task_item_exec
    SET state = 1
    WHERE id IN
    <foreach close=")" collection="list" item="id" open="(" separator=",">
      #{id}
    </foreach>
  </update>

  <select id="selectUnfinishedByTaskItemId" resultType="com.whfc.safety.entity.SafetyTaskItemExec">
    SELECT
    <include refid="Base_Column_List"/>
    FROM safety_task_item_exec
    WHERE task_item_id = #{taskItemId}
    AND state IN ( 0, 1)
  </select>

  <select id="selectExecNumByTaskItemId" resultType="com.whfc.safety.dto.SafetyExecNumDTO">
    SELECT  COUNT(0) as checkNum,
            SUM(if(check_result='隐患问题',1,0)) as issueNum,
            SUM(if(state=2 ,1,0)) as screenNum,
            SUM(if(state=1 ,1,0)) as wayNum
    FROM  safety_task_item_exec
    where task_item_id = #{taskItemId}
  </select>

  <select id="selectExecNumByDeptId" resultType="com.whfc.safety.dto.SafetyExecNumDTO">
    SELECT  COUNT( 0 ) AS checkNum,
            SUM(IF(qtie.state = 0, 1, 0 )) AS notStartedNum,
            SUM(IF(qtie.state = 2, 1, 0 )) AS doneNum,
            SUM(IF(qtie.state = 1, 1, 0 )) AS underwayNum
    FROM safety_task_item_exec qtie
    INNER JOIN safety_task qt ON qt.id = qtie.task_id
    WHERE qtie.del_flag = 0
    and qt.dept_id = #{deptId}
    <if test="userId != null">
      and qtie.check_user_id = #{userId}
    </if>
    <if test="keyword != null">
      and qt.title like concat('%',#{keyword},'%')
    </if>
    <if test="overdue != null">
      and qtie.overdue = #{overdue}
    </if>
    <if test="startTime != null and endTime != null">
      AND qtie.check_time BETWEEN #{startTime} and #{endTime}
    </if>

  </select>

  <select id="selectMeNum" resultType="com.whfc.safety.dto.SafetyExecNumDTO">
    select  sum(if(qtie.check_user_id = #{userId},1,0)) as meNum,
            COUNT(0) as checkNum
    from safety_task_item_exec qtie
    where qtie.del_flag = 0
    and qtie.state =2
    and qtie.dept_id = #{deptId}
    <if test="partId != null">
      and qtie.part_id = #{partId}
    </if>
    <if test="startTime != null and endTime != null">
      AND qtie.check_time BETWEEN #{startTime} and #{endTime}
    </if>
  </select>

  <select id="selectDetailsByTaskItemId" resultType="com.whfc.safety.dto.SafetyExecDetailsDTO">
    SELECT id as execId,
            check_user_name,
            check_time,
            part_name,
            state,
            check_result,
            overdue,
            lat,
            lng,
            location
    FROM safety_task_item_exec
    where task_item_id = #{taskItemId}
  </select>

  <select id="selectUnfinishedByTaskId" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM safety_task_item_exec
    WHERE task_id = #{taskId}
    AND state IN ( 0, 1)
  </select>

  <select id="selectByTaskId" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM safety_task_item_exec
    WHERE require_start_time &lt;= DATE_ADD( DATE_ADD( str_to_date( DATE_FORMAT( NOW(), '%Y-%m-%d' ), '%Y-%m-%d 59:59:59'
    ), INTERVAL 1 DAY ), INTERVAL - 1 SECOND )
    AND state = 0
    AND del_flag = 0
    AND task_id = #{taskId}
  </select>

  <select id="selectByDeptIdAndUserId" resultType="com.whfc.safety.dto.SafetyTaskExecDTO">
    SELECT  qtie.id as execId,
            qtie.task_id,
            qtie.task_id,
            qtie.check_time,
            qtie.part_name,
            qtie.part_id,
            qtie.check_user_name as checkName,
            qtie.task_type,
            qtie.check_result,
            qtie.overdue,
            qtie.lat,
            qtie.lng,
            qtie.location,
            qtie.state
    FROM safety_task_item_exec qtie
    where qtie.del_flag = 0
    and qtie.dept_id = #{deptId}
    <if test="userId != null">
      and qtie.check_user_id = #{userId}
    </if>
    <if test="partId != null">
      and qtie.part_id = #{partId}
    </if>
    <if test="state != null">
      and qtie.state = #{state}
    </if>
    <if test="startTime != null and endTime != null">
      AND qtie.check_time BETWEEN #{startTime} and #{endTime}
    </if>
    order by qtie.check_time desc
  </select>

  <select id="selectNumByDeptId" resultType="java.lang.Integer">
    select count(0)
    from safety_task_item_exec
    where del_flag = 0
    and dept_id in (
    <foreach collection="deptIdList" item="deptId" separator=",">
      #{deptId}
    </foreach>
    )
    <if test="startTime != null and endTime != null">
      and check_time >= #{startTime}
      and #{endTime} >= check_time
    </if>
  </select>

  <update id="logicDel">
    UPDATE safety_task_item_exec SET del_flag = 1 WHERE id = #{execId}
  </update>

  <select id="selectByExecIds" resultType="com.whfc.safety.dto.SafetyExecDetailsDTO">
      SELECT id as execId,
      check_user_name,
      check_time,
      part_name,
      state,
      check_result,
      overdue,
      lat,
      lng,
      location
      FROM safety_task_item_exec
      where del_flag = 0
      and id in
      <foreach collection="execIds" item="execId" separator="," open="(" close=")">
          #{execId}
      </foreach>
  </select>

</mapper>