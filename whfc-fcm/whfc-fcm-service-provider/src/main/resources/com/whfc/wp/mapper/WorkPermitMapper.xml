<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.wp.dao.WorkPermitMapper">
    <resultMap id="BaseResultMap" type="com.whfc.wp.entity.WorkPermit">
        <!--@mbg.generated-->
        <!--@Table work_permit-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="dept_id" jdbcType="INTEGER" property="deptId"/>
        <result column="guid" jdbcType="VARCHAR" property="guid"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="level_id" jdbcType="INTEGER" property="levelId"/>
        <result column="level_name" jdbcType="VARCHAR" property="levelName"/>
        <result column="work_type_id" jdbcType="INTEGER" property="workTypeId"/>
        <result column="work_type_name" jdbcType="VARCHAR" property="workTypeName"/>
        <result column="work_start_time" jdbcType="TIMESTAMP" property="workStartTime"/>
        <result column="work_end_time" jdbcType="TIMESTAMP" property="workEndTime"/>
        <result column="rest_period" jdbcType="VARCHAR" property="restPeriod"/>
        <result column="work_unit_id" jdbcType="INTEGER" property="workUnitId"/>
        <result column="work_unit_name" jdbcType="VARCHAR" property="workUnitName"/>
        <result column="work_content" jdbcType="VARCHAR" property="workContent"/>
        <result column="work_area_id" jdbcType="INTEGER" property="workAreaId"/>
        <result column="work_area_name" jdbcType="VARCHAR" property="workAreaName"/>
        <result column="worker_num" jdbcType="INTEGER" property="workerNum"/>
        <result column="lng" jdbcType="DOUBLE" property="lng"/>
        <result column="lat" jdbcType="DOUBLE" property="lat"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="tool" jdbcType="VARCHAR" property="tool"/>
        <result column="special_operation_person" jdbcType="VARCHAR" property="specialOperationPerson"/>
        <result column="guardian_name" jdbcType="VARCHAR" property="guardianName"/>
        <result column="guardian_contact" jdbcType="VARCHAR" property="guardianContact"/>
        <result column="apply_user_id" jdbcType="INTEGER" property="applyUserId"/>
        <result column="apply_user_name" jdbcType="VARCHAR" property="applyUserName"/>
        <result column="apply_user_contact" jdbcType="VARCHAR" property="applyUserContact"/>
        <result column="apply_user_sign" jdbcType="VARCHAR" property="applyUserSign"/>
        <result column="apply_time" jdbcType="TIMESTAMP" property="applyTime"/>
        <result column="issue_user_id" jdbcType="INTEGER" property="issueUserId"/>
        <result column="issue_user_name" jdbcType="VARCHAR" property="issueUserName"/>
        <result column="issue_user_contact" jdbcType="VARCHAR" property="issueUserContact"/>
        <result column="issue_user_sign" jdbcType="VARCHAR" property="issueUserSign"/>
        <result column="issue_time" jdbcType="TIMESTAMP" property="issueTime"/>
        <result column="site_user_id" jdbcType="INTEGER" property="siteUserId"/>
        <result column="site_user_name" jdbcType="VARCHAR" property="siteUserName"/>
        <result column="site_user_sign" jdbcType="VARCHAR" property="siteUserSign"/>
        <result column="safety_user_id" jdbcType="INTEGER" property="safetyUserId"/>
        <result column="safety_user_name" jdbcType="VARCHAR" property="safetyUserName"/>
        <result column="safety_user_sign" jdbcType="VARCHAR" property="safetyUserSign"/>
        <result column="gas_detection" jdbcType="LONGVARCHAR" property="gasDetection"/>
        <result column="risk_recognition" jdbcType="LONGVARCHAR" property="riskRecognition"/>
        <result column="control_measure" jdbcType="LONGVARCHAR" property="controlMeasure"/>
        <result column="pre_work_train" jdbcType="LONGVARCHAR" property="preWorkTrain"/>
        <result column="in_work_inspect" jdbcType="LONGVARCHAR" property="inWorkInspect"/>
        <result column="protective_measure" jdbcType="LONGVARCHAR" property="protectiveMeasure"/>
        <result column="state" jdbcType="INTEGER" property="state"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        dept_id,
        guid,
        code,
        level_id,
        level_name,
        work_type_id,
        work_type_name,
        work_start_time,
        work_end_time,
        rest_period,
        work_unit_id,
        work_unit_name,
        work_content,
        work_area_id,
        work_area_name,
        worker_num,
        lng,
        lat,
        address,
        tool,
        special_operation_person,
        guardian_name,
        guardian_contact,
        apply_user_id,
        apply_user_name,
        apply_user_contact,
        apply_user_sign,
        apply_time,
        issue_user_id,
        issue_user_name,
        issue_user_contact,
        issue_user_sign,
        issue_time,
        site_user_id,
        site_user_name,
        site_user_sign,
        safety_user_id,
        safety_user_name,
        safety_user_sign,
        gas_detection,
        risk_recognition,
        control_measure,
        pre_work_train,
        in_work_inspect,
        protective_measure,
        `state`,
        del_flag,
        update_time,
        create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from work_permit
        where id = #{id,jdbcType=INTEGER}
    </select>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.wp.entity.WorkPermit"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into work_permit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="guid != null">
                guid,
            </if>
            <if test="code != null">
                code,
            </if>
            <if test="levelId != null">
                level_id,
            </if>
            <if test="levelName != null">
                level_name,
            </if>
            <if test="workTypeId != null">
                work_type_id,
            </if>
            <if test="workTypeName != null">
                work_type_name,
            </if>
            <if test="workStartTime != null">
                work_start_time,
            </if>
            <if test="workEndTime != null">
                work_end_time,
            </if>
            <if test="restPeriod != null">
                rest_period,
            </if>
            <if test="workUnitId != null">
                work_unit_id,
            </if>
            <if test="workUnitName != null">
                work_unit_name,
            </if>
            <if test="workContent != null">
                work_content,
            </if>
            <if test="workAreaId != null">
                work_area_id,
            </if>
            <if test="workAreaName != null">
                work_area_name,
            </if>
            <if test="workerNum != null">
                worker_num,
            </if>
            <if test="lng != null">
                lng,
            </if>
            <if test="lat != null">
                lat,
            </if>
            <if test="address != null">
                address,
            </if>
            <if test="tool != null">
                tool,
            </if>
            <if test="specialOperationPerson != null">
                special_operation_person,
            </if>
            <if test="guardianName != null">
                guardian_name,
            </if>
            <if test="guardianContact != null">
                guardian_contact,
            </if>
            <if test="applyUserId != null">
                apply_user_id,
            </if>
            <if test="applyUserName != null">
                apply_user_name,
            </if>
            <if test="applyUserContact != null">
                apply_user_contact,
            </if>
            <if test="applyUserSign != null">
                apply_user_sign,
            </if>
            <if test="applyTime != null">
                apply_time,
            </if>
            <if test="issueUserId != null">
                issue_user_id,
            </if>
            <if test="issueUserName != null">
                issue_user_name,
            </if>
            <if test="issueUserContact != null">
                issue_user_contact,
            </if>
            <if test="issueUserSign != null">
                issue_user_sign,
            </if>
            <if test="issueTime != null">
                issue_time,
            </if>
            <if test="siteUserId != null">
                site_user_id,
            </if>
            <if test="siteUserName != null">
                site_user_name,
            </if>
            <if test="siteUserSign != null">
                site_user_sign,
            </if>
            <if test="safetyUserId != null">
                safety_user_id,
            </if>
            <if test="safetyUserName != null">
                safety_user_name,
            </if>
            <if test="safetyUserSign != null">
                safety_user_sign,
            </if>
            <if test="gasDetection != null">
                gas_detection,
            </if>
            <if test="riskRecognition != null">
                risk_recognition,
            </if>
            <if test="controlMeasure != null">
                control_measure,
            </if>
            <if test="preWorkTrain != null">
                pre_work_train,
            </if>
            <if test="inWorkInspect != null">
                in_work_inspect,
            </if>
            <if test="protectiveMeasure != null">
                protective_measure,
            </if>
            <if test="state != null">
                `state`,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                #{deptId,jdbcType=INTEGER},
            </if>
            <if test="guid != null">
                #{guid,jdbcType=VARCHAR},
            </if>
            <if test="code != null">
                #{code,jdbcType=VARCHAR},
            </if>
            <if test="levelId != null">
                #{levelId,jdbcType=INTEGER},
            </if>
            <if test="levelName != null">
                #{levelName,jdbcType=VARCHAR},
            </if>
            <if test="workTypeId != null">
                #{workTypeId,jdbcType=INTEGER},
            </if>
            <if test="workTypeName != null">
                #{workTypeName,jdbcType=VARCHAR},
            </if>
            <if test="workStartTime != null">
                #{workStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="workEndTime != null">
                #{workEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="restPeriod != null">
                #{restPeriod,jdbcType=VARCHAR},
            </if>
            <if test="workUnitId != null">
                #{workUnitId,jdbcType=INTEGER},
            </if>
            <if test="workUnitName != null">
                #{workUnitName,jdbcType=VARCHAR},
            </if>
            <if test="workContent != null">
                #{workContent,jdbcType=VARCHAR},
            </if>
            <if test="workAreaId != null">
                #{workAreaId,jdbcType=INTEGER},
            </if>
            <if test="workAreaName != null">
                #{workAreaName,jdbcType=VARCHAR},
            </if>
            <if test="workerNum != null">
                #{workerNum,jdbcType=INTEGER},
            </if>
            <if test="lng != null">
                #{lng,jdbcType=DOUBLE},
            </if>
            <if test="lat != null">
                #{lat,jdbcType=DOUBLE},
            </if>
            <if test="address != null">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="tool != null">
                #{tool,jdbcType=VARCHAR},
            </if>
            <if test="specialOperationPerson != null">
                #{specialOperationPerson,jdbcType=VARCHAR},
            </if>
            <if test="guardianName != null">
                #{guardianName,jdbcType=VARCHAR},
            </if>
            <if test="guardianContact != null">
                #{guardianContact,jdbcType=VARCHAR},
            </if>
            <if test="applyUserId != null">
                #{applyUserId,jdbcType=INTEGER},
            </if>
            <if test="applyUserName != null">
                #{applyUserName,jdbcType=VARCHAR},
            </if>
            <if test="applyUserContact != null">
                #{applyUserContact,jdbcType=VARCHAR},
            </if>
            <if test="applyUserSign != null">
                #{applyUserSign,jdbcType=VARCHAR},
            </if>
            <if test="applyTime != null">
                #{applyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="issueUserId != null">
                #{issueUserId,jdbcType=INTEGER},
            </if>
            <if test="issueUserName != null">
                #{issueUserName,jdbcType=VARCHAR},
            </if>
            <if test="issueUserContact != null">
                #{issueUserContact,jdbcType=VARCHAR},
            </if>
            <if test="issueUserSign != null">
                #{issueUserSign,jdbcType=VARCHAR},
            </if>
            <if test="issueTime != null">
                #{issueTime,jdbcType=TIMESTAMP},
            </if>
            <if test="siteUserId != null">
                #{siteUserId,jdbcType=INTEGER},
            </if>
            <if test="siteUserName != null">
                #{siteUserName,jdbcType=VARCHAR},
            </if>
            <if test="siteUserSign != null">
                #{siteUserSign,jdbcType=VARCHAR},
            </if>
            <if test="safetyUserId != null">
                #{safetyUserId,jdbcType=INTEGER},
            </if>
            <if test="safetyUserName != null">
                #{safetyUserName,jdbcType=VARCHAR},
            </if>
            <if test="safetyUserSign != null">
                #{safetyUserSign,jdbcType=VARCHAR},
            </if>
            <if test="gasDetection != null">
                #{gasDetection,jdbcType=LONGVARCHAR},
            </if>
            <if test="riskRecognition != null">
                #{riskRecognition,jdbcType=LONGVARCHAR},
            </if>
            <if test="controlMeasure != null">
                #{controlMeasure,jdbcType=LONGVARCHAR},
            </if>
            <if test="preWorkTrain != null">
                #{preWorkTrain,jdbcType=LONGVARCHAR},
            </if>
            <if test="inWorkInspect != null">
                #{inWorkInspect,jdbcType=LONGVARCHAR},
            </if>
            <if test="protectiveMeasure != null">
                #{protectiveMeasure,jdbcType=LONGVARCHAR},
            </if>
            <if test="state != null">
                #{state,jdbcType=INTEGER},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.wp.entity.WorkPermit">
        <!--@mbg.generated-->
        update work_permit
        <set>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=INTEGER},
            </if>
            <if test="guid != null">
                guid = #{guid,jdbcType=VARCHAR},
            </if>
            <if test="code != null">
                code = #{code,jdbcType=VARCHAR},
            </if>
            <if test="levelId != null">
                level_id = #{levelId,jdbcType=INTEGER},
            </if>
            <if test="levelName != null">
                level_name = #{levelName,jdbcType=VARCHAR},
            </if>
            <if test="workTypeId != null">
                work_type_id = #{workTypeId,jdbcType=INTEGER},
            </if>
            <if test="workTypeName != null">
                work_type_name = #{workTypeName,jdbcType=VARCHAR},
            </if>
            <if test="workStartTime != null">
                work_start_time = #{workStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="workEndTime != null">
                work_end_time = #{workEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="restPeriod != null">
                rest_period = #{restPeriod,jdbcType=VARCHAR},
            </if>
            <if test="workUnitId != null">
                work_unit_id = #{workUnitId,jdbcType=INTEGER},
            </if>
            <if test="workUnitName != null">
                work_unit_name = #{workUnitName,jdbcType=VARCHAR},
            </if>
            <if test="workContent != null">
                work_content = #{workContent,jdbcType=VARCHAR},
            </if>
            <if test="workAreaId != null">
                work_area_id = #{workAreaId,jdbcType=INTEGER},
            </if>
            <if test="workAreaName != null">
                work_area_name = #{workAreaName,jdbcType=VARCHAR},
            </if>
            <if test="workerNum != null">
                worker_num = #{workerNum,jdbcType=INTEGER},
            </if>
            <if test="lng != null">
                lng = #{lng,jdbcType=DOUBLE},
            </if>
            <if test="lat != null">
                lat = #{lat,jdbcType=DOUBLE},
            </if>
            <if test="address != null">
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="tool != null">
                tool = #{tool,jdbcType=VARCHAR},
            </if>
            <if test="specialOperationPerson != null">
                special_operation_person = #{specialOperationPerson,jdbcType=VARCHAR},
            </if>
            <if test="guardianName != null">
                guardian_name = #{guardianName,jdbcType=VARCHAR},
            </if>
            <if test="guardianContact != null">
                guardian_contact = #{guardianContact,jdbcType=VARCHAR},
            </if>
            <if test="applyUserId != null">
                apply_user_id = #{applyUserId,jdbcType=INTEGER},
            </if>
            <if test="applyUserName != null">
                apply_user_name = #{applyUserName,jdbcType=VARCHAR},
            </if>
            <if test="applyUserContact != null">
                apply_user_contact = #{applyUserContact,jdbcType=VARCHAR},
            </if>
            <if test="applyUserSign != null">
                apply_user_sign = #{applyUserSign,jdbcType=VARCHAR},
            </if>
            <if test="applyTime != null">
                apply_time = #{applyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="issueUserId != null">
                issue_user_id = #{issueUserId,jdbcType=INTEGER},
            </if>
            <if test="issueUserName != null">
                issue_user_name = #{issueUserName,jdbcType=VARCHAR},
            </if>
            <if test="issueUserContact != null">
                issue_user_contact = #{issueUserContact,jdbcType=VARCHAR},
            </if>
            <if test="issueUserSign != null">
                issue_user_sign = #{issueUserSign,jdbcType=VARCHAR},
            </if>
            <if test="issueTime != null">
                issue_time = #{issueTime,jdbcType=TIMESTAMP},
            </if>
            <if test="siteUserId != null">
                site_user_id = #{siteUserId,jdbcType=INTEGER},
            </if>
            <if test="siteUserName != null">
                site_user_name = #{siteUserName,jdbcType=VARCHAR},
            </if>
            <if test="siteUserSign != null">
                site_user_sign = #{siteUserSign,jdbcType=VARCHAR},
            </if>
            <if test="safetyUserId != null">
                safety_user_id = #{safetyUserId,jdbcType=INTEGER},
            </if>
            <if test="safetyUserName != null">
                safety_user_name = #{safetyUserName,jdbcType=VARCHAR},
            </if>
            <if test="safetyUserSign != null">
                safety_user_sign = #{safetyUserSign,jdbcType=VARCHAR},
            </if>
            <if test="gasDetection != null">
                gas_detection = #{gasDetection,jdbcType=LONGVARCHAR},
            </if>
            <if test="riskRecognition != null">
                risk_recognition = #{riskRecognition,jdbcType=LONGVARCHAR},
            </if>
            <if test="controlMeasure != null">
                control_measure = #{controlMeasure,jdbcType=LONGVARCHAR},
            </if>
            <if test="preWorkTrain != null">
                pre_work_train = #{preWorkTrain,jdbcType=LONGVARCHAR},
            </if>
            <if test="inWorkInspect != null">
                in_work_inspect = #{inWorkInspect,jdbcType=LONGVARCHAR},
            </if>
            <if test="protectiveMeasure != null">
                protective_measure = #{protectiveMeasure,jdbcType=LONGVARCHAR},
            </if>
            <if test="state != null">
                `state` = #{state,jdbcType=INTEGER},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectByGuid" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from work_permit
        where guid = #{guid,jdbcType=VARCHAR}
          and del_flag = 0
    </select>

    <select id="selectByCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from work_permit
        where dept_id = #{deptId,jdbcType=INTEGER}
          and code = #{code,jdbcType=VARCHAR}
          and del_flag = 0
    </select>

    <update id="logicDeleteById">
        update work_permit
        set del_flag = 1
        where id = #{id,jdbcType=INTEGER}
    </update>


    <select id="selectWorkPermitList" resultType="com.whfc.wp.dto.WorkPermitDTO">
        select dept_id,
               guid,
               code,
               level_id,
               level_name,
               work_type_id,
               work_type_name,
               work_start_time,
               work_end_time,
               work_unit_id,
               work_unit_name,
               work_content,
               work_area_id,
               work_area_name,
               worker_num,
               lng,
               lat,
               address,
               apply_user_id,
               apply_user_name,
               apply_time,
               issue_user_id,
               issue_user_name,
               issue_time,
               site_user_id,
               site_user_name,
               safety_user_id,
               safety_user_name,
               `state`
        from work_permit
        where dept_id = #{deptId,jdbcType=INTEGER}
          and del_flag = 0
        <if test="workTypeId != null">
            and work_type_id = #{workTypeId,jdbcType=INTEGER}
        </if>
        <if test="state != null">
            and `state` = #{state,jdbcType=INTEGER}
        </if>
        <if test="startTime != null">
            and work_start_time &gt;= #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            and work_end_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        </if>
        <if test="keyword != null and keyword != ''">
            and (code like concat('%', #{keyword,jdbcType=VARCHAR}, '%')
                or work_content like concat('%', #{keyword,jdbcType=VARCHAR}, '%'))
        </if>
        <if test="ids != null and ids.size() != 0">
            and id in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id,jdbcType=INTEGER}
            </foreach>
        </if>
        order by create_time desc
    </select>

    <update id="updateState">
        update work_permit
        set `state` = #{state,jdbcType=INTEGER}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateIssueInfo">
        update work_permit
        set issue_time       = #{issueTime,jdbcType=TIMESTAMP},
            issue_user_sign  = #{issueUserSign,jdbcType=VARCHAR},
            site_user_id     = #{siteUserId,jdbcType=INTEGER},
            site_user_name   = #{siteUserName,jdbcType=VARCHAR},
            safety_user_id   = #{safetyUserId,jdbcType=INTEGER},
            safety_user_name = #{safetyUserName,jdbcType=VARCHAR}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateProtectiveMeasure">
        update work_permit
        set protective_measure = #{protectiveMeasure,jdbcType=LONGVARCHAR}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectMyWorkPermitIdList" resultType="java.lang.Integer">
        select id
        from work_permit
        where apply_user_id = #{currUserId,jdbcType=INTEGER}
           or issue_user_id = #{currUserId,jdbcType=INTEGER}
           or site_user_id = #{currUserId,jdbcType=INTEGER}
           or safety_user_id = #{currUserId,jdbcType=INTEGER}
            and del_flag = 0
    </select>

    <select id="selectMySubmitWorkPermitIdList" resultType="java.lang.Integer">
        select id
        from work_permit
        where apply_user_id = #{currUserId,jdbcType=INTEGER}
          and del_flag = 0
    </select>

    <select id="selectMyAuditWorkPermitIdList" resultType="java.lang.Integer">
        select id
        from work_permit
        where (state = 0 and apply_user_id = #{currUserId,jdbcType=INTEGER})
           or (state = 10 and issue_user_id = #{currUserId,jdbcType=INTEGER})
           or (state = 11 and apply_user_id = #{currUserId,jdbcType=INTEGER})
           or (state = 20 and (apply_user_id = #{currUserId,jdbcType=INTEGER}
            or issue_user_id = #{currUserId,jdbcType=INTEGER}
            or site_user_id = #{currUserId,jdbcType=INTEGER}
            or safety_user_id = #{currUserId,jdbcType=INTEGER}))
           or (state = 90 and (site_user_id = #{currUserId,jdbcType=INTEGER}))
            and del_flag = 0
    </select>

    <update id="updateSiteUserSign">
        update work_permit
        set site_user_sign = #{sign,jdbcType=VARCHAR}
        where id = #{id,jdbcType=INTEGER}
    </update>
</mapper>