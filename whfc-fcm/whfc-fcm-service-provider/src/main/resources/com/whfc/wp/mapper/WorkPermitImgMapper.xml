<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.whfc.wp.dao.WorkPermitImgMapper">
    <resultMap id="BaseResultMap" type="com.whfc.wp.entity.WorkPermitImg">
        <!--@mbg.generated-->
        <!--@Table work_permit_img-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="dept_id" jdbcType="INTEGER" property="deptId"/>
        <result column="permit_id" jdbcType="INTEGER" property="permitId"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="img_url" jdbcType="VARCHAR" property="imgUrl"/>
        <result column="del_flag" jdbcType="INTEGER" property="delFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        dept_id,
        permit_id,
        `type`,
        img_url,
        del_flag,
        update_time,
        create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from work_permit_img
        where id = #{id,jdbcType=INTEGER}
    </select>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.whfc.wp.entity.WorkPermitImg"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into work_permit_img
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="permitId != null">
                permit_id,
            </if>
            <if test="type != null">
                `type`,
            </if>
            <if test="imgUrl != null">
                img_url,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                #{deptId,jdbcType=INTEGER},
            </if>
            <if test="permitId != null">
                #{permitId,jdbcType=INTEGER},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="imgUrl != null">
                #{imgUrl,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.whfc.wp.entity.WorkPermitImg">
        <!--@mbg.generated-->
        update work_permit_img
        <set>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=INTEGER},
            </if>
            <if test="permitId != null">
                permit_id = #{permitId,jdbcType=INTEGER},
            </if>
            <if test="type != null">
                `type` = #{type,jdbcType=INTEGER},
            </if>
            <if test="imgUrl != null">
                img_url = #{imgUrl,jdbcType=VARCHAR},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectUrlsByPermitId" resultType="com.whfc.wp.dto.WorkPermitImgDTO">
        select type, img_url
        from work_permit_img
        where permit_id = #{permitId,jdbcType=INTEGER}
          and del_flag = 0
    </select>

    <update id="logicDelByPermitId">
        update work_permit_img
        set del_flag = 1
        where permit_id = #{permitId,jdbcType=INTEGER}
        <if test="imageTypes != null and imageTypes.size() != 0">
            and `type` in
            <foreach collection="imageTypes" item="type" open="(" close=")" separator=",">
                #{type}
            </foreach>
        </if>
    </update>

    <insert id="batchInsert">
        insert into work_permit_img (dept_id, permit_id, type, img_url) values
        <foreach collection="imgList" item="img" separator=",">
            (#{img.deptId,jdbcType=INTEGER}, #{img.permitId,jdbcType=INTEGER}, #{img.type,jdbcType=INTEGER},
             #{img.imgUrl,jdbcType=VARCHAR})
        </foreach>
    </insert>
</mapper>