package com.whfc.wp.dao;

import com.whfc.wp.dto.WorkPermitDTO;
import com.whfc.wp.entity.WorkPermit;
import com.whfc.wp.param.WorkPermitQueryParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 作业票数据访问接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/31
 */
@Mapper
public interface WorkPermitMapper {

    int insertSelective(WorkPermit record);

    WorkPermit selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(WorkPermit record);

    /**
     * 根据GUID查询
     *
     * @param guid GUID
     * @return 作业票实体
     */
    WorkPermit selectByGuid(@Param("guid") String guid);

    /**
     * 根据编号查询
     *
     * @param deptId 项目ID
     * @param code   编号
     * @return 作业票实体
     */
    WorkPermit selectByCode(@Param("deptId") Integer deptId, @Param("code") String code);

    /**
     * 逻辑删除
     *
     * @param id 主键
     * @return 影响行数
     */
    int logicDeleteById(@Param("id") Integer id);

    /**
     * 查询作业票列表
     *
     * @param deptId     项目ID
     * @param workTypeId 作业类型ID
     * @param state      状态
     * @param startTime  作业开始时间
     * @param endTime    作业结束时间
     * @param keyword    关键字
     * @param ids        作业票ID列表
     * @return 作业票列表
     */
    List<WorkPermitDTO> selectWorkPermitList(@Param("deptId") Integer deptId,
                                             @Param("workTypeId") Integer workTypeId,
                                             @Param("state") Integer state,
                                             @Param("startTime") Date startTime,
                                             @Param("endTime") Date endTime,
                                             @Param("keyword") String keyword,
                                             @Param("ids") List<Integer> ids);


    /**
     * 更新状态
     *
     * @param id    主键
     * @param state 状态
     * @return 影响行数
     */
    int updateState(@Param("id") Integer id, @Param("state") Integer state);

    /**
     * 更新签发信息
     *
     * @param id        主键
     * @param issueTime 签发时间
     * @return 影响行数
     */
    int updateIssueInfo(@Param("id") Integer id,
                        @Param("issueTime") Date issueTime,
                        @Param("issueUserSign") String issueUserSign,
                        @Param("siteUserId") Integer siteUserId,
                        @Param("siteUserName") String siteUserName,
                        @Param("safetyUserId") Integer safetyUserId,
                        @Param("safetyUserName") String safetyUserName);


    /**
     * 更新保护措施
     *
     * @param id                主键
     * @param protectiveMeasure 保护措施
     */
    void updateProtectiveMeasure(@Param("id") Integer id,
                                 @Param("protectiveMeasure") String protectiveMeasure);

    /**
     * 查询我提交的作业票ID列表
     *
     * @param currUserId
     * @return
     */
    List<Integer> selectMyWorkPermitIdList(@Param("currUserId") Integer currUserId);

    /**
     * 查询我提交的作业票ID列表
     *
     * @param currUserId
     * @return
     */
    List<Integer> selectMySubmitWorkPermitIdList(@Param("currUserId") Integer currUserId);

    /**
     * 查询需要我处理的作业票ID列表
     *
     * @param currUserId
     * @return
     */
    List<Integer> selectMyAuditWorkPermitIdList(@Param("currUserId") Integer currUserId);

    /**
     * 更新现场管理人员签名
     *
     * @param id
     * @param sign
     */
    void updateSiteUserSign(@Param("id") Integer id,
                            @Param("sign") String sign);
}