package com.whfc.wp.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.whfc.common.easyexcel.BorderHandler;
import com.whfc.common.exception.BizException;
import com.whfc.common.result.PageData;
import com.whfc.common.result.ResultEnum;
import com.whfc.common.util.PageUtil;
import com.whfc.common.util.RandomUtil;
import com.whfc.fuum.dto.SysDictDTO;
import com.whfc.fuum.service.SysDictService;
import com.whfc.safety.dao.SafetyCheckImgMapper;
import com.whfc.safety.dao.SafetyCheckMapper;
import com.whfc.safety.dao.SafetyTaskItemExecMapper;
import com.whfc.safety.dao.SafetyTaskItemImgMapper;
import com.whfc.safety.dto.SafetyCheckDTO;
import com.whfc.safety.dto.SafetyExecDetailsDTO;
import com.whfc.safety.enums.SafetyOptionType;
import com.whfc.wp.dao.*;
import com.whfc.wp.dto.*;
import com.whfc.wp.entity.WorkPermit;
import com.whfc.wp.entity.WorkPermitImg;
import com.whfc.wp.entity.WorkPermitInspection;
import com.whfc.wp.entity.WorkPermitOpLog;
import com.whfc.wp.enums.WorkPermitEvent;
import com.whfc.wp.enums.WorkPermitImgType;
import com.whfc.wp.enums.WorkPermitRole;
import com.whfc.wp.enums.WorkPermitState;
import com.whfc.wp.param.*;
import com.whfc.wp.service.WorkPermitService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.io.File;
import java.nio.file.Files;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 作业票服务实现
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/31
 */
@DubboService(interfaceClass = WorkPermitService.class, version = "1.0.0")
public class WorkPermitServiceImpl implements WorkPermitService {

    private static final Logger logger = LoggerFactory.getLogger(WorkPermitServiceImpl.class);

    @Autowired
    private WorkPermitMapper workPermitMapper;

    @Autowired
    private WorkPermitOpLogMapper workPermitOpLogMapper;

    @Autowired
    private WorkPermitImgMapper workPermitImgMapper;

    @Autowired
    private WorkPermitConfigMapper workPermitConfigMapper;

    @Autowired
    private WorkPermitInspectionMapper workPermitInspectionMapper;

    @Autowired
    private SafetyCheckMapper safetyCheckMapper;

    @Autowired
    private SafetyCheckImgMapper safetyCheckImgMapper;

    @Autowired
    private SafetyTaskItemExecMapper safetyTaskItemExecMapper;

    @Autowired
    private SafetyTaskItemImgMapper safetyTaskItemImgMapper;

    /**
     * 我的作业票
     */
    private static final Integer TYPE_MY = 1;
    /**
     * 我提交的作业票
     */
    private static final Integer TYPE_MY_SUBMIT = 2;
    /**
     * 需要我处理的作业票
     */
    private static final Integer TYPE_MY_AUDIT = 3;
    /**
     * 作业中
     */
    private static final Integer TYPE_IN_PROGRESS = 4;
    /**
     * 已打回
     */
    private static final Integer TYPE_REJECTED = 5;
    /**
     * 已结束
     */
    private static final Integer TYPE_CLOSED = 6;

    /**
     * 安全巡查类型
     */
    private static final Integer INSPECTION_TYPE_SAFETY = 1;

    /**
     * 隐患排查类型
     */
    private static final Integer INSPECTION_TYPE_HAZARD = 2;

    @DubboReference(interfaceClass = SysDictService.class, version = "1.0.0")
    private SysDictService sysDictService;


    @Override
    public List<WorkPermitConfigDTO> getPermitConfigList(Integer deptId, String workTypeId) throws BizException {
        List<WorkPermitConfigDTO> list = workPermitConfigMapper.selectByDeptId(deptId);
        if (CollectionUtils.isEmpty(list)) {
            deptId = 0;
            list = workPermitConfigMapper.selectByDeptId(deptId);
        }
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        if (StringUtils.isNotBlank(workTypeId)) {
            return list.stream().filter(item -> workTypeId.equals(item.getWorkTypeId())).collect(Collectors.toList());
        }
        return list;
    }

    @Override
    public PageData<WorkPermitDTO> page(WorkPermitQueryParam query) throws BizException {
        logger.info("分页查询作业票列表, query: {}", query);
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        // 作业票类型转换
        coverQueryType(query);
        List<WorkPermitDTO> list = workPermitMapper.selectWorkPermitList(query.getDeptId(), query.getWorkTypeId(),
                query.getState(), query.getStartTime(), query.getEndTime(), query.getKeyword(), query.getWorkPermitIdList());
        PageHelper.clearPage();
        setOperState(query.getCurrUserId(), list);
        return PageUtil.pageData(PageInfo.of(list));
    }

    private void coverQueryType(WorkPermitQueryParam query) {
        if (Objects.equals(query.getType(), TYPE_MY)) {
            // 我的作业票
            List<Integer> myWorkPermitIdList = workPermitMapper.selectMyWorkPermitIdList(query.getCurrUserId());
            query.setWorkPermitIdList(myWorkPermitIdList);
        } else if (Objects.equals(query.getType(), TYPE_MY_SUBMIT)) {
            // 我提交的作业票
            List<Integer> mySubmitWorkPermitIdList = workPermitMapper.selectMySubmitWorkPermitIdList(query.getCurrUserId());
            query.setWorkPermitIdList(mySubmitWorkPermitIdList);
        } else if (Objects.equals(query.getType(), TYPE_MY_AUDIT)) {
            // 需要我处理的作业票
            List<Integer> myAuditWorkPermitIdList = workPermitMapper.selectMyAuditWorkPermitIdList(query.getCurrUserId());
            query.setWorkPermitIdList(myAuditWorkPermitIdList);
        } else if (Objects.equals(query.getType(), TYPE_IN_PROGRESS)) {
            // 作业中
            query.setState(WorkPermitState.IN_PROGRESS.getValue());
        } else if (Objects.equals(query.getType(), TYPE_REJECTED)) {
            // 已打回
            query.setState(WorkPermitState.REJECTED.getValue());
        } else if (Objects.equals(query.getType(), TYPE_CLOSED)) {
            // 已结束
            query.setState(WorkPermitState.CLOSED.getValue());
        }
    }

    @Override
    public List<WorkPermitDTO> list(WorkPermitQueryParam query) throws BizException {
        logger.info("查询作业票列表, query: {}", query);
        coverQueryType(query);
        List<WorkPermitDTO> list = workPermitMapper.selectWorkPermitList(query.getDeptId(), query.getWorkTypeId(),
                query.getState(), query.getStartTime(), query.getEndTime(), query.getKeyword(), query.getWorkPermitIdList());
        setOperState(query.getCurrUserId(), list);
        return list;
    }

    @Override
    public WorkPermitDTO getWorkPermitDetail(Integer userId, String guid) throws BizException {
        logger.info("获取作业票详情, guid: {}", guid);
        WorkPermit workPermit = workPermitMapper.selectByGuid(guid);
        if (workPermit == null) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), "作业票不存在");
        }
        WorkPermitDTO workPermitDTO = new WorkPermitDTO();
        BeanUtils.copyProperties(workPermit, workPermitDTO);

        // 查询操作日志
        List<WorkPermitOpLogDTO> opLogList = workPermitOpLogMapper.selectByPermitId(workPermit.getId());
        workPermitDTO.setPermitOpLogList(opLogList);

        if (Objects.equals(workPermit.getState(), WorkPermitState.REJECTED.getValue()) && !CollectionUtils.isEmpty(opLogList)) {
            // 取出操作日志中最后一条打回的原因
            opLogList.stream().filter(log -> Objects.equals(log.getType(), WorkPermitEvent.REJECT.getCode()))
                    .forEach(log -> workPermitDTO.setRejectedRemark(log.getRemark()));
        }
        // 查询图片列表
        List<WorkPermitImgDTO> imageUrls = workPermitImgMapper.selectUrlsByPermitId(workPermit.getId());
        workPermitDTO.setPermitImgList(imageUrls);
        setOperState(userId, Collections.singletonList(workPermitDTO));

        // 查询巡查记录
        List<WorkPermitInspection> inspectionList = workPermitInspectionMapper.selectByPermitId(workPermit.getId());
        if (CollectionUtils.isEmpty(inspectionList)) {
            return workPermitDTO;
        }
        // 根据类型分组
        Map<Integer, List<WorkPermitInspection>> map = inspectionList.stream().collect(Collectors.groupingBy(WorkPermitInspection::getType));
        for (Map.Entry<Integer, List<WorkPermitInspection>> entry : map.entrySet()) {
            Integer key = entry.getKey();
            List<WorkPermitInspection> value = entry.getValue();
            if (INSPECTION_TYPE_SAFETY.equals(key)) {
                // 安全巡查
                // 巡查记录ID
                List<Integer> execIds = value.stream().map(WorkPermitInspection::getInspectionId).collect(Collectors.toList());
                List<SafetyExecDetailsDTO> safetyExecList = safetyTaskItemExecMapper.selectByExecIds(execIds);
                if (!CollectionUtils.isEmpty(safetyExecList)) {
                    // 查询图片
                    safetyExecList.forEach(exec -> {
                        List<String> imgUrls = safetyTaskItemImgMapper.selectImgUrlByExecId(exec.getExecId());
                        exec.setImgUrls(imgUrls);
                    });
                }
                // 查询现场巡查详情
                workPermitDTO.setSafetyInspectList(safetyExecList);
            } else if (INSPECTION_TYPE_HAZARD.equals(key)) {
                // 隐患巡查
                // 巡查记录ID
                List<Integer> checkIds = value.stream().map(WorkPermitInspection::getInspectionId).collect(Collectors.toList());
                List<SafetyCheckDTO> hazardCheckList = safetyCheckMapper.selectByCheckIds(checkIds);
                if (!CollectionUtils.isEmpty(hazardCheckList)) {
                    // 查询图片
                    hazardCheckList.forEach(check -> {
                        List<String> imgUrls = safetyCheckImgMapper.selectByCheckId(check.getCheckId(), SafetyOptionType.REPORT.getValue(), null);
                        check.setImgUrlList(imgUrls);
                    });
                }
                // 查询现场巡查详情
                workPermitDTO.setHazardInspectList(hazardCheckList);
            }
        }
        return workPermitDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addWorkPermit(WorkPermitParam param) throws BizException {
        logger.info("新增作业票, param: {}", param);
        String guid = param.getGuid();
        WorkPermit workPermit;
        if (StringUtils.isBlank(guid)) {
            // 新增
            // 创建作业票实体
            workPermit = new WorkPermit();
            BeanUtils.copyProperties(param, workPermit);
            // 生成GUID和编号
            guid = IdUtil.simpleUUID();
            // 生成作业票编号
            Date now = new Date();
            int randomNum = new Random().nextInt(9000) + 1000;
            String code = "WP-" + DateUtil.format(now, "yyyyMMddHHmmssSSS") + randomNum;
            workPermit.setGuid(guid);
            workPermit.setCode(code);
            workPermit.setState(WorkPermitState.DRAFT.getValue());
            // 设置申请人信息
            workPermit.setApplyTime(new Date());
            // 插入作业票
            workPermitMapper.insertSelective(workPermit);
        } else {
            // 再次保存
            workPermit = workPermitMapper.selectByGuid(guid);
            if (!WorkPermitState.DRAFT.getValue().equals(workPermit.getState())) {
                throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "只有草稿状态才能保存");
            }
            if (!param.getApplyUserId().equals(workPermit.getApplyUserId())) {
                throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "只有申请人能保存");
            }
            BeanUtils.copyProperties(param, workPermit);
            workPermit.setState(WorkPermitState.DRAFT.getValue());
            workPermitMapper.updateByPrimaryKeySelective(workPermit);
        }
        // 保存图片信息
        Integer deptId = param.getDeptId();
        Integer id = workPermit.getId();
        // 新增传递照片类型为 现场照片  班前讲话
        List<Integer> imageTypes = Arrays.asList(WorkPermitImgType.APPLY.getValue(), WorkPermitImgType.BEFORE.getValue());
        // 保存照片
        saveWorkPermitImages(deptId, id, imageTypes, param.getPermitImgList());
        // 记录操作日志
        saveOpLog(deptId, id, WorkPermitEvent.CREATE, 1, "", param.getApplyUserId(), param.getApplyUserName(),
                param.getApplyUserSign(), WorkPermitRole.APPLY.getDesc());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitWorkPermit(WorkPermitParam param) throws BizException {
        logger.info("提交作业票, param: {}", param);
        String guid = param.getGuid();
        WorkPermit workPermit;
        if (StringUtils.isBlank(guid)) {
            // 提交
            // 创建作业票实体
            workPermit = new WorkPermit();
            BeanUtils.copyProperties(param, workPermit);
            // 生成GUID和编号
            guid = IdUtil.simpleUUID();
            // 生成作业票编号
            Date now = new Date();
            int randomNum = new Random().nextInt(9000) + 1000;
            String code = "WP-" + DateUtil.format(now, "yyyyMMddHHmmssSSS") + randomNum;
            workPermit.setGuid(guid);
            workPermit.setCode(code);
            if (ObjectUtils.isEmpty(param.getIssueUserId())) {
                throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "签发人不能为空。");
            }
            workPermit.setState(WorkPermitState.PENDING_ISSUE.getValue());
            // 设置申请人信息
            workPermit.setApplyTime(new Date());
            // 插入作业票
            workPermitMapper.insertSelective(workPermit);
        } else {
            // 再次提交
            workPermit = workPermitMapper.selectByGuid(guid);
            if (!Objects.equals(workPermit.getState(), WorkPermitState.DRAFT.getValue()) &&
                    !Objects.equals(workPermit.getState(), WorkPermitState.REJECTED.getValue())) {
                throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "只有草稿或者打回状态才能提交");
            }
            if (!param.getApplyUserId().equals(workPermit.getApplyUserId())) {
                throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "只有申请人能提交");
            }
            BeanUtils.copyProperties(param, workPermit);
            workPermit.setState(WorkPermitState.PENDING_ISSUE.getValue());
            workPermitMapper.updateByPrimaryKeySelective(workPermit);
        }
        // 保存图片信息
        Integer deptId = param.getDeptId();
        Integer id = workPermit.getId();
        // 提交传递照片类型为 现场照片  班前讲话
        List<Integer> imageTypes = Arrays.asList(WorkPermitImgType.APPLY.getValue(), WorkPermitImgType.BEFORE.getValue());
        // 保存照片
        saveWorkPermitImages(deptId, id, imageTypes, param.getPermitImgList());
        // 记录操作日志
        saveOpLog(workPermit.getDeptId(), workPermit.getId(), WorkPermitEvent.SUBMIT, 1, "",
                param.getApplyUserId(), param.getApplyUserName(), param.getApplyUserSign(), WorkPermitRole.APPLY.getDesc());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editWorkPermit(WorkPermitParam param) throws BizException {
        logger.info("编辑作业票, param: {}", param);

        WorkPermit workPermit = workPermitMapper.selectByGuid(param.getGuid());
        if (workPermit == null) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), "作业票不存在");
        }

        // 更新作业票信息
        BeanUtils.copyProperties(param, workPermit);

        workPermitMapper.updateByPrimaryKeySelective(workPermit);

        // 编辑传递照片类型为所有类型
        List<Integer> imageTypes = Arrays.stream(WorkPermitImgType.values()).map(WorkPermitImgType::getValue).collect(Collectors.toList());
        // 保存照片
        saveWorkPermitImages(workPermit.getDeptId(), workPermit.getId(), imageTypes, param.getPermitImgList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteWorkPermit(String guid) throws BizException {
        logger.info("删除作业票, guid: {}", guid);

        WorkPermit workPermit = workPermitMapper.selectByGuid(guid);
        if (workPermit == null) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), "作业票不存在");
        }
        // 逻辑删除图片
        workPermitImgMapper.logicDelByPermitId(workPermit.getId(), null);

        // 逻辑删除操作日志
        workPermitOpLogMapper.logicDelByPermitId(workPermit.getId());

        // 逻辑删除
        workPermitMapper.logicDeleteById(workPermit.getId());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void issueWorkPermit(WorkPermitIssueParam param) throws BizException {
        logger.info("签发作业票, param: {}", param);
        WorkPermit workPermit = workPermitMapper.selectByGuid(param.getGuid());
        if (workPermit == null) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), "作业票不存在");
        }
        if (!Objects.equals(workPermit.getState(), WorkPermitState.PENDING_ISSUE.getValue())) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "只有待签发状态才能签发");
        }
        if (!param.getCurrentUserId().equals(workPermit.getIssueUserId())) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "只有签发人才能签发");
        }
        Integer result = param.getResult();
        Integer state = null;
        WorkPermitEvent event = WorkPermitEvent.ISSUE;
        if (result == 1) {
            // 通过
            state = WorkPermitState.IN_PROGRESS.getValue();
            if (param.getSiteUserId() == null || param.getSafetyUserId() == null) {
                throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "现场管理人员和安全监督人员不能为空");
            }
        } else if (result == 2) {
            // 不通过
            state = WorkPermitState.REJECTED.getValue();
            event = WorkPermitEvent.REJECT;
        }
        Integer id = workPermit.getId();
        workPermitMapper.updateState(id, state);

        // 更新签发信息
        workPermitMapper.updateIssueInfo(id, new Date(), param.getSign(), param.getSiteUserId(),
                param.getSiteUserName(), param.getSafetyUserId(), param.getSafetyUserName());

        // 记录操作日志
        saveOpLog(workPermit.getDeptId(), id, event, result, param.getRemark(),
                param.getCurrentUserId(), param.getCurrentUserName(), param.getSign(), WorkPermitRole.ISSUE.getDesc());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void closeWorkPermit(WorkPermitCloseParam param) throws BizException {
        logger.info("关闭作业票, param: {}", param);
        WorkPermit workPermit = workPermitMapper.selectByGuid(param.getGuid());
        if (workPermit == null) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), "作业票不存在");
        }
        if (!Objects.equals(workPermit.getState(), WorkPermitState.IN_PROGRESS.getValue())) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "只有作业中才能关闭");
        }
        if (!Objects.equals(param.getCurrentUserId(), workPermit.getApplyUserId())) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "只有申请人能关闭");
        }
        Integer state = WorkPermitState.CLOSING.getValue();
        Integer id = workPermit.getId();
        Integer deptId = workPermit.getDeptId();
        workPermitMapper.updateState(id, state);
        workPermitMapper.updateProtectiveMeasure(id, param.getProtectiveMeasure());
        // 保存图片信息
        // 申请关闭传递照片类型为 作业后图片
        List<Integer> imageTypes = Collections.singletonList(WorkPermitImgType.AFTER.getValue());
        // 保存照片
        saveWorkPermitImages(deptId, id, imageTypes, param.getPermitImgList());
        // 记录操作日志
        saveOpLog(deptId, id, WorkPermitEvent.CLOSE, 1, "", param.getCurrentUserId(),
                param.getCurrentUserName(), param.getSign(), WorkPermitRole.APPLY.getDesc());
    }

    @Override
    public void closeConfirmWorkPermit(WorkPermitCloseConfirmParam param) {
        logger.info("关闭作业票确认, param: {}", param);
        WorkPermit workPermit = workPermitMapper.selectByGuid(param.getGuid());
        if (workPermit == null) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), "作业票不存在");
        }
        if (!Objects.equals(workPermit.getState(), WorkPermitState.CLOSING.getValue())) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "只有关闭中状态才能确认关闭");
        }
        if (!Objects.equals(param.getCurrentUserId(), workPermit.getSiteUserId())) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "只有现场管理才能确认关闭");
        }
        Integer state = WorkPermitState.CLOSED.getValue();
        Integer id = workPermit.getId();
        Integer deptId = workPermit.getDeptId();
        workPermitMapper.updateState(id, state);
        workPermitMapper.updateSiteUserSign(id, param.getSign());
        // 记录操作日志
        saveOpLog(deptId, id, WorkPermitEvent.CLOSE_CONFIRM, 1, "", param.getCurrentUserId(),
                param.getCurrentUserName(), param.getSign(), WorkPermitRole.SITE.getDesc());
    }

    @Override
    public void bindInspection(WorkPermitBindParam param) throws BizException {
        logger.info("作业票绑定巡查, param: {}", param);
        WorkPermit workPermit = workPermitMapper.selectByGuid(param.getGuid());
        if (workPermit == null) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), "作业票不存在");
        }
        if (!Objects.equals(workPermit.getState(), WorkPermitState.IN_PROGRESS.getValue())) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), "只有作业中状态才能巡查");
        }
        // 绑定巡查记录
        WorkPermitInspection inspection = new WorkPermitInspection();
        Integer id = workPermit.getId();
        inspection.setPermitId(id);
        inspection.setType(param.getType());
        inspection.setInspectionId(param.getInspectionId());
        workPermitInspectionMapper.insertSelective(inspection);

        String position = "";
        WorkPermitEvent event = WorkPermitEvent.SITE_INSPECT;
        if (param.getCurrentUserId().equals(workPermit.getSiteUserId())) {
            // 现场巡查
            position = WorkPermitRole.SITE.getDesc();
            event = WorkPermitEvent.SITE_INSPECT;
        } else if (param.getCurrentUserId().equals(workPermit.getSafetyUserId())) {
            // 安全巡查
            position = WorkPermitRole.SAFETY.getDesc();
            event = WorkPermitEvent.SAFETY_INSPECT;
        }
        // 记录操作日志
        saveOpLog(workPermit.getDeptId(), id, event, 1, "", param.getCurrentUserId(),
                param.getCurrentUserName(), "", position);
    }

    @Override
    public void exportWorkPermit(Integer deptId, WorkPermitExportParam param) throws BizException {
        List<String> guids = param.getGuids();

        // 获取模板
        String templatePath = "D:\\download\\work-permit-template.xlsx";
        File templateFile = new File(templatePath);

        // 获取作业类型

        String typeCode = "work_permit_type";
        SysDictDTO dict = sysDictService.getDictDataByTypeCode(deptId, typeCode);
        if (dict == null) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), "作业票类型未配置.");
        }
        Map<Integer, SysDictDTO> workPermitTypeMap = dict.getChildList().stream().collect(Collectors.toMap(SysDictDTO::getId, Function.identity()));

        for (String guid : guids) {
            try {
                WorkPermitDTO permitDetail = getWorkPermitDetail(param.getCurrUserId(), guid);
                if (permitDetail == null) {
                    continue;
                }
                String fileName = RandomUtil.getGuid();
                File excel = File.createTempFile(fileName, ".xlsx");
                // 封装参数
                Map<String, Object> attrMap = buildExportDataMap(permitDetail, workPermitTypeMap);
                // 创建工作表
                WriteSheet writeSheet = EasyExcel.writerSheet().build();
                // 创建 ExcelWriter，注册合并策略和全局样式
                ExcelWriter excelWriter = EasyExcel.write(excel)
                        .withTemplate(Files.newInputStream(templateFile.toPath()))
                        .registerWriteHandler(new BorderHandler())
                        .inMemory(true)
                        .build();
                // 填充数据 - 使用单一配置填充所有数据
                excelWriter.fill(attrMap, writeSheet);

                // 完成写入
                excelWriter.finish();
                logger.info("施工日报导出文件路径:");
                logger.info("\r\n" + excel.getAbsolutePath());
            } catch (Exception e) {
                logger.info("导出作业票失败, guid: {}", guid, e);
            }
        }
    }

    /**
     * 保存作业票图片
     *
     * @param deptId       项目ID
     * @param workPermitId 作业票ID
     * @param imageUrls    图片列表
     */
    private void saveWorkPermitImages(Integer deptId, Integer workPermitId, List<Integer> imageTypes, List<WorkPermitImgDTO> imageUrls) {
        // 删除旧图片
        workPermitImgMapper.logicDelByPermitId(workPermitId, imageTypes);

        if (CollectionUtils.isEmpty(imageUrls)) {
            return;
        }

        // 保存图片
        List<WorkPermitImg> images = new ArrayList<>();
        for (WorkPermitImgDTO imgDto : imageUrls) {
            WorkPermitImg img = new WorkPermitImg();
            img.setDeptId(deptId);
            img.setPermitId(workPermitId);
            img.setType(imgDto.getType());
            img.setImgUrl(imgDto.getImgUrl());
            images.add(img);
        }
        workPermitImgMapper.batchInsert(images);
    }

    /**
     * 保存操作日志
     *
     * @param deptId       项目ID
     * @param workPermitId 作业票ID
     * @param event        操作事件
     * @param result       操作结果
     * @param remark       备注
     * @param opUserId     操作用户ID
     * @param opUserName   操作用户名称
     * @param sign         签名图片
     * @param position     职位
     */
    private void saveOpLog(Integer deptId, Integer workPermitId, WorkPermitEvent event, Integer result, String remark,
                           Integer opUserId, String opUserName, String sign, String position) {
        WorkPermitOpLog opLog = new WorkPermitOpLog();
        opLog.setDeptId(deptId);
        // 默认对象类型为1
        opLog.setObjType(1);
        opLog.setObjId(workPermitId);
        opLog.setType(event.getCode());
        opLog.setName(event.getDesc());
        opLog.setResult(result);
        // 默认已操作
        opLog.setOpState(1);
        opLog.setOpUserId(opUserId);
        opLog.setOpUserName(opUserName);
        opLog.setOpTime(new Date());
        opLog.setRemark(remark);
        opLog.setSign(sign);
        opLog.setPosition(position);
        workPermitOpLogMapper.insertSelective(opLog);
    }

    /**
     * 设置操作状态
     *
     * @param currUserId 当前用户ID
     * @param list       作业票列表
     */
    private void setOperState(Integer currUserId, List<WorkPermitDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (WorkPermitDTO workPermitDTO : list) {
            Integer state = workPermitDTO.getState();
            if (WorkPermitState.DRAFT.getValue().equals(state)) {
                // 草稿  	 申请人-编辑/提交
                if (currUserId.equals(workPermitDTO.getApplyUserId())) {
                    workPermitDTO.setCanEdit(true);
                }
            } else if (WorkPermitState.PENDING_ISSUE.getValue().equals(state)) {
                // 待签发  	 签发人-审批/打回
                if (currUserId.equals(workPermitDTO.getIssueUserId())) {
                    workPermitDTO.setCanAudit(true);
                }
            } else if (WorkPermitState.REJECTED.getValue().equals(state)) {
                // 打回     申请人-编辑/提交
                if (currUserId.equals(workPermitDTO.getApplyUserId())) {
                    workPermitDTO.setCanEdit(true);
                }
            } else if (WorkPermitState.IN_PROGRESS.getValue().equals(state)) {
                // 申请人-关闭作业票
                if (currUserId.equals(workPermitDTO.getApplyUserId())) {
                    workPermitDTO.setCanEdit(true);
                    workPermitDTO.setCanClose(true);
                }
                // 作业中   现场管理/安全巡查, 安全巡查/隐患排查.
                if (currUserId.equals(workPermitDTO.getSiteUserId())
                        || currUserId.equals(workPermitDTO.getSafetyUserId())) {
                    workPermitDTO.setCanInspect(true);
                }
            } else if (WorkPermitState.CLOSING.getValue().equals(state)) {
                // 关闭中   现场管理-确认关闭
                if (currUserId.equals(workPermitDTO.getSiteUserId())) {
                    workPermitDTO.setCanAudit(true);
                }
            }
        }
    }

    /**
     * 封装导出数据
     *
     * @param permitDetail      作业票详情
     * @param workPermitTypeMap 作业票类型字典映射
     * @return 导出数据
     */
    private Map<String, Object> buildExportDataMap(WorkPermitDTO permitDetail, Map<Integer, SysDictDTO> workPermitTypeMap) {
        Map<String, Object> attrMap = new HashMap<>();
        String workTypeName = permitDetail.getWorkTypeName();
        attrMap.put("title", workTypeName + "票");
        Integer workTypeId = permitDetail.getWorkTypeId();
        SysDictDTO sysDictDTO = workPermitTypeMap.get(workTypeId);
        String workType = "一般作业";
        if (sysDictDTO != null && !"wp_ybzy".equals(sysDictDTO.getCode())) {
            workType = "危险作业";
        }
        attrMap.put("workType", workType);
        attrMap.put("code", permitDetail.getCode());
        attrMap.put("workUnitName", permitDetail.getWorkUnitName());
        attrMap.put("applyUserName", permitDetail.getApplyUserName());
        Date applyTime = permitDetail.getApplyTime();
        String applyTimeStr = "";
        if (applyTime != null) {
            applyTimeStr = DateUtil.format(applyTime, "yyyy-MM-dd");
        }
        attrMap.put("applyTime", applyTimeStr);
        attrMap.put("applyUserContact", permitDetail.getApplyUserContact());
        // 处理时间  年  月  日  时-   月  日  时
        Date workStartTime = permitDetail.getWorkStartTime();
        String startTimeStr = "  年  月  日  时";
        if (workStartTime != null) {
            startTimeStr = DateUtil.format(workStartTime, "yyyy年MM月dd日HH时");
        }
        Date workEndTime = permitDetail.getWorkEndTime();
        String endTimeStr = " 月  日  时";
        if (workEndTime != null) {
            endTimeStr = DateUtil.format(workEndTime, "MM月dd日HH时");
        }
        attrMap.put("workTime", startTimeStr + "-" + endTimeStr);
        attrMap.put("restPeriod", permitDetail.getRestPeriod());
        attrMap.put("workContent", permitDetail.getWorkContent());
        attrMap.put("workAreaName", permitDetail.getWorkAreaName());
        attrMap.put("workerNum", permitDetail.getWorkerNum());
        attrMap.put("guardianName", permitDetail.getGuardianName());
        attrMap.put("guardianContact", permitDetail.getGuardianContact());
        attrMap.put("tool", permitDetail.getTool());
        attrMap.put("applyUserSign", permitDetail.getApplyUserSign());
        attrMap.put("issueUserSign", permitDetail.getIssueUserSign());
        attrMap.put("issueUserContact", permitDetail.getIssueUserContact());
        // 获取审批意见
        String remark = "";
        attrMap.put("issueRemark", remark);
        attrMap.put("siteUserName", permitDetail.getSiteUserName());
        attrMap.put("safetyUserName", permitDetail.getSafetyUserName());
        attrMap.put("siteUserSign", permitDetail.getSiteUserSign());
        // 获取巡查详情
        String inspectionDetail = "";
        attrMap.put("inspectionDetail", inspectionDetail);

        // 动态添加特种作业人员字段，如果有值才添加
        String specialOperationPerson = permitDetail.getSpecialOperationPerson();
        if (specialOperationPerson != null && !specialOperationPerson.trim().isEmpty()) {
            attrMap.put("specialOperationPerson", specialOperationPerson);
        }

        // 处理安全风险
        String riskRecognition = permitDetail.getRiskRecognition();
        List<WorkPermitConfigDetailDTO> riskRecognitionList = JSON.parseArray(riskRecognition, WorkPermitConfigDetailDTO.class);
        for (int i = 0; i < riskRecognitionList.size(); i++) {
            WorkPermitConfigDetailDTO detailDTO = riskRecognitionList.get(i);
            if (detailDTO == null) {
                continue;
            }
            String checkBox = "☑";
            if (!detailDTO.getFlag()) {
                checkBox = "□";
            }
            // 处理其它
            if (detailDTO.getIsInput() != null && detailDTO.getIsInput()) {
                attrMap.put("riskRecognition", detailDTO.getName() + ":" + detailDTO.getRemark());
            }
            {
                attrMap.put("riskRecognition" + (i + 1), checkBox + detailDTO.getName());
            }
        }

        // 处理管控措施
        String controlMeasure = permitDetail.getControlMeasure();
        List<WorkPermitConfigDetailDTO> controlMeasureList = JSON.parseArray(controlMeasure, WorkPermitConfigDetailDTO.class);
        for (int i = 0; i < controlMeasureList.size(); i++) {
            WorkPermitConfigDetailDTO detailDTO = controlMeasureList.get(i);
            if (detailDTO == null) {
                continue;
            }
            String checkBox = "☑";
            if (!detailDTO.getFlag()) {
                checkBox = "□";
            }
            // 处理其它
            if (detailDTO.getIsInput() != null && detailDTO.getIsInput()) {
                attrMap.put("controlMeasure", detailDTO.getName() + ":" + detailDTO.getRemark());
            }
            {
                attrMap.put("controlMeasure" + (i + 1), checkBox + detailDTO.getName());
            }
        }

        // 处理班前讲话
        String preWorkTrain = permitDetail.getPreWorkTrain();
        List<WorkPermitConfigDetailDTO> preWorkTrainList = JSON.parseArray(preWorkTrain, WorkPermitConfigDetailDTO.class);
        for (int i = 0; i < preWorkTrainList.size(); i++) {
            WorkPermitConfigDetailDTO detailDTO = preWorkTrainList.get(i);
            if (detailDTO == null) {
                continue;
            }
            String checkBox = "☑";
            if (!detailDTO.getFlag()) {
                checkBox = "□";
            }
            // 处理其它
            if (detailDTO.getIsInput() != null && detailDTO.getIsInput()) {
                attrMap.put("preWorkTrain", detailDTO.getName() + ":" + detailDTO.getRemark());
            }
            {
                attrMap.put("preWorkTrain" + (i + 1), checkBox + detailDTO.getName());
            }
        }

        // 处理作业检查
        String inWorkInspect = permitDetail.getInWorkInspect();
        List<WorkPermitConfigDetailDTO> inWorkInspectList = JSON.parseArray(inWorkInspect, WorkPermitConfigDetailDTO.class);
        for (int i = 0; i < inWorkInspectList.size(); i++) {
            WorkPermitConfigDetailDTO detailDTO = inWorkInspectList.get(i);
            if (detailDTO == null) {
                continue;
            }
            String checkState = "☑满足 □不满足";
            if (!detailDTO.getFlag()) {
                checkState = "□满足 ☑不满足";
            }

            // 处理其它
            if (detailDTO.getIsInput() != null && detailDTO.getIsInput()) {
                attrMap.put("inWorkInspect", detailDTO.getIndex() + "." + detailDTO.getName() + ":" + detailDTO.getRemark());
                attrMap.put("inWorkInspectCheck", checkState);
            }
            {
                attrMap.put("inWorkInspect" + (i + 1), detailDTO.getIndex() + "." + detailDTO.getName());
                attrMap.put("inWorkInspectCheck" + (i + 1), checkState);
            }
        }

        // 处理保护措施
        String protectiveMeasure = permitDetail.getProtectiveMeasure();
        WpProtectiveMeasureDTO protectiveMeasureDTO = JSON.parseObject(protectiveMeasure, WpProtectiveMeasureDTO.class);
        if (protectiveMeasureDTO != null) {
            StringBuilder sb = new StringBuilder();
            // 保护措施
            List<WorkPermitConfigDetailDTO> protectiveMeasureList = protectiveMeasureDTO.getProtectiveMeasure();
            for (WorkPermitConfigDetailDTO detailDTO : protectiveMeasureList) {
                if (detailDTO == null) {
                    continue;
                }
                String checkBox = "☑";
                if (!detailDTO.getFlag()) {
                    checkBox = "□";
                }
                sb.append(checkBox).append(detailDTO.getName()).append("\n");
            }
            attrMap.put("protectiveMeasure", sb.toString());

            // 申请关闭
            WorkPermitConfigDetailDTO toClose = protectiveMeasureDTO.getToClose();
            if (toClose != null) {
                String checkBox = "☑";
                if (!toClose.getFlag()) {
                    checkBox = "□";
                }
                attrMap.put("protectiveMeasureToClose", checkBox + toClose.getName());
            }
            // 关闭确认
            WorkPermitConfigDetailDTO closeConfirm = protectiveMeasureDTO.getCloseConfirm();
            if (closeConfirm != null) {
                String checkBox = "☑";
                if (!closeConfirm.getFlag()) {
                    checkBox = "□";
                }
                attrMap.put("protectiveMeasureCloseConfirm", checkBox + closeConfirm.getName());
            }
        }
        return attrMap;
    }
}
