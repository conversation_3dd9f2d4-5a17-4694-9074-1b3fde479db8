package com.whfc.wp.dao;

import com.whfc.wp.dto.WorkPermitImgDTO;
import com.whfc.wp.entity.WorkPermitImg;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 作业票图片数据访问接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/31
 */
@Mapper
public interface WorkPermitImgMapper {

    int insertSelective(WorkPermitImg record);

    WorkPermitImg selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(WorkPermitImg record);

    /**
     * 根据作业票ID查询图片URL列表
     *
     * @param permitId 作业票ID
     * @return 图片URL列表
     */
    List<WorkPermitImgDTO> selectUrlsByPermitId(@Param("permitId") Integer permitId);

    /**
     * 根据作业票ID删除图片
     *
     * @param permitId   作业票ID
     * @param imageTypes
     * @return 影响行数
     */
    int logicDelByPermitId(@Param("permitId") Integer permitId,
                           @Param("imageTypes") List<Integer> imageTypes);

    /**
     * 批量插入
     *
     * @param imgList 图片列表
     * @return 影响行数
     */
    int batchInsert(@Param("imgList") List<WorkPermitImg> imgList);
}