package com.whfc.wp.handler;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.handler.context.SheetWriteHandlerContext;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * 条件行处理器 - 用于根据条件删除Excel行
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/8
 */
public class ConditionalRowHandler implements SheetWriteHandler {

    private static final Logger logger = LoggerFactory.getLogger(ConditionalRowHandler.class);

    private final Map<String, Object> dataMap;

    public ConditionalRowHandler(Map<String, Object> dataMap) {
        this.dataMap = dataMap;
    }

    @Override
    public void afterSheetCreate(SheetWriteHandlerContext context) {
        Sheet sheet = context.getWriteSheetHolder().getSheet();

        Boolean hasSpecialOperationPerson = (Boolean) dataMap.get("hasSpecialOperationPerson");
        Boolean hasGasDetection = (Boolean) dataMap.get("hasGasDetection");

        int deletedRows = 0;

        // 如果没有有毒气体数据 → 删除 7-11 行
        if (hasGasDetection == null || !hasGasDetection) {
            // 注意：索引从0开始，所以7-11行是6~10
            deletedRows += deleteGasDetectionRows(sheet, 6, 10);
        }

        // 如果没有特种作业人员数据 → 删除对应行（考虑前面已删除的行数）
        if (hasSpecialOperationPerson == null || !hasSpecialOperationPerson) {
            // 原第15行 → 索引14
            int specialRowIndex = 14 - deletedRows;
            deleteSpecialOperationPersonRow(sheet, specialRowIndex);
        }
    }

    /**
     * 删除有毒气体行（7-10行）并返回删除的行数
     */
    private int deleteGasDetectionRows(Sheet sheet, int startIndex, int endIndex) {
        int lastRowNum = sheet.getLastRowNum();
        if (startIndex > lastRowNum) return 0;

        // 先删除涉及的合并单元格
        for (int i = startIndex; i <= endIndex && i <= lastRowNum; i++) {
            removeMergedRegionsInRow(sheet, i);
        }

        // shiftRows 将 endIndex+1 ~ lastRowNum 上移 (endIndex - startIndex + 1) 行
        int rowsToDelete = Math.min(endIndex, lastRowNum) - startIndex + 1;
        if (endIndex < lastRowNum) {
            sheet.shiftRows(endIndex + 1, lastRowNum, -rowsToDelete, true, false);
        } else {
            // 如果删到最后一行，只清除
            for (int i = startIndex; i <= endIndex && i <= lastRowNum; i++) {
                Row row = sheet.getRow(i);
                if (row != null) sheet.removeRow(row);
            }
        }

        // 合并行
        sheet.addMergedRegion(new CellRangeAddress(startIndex - 4, startIndex + 1, 0, 0));
        logger.debug("删除有毒气体行 {}-{} 共 {} 行", startIndex, endIndex, rowsToDelete);
        return rowsToDelete;
    }

    private void deleteSpecialOperationPersonRow(Sheet sheet, int targetRowIndex) {
        int lastRowNum = sheet.getLastRowNum();
        if (targetRowIndex > lastRowNum) return;

        removeMergedRegionsInRow(sheet, targetRowIndex);

        if (targetRowIndex < lastRowNum) {
            sheet.shiftRows(targetRowIndex + 1, lastRowNum, -1, true, false);
        } else {
            Row row = sheet.getRow(targetRowIndex);
            if (row != null) sheet.removeRow(row);
        }
        sheet.addMergedRegion(new CellRangeAddress(targetRowIndex - 1, targetRowIndex + 4, 0, 0));
        sheet.addMergedRegion(new CellRangeAddress(targetRowIndex - 1, targetRowIndex + 4, 1, 1));
        logger.debug("删除特种作业人员行 索引 {}", targetRowIndex);
    }

    private void removeMergedRegionsInRow(Sheet sheet, int rowIndex) {
        for (int i = sheet.getNumMergedRegions() - 1; i >= 0; i--) {
            CellRangeAddress merged = sheet.getMergedRegion(i);
            if (merged.getFirstRow() <= rowIndex && merged.getLastRow() >= rowIndex) {
                sheet.removeMergedRegion(i);
            }
        }
    }



}
