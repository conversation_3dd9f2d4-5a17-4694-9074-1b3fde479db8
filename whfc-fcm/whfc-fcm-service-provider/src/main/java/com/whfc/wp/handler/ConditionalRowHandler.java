package com.whfc.wp.handler;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.handler.context.SheetWriteHandlerContext;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * 条件行处理器 - 用于根据条件删除Excel行
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/8
 */
public class ConditionalRowHandler implements SheetWriteHandler {

    private static final Logger logger = LoggerFactory.getLogger(ConditionalRowHandler.class);

    private final Map<String, Object> dataMap;

    private static final Integer GAS_ROW_START_INDEX = 6;
    private static final Integer GAS_ROW_END_INDEX = 10;

    private static final Integer SPECIAL_OPERATION_PERSON_ROW_INDEX = 14;

    public ConditionalRowHandler(Map<String, Object> dataMap) {
        this.dataMap = dataMap;
    }

    @Override
    public void afterSheetCreate(SheetWriteHandlerContext context) {
        Sheet sheet = context.getWriteSheetHolder().getSheet();

        Boolean hasSpecialOperationPerson = (Boolean) dataMap.get("hasSpecialOperationPerson");
        Boolean hasGasDetection = (Boolean) dataMap.get("hasGasDetection");

        int deletedRows = 0;

        // 如果没有有毒气体数据 删除行
        if (hasGasDetection == null || !hasGasDetection) {
            logger.debug("删除有毒气体行");
            deletedRows += deleteGasDetectionRows(sheet);
        }

        // 如果没有特种作业人员数据 删除对应行
        if (hasSpecialOperationPerson == null || !hasSpecialOperationPerson) {
            logger.debug("删除特种作业人员行");
            int specialRowIndex = SPECIAL_OPERATION_PERSON_ROW_INDEX - deletedRows;
            deleteSpecialOperationPersonRow(sheet, specialRowIndex);
        }
    }

    /**
     * 删除有毒气体行
     *
     * @param sheet sheet
     * @return 删除的行数
     */
    private int deleteGasDetectionRows(Sheet sheet) {
        int lastRowNum = sheet.getLastRowNum();
        if (GAS_ROW_START_INDEX > lastRowNum) {
            return 0;
        }

        // 先删除涉及的合并单元格
        for (int i = GAS_ROW_START_INDEX; i <= GAS_ROW_END_INDEX && i <= lastRowNum; i++) {
            removeMergedRegionsInRow(sheet, i);
        }

        // shiftRows 将 endIndex+1 ~ lastRowNum 上移 (endIndex - startIndex + 1) 行
        int rowsToDelete = Math.min(GAS_ROW_END_INDEX, lastRowNum) - GAS_ROW_START_INDEX + 1;
        if (GAS_ROW_END_INDEX < lastRowNum) {
            sheet.shiftRows(GAS_ROW_END_INDEX + 1, lastRowNum, -rowsToDelete, true, false);
        } else {
            // 如果删到最后一行，只清除
            for (int i = GAS_ROW_START_INDEX; i <= GAS_ROW_END_INDEX && i <= lastRowNum; i++) {
                Row row = sheet.getRow(i);
                if (row != null) {
                    sheet.removeRow(row);
                }
            }
        }

        // 合并行
        sheet.addMergedRegion(new CellRangeAddress(GAS_ROW_START_INDEX - 4, GAS_ROW_START_INDEX + 1, 0, 0));
        return rowsToDelete;
    }

    /**
     * 删除特种作业人员行
     *
     * @param sheet          sheet
     * @param targetRowIndex 目标行索引
     */
    private void deleteSpecialOperationPersonRow(Sheet sheet, int targetRowIndex) {
        int lastRowNum = sheet.getLastRowNum();
        if (targetRowIndex > lastRowNum) {
            return;
        }
        // 先删除涉及的合并单元格
        removeMergedRegionsInRow(sheet, targetRowIndex);

        if (targetRowIndex < lastRowNum) {
            sheet.shiftRows(targetRowIndex + 1, lastRowNum, -1, true, false);
        } else {
            Row row = sheet.getRow(targetRowIndex);
            if (row != null) {
                sheet.removeRow(row);
            }
        }
        // 合并行
        sheet.addMergedRegion(new CellRangeAddress(targetRowIndex - 1, targetRowIndex + 4, 0, 0));
        sheet.addMergedRegion(new CellRangeAddress(targetRowIndex - 1, targetRowIndex + 4, 1, 1));
    }

    /**
     * 删除指定行涉及的合并单元格
     *
     * @param sheet    sheet
     * @param rowIndex 行索引
     */
    private void removeMergedRegionsInRow(Sheet sheet, int rowIndex) {
        for (int i = sheet.getNumMergedRegions() - 1; i >= 0; i--) {
            CellRangeAddress merged = sheet.getMergedRegion(i);
            if (merged.getFirstRow() <= rowIndex && merged.getLastRow() >= rowIndex) {
                sheet.removeMergedRegion(i);
            }
        }
    }
}
