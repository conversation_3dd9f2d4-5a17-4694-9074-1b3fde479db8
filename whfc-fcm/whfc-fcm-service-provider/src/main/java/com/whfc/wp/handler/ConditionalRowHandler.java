package com.whfc.wp.handler;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.handler.context.SheetWriteHandlerContext;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * 条件行处理器 - 用于根据条件删除Excel行
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/8
 */
public class ConditionalRowHandler implements SheetWriteHandler {

    private static final Logger logger = LoggerFactory.getLogger(ConditionalRowHandler.class);

    private final Map<String, Object> dataMap;

    public ConditionalRowHandler(Map<String, Object> dataMap) {
        this.dataMap = dataMap;
    }

    @Override
    public void afterSheetCreate(SheetWriteHandlerContext context) {
        // 在Sheet创建完成、所有数据填充完成后执行
        Sheet sheet = context.getWriteSheetHolder().getSheet();

        Boolean hasSpecialOperationPerson = (Boolean) dataMap.get("hasSpecialOperationPerson");
        if (hasSpecialOperationPerson == null || !hasSpecialOperationPerson) {
            logger.debug("没有特种作业人员数据，准备删除相关行");
            deleteSpecialOperationPersonRow(sheet);
            logger.info("成功删除特种作业人员行");
        } else {
            logger.debug("有特种作业人员数据，保留相关行");
        }
    }
    


    /**
     * 删除特种作业人员行
     */
    private void deleteSpecialOperationPersonRow(Sheet sheet) {
        try {
            logger.debug("开始查找并删除特种作业人员行，总行数: {}", sheet.getLastRowNum() + 1);

            // 遍历所有行，查找包含"特种作业持证人员名字"的行
            for (int i = 0; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row != null && isSpecialOperationPersonRow(row)) {
                    logger.debug("找到特种作业人员行，行号: {}", i);

                    // 删除该行
                    sheet.removeRow(row);

                    // 向上移动后续行
                    if (i < sheet.getLastRowNum()) {
                        sheet.shiftRows(i + 1, sheet.getLastRowNum(), -1);
                        logger.debug("向上移动行，从第{}行开始向上移动1行", i + 1);
                    }

                    logger.info("成功删除特种作业人员行，原行号: {}", i);
                    break; // 只删除第一个匹配的行
                }
            }

        } catch (Exception e) {
            logger.error("删除特种作业人员行失败", e);
        }
    }

    /**
     * 判断是否为特种作业人员行
     */
    private boolean isSpecialOperationPersonRow(Row row) {
        if (row == null) {
            return false;
        }

        // 遍历行中的所有单元格，查找包含"特种作业持证人员名字"的单元格
        for (int i = 0; i < row.getLastCellNum(); i++) {
            if (row.getCell(i) != null) {
                String cellValue = getCellValueAsString(row.getCell(i));
                if (cellValue != null && (
                    cellValue.contains("特种作业持证人员名字") ||
                    cellValue.contains("特种作业持证人员") ||
                    cellValue.contains("specialOperationPerson")
                )) {
                    logger.debug("在单元格[{},{}]找到特种作业人员标识: '{}'", row.getRowNum(), i, cellValue);
                    return true;
                }
            }
        }
        return false;
    }


    /**
     * 获取单元格的字符串值
     */
    private String getCellValueAsString(org.apache.poi.ss.usermodel.Cell cell) {
        if (cell == null) {
            return null;
        }
        
        try {
            switch (cell.getCellType()) {
                case STRING:
                    return cell.getStringCellValue();
                case NUMERIC:
                    return String.valueOf(cell.getNumericCellValue());
                case BOOLEAN:
                    return String.valueOf(cell.getBooleanCellValue());
                case FORMULA:
                    return cell.getCellFormula();
                default:
                    return null;
            }
        } catch (Exception e) {
            logger.warn("获取单元格值失败: {}", e.getMessage());
            return null;
        }
    }
}
