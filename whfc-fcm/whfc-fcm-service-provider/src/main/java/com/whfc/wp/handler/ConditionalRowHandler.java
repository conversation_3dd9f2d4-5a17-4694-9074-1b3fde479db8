package com.whfc.wp.handler;

import com.alibaba.excel.write.handler.RowWriteHandler;
import com.alibaba.excel.write.handler.context.RowWriteHandlerContext;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * 条件行处理器 - 用于根据条件隐藏Excel行
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/8
 */
public class ConditionalRowHandler implements RowWriteHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(ConditionalRowHandler.class);
    
    private final Map<String, Object> dataMap;
    
    public ConditionalRowHandler(Map<String, Object> dataMap) {
        this.dataMap = dataMap;
    }
    
    @Override
    public void afterRowDispose(RowWriteHandlerContext context) {
        if (context.getHead()) {
            return;
        }
        
        Row row = context.getRow();
        Sheet sheet = context.getWriteSheetHolder().getSheet();
        
        // 检查是否包含特种作业人员相关的行
        if (isSpecialOperationPersonRow(row)) {
            Boolean hasSpecialOperationPerson = (Boolean) dataMap.get("hasSpecialOperationPerson");
            if (hasSpecialOperationPerson == null || !hasSpecialOperationPerson) {
                // 隐藏该行
                row.setZeroHeight(true);
                logger.debug("隐藏特种作业人员行，行号: {}", row.getRowNum());
            }
        }
    }
    
    /**
     * 判断是否为特种作业人员相关的行
     * 通过检查行中是否包含特定的标识符来判断
     */
    private boolean isSpecialOperationPersonRow(Row row) {
        if (row == null) {
            return false;
        }
        
        // 遍历行中的所有单元格，查找包含特种作业人员标识的单元格
        for (int i = 0; i < row.getLastCellNum(); i++) {
            if (row.getCell(i) != null) {
                String cellValue = getCellValueAsString(row.getCell(i));
                if (cellValue != null && (
                    cellValue.contains("特种作业持证人员名字") || 
                    cellValue.contains("{specialOperationPerson}") ||
                    cellValue.contains("specialOperationPerson")
                )) {
                    return true;
                }
            }
        }
        return false;
    }
    
    /**
     * 获取单元格的字符串值
     */
    private String getCellValueAsString(org.apache.poi.ss.usermodel.Cell cell) {
        if (cell == null) {
            return null;
        }
        
        try {
            switch (cell.getCellType()) {
                case STRING:
                    return cell.getStringCellValue();
                case NUMERIC:
                    return String.valueOf(cell.getNumericCellValue());
                case BOOLEAN:
                    return String.valueOf(cell.getBooleanCellValue());
                case FORMULA:
                    return cell.getCellFormula();
                default:
                    return null;
            }
        } catch (Exception e) {
            logger.warn("获取单元格值失败: {}", e.getMessage());
            return null;
        }
    }
}
