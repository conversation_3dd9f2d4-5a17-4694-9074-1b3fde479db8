package com.whfc.wp.handler;

import com.alibaba.excel.write.handler.RowWriteHandler;
import com.alibaba.excel.write.handler.context.RowWriteHandlerContext;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * 条件行处理器 - 用于根据条件动态插入Excel行
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/8
 */
public class ConditionalRowHandler implements RowWriteHandler {

    private static final Logger logger = LoggerFactory.getLogger(ConditionalRowHandler.class);

    private final Map<String, Object> dataMap;
    private boolean hasInsertedSpecialRow = false;

    public ConditionalRowHandler(Map<String, Object> dataMap) {
        this.dataMap = dataMap;
    }

    @Override
    public void afterRowDispose(RowWriteHandlerContext context) {
        if (context.getHead()) {
            return;
        }

        Row row = context.getRow();
        Sheet sheet = context.getWriteSheetHolder().getSheet();

        // 检查是否为"要使用的工器具"行，在其后插入特种作业人员行
        if (!hasInsertedSpecialRow && isToolRow(row)) {
            Boolean hasSpecialOperationPerson = (Boolean) dataMap.get("hasSpecialOperationPerson");
            if (hasSpecialOperationPerson != null && hasSpecialOperationPerson) {
                logger.debug("找到工器具行，准备插入特种作业人员行，当前行号: {}", row.getRowNum());
                insertSpecialOperationPersonRow(sheet, row.getRowNum() + 1);
                hasInsertedSpecialRow = true;
                logger.info("成功插入特种作业人员行");
            } else {
                logger.debug("没有特种作业人员数据，跳过插入");
            }
        }
    }
    
    /**
     * 判断是否为"要使用的工器具"行
     */
    private boolean isToolRow(Row row) {
        if (row == null) {
            return false;
        }

        // 遍历行中的所有单元格，查找包含"要使用的工器具"的单元格
        for (int i = 0; i < row.getLastCellNum(); i++) {
            if (row.getCell(i) != null) {
                String cellValue = getCellValueAsString(row.getCell(i));
                if (cellValue != null) {
                    logger.debug("检查单元格[{},{}]: '{}'", row.getRowNum(), i, cellValue);
                    if (cellValue.contains("要使用的工器具")) {
                        logger.debug("找到工器具行，行号: {}", row.getRowNum());
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * 插入特种作业人员行
     */
    private void insertSpecialOperationPersonRow(Sheet sheet, int insertRowIndex) {
        try {
            logger.debug("开始插入特种作业人员行，插入位置: {}, 当前最后行号: {}", insertRowIndex, sheet.getLastRowNum());

            // 向下移动现有行
            if (insertRowIndex <= sheet.getLastRowNum()) {
                sheet.shiftRows(insertRowIndex, sheet.getLastRowNum(), 1);
                logger.debug("成功移动行，从第{}行开始向下移动1行", insertRowIndex);
            }

            // 创建新行
            Row newRow = sheet.createRow(insertRowIndex);

            // 设置行高
            newRow.setHeight((short) 400);

            // 创建单元格并设置内容
            Cell cell1 = newRow.createCell(0);
            cell1.setCellValue("特种作业持证人员名字");

            Cell cell2 = newRow.createCell(1);
            String specialOperationPerson = (String) dataMap.get("specialOperationPerson");
            cell2.setCellValue(specialOperationPerson != null ? specialOperationPerson : "");

            logger.debug("设置单元格内容: 标题='特种作业持证人员名字', 内容='{}'", specialOperationPerson);

            // 设置单元格样式（复制上一行的样式）
            if (insertRowIndex > 0) {
                Row previousRow = sheet.getRow(insertRowIndex - 1);
                if (previousRow != null) {
                    copyRowStyle(sheet, previousRow, newRow);
                    logger.debug("复制了上一行的样式");
                }
            }

            // 合并单元格 - 将第2到第8列合并
            CellRangeAddress mergedRegion = new CellRangeAddress(insertRowIndex, insertRowIndex, 1, 7);
            sheet.addMergedRegion(mergedRegion);
            logger.debug("合并单元格: 行{}, 列1-7", insertRowIndex);

            logger.info("成功插入特种作业人员行，行号: {}, 内容: {}", insertRowIndex, specialOperationPerson);

        } catch (Exception e) {
            logger.error("插入特种作业人员行失败", e);
        }
    }

    /**
     * 复制行样式
     */
    private void copyRowStyle(Sheet sheet, Row sourceRow, Row targetRow) {
        try {
            Workbook workbook = sheet.getWorkbook();

            for (int i = 0; i < sourceRow.getLastCellNum(); i++) {
                Cell sourceCell = sourceRow.getCell(i);
                if (sourceCell != null) {
                    Cell targetCell = targetRow.getCell(i);
                    if (targetCell == null) {
                        targetCell = targetRow.createCell(i);
                    }

                    // 复制单元格样式
                    CellStyle newStyle = workbook.createCellStyle();
                    newStyle.cloneStyleFrom(sourceCell.getCellStyle());
                    targetCell.setCellStyle(newStyle);
                }
            }
        } catch (Exception e) {
            logger.warn("复制行样式失败", e);
        }
    }
    
    /**
     * 获取单元格的字符串值
     */
    private String getCellValueAsString(org.apache.poi.ss.usermodel.Cell cell) {
        if (cell == null) {
            return null;
        }
        
        try {
            switch (cell.getCellType()) {
                case STRING:
                    return cell.getStringCellValue();
                case NUMERIC:
                    return String.valueOf(cell.getNumericCellValue());
                case BOOLEAN:
                    return String.valueOf(cell.getBooleanCellValue());
                case FORMULA:
                    return cell.getCellFormula();
                default:
                    return null;
            }
        } catch (Exception e) {
            logger.warn("获取单元格值失败: {}", e.getMessage());
            return null;
        }
    }
}
