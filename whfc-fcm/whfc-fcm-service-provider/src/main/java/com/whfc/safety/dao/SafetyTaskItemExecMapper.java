package com.whfc.safety.dao;

import com.whfc.safety.dto.*;
import com.whfc.safety.entity.SafetyTaskItem;
import com.whfc.safety.entity.SafetyTaskItemExec;
import com.whfc.safety.entity.SafetyTaskItemTime;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface SafetyTaskItemExecMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(SafetyTaskItemExec record);

    int insertSelective(SafetyTaskItemExec record);

    SafetyTaskItemExec selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(SafetyTaskItemExec record);

    int updateByPrimaryKey(SafetyTaskItemExec record);

    /**
     * 检查日志
     *
     * @param deptId
     * @param date
     * @return
     */
    List<SafetyUserNumDTO> selectUserNum(@Param("deptId") Integer deptId, @Param("date") Date date);

    /**
     * 批量添加
     *
     * @param deptId
     * @param taskId
     * @param taskItem
     * @param itemParts
     * @param itemTimes
     */
    void insertAll(@Param("deptId") Integer deptId,
                   @Param("taskId") Integer taskId,
                   @Param("taskItem") SafetyTaskItem taskItem,
                   @Param("itemParts") List<SafetyTaskItemPartDTO> itemParts,
                   @Param("itemTimes") List<SafetyTaskItemTime> itemTimes);

    /**
     * 检查记录列表
     *
     * @param userId
     * @param deptId
     * @param keyword
     * @param overdue
     * @param startTime
     * @param endTime
     * @return
     */
    List<SafetyTaskExecDTO> selectByDeptId(@Param("userId") Integer userId,
                                           @Param("deptId") Integer deptId,
                                           @Param("keyword") String keyword,
                                           @Param("overdue") Integer overdue,
                                           @Param("partId") Integer partId,
                                           @Param("state") Integer state,
                                           @Param("startTime") Date startTime,
                                           @Param("endTime") Date endTime);

    /**
     * 查询到期时间为昨天的任务
     *
     * @return
     */
    List<SafetyTaskItemExec> selectYesterday();

    /**
     * 更新任务超期状态为超期
     *
     * @param execIds
     */
    void updateOverdue(List<Integer> execIds);

    /**
     * 查询开始时间为昨天的任务
     *
     * @return
     */
    List<SafetyTaskItemExec> selectToday();

    /**
     * 查询任务
     *
     * @param taskId
     * @return
     */
    List<SafetyTaskItemExec> selectByTaskId(Integer taskId);

    /**
     * 更新任务状态
     *
     * @param execIds
     */
    void updateStateByExecIds(List<Integer> execIds);

    /**
     * 查询是否存在未完成数据
     *
     * @param taskItemId
     * @return
     */
    List<SafetyTaskItemExec> selectUnfinishedByTaskItemId(Integer taskItemId);

    /**
     * 查询是否存在未完成数据
     *
     * @param taskId
     * @return
     */
    List<SafetyTaskItemExec> selectUnfinishedByTaskId(Integer taskId);

    /**
     * 检查信息数据
     *
     * @param taskItemId
     * @return
     */
    SafetyExecNumDTO selectExecNumByTaskItemId(Integer taskItemId);

    /**
     * 任务统计
     *
     * @param userId
     * @param deptId
     * @param keyword
     * @param overdue
     * @param startTime
     * @param endTime
     * @return
     */
    SafetyExecNumDTO selectExecNumByDeptId(@Param("userId") Integer userId,
                                           @Param("deptId") Integer deptId,
                                           @Param("keyword") String keyword,
                                           @Param("overdue") Integer overdue,
                                           @Param("startTime") Date startTime,
                                           @Param("endTime") Date endTime);

    /**
     * 查询我上报的数量
     *
     * @param userId
     * @param deptId
     * @param partId
     * @param startTime
     * @param endTime
     * @return
     */
    SafetyExecNumDTO selectMeNum(@Param("userId") Integer userId,
                                 @Param("deptId") Integer deptId,
                                 @Param("partId") Integer partId,
                                 @Param("startTime") Date startTime,
                                 @Param("endTime") Date endTime);

    /**
     * 检查信息详情
     *
     * @param taskItemId
     * @return
     */
    List<SafetyExecDetailsDTO> selectDetailsByTaskItemId(Integer taskItemId);

    /**
     * 查询检查任务信息
     *
     * @param userId
     * @param deptId
     * @param partId
     * @param state
     * @param startTime
     * @param endTime
     * @return
     */
    List<SafetyTaskExecDTO> selectByDeptIdAndUserId(@Param("userId") Integer userId,
                                                    @Param("deptId") Integer deptId,
                                                    @Param("partId") Integer partId,
                                                    @Param("state") Integer state,
                                                    @Param("startTime") Date startTime,
                                                    @Param("endTime") Date endTime);

    /**
     * 查询排查任务数量
     *
     * @param deptIdList
     * @param startTime
     * @param endTime
     * @return
     */
    Integer selectNumByDeptId(@Param("deptIdList") List<Integer> deptIdList,
                              @Param("startTime") Date startTime,
                              @Param("endTime") Date endTime);

    /**
     * 删除检查任务
     *
     * @param execId 检查任务ID
     */
    void logicDel(@Param("execId") Integer execId);

    /**
     * 根据检查任务ID查询检查任务信息
     *
     * @param execIds
     * @return
     */
    List<SafetyExecDetailsDTO> selectByExecIds(@Param("execIds") List<Integer> execIds);
}