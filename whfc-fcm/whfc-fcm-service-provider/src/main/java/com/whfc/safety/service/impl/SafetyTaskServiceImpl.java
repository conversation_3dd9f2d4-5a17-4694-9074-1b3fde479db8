package com.whfc.safety.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.deepoove.poi.XWPFTemplate;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.whfc.base.param.AppFileExportParam;
import com.whfc.base.service.AppExportService;
import com.whfc.common.exception.BizException;
import com.whfc.common.file.FileHandler;
import com.whfc.common.file.FileUploadUtil;
import com.whfc.common.file.properties.FileExpirationRules;
import com.whfc.common.poitl.Image;
import com.whfc.common.poitl.PoiTlUtil;
import com.whfc.common.result.PageData;
import com.whfc.common.result.ResultEnum;
import com.whfc.common.util.*;
import com.whfc.fuum.service.SysDeptService;
import com.whfc.safety.dao.*;
import com.whfc.safety.dto.*;
import com.whfc.safety.entity.*;
import com.whfc.safety.enums.*;
import com.whfc.safety.param.*;
import com.whfc.safety.service.SafetyTaskService;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.UrlResource;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2023/6/27 14:06
 */
@DubboService(interfaceClass = SafetyTaskService.class, version = "1.0.0", timeout = 30 * 1000)
public class SafetyTaskServiceImpl implements SafetyTaskService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private SafetyDictMapper safetyDictMapper;

    @Autowired
    private SafetyTaskMapper safetyTaskMapper;

    @Autowired
    private SafetyTaskItemMapper safetyTaskItemMapper;

    @Autowired
    private SafetyTaskItemPartMapper safetyTaskItemPartMapper;

    @Autowired
    private SafetyTaskItemTimeMapper safetyTaskItemTimeMapper;

    @Autowired
    private SafetyTaskItemExecMapper safetyTaskItemExecMapper;

    @Autowired
    private SafetyTaskItemImgMapper safetyTaskItemImgMapper;

    @DubboReference(interfaceClass = SysDeptService.class, version = "1.0.0")
    private SysDeptService sysDeptService;

    @DubboReference(interfaceClass = AppExportService.class, version = "1.0.0")
    private AppExportService appExportService;

    @Autowired
    private FileUploadUtil fileUploadUtil;

    @Autowired
    private FileHandler fileHandler;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void taskAdd(SafetyTaskAddParam param) {
        logger.info("添加检查任务,param:{}", param);
        SafetyTask qualityTask = new SafetyTask();
        qualityTask.setDeptId(param.getDeptId());
        qualityTask.setTitle(param.getTitle());
        qualityTask.setContent(param.getContent());
        qualityTask.setCreateUserId(param.getCreateUserId());
        qualityTask.setCreateUserName(param.getCreateUserName());
        safetyTaskMapper.insertSelective(qualityTask);
        List<SafetyTaskAddListParam> taskList = param.getTaskList();
        saveItem(qualityTask.getId(), taskList);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void taskEdit(SafetyTaskEditParam param) {
        logger.info("修改检查任务,param:{}", JSONObject.toJSONString(param));
        SafetyTask qualityTask = safetyTaskMapper.selectByPrimaryKey(param.getTaskId());
        if (qualityTask == null) {
            throw new BizException(ResultEnum.FAILURE.getCode(), "检查任务不存在");
        }
        if (!TaskState.unpublished.getValue().equals(qualityTask.getState())) {
            throw new BizException(ResultEnum.FAILURE.getCode(), "检查任务已发布不可修改");
        }
        qualityTask.setTitle(param.getTitle());
        qualityTask.setContent(param.getContent());
        qualityTask.setUpdateUserId(param.getUpdateUserId());
        qualityTask.setUpdateUserName(param.getUpdateUserName());
        safetyTaskMapper.updateByPrimaryKeySelective(qualityTask);
        // 删除对应任务
        safetyTaskItemMapper.del(qualityTask.getId());
        safetyTaskItemTimeMapper.del(qualityTask.getId());
        safetyTaskItemPartMapper.del(qualityTask.getId());
        List<SafetyTaskAddListParam> taskList = param.getTaskList();
        saveItem(qualityTask.getId(), taskList);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delTask(Integer taskId) throws BizException {
        logger.info("删除检查任务，taskId:{}", taskId);
        SafetyTask qualityTask = safetyTaskMapper.selectByPrimaryKey(taskId);
        if (qualityTask == null) {
            throw new BizException(ResultEnum.FAILURE.getCode(), "检查任务不存在");
        }
        if (!TaskState.unpublished.getValue().equals(qualityTask.getState())) {
            throw new BizException(ResultEnum.FAILURE.getCode(), "检查任务已发布不可删除");
        }
        qualityTask.setDelFlag(1);
        safetyTaskItemMapper.del(qualityTask.getId());
        safetyTaskItemTimeMapper.del(qualityTask.getId());
        safetyTaskItemPartMapper.del(qualityTask.getId());
        safetyTaskMapper.updateByPrimaryKeySelective(qualityTask);
    }

    @Override
    public PageData<SafetyTaskDTO> taskList(Integer deptId, String title, Integer overdue, Integer state, Date startTime, Date endTime, Integer pageNum, Integer pageSize) throws BizException {
        logger.info("检查任务列表,deptId:{},title:{},overdue:{},state:{},startTime:{},endTime:{},pageNum:{},pageSize:{}",
                deptId, title, overdue, state, startTime, endTime, pageNum, pageSize);
        if (startTime != null || endTime != null) {
            startTime = DateUtil.getDateBegin(startTime);
            endTime = DateUtil.getDateEnd(endTime);
        }
        PageHelper.startPage(pageNum, pageSize);
        List<SafetyTaskDTO> list = safetyTaskMapper.selectByDeptId(deptId, title, overdue, state, startTime, endTime);
        PageHelper.clearPage();
        return PageUtil.pageData(PageInfo.of(list));
    }


    @Override
    public SafetyExecNumDTO taskNum(Integer deptId, String title, Integer overdue, Date startTime, Date endTime) throws BizException {
        logger.info("检查任务统计,deptId:{},title：{}，overdue：{}，startTime:{},endTime:{}",
                deptId, title, overdue, startTime, endTime);
        if (startTime != null || endTime != null) {
            startTime = DateUtil.getDateBegin(startTime);
            endTime = DateUtil.getDateEnd(endTime);
        }
        return safetyTaskMapper.selectNumByDeptId(deptId, title, overdue, startTime, endTime);
    }

    @Override
    public List<SafetyTaskDetailsDTO> taskDetails(Integer taskId) throws BizException {
        logger.info("检查任务详情（不分页）,taskId:{}", taskId);
        List<SafetyTaskDetailsDTO> list = safetyTaskItemMapper.selectDetailsByTaskId(taskId);
        getTaskDetails(list);
        return list;
    }

    @Override
    public PageData<SafetyTaskDetailsDTO> taskDetails(Integer taskId, Integer pageNum, Integer pageSize) throws BizException {
        logger.info("检查任务详情（分页）,taskId:{}，pageNum：{}，pageSize：{}", taskId, pageNum, pageSize);
        PageHelper.startPage(pageNum, pageSize);
        List<SafetyTaskDetailsDTO> list = safetyTaskItemMapper.selectDetailsByTaskId(taskId);
        PageHelper.clearPage();
        getTaskDetails(list);
        return PageUtil.pageData(PageInfo.of(list));
    }

    @Override
    public PageData<SafetyExecDetailsDTO> execDetailsList(Integer taskItemId, Integer pageNum, Integer pageSize) throws BizException {
        logger.info("检查信息详情,taskItemId:{}，pageNum：{}，pageSize：{}", taskItemId, pageNum, pageSize);
        PageHelper.startPage(pageNum, pageSize);
        List<SafetyExecDetailsDTO> list = safetyTaskItemExecMapper.selectDetailsByTaskItemId(taskItemId);
        PageHelper.clearPage();
        list.forEach(qualityTaskExecDTO -> {
            List<String> imgUrls = safetyTaskItemImgMapper.selectImgUrlByExecId(qualityTaskExecDTO.getExecId());
            qualityTaskExecDTO.setImgUrls(imgUrls);
        });
        return PageUtil.pageData(PageInfo.of(list));
    }

    @Override
    public SafetyExecNumDTO execNum(Integer taskItemId) throws BizException {
        logger.info("检查信息数据,taskItemId:{}", taskItemId);
        return safetyTaskItemExecMapper.selectExecNumByTaskItemId(taskItemId);
    }


    @Override
    public PageData<SafetyTaskExecDTO> taskExecList(Integer userId, Integer deptId,
                                                    String keyword, Integer overdue,
                                                    Integer partId, Integer state,
                                                    Date startTime, Date endTime,
                                                    Integer pageNum, Integer pageSize) throws BizException {
        logger.info("我的任务,userId:{},deptId:{},keyword:{},overdue:{},partId:{},state:{},startTime:{},endTime:{},pageNum:{},pageSize:{}",
                userId, deptId, keyword, overdue, partId, state, startTime, endTime, pageNum, pageSize);
        if (startTime != null || endTime != null) {
            startTime = DateUtil.getDateBegin(startTime);
            endTime = DateUtil.getDateEnd(endTime);
        }
        PageHelper.startPage(pageNum, pageSize);
        List<SafetyTaskExecDTO> list = safetyTaskItemExecMapper.selectByDeptId(userId, deptId, keyword, overdue, partId, state, startTime, endTime);
        PageHelper.clearPage();
        list.forEach(qualityTaskExecDTO -> {
            List<String> imgUrls = safetyTaskItemImgMapper.selectImgUrlByExecId(qualityTaskExecDTO.getExecId());
            qualityTaskExecDTO.setImgUrls(imgUrls);
        });
        return PageUtil.pageData(PageInfo.of(list));
    }

    @Override
    public List<SafetyTaskExecDTO> taskExecList(Integer userId, Integer deptId, String keyword, Integer overdue, Integer state, Date startTime, Date endTime) throws BizException {
        logger.info("我的任务列表,userId:{},deptId:{},keyword:{},overdue:{},state:{},startTime:{},endTime:{}", userId, deptId, keyword, overdue, state, startTime, endTime);
        if (startTime != null || endTime != null) {
            startTime = DateUtil.getDateBegin(startTime);
            endTime = DateUtil.getDateEnd(endTime);
        }
        List<SafetyTaskExecDTO> list = safetyTaskItemExecMapper.selectByDeptId(userId, deptId, keyword, overdue, null, state, startTime, endTime);
        list.forEach(qualityTaskExecDTO -> {
            List<String> imgUrls = safetyTaskItemImgMapper.selectImgUrlByExecId(qualityTaskExecDTO.getExecId());
            qualityTaskExecDTO.setImgUrls(imgUrls);
        });
        return list;
    }

    @Override
    public PageData<SafetyTaskExecDTO> taskExecList(Integer deptId, Integer partId,
                                                    Date startTime, Date endTime,
                                                    Integer pageNum, Integer pageSize) throws BizException {
        logger.info("检查记录列表（分页）,deptId:{},partId:{},startTime:{},endTime:{},pageNum:{},pageSize:{}",
                deptId, partId, startTime, endTime, pageNum, pageSize);
        if (startTime != null || endTime != null) {
            startTime = DateUtil.getDateBegin(startTime);
            endTime = DateUtil.getDateEnd(endTime);
        }
        PageHelper.startPage(pageNum, pageSize);
        List<SafetyTaskExecDTO> list = safetyTaskItemExecMapper.selectByDeptIdAndUserId(null, deptId, partId, 2, startTime, endTime);
        PageHelper.clearPage();
        list.forEach(qualityTaskExecDTO -> {
            List<String> imgUrls = safetyTaskItemImgMapper.selectImgUrlByExecId(qualityTaskExecDTO.getExecId());
            qualityTaskExecDTO.setImgUrls(imgUrls);
        });
        return PageUtil.pageData(PageInfo.of(list));
    }

    @Override
    public List<SafetyTaskExecDTO> taskExecList(Integer deptId, Integer partId,
                                                Date startTime, Date endTime) throws BizException {
        logger.info("检查记录列表（不分页）,deptId：{},partId：{},startTime：{},endTime：{}", deptId, partId, startTime, endTime);
        if (startTime != null || endTime != null) {
            startTime = DateUtil.getDateBegin(startTime);
            endTime = DateUtil.getDateEnd(endTime);
        }
        List<SafetyTaskExecDTO> list = safetyTaskItemExecMapper.selectByDeptIdAndUserId(null, deptId, partId, 2, startTime, endTime);
        list.forEach(qualityTaskExecDTO -> {
            List<String> imgUrls = safetyTaskItemImgMapper.selectImgUrlByExecId(qualityTaskExecDTO.getExecId());
            qualityTaskExecDTO.setImgUrls(imgUrls);
        });
        return list;
    }

    @Override
    public PageData<SafetyTaskExecDTO> taskExecList(Integer userId, Integer deptId, Integer partId, Date startTime, Date endTime, Integer pageNum, Integer pageSize) throws BizException {
        logger.info("检查记录列表（分页）,userId:{},deptId:{},partId:{},startTime:{},endTime:{},pageNum:{},pageSize:{}",
                userId, deptId, partId, startTime, endTime, pageNum, pageSize);
        if (startTime != null || endTime != null) {
            startTime = DateUtil.getDateBegin(startTime);
            endTime = DateUtil.getDateEnd(endTime);
        }
        PageHelper.startPage(pageNum, pageSize);
        List<SafetyTaskExecDTO> list = safetyTaskItemExecMapper.selectByDeptIdAndUserId(userId, deptId, partId, 2, startTime, endTime);
        PageHelper.clearPage();
        list.forEach(qualityTaskExecDTO -> {
            List<String> imgUrls = safetyTaskItemImgMapper.selectImgUrlByExecId(qualityTaskExecDTO.getExecId());
            qualityTaskExecDTO.setImgUrls(imgUrls);
        });
        return PageUtil.pageData(PageInfo.of(list));
    }

    @Override
    public List<SafetyTaskExecDTO> taskExecList(Integer userId, Integer deptId, Integer partId, Date startTime, Date endTime) throws BizException {
        logger.info("检查记录列表（不分页）,userId:{},deptId：{},partId：{},startTime：{},endTime：{}", userId, deptId, partId, startTime, endTime);
        if (startTime != null || endTime != null) {
            startTime = DateUtil.getDateBegin(startTime);
            endTime = DateUtil.getDateEnd(endTime);
        }
        List<SafetyTaskExecDTO> list = safetyTaskItemExecMapper.selectByDeptIdAndUserId(userId, deptId, partId, 2, startTime, endTime);
        list.forEach(qualityTaskExecDTO -> {
            List<String> imgUrls = safetyTaskItemImgMapper.selectImgUrlByExecId(qualityTaskExecDTO.getExecId());
            qualityTaskExecDTO.setImgUrls(imgUrls);
        });
        return list;
    }

    @Override
    public void execDel(Integer execId) throws BizException {
        safetyTaskItemExecMapper.logicDel(execId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer report(SafetyTaskReportParam param) {
        logger.info("排查上报,param：{}", param);
        SafetyTaskItemExec exec = new SafetyTaskItemExec();
        exec.setDeptId(param.getDeptId());
        exec.setLat(param.getLat());
        exec.setLng(param.getLng());
        exec.setLocation(param.getLocation());
        if (param.getPartId() == null) {
            param.setPartId(-1);
        }
        exec.setPartId(param.getPartId());
        exec.setPartName(param.getPartName());
        exec.setCheckUserId(param.getUserId());
        exec.setCheckUserName(param.getUserName());
        exec.setCheckTime(new Date());
        exec.setOverdue(OverdueStatus.NOT_OVERDUE.getValue());
        exec.setCheckResult(param.getCheckResult());
        exec.setTaskType(TaskType.report.getValue());
        exec.setState(TaskItemState.done.getValue());
        safetyTaskItemExecMapper.insertSelective(exec);
        List<String> imgUrls = param.getImgUrls();
        if (imgUrls != null && imgUrls.size() > 0) {
            safetyTaskItemImgMapper.insertAll(exec.getId(), imgUrls);
        }
        return exec.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void taskRelease(SafetyTaskReleaseParam param) {
        logger.info("发布,param:{}", JSONObject.toJSONString(param));
        SafetyTask qualityTask = safetyTaskMapper.selectByPrimaryKey(param.getTaskId());
        if (qualityTask == null) {
            throw new BizException(ResultEnum.FAILURE.getCode(), "检查任务不存在");
        }
        qualityTask.setPublishUserId(param.getUserId());
        qualityTask.setPublishUserName(param.getUserName());
        qualityTask.setPublishTime(new Date());
        qualityTask.setUpdateUserId(param.getUserId());
        qualityTask.setUpdateUserName(param.getUserName());
        qualityTask.setState(TaskState.unstarted.getValue());
        List<SafetyTaskItem> items = safetyTaskItemMapper.selectByTaskId(param.getTaskId());
        items.forEach(taskItem -> {
            List<SafetyTaskItemPartDTO> itemParts = safetyTaskItemPartMapper.selectByTaskItemId(taskItem.getId());
            List<SafetyTaskItemTime> itemTimes = safetyTaskItemTimeMapper.selectByTaskItemId(taskItem.getId());
            safetyTaskItemExecMapper.insertAll(qualityTask.getDeptId(), qualityTask.getId(), taskItem, itemParts, itemTimes);
        });
        safetyTaskMapper.updateByPrimaryKeySelective(qualityTask);

        List<SafetyTaskItemExec> execs = safetyTaskItemExecMapper.selectByTaskId(param.getTaskId());
        if (execs == null || execs.isEmpty()) {
            return;
        }
        List<Integer> execIds = execs.stream().map(SafetyTaskItemExec::getId).distinct().collect(Collectors.toList());
        List<Integer> taskIds = execs.stream().map(SafetyTaskItemExec::getTaskId).distinct().collect(Collectors.toList());
        List<Integer> taskItemIds = execs.stream().map(SafetyTaskItemExec::getTaskItemId).distinct().collect(Collectors.toList());
        safetyTaskItemExecMapper.updateStateByExecIds(execIds);
        safetyTaskMapper.updateStateByTaskIds(taskIds);
        safetyTaskItemMapper.updateStateByTaskItem(taskItemIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void complete(SafetyTaskCompleteParam param) throws BizException {
        logger.info("完成任务,param:{}", JSONObject.toJSONString(param));
        SafetyTaskItemExec exec = safetyTaskItemExecMapper.selectByPrimaryKey(param.getExecId());
        if (exec == null) {
            throw new BizException(ResultEnum.FAILURE.getCode(), "检查任务不存在");
        }
        exec.setLat(param.getLat());
        exec.setLng(param.getLng());
        exec.setLocation(param.getLocation());
        exec.setCheckUserId(param.getUserId());
        exec.setCheckUserName(param.getUserName());
        exec.setCheckTime(new Date());
        exec.setCheckResult(param.getCheckResult());
        exec.setState(TaskItemState.done.getValue());
        safetyTaskItemExecMapper.updateByPrimaryKeySelective(exec);

        Integer taskItemId = exec.getTaskItemId();
        Integer taskId = exec.getTaskId();

        SafetyTaskItem item = safetyTaskItemMapper.selectByPrimaryKey(taskItemId);
        String[] progress = item.getProgress().split("/");
        Integer completeNum = Integer.valueOf(progress[0]);
        completeNum++;
        item.setProgress(completeNum + "/" + progress[1]);
        safetyTaskItemMapper.updateByPrimaryKeySelective(item);

        List<SafetyTaskItemExec> execs = safetyTaskItemExecMapper.selectUnfinishedByTaskItemId(taskItemId);
        if (execs == null || execs.isEmpty()) {
            item.setState(TaskItemState.done.getValue());
            safetyTaskItemMapper.updateByPrimaryKeySelective(item);
        }

        execs = safetyTaskItemExecMapper.selectUnfinishedByTaskId(taskId);
        if (execs == null || execs.isEmpty()) {
            SafetyTask qualityTask = safetyTaskMapper.selectByPrimaryKey(taskId);
            qualityTask.setState(TaskState.done.getValue());
            safetyTaskMapper.updateByPrimaryKeySelective(qualityTask);
        }
        List<String> imgs = param.getImgUrls();
        if (imgs != null && imgs.size() > 0) {
            safetyTaskItemImgMapper.insertAll(exec.getId(), imgs);
        }
    }


    @Override
    public SafetyExecNumDTO execNum(Integer userId, Integer deptId, String keyword, Integer overdue, Date startTime, Date endTime) throws BizException {
        logger.info("我的任务,userId:{},deptId:{},keyword:{},overdue:{},startTime:{},endTime:{}",
                userId, deptId, keyword, overdue, startTime, endTime);
        if (startTime != null || endTime != null) {
            startTime = DateUtil.getDateBegin(startTime);
            endTime = DateUtil.getDateEnd(endTime);
        }
        return safetyTaskItemExecMapper.selectExecNumByDeptId(userId, deptId, keyword, overdue, startTime, endTime);
    }


    @Override
    public SafetyExecNumDTO execNum(Integer userId, Integer deptId, Integer partId, Date startTime, Date endTime) throws BizException {
        logger.info("检查记录统计,deptId:{},userId:{},partId:{},startTime:{},endTime:{}",
                deptId, userId, partId, startTime, endTime);
        startTime = DateUtil.getDateBegin(startTime);
        endTime = DateUtil.getDateEnd(endTime);
        return safetyTaskItemExecMapper.selectMeNum(userId, deptId, partId, startTime, endTime);
    }

    @Override
    public void excExport(List<Integer> execIds, Integer deptId) throws BizException {

        // 导出参数
        SafetyDict dict = safetyDictMapper.selectByDeptIdAndCode(deptId, SafetyDictConst.safety_check_export);
        if (dict == null) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), "缺少导出参数配置");
        }
        SafetyCheckExportParam param = JSONUtil.parseObject(dict.getParam(), SafetyCheckExportParam.class);
        if (param == null) {
            throw new BizException(ResultEnum.NOT_FOUND.getCode(), "缺少导出参数配置");
        }
        String template = param.getTemplateTask();

        // 导出记录
        String fileDesc = "安全巡查导出";
        String fileName = RandomUtil.getRandomFileName();
        String suffix = execIds.size() > 1 ? "zip" : "docx";
        AppFileExportParam fileExport = new AppFileExportParam();
        fileExport.setDeptId(deptId);
        fileExport.setFileName(fileName);
        fileExport.setFileFormat(suffix.toUpperCase());
        fileExport.setFileState(0);
        fileExport.setFileDesc(fileDesc);
        Integer exportId = appExportService.install(fileExport);

        // 异步执行导出
        CompletableFuture.runAsync(() -> {
            try {
                // 导出参数
                String deptName = sysDeptService.getDeptName(deptId);

                logger.info("安全task导出,生成文件开始.....");

                List<InputStream> iss = new ArrayList<>(execIds.size());
                for (Integer execId : execIds) {
                    logger.info("安全task导出,deptName:{},execId:{}", deptName, execId);

                    Map<String, Object> checkMap = this.getTaskExportMap(execId);
                    checkMap.put("deptName", deptName);

                    // 模版文件
                    InputStream is = new UrlResource(template).getInputStream();

                    // 目标文件
                    File tempFile = File.createTempFile("safety", ".docx");
                    String tempFilePath = tempFile.getAbsolutePath();

                    // 生成文件
                    XWPFTemplate.compile(is).render(checkMap).writeToFile(tempFilePath);
                    logger.info("安全task导出文件路径:{}", tempFilePath);

                    iss.add(new FileInputStream(tempFilePath));
                }

                logger.info("安全task导出,生成文件结束.....");

                // 文件压缩
                InputStream is = suffix.equals("zip") ? ZipUtil.zipWordFile(iss) : iss.get(0);

                // 上传oss
                String path = "safety/task/" + fileName + "." + suffix;
                String fileUrl = fileUploadUtil.upload(path, is, FileExpirationRules.DAYS_7);

                // 导出成功
                logger.info("安全task导出成功,文件路径:{}", fileUrl);
                appExportService.fileExportSuccess(exportId, fileUrl);
            } catch (Exception ex) {
                appExportService.fileExportFailure(exportId);
                logger.error("安全task导出失败", ex);
            }
        });
    }

    /**
     * 保存检查任务项
     *
     * @param taskId
     * @param taskList
     */
    private void saveItem(Integer taskId, List<SafetyTaskAddListParam> taskList) {
        taskList.forEach(taskParam -> {
            SafetyTaskItem item = new SafetyTaskItem();
            item.setTaskId(taskId);
            item.setCheckUserId(taskParam.getUserId());
            item.setCheckUserName(taskParam.getUserName());
            item.setCheckType(taskParam.getCheckType());
            List<Date> timeList = taskParam.getTimeList();
            List<Integer> partIdList = taskParam.getPartIdList();
            item.setProgress(getProgress(taskParam.getCheckType(), partIdList, timeList));
            safetyTaskItemMapper.insertSelective(item);
            savePartAndTime(taskId, item.getId(), taskParam);
        });
    }


    /**
     * 保存部位和时间
     *
     * @param taskId
     * @param itemId
     * @param param
     */
    private void savePartAndTime(Integer taskId, Integer itemId, SafetyTaskAddListParam param) {
        List<Date> timeList = param.getTimeList();
        timeList.sort(Comparator.naturalOrder());
        if (CheckType.dailyCheck.getValue().equals(param.getCheckType())) {
            List<SafetyTaskItemTime> mapList = new ArrayList<>();
            timeList.forEach(date -> {
                SafetyTaskItemTime time = new SafetyTaskItemTime();
                time.setStartDate(DateUtil.getDateBegin(date));
                time.setEndDate(DateUtil.getDateEnd(date));
                mapList.add(time);
            });
            safetyTaskItemTimeMapper.insertAll(taskId, itemId, mapList);
        } else if (CheckType.singleCheck.getValue().equals(param.getCheckType())) {
            SafetyTaskItemTime time = new SafetyTaskItemTime();
            time.setTaskId(taskId);
            time.setTaskItemId(itemId);
            time.setStartDate(DateUtil.getDateBegin(param.getTimeList().get(0)));
            time.setEndDate(DateUtil.getDateEnd(param.getTimeList().get(1)));
            safetyTaskItemTimeMapper.insertSelective(time);
        }
        safetyTaskItemPartMapper.insertAll(taskId, itemId, param.getPartIdList());
    }

    /**
     * 获取进度
     *
     * @param checkType
     * @param partIdList
     * @param timeList
     * @return
     */
    private String getProgress(Integer checkType, List<Integer> partIdList, List<Date> timeList) {
        Integer taskAllNum = 0;
        if (CheckType.dailyCheck.getValue().equals(checkType)) {
            taskAllNum = timeList.size() * partIdList.size();
        } else if (CheckType.singleCheck.getValue().equals(checkType)) {
            taskAllNum = partIdList.size();
        }
        return "0/" + taskAllNum;
    }

    /**
     * 获取检查任务详情
     *
     * @param list
     */
    private void getTaskDetails(List<SafetyTaskDetailsDTO> list) {
        list.forEach(qualityTaskDetailsDTO -> {
            List<SafetyTaskItemPartDTO> partList = safetyTaskItemPartMapper.selectByTaskItemId(qualityTaskDetailsDTO.getTaskItemId());
            List<SafetyTaskItemTime> timeList = safetyTaskItemTimeMapper.selectByTaskItemId(qualityTaskDetailsDTO.getTaskItemId());
            List<Date> times = new ArrayList<>();
            if (CheckType.singleCheck.getValue().equals(qualityTaskDetailsDTO.getCheckType())) {
                SafetyTaskItemTime time = timeList.get(0);
                times.add(time.getStartDate());
                times.add(time.getEndDate());
            } else {
                times = timeList.stream().map(SafetyTaskItemTime::getStartDate).collect(Collectors.toList());
            }
            qualityTaskDetailsDTO.setTimes(times);
            qualityTaskDetailsDTO.setPartList(partList);
        });
    }

    private Map<String, Object> getTaskExportMap(Integer execId) {
        SafetyTaskItemExec exec = safetyTaskItemExecMapper.selectByPrimaryKey(execId);
        List<String> imgUrls = safetyTaskItemImgMapper.selectImgUrlByExecId(execId);
        Map<String, Object> taskMap = new HashMap<>();
        taskMap.put("checkUserName", exec.getCheckUserName());
        taskMap.put("checkTime", DateUtil.formatDateTime(exec.getCheckTime()));
        taskMap.put("partName", exec.getPartName());
        taskMap.put("checkResult", exec.getCheckResult());
        taskMap.put("checkImgUrls", getImages(imgUrls, 300, 200));
        return taskMap;
    }

    private List<Image> getImages(List<String> imgUrls, int width, int height) {
        return imgUrls.stream()
                .filter(imgUrl -> !StringUtils.isEmpty(imgUrl))
                .map(imgUrl -> fileHandler.getDownloadUrl(imgUrl))
                .map(imgUrl -> PoiTlUtil.getImage(imgUrl, width, height))
                .collect(Collectors.toList());
    }
}
