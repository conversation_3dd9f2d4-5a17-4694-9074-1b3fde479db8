package com.whfc.ms.api.safety.service.impl;

import com.whfc.common.result.PageData;
import com.whfc.ms.api.safety.service.MsSafetyTaskService;
import com.whfc.safety.dto.*;
import com.whfc.safety.param.*;
import com.whfc.safety.service.SafetyTaskService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description 检查任务
 * <AUTHOR>
 * @Date 2021/3/10 15:00
 * @Version 1.0
 */
@Service
public class MsSafetyTaskServiceImpl implements MsSafetyTaskService {

    @DubboReference(interfaceClass = SafetyTaskService.class, version = "1.0.0")
    private SafetyTaskService safetyTaskService;

    @Override
    public void taskAdd(SafetyTaskAddParam param) {
        safetyTaskService.taskAdd(param);
    }

    @Override
    public void taskEdit(SafetyTaskEditParam param) {
        safetyTaskService.taskEdit(param);
    }

    @Override
    public void del(Integer taskId) {
        safetyTaskService.delTask(taskId);
    }

    @Override
    public PageData<SafetyTaskDTO> list(Integer deptId, String title, Integer overdue, Date startTime, Date endTime, Integer state, Integer pageSize, Integer pageNum) {
        return safetyTaskService.taskList(deptId, title, overdue, state, startTime, endTime, pageNum, pageSize);
    }

    @Override
    public SafetyExecNumDTO taskNum(Integer deptId, String title, Integer overdue, Date startTime, Date endTime) {
        return safetyTaskService.taskNum(deptId, title, overdue, startTime, endTime);
    }

    @Override
    public PageData<SafetyTaskDetailsDTO> taskDetails(Integer taskId, Integer pageSize, Integer pageNum) {
        return safetyTaskService.taskDetails(taskId, pageNum, pageSize);
    }

    @Override
    public List<SafetyTaskDetailsDTO> taskDetails(Integer taskId) {
        return safetyTaskService.taskDetails(taskId);
    }

    @Override
    public PageData<SafetyExecDetailsDTO> execDetailsList(Integer taskItemId, Integer pageSize, Integer pageNum) {
        return safetyTaskService.execDetailsList(taskItemId, pageNum, pageSize);
    }

    @Override
    public SafetyExecNumDTO execNum(Integer taskItemId) {
        return safetyTaskService.execNum(taskItemId);
    }

    @Override
    public PageData<SafetyTaskExecDTO> taskExecList(Integer userId, Integer deptId, String keyword, Integer overdue, Integer state, Date startTime, Date endTime, Integer pageSize, Integer pageNum) {
        return safetyTaskService.taskExecList(userId, deptId, keyword, overdue, null, state, startTime, endTime, pageNum, pageSize);
    }

    @Override
    public SafetyExecNumDTO execNum(Integer userId, Integer deptId, String keyword, Integer overdue, Date startTime, Date endTime) {
        return safetyTaskService.execNum(userId, deptId, keyword, overdue, startTime, endTime);
    }

    @Override
    public Integer execAdd(SafetyTaskReportParam param) {
        return safetyTaskService.report(param);
    }

    @Override
    public void execDel(Integer execId) {
        safetyTaskService.execDel(execId);
    }

    @Override
    public void execComplete(SafetyTaskCompleteParam param) {
        safetyTaskService.complete(param);
    }

    @Override
    public void taskRelease(SafetyTaskReleaseParam param) {
        safetyTaskService.taskRelease(param);
    }

    @Override
    public SafetyExecNumDTO execNum(Integer userId, Integer deptId, Integer partId, Date startTime, Date endTime) {
        return safetyTaskService.execNum(userId, deptId, partId, startTime, endTime);
    }

    @Override
    public List<SafetyTaskExecDTO> execList(Integer userId, Integer deptId, Integer partId, Integer report, Date startTime, Date endTime) {
        if (1 == report) {
            return safetyTaskService.taskExecList(userId, deptId, partId, startTime, endTime);
        } else {
            return safetyTaskService.taskExecList(deptId, partId, startTime, endTime);
        }
    }

    @Override
    public PageData<SafetyTaskExecDTO> execList(Integer userId, Integer deptId, Integer partId, Integer report, Date startTime, Date endTime, Integer pageSize, Integer pageNum) {
        if (1 == report) {

            return safetyTaskService.taskExecList(userId, deptId, partId, startTime, endTime, pageNum, pageSize);
        } else {
            return safetyTaskService.taskExecList(deptId, partId, startTime, endTime, pageNum, pageSize);
        }
    }

    @Override
    public SafetyExecNumDTO execNumDeptId(Integer deptId, String title, Integer overdue, Date startTime, Date endTime) {
        return safetyTaskService.execNum(null, deptId, title, overdue, startTime, endTime);
    }

    @Override
    public void execExport(String execIds, Integer deptId) {
        String[] strIds = execIds.split(",");
        List<Integer> intIds = Arrays.stream(strIds).map(Integer::valueOf).collect(Collectors.toList());
        safetyTaskService.excExport(intIds, deptId);
    }
}
