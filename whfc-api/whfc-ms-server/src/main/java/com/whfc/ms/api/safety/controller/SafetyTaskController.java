package com.whfc.ms.api.safety.controller;

import com.whfc.common.base.BaseController;
import com.whfc.common.result.PageData;
import com.whfc.common.result.Result;
import com.whfc.common.result.ResultUtil;
import com.whfc.common.util.SessionAttr;
import com.whfc.fuum.entity.SysUser;
import com.whfc.ms.api.safety.service.MsSafetyTaskService;
import com.whfc.safety.dto.*;
import com.whfc.safety.param.*;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * @Description 检查任务
 * <AUTHOR>
 * @Date 2021/3/10 14:59
 * @Version 1.0
 */
@RestController
@RequestMapping("/ms/api/safety/task/")
public class SafetyTaskController extends BaseController {

    @Autowired
    private MsSafetyTaskService safetyTaskService;

    @RequiresPermissions("safety/checkTask/add")
    @PostMapping("add")
    public Result taskAdd(@SessionAttribute(SessionAttr.MS_USER) SysUser user, @Validated @RequestBody SafetyTaskAddParam param) {
        logger.info("添加检查任务,loginer:{},param:{}", user.getUsername(), param);
        param.setCreateUserId(user.getId());
        param.setCreateUserName(user.getNickname());
        safetyTaskService.taskAdd(param);
        return ResultUtil.success();
    }

    @RequiresPermissions("safety/checkTask/edit")
    @PostMapping("edit")
    public Result edit(@SessionAttribute(SessionAttr.MS_USER) SysUser user, @Validated @RequestBody SafetyTaskEditParam param) {
        logger.info("修改检查任务,loginer:{},param:{}", user.getUsername(), param);
        param.setUpdateUserId(user.getId());
        param.setUpdateUserName(user.getNickname());
        safetyTaskService.taskEdit(param);
        return ResultUtil.success();
    }

    @RequiresPermissions("safety/checkTask/del")
    @PostMapping("/del/{taskId}")
    public Result del(@SessionAttribute(SessionAttr.MS_USER) SysUser user,
                      @PathVariable("taskId") Integer taskId) {
        logger.info("删除质量巡检,loginer:{},taskId:{}", user.getUsername(), taskId);
        safetyTaskService.del(taskId);
        return ResultUtil.success();
    }

    @GetMapping("list")
    public Result list(@SessionAttribute(SessionAttr.MS_USER) SysUser user,
                       @RequestParam("deptId") Integer deptId,
                       @RequestParam(value = "title", required = false) String title,
                       @RequestParam(value = "overdue", required = false) Integer overdue,
                       @RequestParam(value = "startTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startTime,
                       @RequestParam(value = "endTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endTime,
                       @RequestParam(value = "state", required = false) Integer state,
                       @RequestParam("pageSize") Integer pageSize,
                       @RequestParam("pageNum") Integer pageNum) {
        logger.info("检查任务列表,loginer:{},deptId:{},title:{},overdue:{},startTime:{},endTime:{},state:{}",
                user.getUsername(), deptId, title, overdue, startTime, endTime, state);
        PageData<SafetyTaskDTO> list = safetyTaskService.list(deptId, title, overdue, startTime, endTime, state, pageSize, pageNum);
        return ResultUtil.success(list);
    }

    @GetMapping("num")
    public Result taskNum(@SessionAttribute(SessionAttr.MS_USER) SysUser user,
                          @RequestParam("deptId") Integer deptId,
                          @RequestParam(value = "title", required = false) String title,
                          @RequestParam(value = "overdue", required = false) Integer overdue,
                          @RequestParam(value = "startTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startTime,
                          @RequestParam(value = "endTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endTime) {
        logger.info("检查任务统计,loginer:{},deptId:{},title:{},overdue:{},startTime:{},endTime:{}", user.getUsername(), deptId, title, overdue, startTime, endTime);
        SafetyExecNumDTO data = safetyTaskService.taskNum(deptId, title, overdue, startTime, endTime);
        return ResultUtil.success(data);
    }

    @GetMapping(value = "detailsList", params = {"pageSize", "pageNum"})
    public Result taskDetails(@SessionAttribute(SessionAttr.MS_USER) SysUser user,
                              @RequestParam("taskId") Integer taskId,
                              @RequestParam("pageSize") Integer pageSize,
                              @RequestParam("pageNum") Integer pageNum) {
        logger.info("查询检查任务详情（分页）,loginer:{},taskId:{}", user.getUsername(), taskId);
        PageData<SafetyTaskDetailsDTO> list = safetyTaskService.taskDetails(taskId, pageSize, pageNum);
        return ResultUtil.success(list);
    }

    @GetMapping(value = "detailsList")
    public Result taskDetails(@SessionAttribute(SessionAttr.MS_USER) SysUser user,
                              @RequestParam("taskId") Integer taskId) {
        logger.info("查询检查任务详情（不分页）,loginer:{},taskId:{}", user.getUsername(), taskId);
        List<SafetyTaskDetailsDTO> list = safetyTaskService.taskDetails(taskId);
        return ResultUtil.success(list);
    }


    @GetMapping("execDetailsList")
    public Result execDetailsList(@SessionAttribute(SessionAttr.MS_USER) SysUser user,
                                  @RequestParam("taskItemId") Integer taskItemId,
                                  @RequestParam("pageSize") Integer pageSize,
                                  @RequestParam("pageNum") Integer pageNum) {
        logger.info("检查信息详情,loginer:{},taskId:{},pageSize：{}，pageNum：{}", user.getUsername(), taskItemId, pageSize, pageNum);
        PageData<SafetyExecDetailsDTO> list = safetyTaskService.execDetailsList(taskItemId, pageSize, pageNum);
        return ResultUtil.success(list);
    }

    @GetMapping(value = "execNum")
    public Result execNum(@SessionAttribute(SessionAttr.MS_USER) SysUser user, @RequestParam("taskItemId") Integer taskItemId) {
        logger.info("检查信息数据,loginer:{},taskId:{}", user.getUsername(), taskItemId);
        SafetyExecNumDTO data = safetyTaskService.execNum(taskItemId);
        return ResultUtil.success(data);
    }

    @RequiresPermissions("safety/checkTask/release")
    @PostMapping("release")
    public Result taskRelease(@SessionAttribute(SessionAttr.MS_USER) SysUser user,
                              @Validated @RequestBody SafetyTaskReleaseParam param) {
        logger.info("发布,loginer:{},param:{}", user.getUsername(), param);
        safetyTaskService.taskRelease(param);
        return ResultUtil.success();
    }

    @GetMapping(value = "me/exec/list")
    public Result execList(@SessionAttribute(SessionAttr.MS_USER) SysUser user,
                           @RequestParam("deptId") Integer deptId,
                           @RequestParam(value = "state", required = false) Integer state,
                           @RequestParam(value = "keyword", required = false) String keyword,
                           @RequestParam(value = "overdue", required = false) Integer overdue,
                           @RequestParam(value = "startTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startTime,
                           @RequestParam(value = "endTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endTime,
                           @RequestParam("pageSize") Integer pageSize,
                           @RequestParam("pageNum") Integer pageNum) {
        logger.info("我的任务,loginer:{},deptId：{},keyword：{},overdue：{},startTime：{},endTime：{}",
                user.getUsername(), deptId, keyword, overdue, startTime, endTime);
        PageData<SafetyTaskExecDTO> list = safetyTaskService.taskExecList(user.getId(), deptId, keyword, overdue, state, startTime, endTime, pageSize, pageNum);
        return ResultUtil.success(list);
    }

    @GetMapping(value = "me/exec/num")
    public Result execNum(@SessionAttribute(SessionAttr.MS_USER) SysUser user,
                          @RequestParam("deptId") Integer deptId,
                          @RequestParam(value = "keyword", required = false) String keyword,
                          @RequestParam(value = "overdue", required = false) Integer overdue,
                          @RequestParam(value = "startTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startTime,
                          @RequestParam(value = "endTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endTime) {
        logger.info("我的任务统计,loginer:{},deptId：{},keyword：{},overdue：{},startTime：{},endTime：{}",
                user.getUsername(), deptId, keyword, overdue, startTime, endTime);
        SafetyExecNumDTO date = safetyTaskService.execNum(user.getId(), deptId, keyword, overdue, startTime, endTime);
        return ResultUtil.success(date);
    }

    @PostMapping("/exec/add")
    public Result execAdd(@SessionAttribute(SessionAttr.MS_USER) SysUser user,
                          @Validated @RequestBody SafetyTaskReportParam param) {
        logger.info("排查上报,loginer:{},param:{}", user.getUsername(), param);
        param.setUserId(user.getId());
        param.setUserName(user.getNickname());
        Integer execId = safetyTaskService.execAdd(param);
        return ResultUtil.success(execId);
    }

    @RequiresPermissions(value = "safety/task/exec/del")
    @PostMapping("/exec/del/{execId}")
    public Result execDel(@SessionAttribute(SessionAttr.MS_USER) SysUser user,
                          @PathVariable("execId") Integer execId) {
        logger.info("删除检查信息,loginer:{},taskId:{}", user.getUsername(), execId);
        safetyTaskService.execDel(execId);
        return ResultUtil.success();
    }

    @RequiresPermissions("safety/task/complete")
    @PostMapping("/exec/complete")
    public Result execComplete(@SessionAttribute(SessionAttr.MS_USER) SysUser user,
                               @Validated @RequestBody SafetyTaskCompleteParam param) {
        logger.info("完成任务,loginer:{},param:{}", user.getUsername(), param);
        safetyTaskService.execComplete(param);
        return ResultUtil.success();
    }


    @GetMapping("index/execNum")
    public Result execNum(@SessionAttribute(SessionAttr.MS_USER) SysUser user,
                          @RequestParam("deptId") Integer deptId,
                          @RequestParam(value = "partId", required = false) Integer partId,
                          @RequestParam(value = "startTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startTime,
                          @RequestParam(value = "endTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endTime) {
        logger.info("检查记录统计,loginer:{},deptId：{},partId：{},startTime：{},endTime：{}", user.getUsername(), deptId, partId, startTime, endTime);
        SafetyExecNumDTO data = safetyTaskService.execNum(user.getId(), deptId, partId, startTime, endTime);
        return ResultUtil.success(data);
    }

    @GetMapping("exec/list")
    public Result execList(@SessionAttribute(SessionAttr.MS_USER) SysUser user,
                           @RequestParam("deptId") Integer deptId,
                           @RequestParam(value = "partId", required = false) Integer partId,
                           @RequestParam(value = "report", required = false) Integer report,
                           @RequestParam(value = "startTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startTime,
                           @RequestParam(value = "endTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endTime) {
        logger.info("检查记录列表（分页）,loginer:{},deptId:{},partId:{},report:{},startTime:{},endTime:{}", user.getUsername(), deptId, partId, report, startTime, endTime);
        List<SafetyTaskExecDTO> list = safetyTaskService.execList(user.getId(), deptId, partId, report, startTime, endTime);
        return ResultUtil.success(list);
    }

    @GetMapping(value = "exec/list", params = {"pageSize", "pageNum"})
    public Result execList(@SessionAttribute(SessionAttr.MS_USER) SysUser user,
                           @RequestParam("deptId") Integer deptId,
                           @RequestParam(value = "partId", required = false) Integer partId,
                           @RequestParam(value = "report", required = false) Integer report,
                           @RequestParam(value = "startTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startTime,
                           @RequestParam(value = "endTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endTime,
                           @RequestParam("pageSize") Integer pageSize,
                           @RequestParam("pageNum") Integer pageNum) {
        logger.info("检查记录列表（不分页）,loginer:{},deptId:{},partId:{},report:{},startTime:{},endTime:{},pageSize:{},pageNum:{}",
                user.getUsername(), deptId, partId, report, startTime, endTime, pageSize, pageNum);
        PageData<SafetyTaskExecDTO> list = safetyTaskService.execList(user.getId(), deptId, partId, report, startTime, endTime, pageSize, pageNum);
        return ResultUtil.success(list);
    }

    @GetMapping("exec/num")
    public Result execNumDeptId(@SessionAttribute(SessionAttr.MS_USER) SysUser user,
                                @RequestParam("deptId") Integer deptId,
                                @RequestParam(value = "title", required = false) String title,
                                @RequestParam(value = "overdue", required = false) Integer overdue,
                                @RequestParam(value = "startTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startTime,
                                @RequestParam(value = "endTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endTime) {
        logger.info("检查任务统计,loginer:{},deptId:{},title:{},overdue:{},startTime:{},endTime:{}", user.getUsername(), deptId, title, overdue, startTime, endTime);
        SafetyExecNumDTO data = safetyTaskService.execNumDeptId(deptId, title, overdue, startTime, endTime);
        return ResultUtil.success(data);
    }

    @GetMapping("exec/export")
    public Result execExport(@SessionAttribute(SessionAttr.MS_USER) SysUser user,
                             @RequestParam("deptId") Integer deptId,
                             @RequestParam("execIds") String execIds) {
        logger.info("检查记录导出,loginer:{},execIds:{}", user.getUsername(), execIds);
        safetyTaskService.execExport(execIds, deptId);
        return ResultUtil.success();
    }
}
