package com.whfc.ms.api.safety.service;

import com.whfc.common.result.PageData;
import com.whfc.safety.dto.*;
import com.whfc.safety.param.*;

import java.util.Date;
import java.util.List;

/**
 * @Description 检查任务
 * <AUTHOR>
 * @Date 2021/3/10 15:00
 * @Version 1.0
 */
public interface MsSafetyTaskService {

    /**
     * 添加检查任务
     *
     * @param param
     */
    void taskAdd(SafetyTaskAddParam param);


    /**
     * 修改检查任务
     *
     * @param param
     */
    void taskEdit(SafetyTaskEditParam param);

    /**
     * 删除
     *
     * @param taskId
     */
    void del(Integer taskId);

    /**
     * 检查任务列表
     *
     * @param deptId
     * @param title
     * @param overdue
     * @param startTime
     * @param endTime
     * @param state
     * @return
     */
    PageData<SafetyTaskDTO> list(Integer deptId, String title,
                                 Integer overdue, Date startTime,
                                 Date endTime, Integer state,
                                 Integer pageSize, Integer pageNum);

    /**
     * 检查任务统计
     *
     * @param deptId
     * @param title
     * @param overdue
     * @param startTime
     * @param endTime
     * @return
     */
    SafetyExecNumDTO taskNum(Integer deptId, String title, Integer overdue, Date startTime, Date endTime);

    /**
     * 检查任务详情（分页）
     *
     * @param taskId
     * @param pageSize
     * @param pageNum
     * @return
     */
    PageData<SafetyTaskDetailsDTO> taskDetails(Integer taskId, Integer pageSize, Integer pageNum);

    /**
     * 检查任务详情（不分页）
     *
     * @param taskId
     * @return
     */
    List<SafetyTaskDetailsDTO> taskDetails(Integer taskId);

    /**
     * @param taskItemId
     * @return
     */
    PageData<SafetyExecDetailsDTO> execDetailsList(Integer taskItemId, Integer pageSize, Integer pageNum);

    /**
     * 检查信息数据
     *
     * @param taskItemId
     * @return
     */
    SafetyExecNumDTO execNum(Integer taskItemId);

    /**
     * 发布任务
     *
     * @param param
     */
    void taskRelease(SafetyTaskReleaseParam param);

    /**
     * 我的任务
     *
     * @param userId
     * @param deptId
     * @param keyword
     * @param overdue
     * @param startTime
     * @param endTime
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageData<SafetyTaskExecDTO> taskExecList(Integer userId, Integer deptId,
                                             String keyword, Integer overdue,
                                             Integer state,
                                             Date startTime, Date endTime,
                                             Integer pageSize, Integer pageNum);

    /**
     * 我的任务统计
     *
     * @param userId
     * @param deptId
     * @param keyword
     * @param overdue
     * @param startTime
     * @param endTime
     * @return
     */
    SafetyExecNumDTO execNum(Integer userId, Integer deptId, String keyword, Integer overdue, Date startTime, Date endTime);

    /**
     * 排查上报
     *
     * @param param
     */
    Integer execAdd(SafetyTaskReportParam param);

    /**
     * 删除
     *
     * @param execId
     */
    void execDel(Integer execId);

    /**
     * 完成任务
     *
     * @param param
     */
    void execComplete(SafetyTaskCompleteParam param);


    /**
     * 查询任务统计
     *
     * @param userId
     * @param deptId
     * @param partId
     * @param startTime
     * @param endTime
     * @return
     */
    SafetyExecNumDTO execNum(Integer userId, Integer deptId, Integer partId, Date startTime, Date endTime);

    /**
     * 检查记录列表（不分页）
     *
     * @param userId
     * @param deptId
     * @param partId
     * @param report
     * @param startTime
     * @param endTime
     * @return
     */
    List<SafetyTaskExecDTO> execList(Integer userId, Integer deptId, Integer partId, Integer report, Date startTime, Date endTime);

    /**
     * 检查记录列表（分页）
     *
     * @param userId
     * @param deptId
     * @param partId
     * @param report
     * @param startTime
     * @param endTime
     * @param pageSize
     * @param pageNum
     * @return
     */
    PageData<SafetyTaskExecDTO> execList(Integer userId, Integer deptId, Integer partId, Integer report, Date startTime, Date endTime, Integer pageSize, Integer pageNum);


    /**
     * 检查记录统计
     *
     * @param deptId
     * @param title
     * @param overdue
     * @param startTime
     * @param endTime
     * @return
     */
    SafetyExecNumDTO execNumDeptId(Integer deptId, String title, Integer overdue, Date startTime, Date endTime);


    /**
     * 检查记录导出
     *
     * @param execIds
     * @param deptId
     */
    void execExport(String execIds, Integer deptId);

}
