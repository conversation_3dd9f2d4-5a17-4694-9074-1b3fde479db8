package com.whfc.ms.api.uni.controller;

import com.whfc.common.result.Result;
import com.whfc.common.result.ResultUtil;
import com.whfc.common.util.RequestAttr;
import com.whfc.common.util.SessionAttr;
import com.whfc.fuum.entity.SysUser;
import com.whfc.uni.service.AppDictService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@Slf4j
@Validated
@RestController
@RequestMapping("/ms/api/app/dict")
@Tag(name = "应用配置")
public class AppDictController {

    @DubboReference(interfaceClass = AppDictService.class, version = "1.0.0")
    private AppDictService appDictService;

    @Operation(summary = "查询应用配置数据")
    @RequestMapping("/data")
    public Result<Map<String, Object>> getDictData(@Parameter(hidden = true) @SessionAttribute(SessionAttr.MS_USER) SysUser user,
                                                   @Parameter(hidden = true) @RequestAttribute(RequestAttr.DEPT_ID) Integer deptId,
                                                   @RequestParam("code") String code) {
        log.info("获取参数配置,user:{},deptId:{},code:{}", user.getUsername(), deptId, code);
        Map<String, Object> thirdConfig = appDictService.getDictParamData(deptId, code);
        return ResultUtil.success(thirdConfig);
    }
}
