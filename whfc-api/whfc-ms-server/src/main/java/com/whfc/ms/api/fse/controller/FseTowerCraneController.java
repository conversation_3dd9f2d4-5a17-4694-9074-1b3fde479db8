package com.whfc.ms.api.fse.controller;

import com.whfc.common.result.PageData;
import com.whfc.common.result.Result;
import com.whfc.common.result.ResultUtil;
import com.whfc.common.util.RequestAttr;
import com.whfc.common.util.SessionAttr;
import com.whfc.fse.dto.FseTowerCraneDTO;
import com.whfc.fse.dto.FseTowerCraneStatDTO;
import com.whfc.fse.param.FseTowerCraneBindParam;
import com.whfc.fse.param.FseTowerCraneParam;
import com.whfc.fse.service.FseTowerCraneService;
import com.whfc.fuum.entity.SysUser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/19
 */
@Tag(name = "4S天秤吊")
@Slf4j
@RestController
@RequestMapping("/ms/api/fse/towerCrane")
public class FseTowerCraneController {


    @DubboReference(interfaceClass = FseTowerCraneService.class, version = "1.0.0")
    private FseTowerCraneService fseTowerCraneService;


    @Operation(summary = "获取4S天秤吊设备列表(分页)")
    @GetMapping("/page")
    public Result<PageData<FseTowerCraneDTO>> towerCranePage(@Parameter(hidden = true) @SessionAttribute(SessionAttr.MS_USER) SysUser loginUser,
                                                             @Parameter(hidden = true) @RequestAttribute(RequestAttr.DEPT_ID) Integer deptId,
                                                             @RequestParam(value = "keyword", required = false) String keyword,
                                                             @RequestParam("pageNum") Integer pageNum,
                                                             @RequestParam("pageSize") Integer pageSize) {
        PageData<FseTowerCraneDTO> pageData = fseTowerCraneService.page(deptId, keyword, pageNum, pageSize);
        return ResultUtil.success(pageData);
    }

    @Operation(summary = "4S天秤吊设备统计")
    @GetMapping("stat")
    public Result<FseTowerCraneStatDTO> stat(@Parameter(hidden = true) @SessionAttribute(SessionAttr.MS_USER) SysUser loginUser,
                                             @Parameter(hidden = true) @RequestAttribute(RequestAttr.DEPT_ID) Integer deptId) {
        log.info("4S天秤吊设备统计,loginer:{},deptId:{}", loginUser.getUsername(), deptId);
        FseTowerCraneStatDTO stat = fseTowerCraneService.stat(deptId);
        return ResultUtil.success(stat);
    }


    @Operation(summary = "添加4S天秤吊设备")
    @PostMapping("/add")
    public Result<Void> addFseTowerCrane(@Parameter(hidden = true) @SessionAttribute(SessionAttr.MS_USER) SysUser loginUser,
                                         @Parameter(hidden = true) @RequestAttribute(RequestAttr.DEPT_ID) Integer deptId,
                                         @RequestBody FseTowerCraneParam param) {
        log.info("添加4S天秤吊设备,loginer:{},deptId:{},param:{}", loginUser.getUsername(), deptId, param);
        fseTowerCraneService.add(deptId, param);
        return ResultUtil.success();
    }

    @Operation(summary = "修改4S天秤吊设备")
    @PostMapping("/edit")
    public Result<Void> edit(@Parameter(hidden = true) @SessionAttribute(SessionAttr.MS_USER) SysUser loginUser,
                             @RequestBody FseTowerCraneParam param) {
        log.info("修改4S天秤吊设备,loginer:{},param:{}", loginUser.getUsername(), param.toString());
        fseTowerCraneService.edit(param);
        return ResultUtil.success();
    }


    @Operation(summary = "绑定设备")
    @PostMapping("/bind")
    public Result<Void> bind(@Parameter(hidden = true) @SessionAttribute(SessionAttr.MS_USER) SysUser loginUser,
                             @RequestBody FseTowerCraneBindParam param) {
        log.info("绑定4S天秤吊设备,loginer:{},param:{}", loginUser.getUsername(), param.toString());
        fseTowerCraneService.bind(param);
        return ResultUtil.success();
    }


    @Operation(summary = "删除4S天秤吊设备")
    @PostMapping("/delete/{guid}")
    public Result<Void> delete(@Parameter(hidden = true) @SessionAttribute(SessionAttr.MS_USER) SysUser loginUser,
                               @PathVariable("guid") String guid) {
        log.info("删除4S天秤吊设备,loginer:{},guid:{}", loginUser.getUsername(), guid);
        fseTowerCraneService.delete(guid);
        return ResultUtil.success();
    }

}
