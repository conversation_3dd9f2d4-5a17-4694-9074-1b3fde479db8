package com.whfc.ms.api.wp;

import com.whfc.common.enums.OperationType;
import com.whfc.common.enums.SysModule;
import com.whfc.common.result.ListData;
import com.whfc.common.result.PageData;
import com.whfc.common.result.Result;
import com.whfc.common.result.ResultUtil;
import com.whfc.common.util.RequestAttr;
import com.whfc.common.util.SessionAttr;
import com.whfc.ms.aspect.annotation.OperationLog;
import com.whfc.wp.dto.WorkPermitConfigDTO;
import com.whfc.wp.dto.WorkPermitDTO;
import com.whfc.wp.param.*;
import com.whfc.wp.service.WorkPermitService;
import com.whfc.fuum.entity.SysUser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 作业票控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/31
 */
@Slf4j
@Tag(name = "作业票管理")
@RestController
@RequestMapping("/ms/api/work-permit")
public class WorkPermitController {


    @DubboReference(interfaceClass = WorkPermitService.class, version = "1.0.0")
    private WorkPermitService workPermitService;

    @Operation(summary = "获取作业票配置列表")
    @GetMapping("/config")
    public Result<ListData<WorkPermitConfigDTO>> getPermitConfigList(@Parameter(hidden = true) @SessionAttribute(SessionAttr.MS_USER) SysUser user,
                                                                     @Parameter(hidden = true) @RequestAttribute(RequestAttr.DEPT_ID) Integer deptId,
                                                                     @RequestParam(name = "workTypeId", required = false) String workTypeId) {
        log.info("获取作业票配置列表, loginer:{},deptId:{}", user.getUsername(), deptId);
        List<WorkPermitConfigDTO> list = workPermitService.getPermitConfigList(deptId, workTypeId);
        return ResultUtil.success(new ListData<>(list));
    }

    @Operation(summary = "分页查询作业票列表")
    @PostMapping("/page")
    public Result<PageData<WorkPermitDTO>> getWorkPermitList(@Parameter(hidden = true) @SessionAttribute(SessionAttr.MS_USER) SysUser user,
                                                             @Parameter(hidden = true) @RequestAttribute(RequestAttr.DEPT_ID) Integer deptId,
                                                             @RequestBody @Valid WorkPermitQueryParam query) {
        log.info("分页查询作业票列表, loginer:{},deptId:{},query: {}", user.getUsername(), deptId, query);
        query.setDeptId(deptId);
        query.setCurrUserId(user.getId());
        PageData<WorkPermitDTO> result = workPermitService.page(query);
        return ResultUtil.success(result);
    }

    @Operation(summary = "查询作业票列表")
    @PostMapping("/list")
    public Result<List<WorkPermitDTO>> getAllWorkPermitList(@Parameter(hidden = true) @SessionAttribute(SessionAttr.MS_USER) SysUser user,
                                                            @Parameter(hidden = true) @RequestAttribute(RequestAttr.DEPT_ID) Integer deptId,
                                                            @RequestBody @Valid WorkPermitQueryParam query) {
        log.info("查询作业票列表, loginer:{},deptId:{},query: {}", user.getUsername(), deptId, query);
        query.setDeptId(deptId);
        query.setCurrUserId(user.getId());
        List<WorkPermitDTO> result = workPermitService.list(query);
        return ResultUtil.success(result);
    }

    @Operation(summary = "获取作业票详情")
    @GetMapping("/detail/{guid}")
    public Result<WorkPermitDTO> getWorkPermitDetail(@Parameter(hidden = true) @SessionAttribute(SessionAttr.MS_USER) SysUser user,
                                                     @PathVariable(name = "guid") String guid) {
        log.info("获取作业票详情,loginer:{}, guid: {}", user.getUsername(), guid);
        WorkPermitDTO result = workPermitService.getWorkPermitDetail(user.getId(), guid);
        return ResultUtil.success(result);
    }

    @Operation(summary = "保存作业票")
    @PostMapping("/add")
    @OperationLog(operType = OperationType.CREATE, module = SysModule.WP, business = "作业票管理", desc = "新增作业票")
    public Result<String> addWorkPermit(@Parameter(hidden = true) @SessionAttribute(SessionAttr.MS_USER) SysUser user,
                                        @Parameter(hidden = true) @RequestAttribute(RequestAttr.DEPT_ID) Integer deptId,
                                        @RequestBody @Valid WorkPermitParam param) {
        log.info("新增作业票,loginer:{},deptId:{}, param: {}", user.getUsername(), deptId, param);
        param.setDeptId(deptId);
        param.setApplyUserId(user.getId());
        param.setApplyUserName(user.getNickname());
        param.setApplyUserContact(user.getPhone());
        workPermitService.addWorkPermit(param);
        return ResultUtil.success();
    }

    @Operation(summary = "提交作业票")
    @PostMapping("/submit")
    @OperationLog(operType = OperationType.UPDATE, module = SysModule.WP, business = "作业票管理", desc = "提交作业票")
    public Result<Void> submitWorkPermit(@Parameter(hidden = true) @SessionAttribute(SessionAttr.MS_USER) SysUser user,
                                         @Parameter(hidden = true) @RequestAttribute(RequestAttr.DEPT_ID) Integer deptId,
                                         @RequestBody @Valid WorkPermitParam param) {
        log.info("提交作业票, loginer:{}, param: {}", user.getUsername(), param);
        param.setDeptId(deptId);
        param.setApplyUserId(user.getId());
        param.setApplyUserName(user.getNickname());
        param.setApplyUserContact(user.getPhone());
        workPermitService.submitWorkPermit(param);
        return ResultUtil.success();
    }


    @Operation(summary = "编辑作业票")
    @PostMapping("/edit")
    @OperationLog(operType = OperationType.UPDATE, module = SysModule.WP, business = "作业票管理", desc = "编辑作业票")
    public Result<Void> editWorkPermit(@Parameter(hidden = true) @SessionAttribute(SessionAttr.MS_USER) SysUser user,
                                       @RequestBody @Valid WorkPermitParam param) {
        log.info("编辑作业票,loginer:{}, param: {}", user.getUsername(), param);
        workPermitService.editWorkPermit(param);
        return ResultUtil.success();
    }

    @Operation(summary = "删除作业票")
    @PostMapping("/delete/{guid}")
    @OperationLog(operType = OperationType.DELETE, module = SysModule.WP, business = "作业票管理", desc = "删除作业票")
    public Result<Void> deleteWorkPermit(@Parameter(hidden = true) @SessionAttribute(SessionAttr.MS_USER) SysUser user,
                                         @PathVariable String guid) {
        log.info("删除作业票,loginer:{}, guid: {}", user.getUsername(), guid);
        workPermitService.deleteWorkPermit(guid);
        return ResultUtil.success();
    }


    @Operation(summary = "签发作业票")
    @PostMapping("/issue")
    @OperationLog(operType = OperationType.UPDATE, module = SysModule.WP, business = "作业票管理", desc = "签发作业票")
    public Result<Void> issueWorkPermit(@Parameter(hidden = true) @SessionAttribute(SessionAttr.MS_USER) SysUser user,
                                        @RequestBody @Valid WorkPermitIssueParam param) {
        log.info("签发作业票,loginer:{},  param: {}", user.getUsername(), param);

        param.setCurrentUserId(user.getId());
        param.setCurrentUserName(user.getNickname());

        workPermitService.issueWorkPermit(param);
        return ResultUtil.success();
    }


    @Operation(summary = "申请关闭作业票")
    @PostMapping("/close")
    @OperationLog(operType = OperationType.UPDATE, module = SysModule.WP, business = "作业票管理", desc = "申请关闭作业票")
    public Result<Void> closeWorkPermit(@Parameter(hidden = true) @SessionAttribute(SessionAttr.MS_USER) SysUser user,
                                        @RequestBody @Valid WorkPermitCloseParam param) {
        log.info("申请关闭作业票, loginer:{}, param: {}", user.getUsername(), param);
        param.setCurrentUserId(user.getId());
        param.setCurrentUserName(user.getNickname());
        workPermitService.closeWorkPermit(param);
        return ResultUtil.success();
    }

    @Operation(summary = "确认关闭作业票")
    @PostMapping("/close-confirm")
    @OperationLog(operType = OperationType.UPDATE, module = SysModule.WP, business = "作业票管理", desc = "确认关闭作业票")
    public Result<Void> closeConfirmWorkPermit(@Parameter(hidden = true) @SessionAttribute(SessionAttr.MS_USER) SysUser user,
                                               @RequestBody @Valid WorkPermitCloseConfirmParam param) {
        log.info("确认关闭作业票, loginer:{}, param: {}", user.getUsername(), param);
        param.setCurrentUserId(user.getId());
        param.setCurrentUserName(user.getNickname());
        workPermitService.closeConfirmWorkPermit(param);
        return ResultUtil.success();
    }

    @Operation(summary = "作业票绑定巡查")
    @PostMapping("/bind-inspection")
    @OperationLog(operType = OperationType.UPDATE, module = SysModule.WP, business = "作业票管理", desc = "作业票绑定巡查")
    public Result<Void> bindInspection(@Parameter(hidden = true) @SessionAttribute(SessionAttr.MS_USER) SysUser user,
                                       @RequestBody @Valid WorkPermitBindParam param) {
        log.info("作业票绑定巡查, loginer:{}, param: {}", user.getUsername(), param);
        param.setCurrentUserId(user.getId());
        param.setCurrentUserName(user.getNickname());
        workPermitService.bindInspection(param);
        return ResultUtil.success();
    }

    @Operation(summary = "作业票导出")
    @PostMapping("/export")
    @OperationLog(operType = OperationType.EXPORT, module = SysModule.WP, business = "作业票管理", desc = "作业票导出")
    public Result<Void> exportWorkPermit(@Parameter(hidden = true) @SessionAttribute(SessionAttr.MS_USER) SysUser user,
                                         @Parameter(hidden = true) @RequestAttribute(RequestAttr.DEPT_ID) Integer deptId,
                                         @RequestBody @Valid WorkPermitExportParam param) {
        log.info("作业票导出, loginer:{}, deptId:{}, param: {}", user.getUsername(), deptId, param);
        param.setCurrUserId(user.getId());
        workPermitService.exportWorkPermit(deptId, param);
        return ResultUtil.success();
    }

}
