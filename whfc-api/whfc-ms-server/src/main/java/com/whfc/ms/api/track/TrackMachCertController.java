package com.whfc.ms.api.track;

import com.whfc.common.result.ListData;
import com.whfc.common.result.PageData;
import com.whfc.common.result.Result;
import com.whfc.common.result.ResultUtil;
import com.whfc.common.util.RequestAttr;
import com.whfc.common.util.SessionAttr;
import com.whfc.fuum.entity.SysUser;
import com.whfc.uni.dto.track.TrackMachCertDTO;
import com.whfc.uni.param.track.TrackMachCertParam;
import com.whfc.uni.service.track.TrackMachCertService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/11
 */
@Slf4j
@RestController
@RequestMapping("/ms/api/track/mach/cert")
@Tag(name = "数字化追踪设备证书")
public class TrackMachCertController {

    @DubboReference(interfaceClass = TrackMachCertService.class, version = "1.0.0")
    private TrackMachCertService trackMachCertService;

    @GetMapping("/page")
    @Operation(summary = "设备证书列表-分页")
    public Result<PageData<TrackMachCertDTO>> page(@Parameter(hidden = true) @SessionAttribute(SessionAttr.MS_USER) SysUser user,
                                                   @Parameter(hidden = true) @RequestAttribute(RequestAttr.DEPT_ID) Integer deptId,
                                                   @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                   @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                   @RequestParam(value = "certType", required = false) Integer certType,
                                                   @RequestParam(value = "certState", required = false) Integer certState,
                                                   @RequestParam(value = "keyword", required = false) String keyword,
                                                   @RequestParam(value = "machGuid", required = false) String machGuid) {
        log.info("获取设备证书列表,loginer:{},deptId:{},pageNum:{},pageSize:{},certState:{},keyword:{},machGuid:{}",
                user.getUsername(), deptId, pageNum, pageSize, certState, keyword, machGuid);
        PageData<TrackMachCertDTO> page = trackMachCertService.page(deptId, pageNum, pageSize, machGuid, certType, certState, keyword);
        return ResultUtil.success(page);
    }

    @GetMapping("/list")
    @Operation(summary = "设备证书列表")
    public Result<ListData<TrackMachCertDTO>> list(@Parameter(hidden = true) @SessionAttribute(SessionAttr.MS_USER) SysUser user,
                                                   @RequestParam("machGuid") String machGuid) {
        log.info("设备证书列表,loginer:{},machGuid:{}", user.getUsername(), machGuid);
        List<TrackMachCertDTO> list = trackMachCertService.list(machGuid);
        return ResultUtil.success(new ListData<>(list));
    }

    @GetMapping("/detail/{certId}")
    @Operation(summary = "设备证书详情")
    public Result<TrackMachCertDTO> detail(@Parameter(hidden = true) @SessionAttribute(SessionAttr.MS_USER) SysUser user,
                                           @PathVariable("certId") Integer certId) {
        log.info("设备证书详情,loginer:{},certId:{}", user.getUsername(), certId);
        TrackMachCertDTO detail = trackMachCertService.detail(certId);
        return ResultUtil.success(detail);
    }

    @PostMapping("/add")
    @Operation(summary = "添加设备证书")
    public Result add(@Parameter(hidden = true) @SessionAttribute(SessionAttr.MS_USER) SysUser user,
                      @Parameter(hidden = true) @RequestAttribute(RequestAttr.DEPT_ID) Integer deptId,
                      @RequestBody TrackMachCertParam param) {
        log.info("添加设备证书,loginer:{},deptId:{},param:{}", user.getUsername(), deptId, param);
        trackMachCertService.add(deptId, param);
        return ResultUtil.success();
    }

    @PostMapping("/edit")
    @Operation(summary = "修改设备证书")
    public Result edit(@Parameter(hidden = true) @SessionAttribute(SessionAttr.MS_USER) SysUser user,
                       @Parameter(hidden = true) @RequestAttribute(RequestAttr.DEPT_ID) Integer deptId,
                       @RequestBody TrackMachCertParam param) {
        log.info("修改设备证书,loginer:{},deptId:{},param:{}", user.getUsername(), deptId, param);
        trackMachCertService.edit(deptId, param);
        return ResultUtil.success();
    }

    @PostMapping("/del/{certId}")
    @Operation(summary = "删除设备证书")
    public Result del(@Parameter(hidden = true) @SessionAttribute(SessionAttr.MS_USER) SysUser user,
                      @PathVariable("certId") Integer certId) {
        log.info("删除设备证书,loginer:{},certId:{}", user.getUsername(), certId);
        trackMachCertService.del(certId);
        return ResultUtil.success();
    }

}
