package com.whfc.ms.interceptor;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 * @desc 自定义WebMvcConfigurer
 * @date 2019-08-03
 */
@Configuration
public class CustomWebMvcConfigurer implements WebMvcConfigurer {

    @Autowired
    private LoginCheckInterceptor loginCheckInterceptor;

    @Autowired
    private RequestHeaderInterceptor requestHeaderInterceptor;


    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        //风潮登录验证
        registry.addInterceptor(loginCheckInterceptor)
                .addPathPatterns("/ms/api/**")
                .excludePathPatterns("/ms/api/register")
                .excludePathPatterns("/ms/api/login")
                .excludePathPatterns("/ms/api/logout")
                .excludePathPatterns("/ms/api/third-login")
                .excludePathPatterns("/ms/api/wxlogin")
                .excludePathPatterns("/ms/api/msg-login")
                .excludePathPatterns("/ms/api/ws/msg")
                .excludePathPatterns("/ms/api/**/anon/**")
                .excludePathPatterns("/swagger-resources/**")
                .excludePathPatterns("/ms/api/captcha/**")
                .excludePathPatterns("/ms/api/sendCode")
                .excludePathPatterns("/ms/api/jdy/sso/anon")
                .excludePathPatterns("/ms/api/getEncryptKey")
                .excludePathPatterns("/ms/api/area-code/list")
        ;
        //请求头验证 orgId验证
        registry.addInterceptor(requestHeaderInterceptor)
                // TODO: 暂时注释
//                .addPathPatterns("/ms/api/**")
                .addPathPatterns(
                        "/ms/api/fsdm/task/element/tree",
                        "/ms/api/sys/dict/list",
                        "/ms/api/sys/dict/getChildListByParentCode",
                        "/ms/api/fsdm/task/element/tree",
                        "/ms/api/lock/**",
                        "/ms/api/fsdm/milestones/**",
                        "/ms/api/const/**",
                        "/ms/api/emp/device/**",
                        "/ms/api/emp/broadcast/**",
                        "/ms/api/emp/helmet/param",
                        "/ms/api/sendCode",
                        "/ms/api/getEncryptKey",
                        "/ms/api/ai/**",
                        "/ms/api/v1/mach/check/record/**",
                        "/ms/api/v1/mach/cert/**",
                        "/ms/api/fvs/**",
                        "/ms/api/fim/**",
                        "/ms/api/sys/board/**",
                        "/ms/api/tbm/**",
                        "/ms/api/jdy/**",
                        "/ms/api/fse/warn/**",
                        "/ms/api/user/**",
                        "/ms/api/board/**",
                        "/ms/api/blind/**",
                        "/ms/api/emp/ana/**",
                        "/ms/api/fcs/space/**",
                        "/ms/api/warn/emp/**",
                        "/ms/api/fire/work/**",
                        "/ms/api/track/mach/**",
                        "/ms/api/emp/attend/inputData/**",
                        "/ms/api/fse/towerCrane/**",
                        "/ms/api/faceGate/**",
                        "/ms/api/emp/attend/**",
                        "/ms/api/env/dust/**",
                        "/ms/api/env/warn/**",
                        "/ms/api/fse/towerCrane/warn/**",
                        "/ms/api/work-permit/**",
                        "/ms/api/app/dict/**"
                )
                .excludePathPatterns("/ms/api/register")
                .excludePathPatterns("/ms/api/login")
                .excludePathPatterns("/ms/api/logout")
                .excludePathPatterns("/ms/api/third-login")
                .excludePathPatterns("/ms/api/wxlogin")
                .excludePathPatterns("/ms/api/ws/msg")
                .excludePathPatterns("/ms/api/sendCode")
                .excludePathPatterns("/ms/api/getEncryptKey")
                .excludePathPatterns("/ms/api/project/list")    //后台-项目列表
                .excludePathPatterns("/ms/api/user/rule/list")  //后台-权限列表
                .excludePathPatterns("/ms/api/dept/list")       //数据看板-组织机构列表
                .excludePathPatterns("/ms/api/user/rules")      //数据看板-权限列表
                .excludePathPatterns("/ms/api/**/anon/**")
                .excludePathPatterns("/ms/api/jdy/sso/anon")
                .excludePathPatterns("/ms/api/area-code/list");
    }
}
