package com.whfc.wxmp.api.safety.service;

import com.whfc.common.result.ListData;
import com.whfc.common.result.PageData;
import com.whfc.safety.dto.SafetyTaskExecDTO;
import com.whfc.safety.param.SafetyTaskCompleteParam;
import com.whfc.safety.param.SafetyTaskReportParam;

import java.util.Date;

/**
 * @Description 检查任务
 * <AUTHOR>
 * @Date 2021-05-18 10:00
 * @Version 1.0
 */
public interface WxSafetyTaskService {


    /**
     * 检查记录列表（分页）
     *
     * @param userId
     * @param deptId
     * @param report
     * @param startTime
     * @param endTime
     * @param pageSize
     * @param pageNum
     * @return
     */
    PageData<SafetyTaskExecDTO> list(Integer userId, Integer deptId, Integer report, Date startTime, Date endTime, Integer pageSize, Integer pageNum);

    /**
     * 检查记录列表
     *
     * @param userId
     * @param deptId
     * @param report
     * @param startTime
     * @param endTime
     * @return
     */
    ListData<SafetyTaskExecDTO> list(Integer userId, Integer deptId, Integer report, Date startTime, Date endTime);

    /**
     * 需要处理的任务
     *
     * @param userId
     * @param deptId
     * @param pageSize
     * @param pageNum
     * @return
     */
    PageData<SafetyTaskExecDTO> meList(Integer userId, Integer deptId, Integer pageSize, Integer pageNum);

    /**
     * 排查上报
     *
     * @param param
     */
    Integer execAdd(SafetyTaskReportParam param);

    /**
     * 完成任务
     *
     * @param param
     */
    void execComplete(SafetyTaskCompleteParam param);
}
