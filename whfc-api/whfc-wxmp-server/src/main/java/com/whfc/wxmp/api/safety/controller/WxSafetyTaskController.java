package com.whfc.wxmp.api.safety.controller;

import com.whfc.common.base.BaseController;
import com.whfc.common.result.ListData;
import com.whfc.common.result.PageData;
import com.whfc.common.result.Result;
import com.whfc.common.result.ResultUtil;
import com.whfc.common.util.DateUtil;
import com.whfc.common.util.SessionAttr;
import com.whfc.fuum.entity.SysUser;
import com.whfc.safety.dto.SafetyTaskExecDTO;
import com.whfc.safety.param.SafetyTaskCompleteParam;
import com.whfc.safety.param.SafetyTaskReportParam;
import com.whfc.wxmp.api.safety.service.WxSafetyTaskService;
import com.whfc.wxmp.util.RequestConetxtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

/**
 * @Description 检查任务
 * <AUTHOR>
 * @Date 2021-05-18 9:59
 * @Version 1.0
 */
@RestController
@RequestMapping("/mp/api/safety/task/")
public class WxSafetyTaskController extends BaseController {

    @Autowired
    private WxSafetyTaskService wxSafetyTaskService;

    @GetMapping(value = "list", params = {"pageSize", "pageNum"})
    public Result list(@SessionAttribute(SessionAttr.MS_USER) SysUser user,
                       @RequestParam("deptId") Integer deptId,
                       @RequestParam(value = "report", required = false) Integer report,
                       @RequestParam(value = "dayTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date dayTime,
                       @RequestParam(value = "monthTime", required = false) @DateTimeFormat(pattern = "yyyy-MM") Date monthTime,
                       @RequestParam("pageSize") Integer pageSize,
                       @RequestParam("pageNum") Integer pageNum) {
        logger.info("WXMPUA|{}|{}|{}|{}|{}",
                user.getId(), RequestConetxtUtil.getAppType(), RequestConetxtUtil.getPage(),
                "检查任务列表(分页)", RequestConetxtUtil.getParameters());
        Date startTime = null;
        Date endTime = null;
        if (dayTime != null) {
            startTime = DateUtil.getDateBegin(dayTime);
            endTime = DateUtil.getDateEnd(dayTime);
        } else if (monthTime != null) {
            startTime = DateUtil.getMonthBegin(monthTime);
            endTime = DateUtil.getMonthEnd(monthTime);
        }
        PageData<SafetyTaskExecDTO> list = wxSafetyTaskService.list(user.getId(), deptId, report, startTime, endTime, pageSize, pageNum);
        return ResultUtil.success(list);
    }

    @GetMapping("list")
    public Result list(@SessionAttribute(SessionAttr.MS_USER) SysUser user,
                       @RequestParam("deptId") Integer deptId,
                       @RequestParam(value = "dayTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date dayTime,
                       @RequestParam(value = "monthTime", required = false) @DateTimeFormat(pattern = "yyyy-MM") Date monthTime,
                       @RequestParam(value = "report", required = false) Integer report) {
        logger.info("WXMPUA|{}|{}|{}|{}|{}",
                user.getId(), RequestConetxtUtil.getAppType(), RequestConetxtUtil.getPage(),
                "检查任务列表(不分页)", RequestConetxtUtil.getParameters());
        Date startTime = null;
        Date endTime = null;
        if (dayTime != null) {
            startTime = DateUtil.getDateBegin(dayTime);
            endTime = DateUtil.getDateEnd(dayTime);
        } else if (monthTime != null) {
            startTime = DateUtil.getMonthBegin(monthTime);
            endTime = DateUtil.getMonthEnd(monthTime);
        }
        ListData<SafetyTaskExecDTO> list = wxSafetyTaskService.list(user.getId(), deptId, report, startTime, endTime);
        return ResultUtil.success(list);
    }

    @PostMapping("/exec/add")
    public Result execAdd(@SessionAttribute(SessionAttr.MS_USER) SysUser user,
                          @Validated @RequestBody SafetyTaskReportParam param) {
        logger.info("WXMPUA|{}|{}|{}|{}|{}",
                user.getId(), RequestConetxtUtil.getAppType(), RequestConetxtUtil.getPage(),
                "排查上报", param);

        param.setUserId(user.getId());
        param.setUserName(user.getUsername());
        Integer execId = wxSafetyTaskService.execAdd(param);
        return ResultUtil.success(execId);
    }

    @PostMapping("/complete")
    public Result execComplete(@SessionAttribute(SessionAttr.MS_USER) SysUser user,
                               @Validated @RequestBody SafetyTaskCompleteParam param) {
        logger.info("WXMPUA|{}|{}|{}|{}|{}",
                user.getId(), RequestConetxtUtil.getAppType(), RequestConetxtUtil.getPage(),
                "完成任务", RequestConetxtUtil.getParameters());
        wxSafetyTaskService.execComplete(param);
        return ResultUtil.success();
    }

    @GetMapping("/meList")
    public Result meList(@SessionAttribute(SessionAttr.MS_USER) SysUser user,
                         @RequestParam("deptId") Integer deptId,
                         @RequestParam("pageNum") Integer pageNum,
                         @RequestParam("pageSize") Integer pageSize) {
        logger.info("WXMPUA|{}|{}|{}|{}|{}",
                user.getId(), RequestConetxtUtil.getAppType(), RequestConetxtUtil.getPage(),
                "我的任务", RequestConetxtUtil.getParameters());
        PageData<SafetyTaskExecDTO> list = wxSafetyTaskService.meList(user.getId(), deptId, pageSize, pageNum);
        return ResultUtil.success(list);
    }
}
