package com.whfc.wxmp.api.sys.controller;

import com.whfc.common.base.BaseController;
import com.whfc.common.result.Result;
import com.whfc.common.result.ResultUtil;
import com.whfc.common.util.SessionAttr;
import com.whfc.fuum.entity.WxUser;
import com.whfc.fuum.dto.SysDictDTO;
import com.whfc.wxmp.api.sys.service.WxDictDataService;
import com.whfc.wxmp.util.RequestConetxtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @description 字典表接口
 * @date 2019-11-14
 */
@RestController
@RequestMapping("/mp/api/dict")
public class WxDictController extends BaseController {
    @Autowired
    private WxDictDataService wxDictDataService;

    /**
     * 取字典表中的数据
     *
     * @param user
     * @param code
     * @return
     */
    @GetMapping("/getDictData")
    public Result getDictDataByTypeCode(@SessionAttribute(SessionAttr.WXMP_USER) WxUser user, @RequestParam("code") String code) {
        logger.info("小程序获取字典表中的数据,wxUser:{},code:{}", user.getNickName(), code);
        SysDictDTO dictDTO = wxDictDataService.getDictDataByTypeCode(code);
        return ResultUtil.success(dictDTO);
    }
}
