package com.whfc.wxmp.api.safety.service.impl;

import com.whfc.common.result.ListData;
import com.whfc.common.result.PageData;
import com.whfc.safety.dto.SafetyTaskExecDTO;
import com.whfc.safety.param.SafetyTaskCompleteParam;
import com.whfc.safety.param.SafetyTaskReportParam;
import com.whfc.safety.service.SafetyTaskService;
import com.whfc.wxmp.api.safety.service.WxSafetyTaskService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * @Description 检查任务
 * <AUTHOR>
 * @Date 2021-05-18 10:18
 * @Version 1.0
 */
@Service
public class WxSafetyTaskServiceImpl implements WxSafetyTaskService {

    @DubboReference(interfaceClass = SafetyTaskService.class, version = "1.0.0")
    private SafetyTaskService safetyTaskService;

    @Override
    public PageData<SafetyTaskExecDTO> list(Integer userId, Integer deptId, Integer report, Date startTime, Date endTime, Integer pageSize, Integer pageNum) {
        if (report != null && 1 == report) {
            return safetyTaskService.taskExecList(userId, deptId, null, startTime, endTime, pageNum, pageSize);
        } else {
            return safetyTaskService.taskExecList(deptId, null, startTime, endTime, pageNum, pageSize);
        }
    }

    @Override
    public ListData<SafetyTaskExecDTO> list(Integer userId, Integer deptId, Integer report, Date startTime, Date endTime) {
        List<SafetyTaskExecDTO> list;
        if (report != null && 1 == report) {
            list = safetyTaskService.taskExecList(userId, deptId, null, startTime, endTime);
        } else {
            list = safetyTaskService.taskExecList(deptId, null, startTime, endTime);
        }
        return new ListData<>(list);
    }

    @Override
    public PageData<SafetyTaskExecDTO> meList(Integer userId, Integer deptId, Integer pageSize, Integer pageNum) {
        return safetyTaskService.taskExecList(userId, deptId, null, null, null, 1, null, null, pageNum, pageSize);
    }

    @Override
    public Integer execAdd(SafetyTaskReportParam param) {
        return safetyTaskService.report(param);
    }

    @Override
    public void execComplete(SafetyTaskCompleteParam param) {
        safetyTaskService.complete(param);
    }
}
