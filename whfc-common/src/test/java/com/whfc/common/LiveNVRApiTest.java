package com.whfc.common;

import com.whfc.common.util.JSONUtil;
import com.whfc.common.vs.livenvr.LiveNVRApi;
import com.whfc.common.vs.livenvr.entity.LiveQingChannelInfo;
import com.whfc.common.vs.livenvr.entity.LiveQingGroup;
import com.whfc.common.vs.livenvr.entity.LiveQingRecord;
import com.whfc.common.vs.livenvr.entity.LiveQingStream;
import org.junit.Test;

import java.util.Date;
import java.util.List;

public class LiveNVRApiTest {

    private String host = "http://**************:10800";
    // private String host = "https://ss2-nvs.4s-hk.com/nvc/nvr";
    private String user = "admin";
    private String pass = "zt123456";
    private LiveNVRApi api = new LiveNVRApi(host, user, pass);
    private String channel = "1";


    @Test
    public void testLogin() {
        String data = api.login();
        System.out.println(data);
    }

    @Test
    public void testGetGroupList() {
        List<LiveQingGroup> list = api.getGroupList();
        for (LiveQingGroup group : list) {
            System.out.println(String.format("groupName:%s,Channel:%s,subCount:%s", group.getName(), group.getChannel(), group.getSubCount()));
        }
    }

    @Test
    public void testGetSnapshot() {
        String snapshot = api.getSnapshot(channel);
        System.out.println(snapshot);
    }

    @Test
    public void testGetChannelInfo() {
        LiveQingChannelInfo info = api.getChannelInfo(channel);
        System.out.println(JSONUtil.toPrettyString(info));
    }

    @Test
    public void testGetStream() {
        LiveQingStream stream = api.getStreamUrl(channel, "HLS");
        System.out.println(JSONUtil.toPrettyString(stream));
    }

    @Test
    public void testGetDayRecord() {
        List<LiveQingRecord> list = api.getDayRecordList(channel, new Date());
        System.out.println(JSONUtil.toPrettyString(list));
    }
}
