package com.whfc.common.vs.livenvr;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.whfc.common.util.JSONUtil;
import com.whfc.common.util.MD5Util;
import com.whfc.common.vs.livenvr.entity.LiveQingChannelInfo;
import com.whfc.common.vs.livenvr.entity.LiveQingGroup;
import com.whfc.common.vs.livenvr.entity.LiveQingRecord;
import com.whfc.common.vs.livenvr.entity.LiveQingStream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * liveNVR API
 */
@Slf4j
public class LiveNVRApi {

    private static final String LOGIN = "/api/v1/login";
    private static final String DEVICE_LIST = "/api/v1/group/tree?pid=ROOT";
    private static final String SNAPSHOT_URL = "/api/v1/getchannelsnap";
    private static final String CHANNEL_INFO = "/api/v1/getchannels";
    private static final String CHANNEL_STREAM = "/api/v1/getchannelstream";
    private static final String CHANNEL_RECORD = "/api/v1/record/querydaily";

    private final String host;

    private final String user;

    private final String pass;

    public LiveNVRApi(String host, String user, String pass) {
        this.host = host;
        this.user = user;
        this.pass = pass;
    }

    public String login() {
        String url = host + LOGIN;
        Map<String, String> params = new HashMap<>();
        params.put("username", user);
        params.put("password", MD5Util.md5Encode(pass));
        try {
            String response = HttpUtil.createPost(url).body(JSON.toJSONString(params)).execute().body();
            log.info("login response: {}", response);
            JSONObject body = parseBody(response);
            if (body != null) {
                return body.getString("Token");
            }
        } catch (Exception e) {
            log.error("login error", e);
        }

        return null;
    }

    /**
     * 获取通道组信息
     *
     * @return 通道组信息
     */
    public List<LiveQingGroup> getGroupList() {
        String url = host + DEVICE_LIST;
        try {
            String response = HttpUtil.get(url);
            log.info("groupList response: {}", response);
            List<LiveQingGroup> groups = JSONUtil.parseArray(response, LiveQingGroup.class);
            if (groups == null) {
                return Collections.emptyList();
            }
            return groups;
        } catch (Exception e) {
            log.error("获取通道信息失败.", e);
        }
        return Collections.emptyList();
    }

    /**
     * 获取抓拍截图
     *
     * @param channel 通道
     * @return 截图
     */
    public String getSnapshot(String channel) {
        return host + SNAPSHOT_URL + "?channel=" + channel;
    }

    /**
     * 获取通道信息
     *
     * @param channel 通道
     * @return 通道信息
     */
    public LiveQingChannelInfo getChannelInfo(String channel) {
        String url = host + CHANNEL_INFO + "?channel=" + channel;
        try {
            String response = HttpUtil.get(url);
            log.info("channelInfo response: {}", response);
            JSONObject body = parseBody(response);
            if (body != null) {
                JSONArray channels = body.getJSONArray("Channels");
                if (!channels.isEmpty()) {
                    return JSONUtil.parseObject(channels.getString(0), LiveQingChannelInfo.class);
                }
            }
        } catch (Exception e) {
            log.error("获取通道信息失败.", e);
        }
        return null;
    }

    /**
     * 获取直播地址
     *
     * @param channel  通道
     * @param protocol 协议
     * @return 直播地址
     */
    public LiveQingStream getStreamUrl(String channel, String protocol) {
        String url = host + CHANNEL_STREAM + "?channel=" + channel + "&protocol=" + protocol + "&fullpath=true";
        try {
            String response = HttpUtil.get(url);
            log.info("streamUrl response:{}", response);
            JSONObject body = parseBody(response);
            if (body != null) {
                LiveQingStream stream = JSONUtil.parseObject(body.toJSONString(), LiveQingStream.class);
                if (stream != null) {
                    if (StringUtils.isNotBlank(stream.getURL()) && !stream.getURL().startsWith("http")) {
                        stream.setURL(host + stream.getURL());
                    }
                    if (StringUtils.isNotBlank(stream.getSnapURL()) && !stream.getSnapURL().startsWith("http")) {
                        stream.setSnapURL(host + stream.getSnapURL());
                    }
                }
                return stream;
            }
        } catch (Exception e) {
            log.error("获取直播地址失败.", e);
        }
        return null;
    }

    /**
     * 获取回放地址
     *
     * @param channel 通道
     * @param date    日期
     * @return 回放地址
     */
    public List<LiveQingRecord> getDayRecordList(String channel, Date date) {
        String url = host + CHANNEL_RECORD + "?id=" + channel + "&period=" + DateUtil.format(date, "yyyyMMdd");
        try {
            String response = HttpUtil.get(url);
            log.info("record response:{}", response);
            JSONObject result = JSONObject.parseObject(response);
            if (result != null) {
                List<LiveQingRecord> records = JSONUtil.parseArray(result.getString("list"), LiveQingRecord.class);
                if (records != null) {
                    for (LiveQingRecord record : records) {
                        String startAt = record.getStartAt();
                        Date startTime = DateUtil.parse(startAt, "yyyyMMddHHmmss").toJdkDate();
                        record.setStartTime(startTime);
                        Date end = DateUtil.offset(startTime, DateField.SECOND, record.getDuration()).toJdkDate();
                        record.setEndTime(end);
                        String endAt = DateUtil.format(end, "yyyyMMddHHmmss");
                        record.setPlayUrl(host + "/api/v1/record/video/play/" + channel + "/" + startAt + "/" + endAt + "/video.mp4");
                    }
                }
                return records;
            }
        } catch (Exception e) {
            log.error("获取回放地址失败.", e);
        }
        return null;
    }

    /**
     * 处理响应体
     *
     * @param response 响应体
     * @return 响应数据
     */
    private JSONObject parseBody(String response) {
        JSONObject result = JSONObject.parseObject(response);
        if (result != null) {
            JSONObject liveQing = result.getJSONObject("LiveQing");
            if (liveQing != null) {
                return liveQing.getJSONObject("Body");
            }
        }
        return null;
    }
}
