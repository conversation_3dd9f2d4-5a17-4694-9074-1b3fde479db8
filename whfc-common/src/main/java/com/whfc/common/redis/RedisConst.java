package com.whfc.common.redis;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/14
 */
public class RedisConst {

    /**********************************  大屏  ****************************************/

    /**
     * 大屏设备统计
     */
    public static final String BOARD_DEVICE_STAT = "board:device-stat:%s";

    /**
     * 大屏告警统计
     */
    public static final String BOARD_WARN_STAT = "board:warn-stat:%s";


    /**********************************  设备  ****************************************/

    /**
     * 设备数字追踪 二维码
     */
    public static final String TRACK_MACH_INFO_QR_CODE = "track:mach-info:qr-code:%s";


    /**********************************  车辆盲区  ****************************************/

    /**
     * 车辆盲区 车载360 龙安天下 api token
     */
    public static final String BLIND_IVC_LATX_API_TOKEN = "blind:ivc:latx-api-token";

    /**
     * 车辆盲区 设备通道数
     */
    public static final String BLIND_DEVICE_CHANNEL_SIZE = "blind:device:channel-size:%s";


    /**********************************  UWB测距  ****************************************/

    /**
     * UWB测距 告警记录查询序列号
     */
    public static final String UWB_WARN_RECORD_QUERY_SEQ = "uwb:warn-record-query-seq:%s";

    /**
     * UWB测距 告警记录查询标记
     */
    public static final String UWB_WARN_RECORD_QUERY_FLAG = "uwb:warn-record-query-flag:%s";

    /**
     * UWB测距 告警记录时间
     */
    public static final String UWB_WARN_RECORD_QUERY_START = "uwb:warn-record-query-start:%s";

    /**
     * UWB测距 设备在线
     */
    public static final String UWB_DEVICE_ONLINE = "uwb:device:online:%s";

    /**********************************  智能监控  ****************************************/

    /**
     * 智能监控 AI服务器在线
     */
    public static final String FIM_AI_SERVER_ONLINE = "fim:ai-server:online:%s";

    /**********************************  天秤吊  ****************************************/

    public static final String FSE_TOWER_WARN_RECORD = "fse:tower:warn-record:{0}:{1}:{2}";

    public static final String FSE_TOWER_WARN_STATE = "fse:tower:warn-state:{0}:{1}:{2}";

    /**********************************  塔吊监控YUKE  ****************************************/

    public static final String YUKE_CRANE_TOKEN = "yuke:crane-token:%s";

    /**********************************  香港理工  ****************************************/

    public static final String HK_POLY_TOKEN = "hk-poly:token";

    public static final String HK_POLY_SSE_TOKEN = "hk-poly:sse-token";

    public static final String HK_POLY_FILE_AUTH = "hk-poly:file-auth";


    /**********************************  简道云许可证  ****************************************/

    public static final String JDY_PERMIT_FORM_STATIC = "jdy-permit:form-static：%s";


    /**********************************  视频监控  ****************************************/
    /**
     * 视频监控 设备在线
     */
    public static final String FVS_DEVICE_ONLINE = "fvs:device:online:%s";

}
