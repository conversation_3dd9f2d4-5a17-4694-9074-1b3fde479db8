package com.whfc.common.enums;

/**
 * 系统模块
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/10
 */
public enum SysModule {

    FUUM("FUUM", "系统模块"),
    BASE("BASE", "基础信息模块"),
    MACH("MACH", "设备模块"),
    EMP("EMP", "人员模块"),
    DOC("DOC", "文件模块"),
    ENV("ENV", "环境监控模块"),
    FMAM("FMAM", "物资管理模块"),
    FVS("FVS", "视频监控模块"),
    FSE("FSE", "特种设备模块"),
    FIM("FIM", "智能监控模块"),
    QUALITY("QUALITY", "质量管理模块"),
    SAFETY("SAFETY", "安全管理模块"),
    FCS("FCS", "密闭空间模块"),
    BLIND("BLIND", "车辆盲区模块"),
    LOCK("LOCK", "智能门锁模块"),
    CONCRETE("CONCRETE", "混凝土测温模块"),
    CAR("CAR", "车辆管理模块"),
    WP("WP", "作业票模块"),

    ;

    private final String code;

    private final String desc;

    SysModule(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
