package com.whfc.fuum.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.whfc.common.enums.I18nErrorCode;
import com.whfc.common.enums.SysUserType;
import com.whfc.common.exception.BizException;
import com.whfc.common.result.PageData;
import com.whfc.common.result.ResultEnum;
import com.whfc.common.third.map.MapApiFactory;
import com.whfc.common.third.map.MapLoc;
import com.whfc.common.util.Gps;
import com.whfc.common.util.PageUtil;
import com.whfc.fuum.dao.SysDeptMapper;
import com.whfc.fuum.dto.*;
import com.whfc.fuum.entity.SysDept;
import com.whfc.fuum.entity.SysUser;
import com.whfc.fuum.enums.ProjectLevel;
import com.whfc.fuum.enums.ProjectStatus;
import com.whfc.fuum.enums.ProjectSv;
import com.whfc.fuum.enums.ProjectSvDays;
import com.whfc.fuum.param.AppProjectParam;
import com.whfc.fuum.param.AppProjectPostParam;
import com.whfc.fuum.param.AppProjectUnitParam;
import com.whfc.fuum.param.ImgParam;
import com.whfc.fuum.service.AppProjectService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@DubboService(interfaceClass = AppProjectService.class, version = "1.0.0")
public class AppProjectServiceImpl implements AppProjectService {

    @Autowired
    private SysDeptMapper sysDeptMapper;

    @Autowired
    private MapApiFactory mapApiFactory;

    @Override
    public PageData<AppProjectDTO> list(SysUser user, Integer pageNum, Integer pageSize, String keyword) throws BizException {
        List<Integer> deptIdList = getDeptIdList(user);
        if (deptIdList == null || deptIdList.isEmpty()) {
            return PageUtil.emptyPage();
        }
        PageHelper.startPage(pageNum, pageSize);
        List<SysDeptExtDTO> sysDeptExtList = sysDeptMapper.selectProjectListByDeptIds(deptIdList, keyword);
        PageHelper.clearPage();

        // 数据转换
        List<AppProjectDTO> list = new ArrayList<>();
        for (SysDeptExtDTO sysDeptExt : sysDeptExtList) {
            AppProjectDTO appProjectDTO = new AppProjectDTO();
            appProjectDTO.setDeptId(sysDeptExt.getDeptId());
            appProjectDTO.setName(sysDeptExt.getName());
            appProjectDTO.setTitle(sysDeptExt.getTitle());
            appProjectDTO.setLng(sysDeptExt.getLng());
            appProjectDTO.setLat(sysDeptExt.getLat());
            appProjectDTO.setAddress(sysDeptExt.getAddress());
            SysDeptExtInfoDTO sysDeptExtInfoDTO = JSON.parseObject(sysDeptExt.getDetail(), SysDeptExtInfoDTO.class);
            if (sysDeptExtInfoDTO != null) {
                appProjectDTO.setProjectCode(sysDeptExtInfoDTO.getProjectCode());
                appProjectDTO.setPeriod(sysDeptExtInfoDTO.getPeriod());
                appProjectDTO.setMoney(sysDeptExtInfoDTO.getMoney());
                appProjectDTO.setStartDate(sysDeptExtInfoDTO.getStartDate());
                appProjectDTO.setCompleteDate(sysDeptExtInfoDTO.getCompleteDate());
                appProjectDTO.setBuilderLicenseNum(sysDeptExtInfoDTO.getBuilderLicenseNum());
                List<AppProjectImgDTO> imgList = sysDeptExtInfoDTO.getImgList();
                if (imgList != null && !imgList.isEmpty()) {
                    appProjectDTO.setCover(imgList.get(0).getImgUrl());
                }
            }
            list.add(appProjectDTO);
        }
        return PageUtil.pageData(PageInfo.of(sysDeptExtList), list);
    }

    @Override
    public List<AppProjectDTO> list(SysUser user) {
        List<Integer> deptIdList = getDeptIdList(user);
        if (deptIdList == null || deptIdList.isEmpty()) {
            return Collections.emptyList();
        }
        List<AppProjectDTO> list = sysDeptMapper.selectBasicProjectListByDeptIds(deptIdList);
        return list;
    }

    @Override
    public AppProjectDetailDTO detail(Integer deptId) {
        SysDeptExtDTO sysDeptExtDTO = sysDeptMapper.selectDetail(deptId);
        if (sysDeptExtDTO == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.SYS_BE_002.getCode());
        }

        AppProjectDetailDTO detailDTO = new AppProjectDetailDTO();
        SysDeptExtInfoDTO extInfo = JSON.parseObject(sysDeptExtDTO.getDetail(), SysDeptExtInfoDTO.class);
        if (extInfo != null) {
            BeanUtils.copyProperties(extInfo, detailDTO);
        }
        detailDTO.setImgList(detailDTO.getImgList() != null ? detailDTO.getImgList() : Collections.emptyList());
        detailDTO.setUnitList(detailDTO.getUnitList() != null ? detailDTO.getUnitList() : Collections.emptyList());
        detailDTO.setPostList(detailDTO.getPostList() != null ? detailDTO.getPostList() : Collections.emptyList());

        detailDTO.setDeptId(sysDeptExtDTO.getDeptId());
        detailDTO.setName(sysDeptExtDTO.getName());
        detailDTO.setTitle(sysDeptExtDTO.getTitle());
        detailDTO.setStatus(sysDeptExtDTO.getStatus());
        detailDTO.setLevel(sysDeptExtDTO.getLevel());
        detailDTO.setSv(sysDeptExtDTO.getSv());
        detailDTO.setSvDays(sysDeptExtDTO.getSvDays());
        detailDTO.setCountry(sysDeptExtDTO.getCountry());
        detailDTO.setProvince(sysDeptExtDTO.getProvince());
        detailDTO.setCity(sysDeptExtDTO.getCity());
        detailDTO.setArea(sysDeptExtDTO.getArea());
        detailDTO.setLng(sysDeptExtDTO.getLng());
        detailDTO.setLat(sysDeptExtDTO.getLat());
        detailDTO.setAddress(sysDeptExtDTO.getAddress());
        return detailDTO;
    }

    @Override
    public AppProjectDTO getByDeptId(Integer deptId) {
        SysDeptExtDTO sysDeptExtDTO = sysDeptMapper.selectDetail(deptId);

        AppProjectDTO appProjectDTO = new AppProjectDTO();

        SysDeptExtInfoDTO extInfoDTO = JSON.parseObject(sysDeptExtDTO.getDetail(), SysDeptExtInfoDTO.class);
        if (extInfoDTO != null) {
            BeanUtils.copyProperties(extInfoDTO, appProjectDTO);
        }
        appProjectDTO.setDeptId(deptId);
        appProjectDTO.setName(sysDeptExtDTO.getName());
        appProjectDTO.setTitle(sysDeptExtDTO.getTitle());
        appProjectDTO.setLng(sysDeptExtDTO.getLng());
        appProjectDTO.setLat(sysDeptExtDTO.getLat());
        appProjectDTO.setAddress(sysDeptExtDTO.getAddress());
        return appProjectDTO;
    }

    @Override
    public Gps getGps(Integer deptId) throws BizException {
        SysDept ext = sysDeptMapper.selectByPrimaryKey(deptId);
        if (ext != null && ext.getLng() != null && ext.getLng() > 0) {
            return new Gps(ext.getLng(), ext.getLat());
        }
        return null;
    }

    @Override
    public void edit(AppProjectParam param) {
        Integer deptId = param.getDeptId();
        Double lng = param.getLng();
        Double lat = param.getLat();
        String address = param.getAddress();

        // 逆地址解析
        if (lng != null && lat != null && StringUtils.isBlank(address)) {
            MapLoc loc = mapApiFactory.getMapApi().geocode(lng, lat);
            if (loc != null) {
                address = loc.getAddress();
            }
        }

        // 获取组织机构
        SysDept sysDept = sysDeptMapper.selectByPrimaryKey(deptId);
        if (sysDept == null) {
            throw new BizException(ResultEnum.PARAM_ERROR.getCode(), I18nErrorCode.SYS_BE_002.getCode());
        }

        // 获取扩展信息
        SysDeptExtInfoDTO extInfo = new SysDeptExtInfoDTO();
        extInfo.setProjectCode(param.getProjectCode());
        extInfo.setBuilderLicenseNum(param.getBuilderLicenseNum());
        extInfo.setStartDate(param.getStartDate());
        extInfo.setCompleteDate(param.getCompleteDate());
        extInfo.setShortInfo(param.getShortInfo());
        extInfo.setPeriod(param.getPeriod());
        extInfo.setConstDays(param.getConstDays());
        extInfo.setMoney(param.getMoney());
        extInfo.setCurrency(param.getCurrency());
        extInfo.setSocialCode(param.getSocialCode());
        extInfo.setBoardBgImg(param.getBoardBgImg());

        // 项目图片
        List<ImgParam> imgList = param.getImgList();
        List<AppProjectImgDTO> projectImgList = new ArrayList<>();
        if (imgList != null && !imgList.isEmpty()) {
            for (ImgParam imgParam : imgList) {
                AppProjectImgDTO appProjectImg = new AppProjectImgDTO();
                appProjectImg.setImgInfo(imgParam.getImgInfo());
                appProjectImg.setImgUrl(imgParam.getImgUrl());
                projectImgList.add(appProjectImg);
            }
            extInfo.setImgList(projectImgList);
        }

        // 项目单位
        List<AppProjectUnitParam> unitList = param.getUnitList();
        List<AppProjectUnitDTO> projectUnitList = new ArrayList<>();
        if (unitList != null && !unitList.isEmpty()) {
            for (AppProjectUnitParam unitParam : unitList) {
                AppProjectUnitDTO appProjectUnitDTO = new AppProjectUnitDTO();
                appProjectUnitDTO.setIdx(unitParam.getIdx());
                appProjectUnitDTO.setName(unitParam.getName());
                appProjectUnitDTO.setType(unitParam.getType());
                projectUnitList.add(appProjectUnitDTO);
            }
            extInfo.setUnitList(projectUnitList);
        }

        // 项目岗位
        List<AppProjectPostParam> postList = param.getPostList();
        List<AppProjectPostDTO> projectPostList = new ArrayList<>();
        if (postList != null && !postList.isEmpty()) {
            for (AppProjectPostParam postParam : postList) {
                AppProjectPostDTO appProjectPostDTO = new AppProjectPostDTO();
                appProjectPostDTO.setPost(postParam.getPost());
                appProjectPostDTO.setName(postParam.getName());
                projectPostList.add(appProjectPostDTO);
            }
            extInfo.setPostList(projectPostList);
        }

        sysDept.setTitle(param.getTitle());
        sysDept.setStatus(param.getStatus());
        sysDept.setLevel(param.getLevel());
        sysDept.setSv(param.getSv());
        sysDept.setCountry(param.getCountry());
        sysDept.setProvince(param.getProvince());
        sysDept.setCity(param.getCity());
        sysDept.setArea(param.getArea());
        sysDept.setLng(lng);
        sysDept.setLat(lat);
        sysDept.setAddress(address);
        sysDept.setDetail(JSON.toJSONString(extInfo));

        sysDeptMapper.updateByPrimaryKeySelective(sysDept);
    }

    @Override
    public List<AppProjectDTO> getProjectList(List<Integer> deptIds) {
        if (deptIds == null || deptIds.isEmpty()) {
            return Collections.emptyList();
        }
        return sysDeptMapper.selectProjectBasicInfo(deptIds);
    }

    @Override
    public List<AppProjectDTO> getProjectList() {
        return sysDeptMapper.selectProjectBasicInfo(null);
    }

    @Override
    public String getProjectCustomizeField(Integer deptId) throws BizException {
        String resultStr = "";
        SysDept sysDept = sysDeptMapper.selectByPrimaryKey(deptId);
        if (sysDept == null) {
            return resultStr;
        }
        SysDeptExtInfoDTO extInfo = JSON.parseObject(sysDept.getDetail(), SysDeptExtInfoDTO.class);
        if (extInfo == null) {
            return resultStr;
        }
        return extInfo.getJsonStr();
    }

    @Override
    public AppProjectStat getProjectStat(Integer deptId) throws BizException {
        AppProjectStat stat = new AppProjectStat();
        List<Integer> deptIdList = sysDeptMapper.selectDescendantDeptIdList(deptId);
        if (deptIdList != null && !deptIdList.isEmpty()) {
            List<AppStatNumDTO> countryStat = sysDeptMapper.selectCountryStat(deptIdList);
            List<AppStatNumDTO> provinceStat = sysDeptMapper.selectProvinceStat(deptIdList);
            List<AppStatNumDTO> cityStat = sysDeptMapper.selectCityStat(deptIdList);
            List<AppStatNumDTO> statusStat = sysDeptMapper.selectStatusStat(deptIdList);
            List<AppStatNumDTO> levelStat = sysDeptMapper.selectLevelStat(deptIdList);
            List<AppStatNumDTO> svStat = sysDeptMapper.selectSvStat(deptIdList);
            List<AppSvDaysNumStat> svDaysStat = sysDeptMapper.selectSvDaysStat(deptIdList);
            statusStat.forEach(item -> {
                ProjectStatus status = ProjectStatus.parseValue(item.getId());
                item.setName(status.getName());
            });
            levelStat.forEach(item -> {
                ProjectLevel level = ProjectLevel.parseValue(item.getId());
                item.setName(level.getName());
            });
            svStat.forEach(item -> {
                ProjectSv sv = ProjectSv.parseValue(item.getId());
                item.setName(sv.getName());
            });
            Map<ProjectSvDays, Integer> svDaysMap = svDaysStat.stream()
                    .collect(Collectors.groupingBy(dto -> ProjectSvDays.parseValue(dto.getSvDays()),
                            Collectors.summingInt(AppSvDaysNumStat::getNum)));
            List<AppStatNumDTO> delayDaysStat = new ArrayList<>(svDaysMap.size());
            svDaysMap.forEach((key, value) -> {
                AppStatNumDTO dto = new AppStatNumDTO();
                dto.setName(key.getName());
                dto.setNum(value);
                delayDaysStat.add(dto);
            });
            int total = statusStat.stream().mapToInt(AppStatNumDTO::getNum).sum();

            stat.setCountryStat(countryStat);
            stat.setProvinceStat(provinceStat);
            stat.setCityStat(cityStat);
            stat.setStatusStat(statusStat);
            stat.setLevelStat(levelStat);
            stat.setSvStat(svStat);
            stat.setDelayDaysStat(delayDaysStat);
            stat.setTotal(total);
        }
        return stat;
    }

    /**
     * 根据用户获取组织机构ID列表
     *
     * @param user 当前用户
     * @return 组织机构列表
     */
    private List<Integer> getDeptIdList(SysUser user) {

        if (user == null) {
            return new ArrayList<>();
        }
        List<SysDeptDTO> deptList;
        // 内部账号
        if (SysUserType.INNER.getValue().equals(user.getType())) {
            deptList = sysDeptMapper.selectAllProject();
        }
        // 客户账号
        else {
            deptList = sysDeptMapper.selectProjectListByUserId(user.getId());
        }

        return deptList.stream().map(SysDeptDTO::getId).collect(Collectors.toList());
    }
}
