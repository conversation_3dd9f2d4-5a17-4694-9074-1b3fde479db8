package com.whfc.fuum.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 项目信息
 */
@Data
public class AppProjectDTO implements Serializable {

    /**
     * 项目ID
     */
    private Integer id;

    /**
     * 组织机构ID
     */
    private Integer deptId;

    /**
     * 组织机构guid
     */
    private String orgId;

    /**
     * 项目名称
     */
    private String name;

    /**
     * 项目简称
     */
    private String title;

    /**
     * 简介
     */
    private String shortInfo;

    /**
     * 项目编码
     */
    private String projectCode;

    /**
     * 合同金额
     */
    private Integer money;

    /**
     * 施工周期(月)
     */
    private Integer period;

    /**
     * 开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startDate;

    /**
     * 截止日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date completeDate;

    /**
     * 施工天数
     */
    private Integer constDays;

    /**
     * 建造许可证
     */
    private String builderLicenseNum;

    /**
     * 项目状态
     */
    private Integer status;

    /**
     * 项目级别
     */
    private Integer level;

    /**
     * 进度偏差
     */
    private Integer sv;

    /**
     * 进度偏差天数
     */
    private Integer svDays;

    /**
     * 国家
     */
    private String country;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区域
     */
    private String area;

    /**
     * 经度
     */
    private Double lng;

    /**
     * 纬度
     */
    private Double lat;

    /**
     * 地址详情
     */
    private String address;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 是否授权
     */
    private Boolean authorized;

    /**
     * 项目封面
     */
    private String cover;

    /**
     * 项目地址
     */
    private String location;
}
