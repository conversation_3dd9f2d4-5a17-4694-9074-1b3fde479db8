package com.whfc.fuum.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-07-22 14:54
 */
@Data
public class SysDeptExtInfoDTO implements Serializable {

    /**
     * 组织机构类型 (特殊情况使用)
     */
    private Integer type;

    /**************************   公司扩展信息    **************************/

    /**
     * 单位代码
     */
    private String corpCode;

    /**
     * 社会信用代码类型
     */
    private Integer corpCodeType;

    /**
     * 注册时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date registerDate;

    /**
     * 经营范围
     */
    private String bizScope;

    /**
     * 法人姓名
     */
    private String legalMan;

    /**
     * 法人电话
     */
    private String legalManPhone;

    /**
     * 公司简介/项目简介
     * 当公司简介与项目简介共存时，则此字段为公司简介，项目简介则用 projectInfo
     */
    private String shortInfo;


    /**************************   项目扩展信息    **************************/

    /**
     * 项目名称(用于公司扩展信息)
     */
    private String name;

    /**
     * 项目标题(用于公司扩展信息)
     */
    private String title;

    /**
     * 项目编码
     */
    private String projectCode;

    /**
     * 施工许可证
     */
    private String builderLicenseNum;

    /**
     * 社会信用代码
     */
    private String socialCode;

    /**
     * 项目投资
     */
    private Integer money;

    /**
     * 货币
     */
    private String currency;

    /**
     * 开工日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startDate;

    /**
     * 竣工日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date completeDate;

    /**
     * 项目周期
     */
    private Integer period;

    /**
     * 施工天数
     */
    private Integer constDays;

    /**
     * 项目简介(用于公司扩展信息)
     */
    private String projectInfo;

    /**
     * 项目造价
     */
    private String constructionUnit;

    /**
     * 设计单位
     */
    private String designUnit;

    /**
     * 监理单位
     */
    private String supervisionUnit;

    /**
     * 勘察单位
     */
    private String explorationUnit;

    /**
     * 施工单位
     */
    private String contractor;

    /**
     * 检测单位
     */
    private String detectionUnit;

    /**
     * 质量看护单位
     */
    private String qualityCareUnit;

    /**
     * 宣传图片
     */
    private List<AppProjectImgDTO> imgList;

    /**
     * 项目单位
     */
    private List<AppProjectUnitDTO> unitList;

    /**
     * 项目岗位
     */
    private List<AppProjectPostDTO> postList;


    /********************* 自定义属性  *************************/

    /**
     * 自定义json属性
     */
    private String jsonStr;

}
