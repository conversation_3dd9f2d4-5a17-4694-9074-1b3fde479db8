package com.whfc.fuum.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 菜单国际化关联表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/6 11:41
 */
@Data
@Schema(description = "菜单国际化关联表")
public class SysRuleI18nDTO implements Serializable {

    /**
     * 菜单ID
     */
    @Schema(description =  "菜单ID")
    private Integer ruleId;

    /**
     * 菜单权限编码
     */
    @ExcelProperty(value = "code")
    @Schema(description =  "菜单权限编码")
    private String code;

    /**
     * 语言
     */
    @Schema(description =  "语言")
    private String language;

    /**
     * 国际化后菜单名称
     */
    @ExcelProperty(value = "name")
    @Schema(description =  "国际化后菜单名称")
    private String name;

    /**
     * 国际化后菜单简称
     */
    @ExcelProperty(value = "shortName")
    @Schema(description =  "国际化后菜单简称")
    private String shortName;

    /**
     * 国际化后菜单描述
     */
    @ExcelProperty(value = "content")
    @Schema(description =  "国际化后菜单描述")
    private String content;


    private static final long serialVersionUID = 1L;

}