package com.whfc.fuum.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-07-06 09:24
 */
@Data
public class SysDeptExtraParam implements Serializable {

    /**
     * 组织机构ID
     */
    @NotNull
    private Integer deptId;

    /**
     * 项目名称
     */
    @NotEmpty
    private String name;

    /**
     * 简称
     */
    private String title;

    /**
     * 社会信用代码类型
     */
    private Integer corpCodeType;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 经度
     */
    private Double lng;

    /**
     * 纬度
     */
    private Double lat;

    /**
     * 法人电话
     */
    private String legalManPhone;

    /**
     * 法人姓名
     */
    private String legalMan;

    /**
     * 经营范围
     */
    private String bizScope;

    /**
     * 注册时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date registerDate;

    /**
     * 单位代码
     */
    private String corpCode;

    /**
     * 项目投资
     */
    private Integer money;

    /**
     * 宣传图片
     */
    private List<String> imgList;

    /**
     * 公司简介
     */
    private String shortInfo;

    /**
     * 项目周期
     */
    private Integer period;

    /**
     * 开工日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startDate;

    /**
     * 竣工日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date completeDate;

    /**
     * 项目简介
     */
    private String projectInfo;

    /**
     * 项目单位
     */
    private List<AppProjectUnitParam> unitList;


}
