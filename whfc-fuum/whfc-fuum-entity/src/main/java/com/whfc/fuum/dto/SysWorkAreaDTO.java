package com.whfc.fuum.dto;

import com.whfc.common.geometry.Point;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 工区表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-09-01 14:24
 */
@Data
public class SysWorkAreaDTO implements Serializable {
    /**
     * 主键ID
     */
    private Integer workAreaId;

    /**
     * 组织机构ID
     */
    private Integer deptId;

    /**
     * 名称
     */
    private String name;

    /**
     * 电子围栏类型（1-多边形 2-圆形）
     */
    private Integer fenceType;

    /**
     * 多边形坐标
     */
    private List<Point> polygonPointList;

    /**
     * 圆形中心点坐标
     */
    private Point centerPoint;

    /**
     * 圆形半径
     */
    private Double radius;


}