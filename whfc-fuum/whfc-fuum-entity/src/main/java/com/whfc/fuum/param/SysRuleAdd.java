package com.whfc.fuum.param;

import com.whfc.common.validator.IntValueBetween;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

@Data
public class SysRuleAdd implements Serializable {

    /**
     * 权限ID
     */
    private Integer ruleId;

    /**
     * 权限名称
     */
    @NotEmpty
    @Size(max = 50)
    private String ruleName;

    /**
     * 简称
     */
    @NotEmpty
    @Size(max = 20)
    private String shortName;

    /**
     * 权限编码
     */
    @NotEmpty
    @Size(max = 50)
    private String code;

    /**
     * 访问路径
     */
    @Size(max = 512)
    private String path;

    /**
     * 权限类型（1-模块，2-页面，3-按钮）
     */
    @NotNull
    @IntValueBetween(between = {1, 2, 3})
    private Integer type;

    /**
     * 页面类型:1-内链 2-外链
     */
    @NotNull
    @IntValueBetween(between = {1, 2})
    private Integer pageType;

    /**
     * 父id
     */
    @NotNull
    private Integer pid;

    /**
     * 排序(同层级排序)
     */
    @NotNull
    private Integer index;

    /**
     * 图标
     */
    @Size(max = 250)
    private String iconPath;

    /**
     * 描述
     */
    @Size(max = 250)
    private String content;

    /**
     * 权限平台类型 1-后台  2-小程序
     */
    @NotNull
    @IntValueBetween(between = {1, 2, 4})
    private Integer platform;
}
