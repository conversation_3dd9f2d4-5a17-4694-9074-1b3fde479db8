package com.whfc.fuum.param;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class SysRoleRuleEdit implements Serializable {

    /**
     * 角色ID
     */
    @NotNull
    private Integer roleId;

    /**
     * 权限平台
     */
    @NotNull
    private Integer platform;

    /**
     * 权限ID集合
     */
    private List<Integer> ruleIds;
}
