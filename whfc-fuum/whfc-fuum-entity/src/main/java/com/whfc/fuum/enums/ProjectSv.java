package com.whfc.fuum.enums;

/**
 * 项目进度偏差
 *
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/6/25 16:14
 */
public enum ProjectSv {

    NORMAL(1, "正常"),

    AHEAD(2, "提前"),

    BEHIND(3, "延期");

    private Integer value;

    private String name;

    ProjectSv(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public static ProjectSv parseValue(Integer value) {
        for (ProjectSv item : ProjectSv.values()) {
            if (item.getValue().equals(value)) {
                return item;
            }
        }
        return NORMAL;
    }
}
