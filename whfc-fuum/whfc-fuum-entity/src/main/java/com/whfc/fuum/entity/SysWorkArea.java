package com.whfc.fuum.entity;

import java.util.Date;

/**
 * 工区表
* <AUTHOR>
* @version 1.0
* @date 2021-09-01 14:24
*/
public class SysWorkArea {
    /**
    * 主键ID
    */
    private Integer id;

    /**
    * 组织机构ID
    */
    private Integer deptId;

    /**
    * 名称
    */
    private String name;

    /**
    * 经度
    */
    private Double lng;

    /**
    * 纬度
    */
    private Double lat;

    /**
    * 位置信息
    */
    private String address;

    /**
    * 详细数据（json）
    */
    private String detail;

    /**
    * 删除标记（0-未删除 1-已删除）
    */
    private Integer delFlag;

    /**
    * 更新时间
    */
    private Date updateTime;

    /**
    * 创建时间
    */
    private Date createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getDeptId() {
        return deptId;
    }

    public void setDeptId(Integer deptId) {
        this.deptId = deptId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Double getLng() {
        return lng;
    }

    public void setLng(Double lng) {
        this.lng = lng;
    }

    public Double getLat() {
        return lat;
    }

    public void setLat(Double lat) {
        this.lat = lat;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}