package com.whfc.fuum.enums.gscx;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-03-11 14:23
 */
public enum GscxDeptType {

    VENDOR(-1, "厂商"),

    COMPANY(0, "公司"),

    PROJECT(1, "项目");

    private Integer value;

    private String desc;

    GscxDeptType(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
