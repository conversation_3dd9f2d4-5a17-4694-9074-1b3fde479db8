package com.whfc.fuum.entity;

import lombok.Data;

import java.util.Date;

/**
 * 组织机构
 */
@Data
public class SysDept {
    private Integer id;

    /**
     * 组织机构uuid
     */
    private String guid;

    /**
     * 父ID
     */
    private Integer pid;

    /**
     * 祖先ID,逗号分隔
     */
    private String pids;

    /**
     * 名称
     */
    private String name;

    /**
     * 项目简称
     */
    private String title;

    /**
     * 机构类型:(0-公司,1-项目 2-工区 3-施工队 4-其他)
     */
    private Integer type;

    /**
     * 状态(0-禁用,1-启用)
     */
    private Integer state;

    /**
     * 项目状态
     */
    private Integer status;

    /**
     * 项目级别
     */
    private Integer level;

    /**
     * 进度偏差
     */
    private Integer sv;

    /**
     * 偏差天数
     */
    private Integer svDays;

    /**
     * 国
     */
    private String country;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 经度
     */
    private Double lng;

    /**
     * 纬度
     */
    private Double lat;

    /**
     * 详细数据
     */
    private String detail;

    /**
     * 排序
     */
    private Integer index;

    /**
     * 删除标记(0-未删除 1-已删除)
     */
    private Integer delFlag;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;
}