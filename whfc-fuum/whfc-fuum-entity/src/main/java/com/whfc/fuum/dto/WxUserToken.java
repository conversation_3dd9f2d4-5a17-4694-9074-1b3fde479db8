package com.whfc.fuum.dto;

import com.whfc.fuum.entity.WxUser;
import lombok.Data;
import org.apache.shiro.authc.AuthenticationToken;

import java.io.Serializable;

/**
 * @Description: 微信网站应用认证token
 * @author: xugcheng
 * @version: 1.0
 * @date: 2019/11/7 15:03
 */
@Data
public class WxUserToken implements AuthenticationToken, Serializable {

    /**
     * 微信网站应用openId
     */
    private WxUser wxUser;

    @Override
    public Object getPrincipal() {
        return this.wxUser.getOpenId();
    }

    @Override
    public Object getCredentials() {
        return this.wxUser.getOpenId();
    }
}
