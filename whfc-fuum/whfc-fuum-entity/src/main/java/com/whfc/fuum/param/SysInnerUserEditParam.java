package com.whfc.fuum.param;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @DESCRIPTION
 * @AUTHOR GuoDong_Sun
 * @DATE 2020/3/31
 */
@Data
public class SysInnerUserEditParam implements Serializable {
    @NotNull
    private Integer userId;
    @NotEmpty
    private String username;
    @NotEmpty
    private String nickname;

    private String areaCode;
    @NotEmpty
    private String phone;
}
