package com.whfc.fuum.param;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.whfc.common.constant.Language;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

@Setter
@Getter
public class UpdatePasswordParam implements Serializable {

    @NotEmpty
    private String newPassword;
    @NotEmpty
    private String oldPassword;

    @JsonProperty(defaultValue = Language.zh_CN)
    private String language;
}
