package com.whfc.fuum.entity;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

import lombok.Data;

/**
 * 大屏配置模板表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/1
 */
@Schema(description = "大屏配置模板表")
@Data
public class SysBoardTemplate {
    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Integer id;

    /**
     * 模板编码
     */
    @Schema(description = "模板编码")
    private String template;

    /**
     * 模板名称
     */
    @Schema(description = "模板名称")
    private String name;

    @Schema(description = "子名称")
    private String subName;

    /**
     * 模板说明
     */
    @Schema(description = "模板说明")
    private String remark;

    /**
     * 模板布局
     */
    @Schema(description = "模板布局")
    private String layout;

    @Schema(description = "语言配置数据")
    private String languageData;

    @Schema(description = "样式配置数据")
    private String style;

    /**
     * 删除标记:0-未删除 1-已删除
     */
    @Schema(description = "删除标记:0-未删除 1-已删除")
    private Integer delFlag;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;
}