package com.whfc.fuum.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class AppProjectParam implements Serializable {

    /**
     * 项目id
     */
    @NotNull
    private Integer deptId;

    /**
     * 名称
     */
    @NotEmpty
    private String name;

    /**
     * 简称
     */
    @NotEmpty
    private String title;

    /**
     * 简介
     */
    private String shortInfo;

    /**
     * 项目周期
     */
    private Integer period;

    /**
     * 项目金额
     */
    private Integer money;

    /**
     * 货币
     */
    private String currency;

    /**
     * 项目编号
     */
    @NotEmpty
    @Length(max = 50)
    private String projectCode;

    /**
     * 建造许可证
     */
    private String builderLicenseNum;

    /**
     * 社会信用代码
     */
    @Length(max = 32)
    private String socialCode;

    /**
     * 开工日期
     */
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startDate;

    /**
     * 竣工日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date completeDate;

    /**
     * 施工天数
     */
    private Integer constDays;

    /**
     * 项目状态
     */
    private Integer status;

    /**
     * 项目级别
     */
    private Integer level;

    /**
     * 进度偏差
     */
    private Integer sv;

    /**
     * 国家
     */
    private String country;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区域
     */
    private String area;

    /**
     * 经度
     */
    private Double lng;

    /**
     * 纬度
     */
    private Double lat;

    /**
     * 地址
     */
    @Length(max = 255)
    private String address;

    /**
     * 图片
     */
    private List<ImgParam> imgList;

    /**
     * 职位
     */
    private List<AppProjectPostParam> postList;

    /**
     * 参建单位
     */
    private List<AppProjectUnitParam> unitList;

    /**
     * 大屏背景图片
     */
    private String boardBgImg;
}
