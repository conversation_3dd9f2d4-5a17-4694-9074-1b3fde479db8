package com.whfc.fuum.param.open;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2019/10/21 11:37
 */
@Data
public class AccessTokenParam implements Serializable {

    /**
     * appId
     */
    @NotEmpty
    private String appId;

    /**
     * 时间戳
     */
    @NotNull
    private Long timestamp;

    /**
     * 随机字符串
     */
    @NotEmpty
    @Size(max = 16)
    private String randomStr;

    /**
     * 签名
     */
    @NotEmpty
    private String sign;
}
