package com.whfc.fuum.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 大屏配置组件
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/1
 */
@Schema(description = "大屏配置组件")
@Data
public class SysBoardComponentDTO implements Serializable {
    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Integer id;

    /**
     * 路由ID
     */
    @Schema(description = "路由ID")
    private Integer routerId;

    /**
     * 组件编码
     */
    @Schema(description = "组件编码")
    private String component;

    /**
     * 组件名称
     */
    @Schema(description = "组件名称")
    private String name;

    /**
     * 子名称
     */
    @Schema(description = "子名称")
    private String subName;

    /**
     * 组件说明
     */
    @Schema(description = "组件说明")
    private String remark;

    /**
     * 语言配置数据
     */
    @Schema(description = "语言配置数据")
    private String languageData;

    /**
     * 样式配置数据
     */
    @Schema(description = "样式配置数据")
    private String style;

    /**
     * 最小宽度
     */
    @Schema(description = "最小宽度")
    private Integer minWidth;

    /**
     * 最小高度
     */
    @Schema(description = "最小高度")
    private Integer minHeight;

}