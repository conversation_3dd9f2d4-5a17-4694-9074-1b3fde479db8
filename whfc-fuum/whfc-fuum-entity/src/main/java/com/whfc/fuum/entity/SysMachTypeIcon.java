package com.whfc.fuum.entity;

import java.util.Date;
import lombok.Data;

/**
    * 设备类别图标表
    */
@Data
public class SysMachTypeIcon {
    /**
    * 主键
    */
    private Integer id;

    /**
    * 设备类型id
    */
    private Integer machTypeId;

    /**
    * 图标类型（根据字典表配置类型显示）
    */
    private Integer state;

    /**
    * 图标地址
    */
    private String iconUrl;

    /**
    * 是否删除 0-未删除，1-删除
    */
    private Integer delFlag;

    /**
    * 更新时间
    */
    private Date updateTime;

    /**
    * 删除时间
    */
    private Date createTime;
}