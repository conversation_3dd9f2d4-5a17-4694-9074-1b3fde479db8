package com.whfc.fuum.param.gscx;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-03-11 10:13
 */
@Data
public class GscxDeptAddParam implements Serializable {

    /**
     * 父节点ID
     */
    @NotNull
    private Integer pid;

    /**
     * 名称
     */
    @NotEmpty
    private String name;

    /**
     * 组织机构类型  -1 厂商 0-公司  1-项目
     */
    @NotNull
    private Integer type;

    /**
     * 经度
     */
    private Double lng;

    /**
     * 纬度
     */
    private Double lat;

    /**
     * 位置详情
     */
    private String address;

    /**
     * 社会信用代码类型(1-社会信用代码 2-组织机构代码)
     */
    private Integer corpCodeType;

    /**
     * 单位代码
     */
    private String corpCode;

    /**
     * 注册时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date registerDate;

    /**
     * 经营范围
     */
    private String bizScope;

    /**
     * 法人姓名
     */
    private String legalMan;

    /**
     * 法人电话
     */
    private String legalManPhone;

    /**
     * 项目编码
     */
    private String projectCode;

    /**
     * 项目造价
     */
    private Integer money;

    /**
     * 施工许可证
     */
    private String builderLicenseNum;

    /**
     * 开工日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startDate;

    /**
     * 竣工日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date completeDate;

    /**
     * 简介
     */
    private String shortInfo;

}
