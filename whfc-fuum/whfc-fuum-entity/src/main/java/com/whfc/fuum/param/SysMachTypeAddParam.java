package com.whfc.fuum.param;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * @ClasssName SysMachTypeAddParam
 * @Description 添加设备类型请求类
 * <AUTHOR>
 * @Date 2020/8/24 18:06
 * @Version 1.0
 */
@Data
public class SysMachTypeAddParam extends SysMachTypeClassifyAddParam implements Serializable {


    /**
     * 父级id
     */
    @NotNull
    private Integer pid;
    /**
     * 上传图标
     */
    @Size(min = 1,max = 4,message = "图片最少上传一张最多4张")
    private List<SysMachTypeIconAddParam> icons;

}
