package com.whfc.fuum.param;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2019/7/23 14:56
 */
@Setter
@Getter
@ToString
public class SysUserEnable implements Serializable {

    @NotNull
    private Integer userId;

    @NotNull
    private Integer status;
}
