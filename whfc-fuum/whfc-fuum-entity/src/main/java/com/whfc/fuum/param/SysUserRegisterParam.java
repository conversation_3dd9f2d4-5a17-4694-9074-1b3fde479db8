package com.whfc.fuum.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/26
 */
@Data
@Schema(description = "用户注册参数")
public class SysUserRegisterParam implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "区号")
    private String areaCode;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "密码")
    private String password;

    @Schema(description = "短信验证码")
    private String msgCode;


}
