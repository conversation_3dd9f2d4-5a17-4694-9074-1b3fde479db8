package com.whfc.fuum.entity;

import lombok.Data;

import java.util.Date;

/**
 * 项目单位
 *
 * <AUTHOR>
 */
@Data
public class AppProjectUnit {
    private Integer id;

    /**
     * 项目id
     */
    private Integer projectId;

    /**
     * 单位类型
     */
    private String type;

    /**
     * 单位名称
     */
    private String name;

    /**
     * 排序
     */
    private Integer idx;

    private Date createTime;

    private Date updateTime;
}