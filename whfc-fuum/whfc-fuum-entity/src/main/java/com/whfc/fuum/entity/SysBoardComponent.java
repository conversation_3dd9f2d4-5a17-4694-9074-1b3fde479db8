package com.whfc.fuum.entity;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

import lombok.Data;

/**
 * 大屏配置组件表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/1
 */
@Schema(description = "大屏配置组件表")
@Data
public class SysBoardComponent {
    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Integer id;

    /**
     * 路由ID
     */
    @Schema(description = "路由ID")
    private Integer routerId;

    /**
     * 组件编码
     */
    @Schema(description = "组件编码")
    private String component;

    /**
     * 组件名称
     */
    @Schema(description = "组件名称")
    private String name;

    @Schema(description = "子名称")
    private String subName;

    /**
     * 组件说明
     */
    @Schema(description = "组件说明")
    private String remark;


    @Schema(description = "语言配置数据")
    private String languageData;

    @Schema(description = "样式配置数据")
    private String style;

    /**
     * 最小宽度
     */
    @Schema(description = "最小宽度")
    private Integer minWidth;

    /**
     * 最小高度
     */
    @Schema(description = "最小高度")
    private Integer minHeight;

    /**
     * 删除标记:0-未删除 1-已删除
     */
    @Schema(description = "删除标记:0-未删除 1-已删除")
    private Integer delFlag;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;
}