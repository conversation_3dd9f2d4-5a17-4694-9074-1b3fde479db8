package com.whfc.fuum.enums;

/**
 * @Description: 页面类型
 * @author: xug<PERSON>
 * @version: 1.0
 * @date: 2021-12-13 11:03
 */
public enum PageType {

    inner(1, "内链"),

    outer(2, "外链");

    private Integer value;

    private String desc;

    PageType(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
