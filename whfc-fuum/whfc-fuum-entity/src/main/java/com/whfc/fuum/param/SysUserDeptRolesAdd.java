package com.whfc.fuum.param;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020-12-03 20:17
 */
@Data
public class SysUserDeptRolesAdd implements Serializable {

    /**
     * 用户ID
     */
    @NotNull
    private Integer userId;

    /**
     * 组织机构ID
     */
    @NotEmpty
    private List<Integer> deptIds;

    /**
     * 角色ID列表
     */
    @NotEmpty
    private List<Integer> roleIds;

}
