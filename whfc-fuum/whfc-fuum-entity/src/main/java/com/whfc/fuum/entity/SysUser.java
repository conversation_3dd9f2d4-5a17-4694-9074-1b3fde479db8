package com.whfc.fuum.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class SysUser implements Serializable {

    private static final long serialVersionUID = -4176400803326478397L;

    private Integer id;

    private String guid;

    private String username;

    private String nickname;

    private String areaCode;

    private String phone;

    private String password;

    private String salt;

    private String email;

    private String sign;

    private Integer status;

    private Integer gender;

    private Integer type;

    private Date expireTime;

    private Integer canDelete;

    private Date loginTime;

    private Date lastLoginTime;

    private Integer delFlag;

    private Date updateTime;

    private Date createTime;
}