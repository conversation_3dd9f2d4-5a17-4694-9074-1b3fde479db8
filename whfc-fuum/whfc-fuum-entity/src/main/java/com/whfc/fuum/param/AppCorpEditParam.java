package com.whfc.fuum.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

@Data
public class AppCorpEditParam implements Serializable {

    @NotNull
    private Integer corpId;

    /**
     * 公司类型id
     */
    @NotNull
    private Integer corpTypeId;

    /**
     * 公司名称
     */
    @NotEmpty
    @Length(max = 120)
    private String corpName;

    /**
     * 公司代码
     */
    @Length(max = 32)
    private String corpCode;

    /**
     * 社会信用代码类型(1-社会信用代码 2-组织机构代码)
     */
    private Integer corpCodeType;

    /**
     * 经营范围
     */
    @Length(max = 1000)
    private String bizScope;

    /**
     * 法人
     */
    @Length(max = 20)
    private String legalMan;

    /**
     * 法人电话
     */
    @Length(max = 20)
    private String legalManPhone;

    @Length(max = 32)
    private String province;

    @Length(max = 32)
    private String city;

    @Length(max = 32)
    private String area;

    @Length(max = 200)
    private String address;

    /**
     * 注册日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date registerDate;
}
