package com.whfc.fuum.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 项目统计
 *
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/6/25 14:34
 */
@Data
public class AppProjectStat implements Serializable {

    /**
     * 总数
     */
    private Integer total;

    /**
     * 国家统计
     */
    private List<AppStatNumDTO> countryStat;

    /**
     * 省份统计
     */
    private List<AppStatNumDTO> provinceStat;

    /**
     * 城市统计
     */
    private List<AppStatNumDTO> cityStat;

    /**
     * 项目级别统计
     */
    private List<AppStatNumDTO> levelStat;

    /**
     * 项目状态统计
     */
    private List<AppStatNumDTO> statusStat;

    /**
     * 进度偏差统计
     */
    private List<AppStatNumDTO> svStat;

    /**
     * 延迟天数统计
     */
    private List<AppStatNumDTO> delayDaysStat;
}
