package com.whfc.fuum.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 大屏配置模板
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/1
 */
@Schema(description = "大屏配置模板")
@Data
public class SysBoardTemplateParam implements Serializable {
    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Integer id;

    /**
     * 模板编码
     */
    @Schema(description = "模板编码")
    private String template;

    /**
     * 模板名称
     */
    @Schema(description = "模板名称")
    private String name;

    /**
     * 子名称
     */
    @Schema(description = "子名称")
    private String subName;

    /**
     * 模板说明
     */
    @Schema(description = "模板说明")
    private String remark;

    /**
     * 模板布局
     */
    @Schema(description = "模板布局")
    private String layout;

    /**
     * 语言配置数据
     */
    @Schema(description = "语言配置数据")
    private String languageData;

    /**
     * 样式配置数据
     */
    @Schema(description = "样式配置数据")
    private String style;

}