package com.whfc.fuum.param;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2019/7/23 10:21
 */
@Data
public class SysDeptEdit implements Serializable {

    @NotNull
    private Integer deptId;

    @NotNull
    @Length(max = 32)
    private String name;
}
