package com.whfc.fuum.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> qzexing
 * @version : 1.0
 * @date : 2020-10-17 14:46
 */
@Data
public class SysDeptUserEdit implements Serializable {

    /**
     * 组织机构ID
     */
    @NotNull
    private Integer deptId;

    /**
     * 用户ID
     */
    @NotNull
    private Integer userId;

    /**
     * 用户名
     */
    @NotEmpty
    @Length(max = 40)
    private String username;

    /**
     * 姓名
     */
    @NotEmpty
    @Size(max = 35,message = "姓名最长为35个字")
    private String nickname;

    /**
     * 区号
     */
    @Length(max = 10)
    private String areaCode;

    /**
     * 手机号
     */
    @NotEmpty
    @Length(max = 40)
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 账号类型  2-普通账号  3-体验账号
     */
    @NotNull
    private Integer type;

    /**
     * 账号到期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date expireTime;

    /**
     * 角色ID列表
     */
    @NotEmpty
    private List<Integer> roleIds;

    /**
     * 合作单位ID
     */
    private Integer corpId;

    /**
     * 部门ID
     */
    private Integer departmentId;

    /**
     * 职位
     */
    @Length(max = 100)
    private String position;

}
