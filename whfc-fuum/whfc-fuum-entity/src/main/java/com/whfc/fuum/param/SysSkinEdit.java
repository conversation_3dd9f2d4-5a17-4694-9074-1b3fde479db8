package com.whfc.fuum.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Schema(description = "皮肤编辑参数")
@Data
public class SysSkinEdit implements Serializable {

    /**
     * 配置ID
     */
    @Schema(description = "配置ID")
    @NotNull
    private Integer skinId;

    /**
     * 域名
     */
    @Schema(description = "域名")
    @NotEmpty
    @Length(max = 64)
    private String host;

    /**
     * 样式
     */
    @Schema(description = "样式")
    @Length(max = 32)
    private String style;

    /**
     * 公司名称
     */
    @Schema(description = "公司名称")
    @Length(max = 64)
    private String company;

    /**
     * 系统名称
     */
    @Schema(description = "系统名称")
    @Length(max = 64)
    private String name;

    /**
     * 系统标题
     */
    @Schema(description = "系统标题")
    @Length(max = 64)
    private String title;

    /**
     * 登录logo
     */
    @Schema(description = "登录logo")
    @Length(max = 255)
    private String logo;

    /**
     * 标题logo
     */
    @Schema(description = "标题logo")
    @Length(max = 255)
    private String titleLogo;

    /**
     * 扩展字段1
     */
    @Schema(description = "扩展字段1")
    @Length(max = 64)
    private String ext1;

    /**
     * 扩展字段2
     */
    @Schema(description = "扩展字段2")
    @Length(max = 64)
    private String ext2;

    /**
     * 扩展字段3
     */
    @Schema(description = "扩展字段3")
    @Length(max = 64)
    private String ext3;

    /**
     * 扩展字段4
     */
    @Schema(description = "扩展字段4")
    @Length(max = 64)
    private String ext4;

    /**
     * 是否开启验证码
     */
    @Schema(description = "是否开启验证码")
    private Integer captcha;
}