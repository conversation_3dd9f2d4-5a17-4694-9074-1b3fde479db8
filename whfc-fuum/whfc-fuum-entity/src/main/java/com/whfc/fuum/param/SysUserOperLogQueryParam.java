package com.whfc.fuum.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 系统用户操作日志查询参数
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/10
 */
@Schema(description = "系统用户操作日志查询参数")
@Data
public class SysUserOperLogQueryParam implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    private Integer deptId;

    /**
     * 操作人ID
     */
    @Schema(description = "操作人ID")
    private Integer userId;

    /**
     * 操作模块
     */
    @Schema(description = "操作模块")
    private String module;

    /**
     * 登录平台：PC, APP等
     */
    @Schema(description = "登录平台：PC, APP等")
    private String loginPlatform;

    /**
     * 操作类型：LOGIN-登录, LOGOUT-退出, CREATE-新增, UPDATE-修改, DELETE-删除 AUDIT-审核 IMPORT-导入 EXPORT-导出
     */
    @Schema(description = "操作类型：LOGIN-登录, LOGOUT-退出, CREATE-新增, UPDATE-修改, DELETE-删除 AUDIT-审核 IMPORT-导入 EXPORT-导出")
    private String type;


    /**
     * 操作结果响应码
     */
    @Schema(description = "操作结果响应码")
    private Integer resultCode;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 关键字
     */
    @Schema(description = "关键字")
    private String keyword;

    /**
     * 页码
     */
    @Schema(description = "页码")
    private Integer pageNum = 1;

    /**
     * 页大小
     */
    @Schema(description = "页大小")
    private Integer pageSize = 10;
}
