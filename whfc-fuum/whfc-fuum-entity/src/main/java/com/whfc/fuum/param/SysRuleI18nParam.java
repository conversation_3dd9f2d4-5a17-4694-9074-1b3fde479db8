package com.whfc.fuum.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/10 11:04
 */
@Data
public class SysRuleI18nParam implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description =  "权限ID")
    private Integer ruleId;

    @Schema(description =  "权限国际化语言参数列表")
    private List<RuleI18nLanguageParam> paramList;

    @Data
    public static class RuleI18nLanguageParam implements Serializable {

        private static final long serialVersionUID = 1L;

        @Schema(description =  "国际化语言")
        private String language;

        @Schema(description =  "权限名称")
        private String name;

        @Schema(description =  "权限简称")
        private String shortName;

        @Schema(description =  "权限内容")
        private String content;

    }

}
