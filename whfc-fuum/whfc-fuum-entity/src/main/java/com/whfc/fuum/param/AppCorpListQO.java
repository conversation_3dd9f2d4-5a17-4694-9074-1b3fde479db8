package com.whfc.fuum.param;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 合作单位查询参数对象
 *
 * @Author: ；likang
 * @Description:
 * @Date:Create：in 2019/12/7 9:05
 * @Version：1.0
 */
@Data
public class AppCorpListQO implements Serializable {

    /**
     * 页码
     */
    @NotNull
    private Integer pageNum;

    /**
     * 分页大小
     */
    @NotNull
    private Integer pageSize;

    /**
     * 项目ID
     */
    @NotNull
    private Integer deptId;

    /**
     * 关键词
     */
    private String keyword;

    /**
     * 合作单位类型ID
     */
    private Integer corpTypeId;

    /**
     * 组织机构ID集合
     */
    private List<Integer> deptIds;

    /**
     * 供应商类别
     */
    private Integer category;

    /**
     * 合作单位ID集合
     */
    private List<Integer> corpIds;
}