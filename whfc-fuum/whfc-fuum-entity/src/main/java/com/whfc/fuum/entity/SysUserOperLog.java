package com.whfc.fuum.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;

/**
 * 系统用户操作日志表
* <AUTHOR>
* @version 1.0
* @date 2025/7/10
*/
@Schema(description="系统用户操作日志表")
@Data
public class SysUserOperLog {
    /**
    * 主键ID
    */
    @Schema(description="主键ID")
    private Integer id;

    /**
    * 项目ID
    */
    @Schema(description="项目ID")
    private Integer deptId;

    /**
    * 操作人ID
    */
    @Schema(description="操作人ID")
    private Integer userId;

    /**
    * 操作人用户名
    */
    @Schema(description="操作人用户名")
    private String nickname;

    /**
    * 操作模块
    */
    @Schema(description="操作模块")
    private String module;

    /**
    * 业务
    */
    @Schema(description="业务")
    private String business;

    /**
    * 操作时间
    */
    @Schema(description="操作时间")
    private Date time;

    /**
    * 登录平台：PC, APP等
    */
    @Schema(description="登录平台：PC, APP等")
    private String loginPlatform;

    /**
    * 操作类型：LOGIN-登录, LOGOUT-退出, CREATE-新增, UPDATE-修改, DELETE-删除 AUDIT-审核 IMPORT-导入 EXPORT-导出
    */
    @Schema(description="操作类型：LOGIN-登录, LOGOUT-退出, CREATE-新增, UPDATE-修改, DELETE-删除 AUDIT-审核 IMPORT-导入 EXPORT-导出")
    private String type;

    /**
    * 操作描述
    */
    @Schema(description="操作描述")
    private String desc;

    /**
    * 请求URI
    */
    @Schema(description="请求URI")
    private String requestUri;

    /**
    * 请求参数
    */
    @Schema(description="请求参数")
    private String requestParams;

    /**
    * 响应码
    */
    @Schema(description="响应码")
    private String resultCode;

    /**
    * 错误信息（操作失败时记录）
    */
    @Schema(description="错误信息（操作失败时记录）")
    private String errorMsg;

    /**
    * 客户端IP
    */
    @Schema(description="客户端IP")
    private String clientIp;

    /**
    * 操作耗时（毫秒）
    */
    @Schema(description="操作耗时（毫秒）")
    private Long duration;

    /**
    * 删除标记:0-未删除 1-已删除
    */
    @Schema(description="删除标记:0-未删除 1-已删除")
    private Integer delFlag;

    /**
    * 更新时间
    */
    @Schema(description="更新时间")
    private Date updateTime;

    /**
    * 创建时间
    */
    @Schema(description="创建时间")
    private Date createTime;
}