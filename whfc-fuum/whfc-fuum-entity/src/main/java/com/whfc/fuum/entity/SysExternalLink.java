package com.whfc.fuum.entity;

import java.util.Date;

/**
* <AUTHOR>
* @version 1.0
* @date 2020-10-29 14:55
*/

/**
    * 系统外链表
    */
public class SysExternalLink {
    /**
    * ID
    */
    private Integer id;

    /**
    * 组织机构ID
    */
    private Integer deptId;

    /**
    * 编码
    */
    private String code;

    /**
    * 链接URL
    */
    private String url;

    /**
    * 删除标记 (0-未删除 1-已删除)
    */
    private Integer delFlag;

    /**
    * 更新时间
    */
    private Date updateTime;

    /**
    * 创建时间
    */
    private Date createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getDeptId() {
        return deptId;
    }

    public void setDeptId(Integer deptId) {
        this.deptId = deptId;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}