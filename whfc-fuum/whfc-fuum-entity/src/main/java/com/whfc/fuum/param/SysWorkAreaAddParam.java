package com.whfc.fuum.param;

import com.whfc.common.geometry.Point;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-09-01 14:56
 */
@Data
public class SysWorkAreaAddParam implements Serializable {


    /**
     * 组织机构ID
     */
    @NotNull
    private Integer deptId;

    /**
     * 名称
     */
    @NotEmpty
    private String name;

    /**
     * 电子围栏类型（1-多边形 2-圆形）
     */
    private Integer fenceType;

    /**
     * 多边形坐标
     */
    private List<Point> polygonPointList;

    /**
     * 圆形中心点坐标
     */
    private Point centerPoint;

    /**
     * 圆形半径
     */
    private Double radius;

}
