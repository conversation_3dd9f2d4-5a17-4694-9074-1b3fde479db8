package com.whfc.fuum.enums;

/**
 * 项目进度偏差天数
 *
 * @Description:
 * @author: xug<PERSON>
 * @version: 1.0
 * @date: 2024/7/16 11:12
 */
public enum ProjectSvDays {

    sv_days_0("正常", 0, 0),

    sv_days_1("延期0-7天", 0, 7),

    sv_days_2("延期7-15天", 7, 15),

    sv_days_3("延期15-30天", 15, 30),

    sv_days_4("延期30天以上", 30, Integer.MAX_VALUE);

    private String name;

    private Integer min;

    private Integer max;

    public String getName() {
        return name;
    }

    public Integer getMin() {
        return min;
    }

    public Integer getMax() {
        return max;
    }

    ProjectSvDays(String name, Integer min, Integer max) {
        this.name = name;
        this.min = min;
        this.max = max;
    }

    public static ProjectSvDays parseValue(Integer value) {
        for (ProjectSvDays projectSvDays : ProjectSvDays.values()) {
            if (value > projectSvDays.min && value <= projectSvDays.max) {
                return projectSvDays;
            }
        }
        return sv_days_0;
    }
}
