package com.whfc.fuum.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-07-06 09:44
 */
@Data
public class SysDeptExtraDTO implements Serializable {

    /**
     * 组织机构名称
     */
    private String name;

    /**
     * 简称
     */
    private String title;

    /**
     * 项目状态
     */
    private Integer status;

    /**
     * 项目级别
     */
    private Integer level;

    /**
     * 进度偏差
     */
    private Integer sv;

    /**
     * 国家
     */
    private String country;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区域
     */
    private String area;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 经度
     */
    private Double lng;

    /**
     * 纬度
     */
    private Double lat;

    /**
     * 社会信用代码类型
     */
    private Integer corpCodeType;

    /**
     * 单位代码
     */
    private String corpCode;

    /**
     * 法人电话
     */
    private String legalManPhone;

    /**
     * 法人姓名
     */
    private String legalMan;

    /**
     * 经营范围
     */
    private String bizScope;

    /**
     * 注册时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date registerDate;

    /**
     * 项目投资
     */
    private Integer money;

    /**
     * 公司简介
     */
    private String shortInfo;

    /**
     * 项目周期
     */
    private Integer period;

    /**
     * 开工日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startDate;

    /**
     * 竣工日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date completeDate;

    /**
     * 项目简介
     */
    private String projectInfo;

    /**
     * 项目单位
     */
    private List<AppProjectUnitDTO> unitList;

    /**
     * 宣传图片
     */
    private List<String> imgList;

    /**
     * 安全生产天数
     */
    private Integer safeDays;
}
