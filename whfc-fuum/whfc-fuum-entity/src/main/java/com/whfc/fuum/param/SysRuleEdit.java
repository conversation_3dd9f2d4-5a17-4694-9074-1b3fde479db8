package com.whfc.fuum.param;

import com.whfc.common.validator.IntValueBetween;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

@Data
public class SysRuleEdit implements Serializable {

    @NotNull
    private Integer ruleId;

    @NotEmpty
    @Size(max = 50)
    private String ruleName;

    @NotEmpty
    @Size(max = 20)
    private String shortName;

    @NotEmpty
    @Size(max = 50)
    private String code;

    @Size(max = 250)
    private String path;

    @NotNull
    @IntValueBetween(between = {1, 2, 3})
    private Integer type;

    /**
     * 页面类型:1-内链 2-外链
     */
    @NotNull
    @IntValueBetween(between = {1, 2})
    private Integer pageType;
    
    @NotNull
    private Integer pid;

    @NotNull
    private Integer index;

    @Size(max = 250)
    private String iconPath;

    /**
     * 描述
     */
    @Size(max = 250)
    private String content;

    @NotNull
    private Integer platform;
}
