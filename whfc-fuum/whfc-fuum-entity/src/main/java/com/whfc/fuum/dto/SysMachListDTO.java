package com.whfc.fuum.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClasssName SysMachListDTO
 * @Description 设备类别树
 * <AUTHOR>
 * @Date 2020/8/31 18:03
 * @Version 1.0
 */
@Data
public class SysMachListDTO implements Serializable {

    /**
     * 主键id
     */
    private Integer machTypeId;
    /**
     * 设备类型编码
     */
    private String machTypeCode;
    /**
     * 设备类型名称
     */
    private String machTypeName;
    /**
     * 父级id
     */
    private Integer pid;

    private List<SysMachListDTO> children;

}
