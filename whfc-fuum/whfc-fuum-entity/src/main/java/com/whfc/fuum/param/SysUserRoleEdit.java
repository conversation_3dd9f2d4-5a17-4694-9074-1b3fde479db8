package com.whfc.fuum.param;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020-10-23 09:55
 */
@Data
public class SysUserRoleEdit implements Serializable {

    /**
     * 用户ID
     */
    @NotNull
    private Integer userId;

    /**
     * 组织机构ID
     */
    @NotNull
    private Integer deptId;

    /**
     * 角色ID列表
     */
    @NotNull
    private List<Integer> roleIds;

}
