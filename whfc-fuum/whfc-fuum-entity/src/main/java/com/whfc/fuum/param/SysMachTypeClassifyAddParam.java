package com.whfc.fuum.param;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * @ClasssName SysMachTypeClassifyAddParam
 * @Description 添加设备分类请求类
 * <AUTHOR>
 * @Date 2020/8/25 10:50
 * @Version 1.0
 */
@Data
public class SysMachTypeClassifyAddParam implements Serializable {

    /**
     * 编码
     */
    @NotEmpty
    @Size(min = 1, max = 64, message = "编码最长为64位")
    private String code;
    /**
     * 名称
     */
    @NotEmpty
    private String name;
}
