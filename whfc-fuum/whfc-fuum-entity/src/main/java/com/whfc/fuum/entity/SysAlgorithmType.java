package com.whfc.fuum.entity;

import java.util.Date;
import lombok.Data;

/**
 * 算法类型表
 */
@Data
public class SysAlgorithmType {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 算法类型名称
     */
    private String name;

    /**
     * 算法类型代码
     */
    private Integer type;

    /**
     * 算法图标
     */
    private String iconUrl;

    /**
     * 算法封面
     */
    private String imgUrl;

    /**
     * 报警标题
     */
    private String tittle;

    /**
     * 报警描述
     */
    private String detail;

    /**
     * 启/禁用 0-禁用 1-启用
     */
    private Integer state;

    /**
     * 删除 0-未删除 1-删除
     */
    private Integer delFlag;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;
}