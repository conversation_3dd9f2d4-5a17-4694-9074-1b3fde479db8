package com.whfc.fuum.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 大屏配置表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/24
 */
@Schema(description = "大屏配置表")
@Data
public class SysBoardConfigParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 组织机构ID
     */
    @Schema(description = "组织机构ID")
    private Integer deptId;
    /**
     * 路由
     */
    @Schema(description = "路由")
    private String router;



    /**
     * 模板
     */
    @Schema(description = "模板")
    private String template;

    @Schema(description = "组件列表")
    private List<Component> componentList;

    @Data
    public static class Component implements Serializable {

        /**
         * 位置
         */
        @Schema(description = "位置")
        private String position;

        /**
         * 组件
         */
        @Schema(description = "组件")
        private String component;
    }
}