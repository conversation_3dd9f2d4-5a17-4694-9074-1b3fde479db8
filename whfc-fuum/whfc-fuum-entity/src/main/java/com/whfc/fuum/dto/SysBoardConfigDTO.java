package com.whfc.fuum.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 大屏配置表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/24
 */
@Schema(description = "大屏配置表")
@Data
public class SysBoardConfigDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Integer id;

    /**
     * 组织机构ID
     */
    @Schema(description = "组织机构ID")
    private Integer deptId;

    /**
     * 路由
     */
    @Schema(description = "路由")
    private String router;

    /**
     * 模板
     */
    @Schema(description = "模板")
    private String template;

    @Schema(description = "模板子名称")
    private String templateSubName;

    @Schema(description = "模板语言配置数据")
    private String templateLanguageData;

    @Schema(description = "模板样式配置数据")
    private String templateStyle;

    /**
     * 模板名称
     */
    @Schema(description = "模板名称")
    private String templateName;

    /**
     * 位置
     */
    @Schema(description = "位置")
    private String position;

    /**
     * 组件
     */
    @Schema(description = "组件")
    private String component;

    /**
     * 组件名称
     */
    @Schema(description = "组件名称")
    private String componentName;

    @Schema(description = "组件子名称")
    private String componentSubName;

    @Schema(description = "组件语言配置数据")
    private String componentLanguageData;

    @Schema(description = "组件样式配置数据")
    private String componentStyle;

}