package com.whfc.fuum.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringExclude;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> qzexing
 * @version : 1.0
 * @date : 2020-10-17 14:41
 */
@Schema(description = "部门用户添加参数")
@Data
public class SysDeptUserAdd implements Serializable {

    /**
     * 组织结构ID
     */
    @Schema(description = "组织结构ID")
    @NotNull
    private Integer deptId;

    /**
     * 用户名
     */
    @Schema(description = "用户名")
    @NotEmpty
    @Length(max = 40)
    private String username;

    /**
     * 密码
     */
    @Schema(description = "密码")
    @NotEmpty
    @ToStringExclude
    private String password;

    /**
     * 姓名
     */
    @Schema(description = "姓名")
    @NotEmpty
    @Size(max = 35, message = "姓名最长为35个字")
    private String nickname;

    /**
     * 区号
     */
    @Schema(description = "区号")
    private String areaCode;

    /**
     * 手机号
     */
    @Schema(description = "手机号")
    @NotEmpty
    @Length(max = 40)
    private String phone;

    /**
     * 邮箱
     */
    @Schema(description = "邮箱")
    private String email;

    /**
     * 用户类型
     */
    @Schema(description = "用户类型")
    @NotNull
    private Integer type;

    /**
     * 账号到期时间
     */
    @Schema(description = "账号到期时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date expireTime;


    /**
     * 角色ID列表
     */
    @Schema(description = "角色ID列表")
    @NotEmpty
    private List<Integer> roleIds;

    /**
     * 用户uuid
     */
    @Schema(description = "用户uuid")
    private String guid;

    /**
     * 合作单位ID
     */
    @Schema(description = "合作单位ID")
    private Integer corpId;

    /**
     * 部门ID
     */
    @Schema(description = "部门ID")
    private Integer departmentId;

    /**
     * 职位
     */
    @Schema(description = "职位")
    @Length(max = 100)
    private String position;

}
