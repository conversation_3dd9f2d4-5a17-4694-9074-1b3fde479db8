package com.whfc.fuum.param;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @Description: 添加硬件升级参数
 * @author: xugcheng
 * @version: 1.0
 * @date: 2020-12-25 15:13
 */
@Data
public class UpgradeDeviceParam implements Serializable {

    /**
     * 升级包ID
     */
    @NotNull(message = "升级包ID不能为空")
    private Integer packageId;

    /**
     * 升级硬件ID
     */
    @NotEmpty(message = "硬件ID不能为空")
    private List<Integer> deviceIdList;
}
