package com.whfc.fuum.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 大屏配置路由
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/1
 */
@Schema(description = "大屏配置路由")
@Data
public class SysBoardRouterDTO implements Serializable {
    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Integer id;

    /**
     * 路由
     */
    @Schema(description = "路由")
    private String router;

    /**
     * 路由名称
     */
    @Schema(description = "路由名称")
    private String name;

}