package com.whfc.fuum.dto;

import java.io.Serializable;

public class SysRuleDTO implements Comparable, Serializable {

    /**
     * 权限ID
     */
    private Integer id;

    /**
     * 权限名称
     */
    private String name;

    /**
     * 权限简称
     */
    private String shortName;

    /**
     * 编码
     */
    private String code;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 页面类型
     */
    private Integer pageType;

    /**
     * 路径
     */
    private String path;

    /**
     * 父权限ID
     */
    private Integer pid;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 排序
     */
    private Integer index;

    /**
     * 图标
     */
    private String iconPath;

    /**
     * 描述
     */
    private String content;

    /**
     * 权限平台
     */
    private Integer platform;

    /**
     * 权限ID
     */
    private Integer ruleId;

    /**
     * 权限名称
     */
    private String ruleName;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getShortName() {
        return shortName;
    }

    public void setShortName(String shortName) {
        this.shortName = shortName;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getPageType() {
        return pageType;
    }

    public void setPageType(Integer pageType) {
        this.pageType = pageType;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public Integer getPid() {
        return pid;
    }

    public void setPid(Integer pid) {
        this.pid = pid;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getIconPath() {
        return iconPath;
    }

    public void setIconPath(String iconPath) {
        this.iconPath = iconPath;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getPlatform() {
        return platform;
    }

    public void setPlatform(Integer platform) {
        this.platform = platform;
    }

    public Integer getRuleId() {
        return ruleId;
    }

    public void setRuleId(Integer ruleId) {
        this.ruleId = ruleId;
    }

    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    @Override
    public int compareTo(Object o) {
        SysRuleDTO ruleDTO = (SysRuleDTO) o;
        return this.id.compareTo(ruleDTO.getId());
    }
}
