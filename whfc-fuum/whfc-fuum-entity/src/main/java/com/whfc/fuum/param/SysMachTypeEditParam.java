package com.whfc.fuum.param;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * @ClasssName SysMachTypeEditParam
 * <AUTHOR>
 * @Date 2020/8/24 18:13
 * @Version 1.0
 */
@Data
public class SysMachTypeEditParam extends SysMachTypeClassifyEditParam implements Serializable {

    /**
     * 主键
     */
    @NotNull
    private Integer pid;

    /**
     * 上传图标
     */
    @Size(min = 1,max = 4,message = "图片最少上传一张最多4张")
    private List<SysMachTypeIconAddParam> icons;
}
