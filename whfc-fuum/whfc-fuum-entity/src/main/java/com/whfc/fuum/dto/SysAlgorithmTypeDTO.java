package com.whfc.fuum.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

@Getter
@Setter
@ToString
public class SysAlgorithmTypeDTO implements Serializable {

    /**
     * 算法id
     */
    private Integer id;
    /**
     * 算法类型
     */
    private Integer type;

    /**
     * 算法名称
     */
    private String name;

    /**
     * 图标
     */
    private String iconUrl;

    /**
     * 算法封面
     */
    private String imgUrl;

    /**
     * 报警标题
     */
    private String tittle;

    /**
     * 报警描述
     */
    private String detail;

    /**
     * 启用状态
     */
    private Integer state;


}