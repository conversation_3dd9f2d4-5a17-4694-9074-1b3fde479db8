package com.whfc.fuum.param;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2019/7/23 20:10
 */
@Setter
@Getter
@ToString
public class SysRoleAdd implements Serializable {

    /**
     * 角色名称
     */
    @NotNull
    private String name;
    /**
     * 组织机构ID
     */
    private Integer deptId;
    /**
     * 创建用户
     */
    private Integer createBy;
    /**
     * 详情
     */
    private String description;
}
