package com.whfc.fuum.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-07-22 16:51
 */
@Data
public class SysDeptExtDTO implements Serializable {

    /**
     * 组织机构ID
     */
    private Integer deptId;

    /**
     * 类型
     * @see com.whfc.common.enums.SysDeptType
     */
    private Integer type;

    /**
     * 名称
     */
    private String name;

    /**
     * 标题
     */
    private String title;

    private Integer status;

    private Integer level;

    private Integer sv;

    private Integer svDays;

    private String country;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 地区
     */
    private String area;

    /**
     * 地址信息
     */
    private String address;

    /**
     * 经度
     */
    private Double lng;

    /**
     * 纬度
     */
    private Double lat;

    /**
     * 扩展信息（json）
     *
     * @see com.whfc.fuum.dto.SysDeptExtInfoDTO
     */
    private String detail;

}
