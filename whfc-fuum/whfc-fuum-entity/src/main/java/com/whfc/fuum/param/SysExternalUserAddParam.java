package com.whfc.fuum.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 系统外部用户信息
 */
@Data
@Schema(description = "系统外部用户信息")
public class SysExternalUserAddParam implements Serializable {

    @Schema(description = "第三方系统平台")
    private String platform;

    @Schema(description = "外部用户列表")
    private List<ExternalUser> externalUserList;

    @Data
    public static class ExternalUser implements Serializable {

        @Schema(description = "系统用户ID")
        private Integer userId;

        @Schema(description = "系统用户账号")
        private String username;

        @Schema(description = "系统用户姓名")
        private String nickname;

        @Schema(description = "第三方用户名")
        private String extUsername;

        @Schema(description = "第三方用户密码")
        private String extPassword;

        @Schema(description = "扩展信息1")
        private String ext1;

        @Schema(description = "扩展信息2")
        private String ext2;
    }

}
