package com.whfc.fuum.entity;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

import lombok.Data;

/**
 * 大屏配置表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/24
 */
@Schema(description = "大屏配置表")
@Data
public class SysBoardConfig {
    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Integer id;

    /**
     * 组织机构ID
     */
    @Schema(description = "组织机构ID")
    private Integer deptId;


    @Schema(description = "路由ID")
    private Integer routerId;

    /**
     * 路由
     */
    @Schema(description = "路由")
    private String router;


    @Schema(description = "模板ID")
    private Integer templateId;

    /**
     * 模板
     */
    @Schema(description = "模板")
    private String template;

    /**
     * 位置
     */
    @Schema(description = "位置")
    private String position;

    @Schema(description = "组件ID")
    private Integer componentId;

    /**
     * 组件
     */
    @Schema(description = "组件")
    private String component;

    /**
     * 删除标记:0-未删除 1-已删除
     */
    @Schema(description = "删除标记:0-未删除 1-已删除")
    private Integer delFlag;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;
}