package com.whfc.fuum.enums;

/**
 * 项目等级
 *
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/6/25 16:14
 */
public enum ProjectLevel {

    COMMON(1, "一般项目"),

    MAJOR(2, "重大项目");

    private Integer value;

    private String name;

    ProjectLevel(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public static ProjectLevel parseValue(Integer value) {
        for (ProjectLevel item : ProjectLevel.values()) {
            if (item.getValue().equals(value)) {
                return item;
            }
        }
        return COMMON;
    }
}
