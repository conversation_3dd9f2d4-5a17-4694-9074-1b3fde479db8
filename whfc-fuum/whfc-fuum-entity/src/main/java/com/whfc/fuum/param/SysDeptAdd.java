package com.whfc.fuum.param;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2019/7/23 10:21
 */
@Data
public class SysDeptAdd implements Serializable {

    private Integer pid;

    @NotNull
    @Length(max = 32)
    private String name;

    @NotNull
    private Integer type;

    /**
     * 组织机构唯一ID
     */
    private String guid;
}
