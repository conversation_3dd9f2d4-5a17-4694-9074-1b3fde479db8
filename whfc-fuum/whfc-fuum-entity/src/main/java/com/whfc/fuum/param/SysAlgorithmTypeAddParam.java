package com.whfc.fuum.param;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Description:
 * @Author: likang
 * @Date: 2020-09-22 13:36
 */
@Data
public class SysAlgorithmTypeAddParam implements Serializable {


    @NotNull
    private Integer type;

    @NotEmpty
    private String name;


    @NotEmpty
    private String iconUrl;

    /**
     * 算法封面
     */
    private String imgUrl;

    /**
     * 报警标题
     */
    private String tittle;

    /**
     * 报警描述
     */
    private String detail;

}
