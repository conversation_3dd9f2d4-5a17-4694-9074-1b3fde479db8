package com.whfc.fuum.enums;

/**
 * 项目状态
 *
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2024/6/25 16:14
 */
public enum ProjectStatus {

    BUILDING(1, "在建"),

    FINISHED(2, "完工"),

    STOP(3, "停工"),

    NO_START(4, "未开工");

    private Integer value;

    private String name;

    ProjectStatus(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public static ProjectStatus parseValue(Integer value) {
        for (ProjectStatus item : ProjectStatus.values()) {
            if (item.getValue().equals(value)) {
                return item;
            }
        }
        return BUILDING;
    }
}
