package com.whfc.fuum.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 系统外部用户信息
 */
@Data
@Schema(description = "系统外部用户信息")
public class SysExternalUserDTO implements Serializable {

    @Schema(description = "主键ID")
    private Integer id;

    @Schema(description = "第三方系统平台")
    private String platform;

    @Schema(description = "系统用户ID")
    private Integer userId;

    @Schema(description = "系统用户名")
    private String username;

    @Schema(description = "系统用户姓名")
    private String nickname;

    @Schema(description = "第三方用户名")
    private String extUsername;

    @Schema(description = "第三方用户密码")
    private String extPassword;

    @Schema(description = "扩展信息1")
    private String ext1;

    @Schema(description = "扩展信息2")
    private String ext2;

    @Schema(description = "同步状态  0-未同步  1-同步中  2-同步成功 3-同步失败")
    private Integer syncState;

    @Schema(description = "同步消息")
    private String syncMsg;

}
