package com.whfc.fuum.param;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @ClasssName SysMachTypeIconAddParam
 * @Description 上传图标请求类
 * <AUTHOR>
 * @Date 2020/8/24 18:07
 * @Version 1.0
 */
@Data
public class SysMachTypeIconAddParam implements Serializable {

    /**
     * 图标地址
     */
    @NotEmpty
    private String icon;

    /**
     * 图标名称
     */
    @NotNull
    private Integer state;
}
