package com.whfc.fuum.param.open;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2020-06-22
 */
@Data
public class OpenApiTokenParam implements Serializable {
    /**
     * appKey
     */
    @NotEmpty
    private String appKey;

    /**
     * 时间戳
     */
    @NotNull
    private Long timestamp;

    /**
     * 随机字符串
     */
    @NotEmpty
    @Size(max = 16)
    private String randomStr;

    /**
     * 签名
     */
    @NotEmpty
    private String sign;
}
