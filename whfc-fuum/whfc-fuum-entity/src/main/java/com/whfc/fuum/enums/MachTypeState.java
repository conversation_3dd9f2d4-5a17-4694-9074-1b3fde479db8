package com.whfc.fuum.enums;

/**
 * @Description 图片颜色枚举
 * <AUTHOR>
 * @Date 2020/8/24 19:51
 * @Version 1.0
 */
public enum MachTypeState {


    GRAY(0, "灰色图标"),

    BLUE(1, "蓝色图标"),

    RED(2, "红色图标"),

    GREEN(3, "绿色图标");

    private Integer value;

    private String desc;

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    MachTypeState(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static MachTypeState parseValue(int value) {
        MachTypeState[] types = MachTypeState.values();
        for (MachTypeState type : types) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }
}
