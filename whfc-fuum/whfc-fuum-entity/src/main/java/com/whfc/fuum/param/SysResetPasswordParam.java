package com.whfc.fuum.param;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.whfc.common.constant.Language;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.builder.ToStringExclude;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Setter
@Getter
@ToString
public class SysResetPasswordParam implements Serializable {

	@NotEmpty
	@ToStringExclude
	private String password;
	@NotNull
	private Integer userId;

	@JsonProperty(defaultValue = Language.zh_CN)
	private String language;
}
