package com.whfc.fuum.param;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class SysDepartmentAdd implements Serializable {

    /**
     * 项目ID
     */
    @NotNull
    private Integer deptId;

    /**
     * 部门名称
     */
    @NotEmpty
    @Length(max = 32)
    private String name;

    /**
     * 部门编号
     */
    @Length(max = 32)
    private String code;
}