package com.whfc.fuum.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 项目详细信息
 */
@Data
public class AppProjectDetailDTO implements Serializable {

    /**
     * 组织机构ID
     */
    private Integer deptId;

    /**
     * 项目名称
     */
    private String name;

    /**
     * 项目简称
     */
    private String title;

    /**
     * 简介
     */
    private String shortInfo;

    /**
     * 项目编码
     */
    private String projectCode;

    /**
     * 合同金额
     */
    private Integer money;

    /**
     * 货币
     */
    private String currency;

    /**
     * 施工周期(月)
     */
    private Integer period;

    /**
     * 开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startDate;

    /**
     * 截止日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date completeDate;

    /**
     * 施工天数
     */
    private Integer constDays;

    /**
     * 建造许可证
     */
    private String builderLicenseNum;

    /**
     * 社会信用代码
     */
    private String socialCode;

    /**
     * 建造单位
     */
    private String constructionUnit;

    /**
     * 设计单位
     */
    private String designUnit;

    /**
     * 监理单位
     */
    private String supervisionUnit;

    /**
     * 勘察单位
     */
    private String explorationUnit;

    /**
     * 施工单位
     */
    private String contractor;

    /**
     * 监测单位
     */
    private String detectionUnit;

    /**
     * 质量看护单位
     */
    private String qualityCareUnit;

    /**
     * 项目状态
     */
    private Integer status;

    /**
     * 项目级别
     */
    private Integer level;

    /**
     * 进度偏差
     */
    private Integer sv;

    /**
     * 进度偏差天数
     */
    private Integer svDays;

    /**
     * 国家
     */
    private String country;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区域
     */
    private String area;

    /**
     * 经度
     */
    private Double lng;

    /**
     * 纬度
     */
    private Double lat;

    /**
     * 地址
     */
    private String address;

    /**
     * 宣传图片
     */
    private List<AppProjectImgDTO> imgList;

    /**
     * 岗位人员
     */
    private List<AppProjectPostDTO> postList;

    /**
     * 项目单位
     */
    private List<AppProjectUnitDTO> unitList;

    /**
     * 合作单位数量统计
     */
    private List<AppStatNumDTO> corpNumList;

    /**
     * 安全施工天数
     */
    private Integer safeDays;

    /**
     * 大屏背景图片
     */
    private String boardBgImg;
}
