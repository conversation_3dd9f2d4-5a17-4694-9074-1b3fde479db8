package com.whfc.fuum.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2019/7/23 15:07
 */
@Setter
@Getter
@ToString
public class SysUserEdit implements Serializable {

    /**
     * 用户ID
     */
    @NotNull
    private Integer userId;

    /**
     * 用户名
     */
    @NotEmpty
    private String username;

    /**
     * 姓名
     */
    @NotEmpty
    private String nickname;


    /**
     * 区号
     */
    private String areaCode;

    /**
     * 手机号
     */
    @NotEmpty
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 用户类型
     */
    @NotNull
    private Integer type;

    /**
     * 账号到期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date expireTime;

}
