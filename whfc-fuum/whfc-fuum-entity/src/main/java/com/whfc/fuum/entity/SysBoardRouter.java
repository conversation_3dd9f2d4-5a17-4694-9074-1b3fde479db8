package com.whfc.fuum.entity;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

import lombok.Data;

/**
 * 大屏配置路由表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/1
 */
@Schema(description = "大屏配置路由表")
@Data
public class SysBoardRouter {
    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Integer id;

    /**
     * 路由
     */
    @Schema(description = "路由")
    private String router;

    /**
     * 路由名称
     */
    @Schema(description = "路由名称")
    private String name;

    /**
     * 删除标记 0-未删除 1-已删除
     */
    @Schema(description = "删除标记 0-未删除 1-已删除")
    private Integer delFlag;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;
}