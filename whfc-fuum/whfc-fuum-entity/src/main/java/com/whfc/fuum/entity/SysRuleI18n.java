package com.whfc.fuum.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 菜单国际化关联表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/6 11:41
 */
@Data
@Schema(description = "菜单国际化关联表")
public class SysRuleI18n implements Serializable {
    /**
     * 主键ID
     */
    @Schema(description =  "主键ID")
    private Long id;

    /**
     * 菜单ID
     */
    @Schema(description =  "菜单ID")
    private Integer ruleId;

    /**
     * 菜单权限编码
     */
    @ExcelProperty(value = "code")
    @Schema(description =  "菜单权限编码")
    private String code;

    /**
     * 语言
     */
    @Schema(description =  "语言")
    private String language;

    /**
     * 国际化后菜单名称
     */
    @ExcelProperty(value = "name")
    @Schema(description =  "国际化后菜单名称")
    private String name;

    /**
     * 国际化后菜单简称
     */
    @ExcelProperty(value = "shortName")
    @Schema(description =  "国际化后菜单简称")
    private String shortName;

    /**
     * 国际化后菜单描述
     */
    @ExcelProperty(value = "content")
    @Schema(description =  "国际化后菜单描述")
    private String content;


    @ExcelProperty(value = "platform")
    @Schema(description =  "平台")
    private Integer platform;

    /**
     * 删除标记:0-未删除 1-已删除
     */
    @Schema(description =  "删除标记:0-未删除 1-已删除")
    private Integer delFlag;

    /**
     * 更新时间
     */
    @Schema(description =  "更新时间")
    private Date updateTime;

    /**
     * 创建时间
     */
    @Schema(description =  "创建时间")
    private Date createTime;

    private static final long serialVersionUID = 1L;

}