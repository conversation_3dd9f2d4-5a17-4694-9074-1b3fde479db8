package com.whfc.fuum.service;

import com.whfc.common.enums.AppType;
import com.whfc.common.exception.BizException;
import com.whfc.common.result.PageData;
import com.whfc.common.weixin.bean.OAuthUserInfo;
import com.whfc.fuum.dto.WxUserDTO;
import com.whfc.fuum.entity.WxUser;

import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2019/7/22 11:23
 */
public interface WxUserService {

    /**
     * 微信用户列表
     *
     * @param pageNum   页码
     * @param pageSize  每页数量
     * @param keyword   搜索关键字
     * @param appType   小程序类型
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 小程序用户列表
     * @throws BizException 业务异常
     */
    PageData<WxUserDTO> wxmpUserList(Integer pageNum, Integer pageSize, String keyword, Integer appType, Date startDate, Date endDate) throws BizException;

    /**
     * 查询微信用户
     *
     * @param openId openId
     * @return 小程序用户
     * @throws BizException 业务异常
     */
    WxUser getUserByOpenId(String openId) throws BizException;

    /**
     * 查询微信用户
     *
     * @param phone 手机号
     * @return 小程序用户列表
     * @throws BizException 业务异常
     */
    List<WxUser> getUserByPhone(String phone) throws BizException;

    /**
     * 保存小程序用户
     *
     * @param appType    小程序模块类型
     * @param openId     openId
     * @param unionId    unionId
     * @param sessionKey sessionKey
     * @return 小程序用户
     * @throws BizException 业务异常
     */
    WxUser saveUser(AppType appType, String openId, String unionId, String sessionKey) throws BizException;

    /**
     * 保存OAUTH用户信息
     *
     * @param appType  小程序类型
     * @param userInfo 用户信息
     * @return 小程序用户
     * @throws BizException 业务异常
     */
    WxUser saveUser(AppType appType, OAuthUserInfo userInfo) throws BizException;

    /**
     * 更新用户信息
     *
     * @param wxUser        小程序用户
     * @param encryptedData 加密数据
     * @param iv            iv
     * @return 小程序用户
     * @throws BizException 业务异常
     */
    WxUser updateUserInfo(WxUser wxUser, String encryptedData, String iv) throws BizException;

    /**
     * 查询用户信息
     *
     * @param wxUserId 微信用户ID
     * @return 小程序用户
     * @throws BizException 业务异常
     */
    WxUser getUserById(Integer wxUserId) throws BizException;

    /**
     * 修改用户姓名
     *
     * @param loginUser 小程序用户
     * @param name      姓名
     * @throws BizException 业务异常
     */
    void updateUserName(WxUser loginUser, String name) throws BizException;

    /**
     * 修改用户手机号
     *
     * @param loginUser 小程序用户
     * @param phone     手机号
     * @throws BizException 业务异常
     */
    void updateUserPhone(WxUser loginUser, String phone) throws BizException;

    /**
     * 更新微信信息
     *
     * @param loginUser 小程序用户
     * @throws BizException 业务异常
     */
    void updateUser(WxUser loginUser) throws BizException;

    /**
     * 清空手机号
     *
     * @param wxUserId
     * @throws BizException
     */
    void cleanUserPhone(Integer wxUserId) throws BizException;
}
