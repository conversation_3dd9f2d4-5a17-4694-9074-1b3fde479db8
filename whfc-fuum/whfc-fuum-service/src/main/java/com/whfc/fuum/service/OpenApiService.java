package com.whfc.fuum.service;

import com.whfc.common.exception.BizException;
import com.whfc.common.result.PageData;
import com.whfc.fuum.dto.SysRuleDTO;
import com.whfc.fuum.dto.open.AccessCodeDTO;
import com.whfc.fuum.dto.open.AccessTokenDTO;
import com.whfc.fuum.dto.open.OpenApiClientDTO;
import com.whfc.fuum.dto.open.OpenApiLicenseDTO;
import com.whfc.fuum.param.open.AccessTokenParam;
import com.whfc.fuum.param.open.OpenApiRuleEditParam;
import com.whfc.fuum.param.open.OpenApiTokenParam;

import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2020-12-29 9:35
 */
public interface OpenApiService {

    /**
     * 获取accessToken(旧GIS)
     *
     * @param accessTokenParam token信息
     * @return accessToken
     * @throws BizException 业务异常
     */
    @Deprecated
    AccessTokenDTO getAccessToken(AccessTokenParam accessTokenParam) throws BizException;

    /**
     * 获取开放平台token(v1)
     *
     * @param param token信息
     * @return token
     * @throws BizException 业务异常
     */
    AccessTokenDTO getOpenApiToken(OpenApiTokenParam param) throws BizException;

    /**
     * 验证开放平台token
     *
     * @param accessToken token
     * @return token信息
     * @throws BizException 业务异常
     */
    OpenApiClientDTO validateOpenApiToken(String accessToken) throws BizException;

    /**
     * 获取开放接口授权信息列表
     *
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @param keyword  搜索关键字
     * @return 开放接口授权信息列表
     * @throws BizException 业务异常
     */
    PageData<OpenApiLicenseDTO> getOpenApiLicenseList(Integer pageNum, Integer pageSize, String keyword) throws BizException;


    /**
     * 获取开放接口授权信息列表
     *
     * @param deptId 组织机构ID
     * @return 开放接口授权信息列表
     * @throws BizException 业务异常
     */
    List<OpenApiLicenseDTO> getOpenApiLicenseList(Integer deptId) throws BizException;

    /**
     * 获取开放授权-所有权限信息
     *
     * @return 所有权限信息
     * @throws BizException 业务异常
     */
    List<SysRuleDTO> getOpenApiRules(String language) throws BizException;

    /**
     * 获取开放授权-应用的授权信息
     *
     * @param appKey appKey
     * @return 应用的授权信息
     * @throws BizException 业务异常
     */
    List<SysRuleDTO> getOpenApiRules(String appKey,String language) throws BizException;

    /**
     * 获取开放授权-应用的授权信息
     *
     * @param deptId 组织机构ID
     * @return 应用的授权信息
     * @throws BizException 业务异常
     */
    List<SysRuleDTO> getOpenApiRulesByDeptId(String language,Integer deptId) throws BizException;

    /**
     * 编辑开放接口授权-权限信息
     *
     * @param param 修改参数
     * @throws BizException 业务异常
     */
    void editOpenApiRules(OpenApiRuleEditParam param) throws BizException;

    /**
     * 启用开放授权
     *
     * @param appKey appKey
     * @throws BizException 业务异常
     */
    void enableOpenApiLicense(String appKey) throws BizException;

    /**
     * 禁用开放授权
     *
     * @param appKey appKey
     * @throws BizException 业务异常
     */
    void disableOpenApiLicense(String appKey) throws BizException;

    /**
     * 查询开发平台配置
     *
     * @param appKey appKey
     * @return 开发平台配置
     * @throws BizException 业务异常
     */
    OpenApiLicenseDTO getOpenApiLicense(String appKey) throws BizException;

    /**
     * 配置开放平台授权
     *
     * @param deptId 组织机构ID
     * @param list   配置列表
     * @throws BizException 业务异常
     */
    void configureOpenApiLicense(Integer deptId, List<OpenApiLicenseDTO> list) throws BizException;
}
