package com.whfc.fuum.service;

import com.whfc.common.exception.BizException;
import com.whfc.common.result.PageData;
import com.whfc.fuum.dto.SysExternalLinkDTO;
import com.whfc.fuum.dto.SysExternalUserDTO;
import com.whfc.fuum.param.SysExternalUserAddParam;
import com.whfc.fuum.param.SysExternalUserEditParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/2
 */
public interface SysExternalLinkService {

    /**
     * 获取外链地址
     *
     * @param deptId
     * @param code
     * @param userId
     * @return
     */
    SysExternalLinkDTO getLinkUrl(Integer deptId, String code, Integer userId) throws BizException;


    /**
     * 获取外链用户
     *
     * @param deptId
     * @param code
     * @param userId
     * @return
     * @throws BizException
     */
    String getLinkUser(Integer deptId, String code, Integer userId) throws BizException;

    /**
     * 获取外链平台
     *
     * @return
     */
    List<SysExternalUserDTO> getLinkPlatform() throws BizException;

    /**
     * 获取外链用户列表
     *
     * @param pageNum
     * @param pageSize
     * @param platform
     * @param keyword
     * @return
     */
    PageData<SysExternalUserDTO> linkUserList(Integer pageNum, Integer pageSize, String platform, String keyword) throws BizException;

    /**
     * 添加外链用户
     *
     * @param param
     */
    void linkUserAdd(SysExternalUserAddParam param) throws BizException;

    /**
     * 编辑外链用户
     *
     * @param param
     */
    void linkUserEdit(SysExternalUserEditParam param) throws BizException;

    /**
     * 同步外链用户
     *
     * @param id
     */
    void linkUserSync(Integer id) throws BizException;

    /**
     * 删除外链用户
     *
     * @param id
     */
    void linkUserDel(Integer id) throws BizException;
}
