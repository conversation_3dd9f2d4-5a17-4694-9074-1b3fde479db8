package com.whfc.fuum.service;

import com.whfc.common.exception.BizException;
import com.whfc.fuum.dto.SysDeptDTO;
import com.whfc.fuum.dto.gscx.GscxDeptDTO;
import com.whfc.fuum.param.gscx.GscxDeptAddParam;
import com.whfc.fuum.param.gscx.GscxDeptEditParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-03-11 10:12
 */
public interface GscxSysService {


    /**
     * 查询用户可以管理的组织机构树
     *
     * @param userId  用户ID
     * @param deptId
     * @param keyword
     * @return
     * @throws BizException 业务异常
     */
    List<SysDeptDTO> getDeptTree(Integer userId, Integer deptId, String keyword) throws BizException;

    /**
     * 添加组织机构
     *
     * @param param 参数
     * @throws BizException 业务异常
     */
    void addDept(GscxDeptAddParam param) throws BizException;

    /**
     * 修改组织机构
     *
     * @param param 参数
     * @throws BizException 业务异常
     */
    void editDept(GscxDeptEditParam param) throws BizException;

    /**
     * 获取组织机构详情
     *
     * @param deptId 组织机构ID
     * @return 组织机构详情
     * @throws BizException 业务异常
     */
    GscxDeptDTO getDeptDetail(Integer deptId) throws BizException;

    /**
     * 删除组织机构
     *
     * @param deptId 组织机构ID
     * @throws BizException 业务异常
     */
    void delDept(Integer deptId) throws BizException;

}
