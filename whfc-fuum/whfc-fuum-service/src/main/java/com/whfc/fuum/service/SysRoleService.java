package com.whfc.fuum.service;


import com.whfc.common.exception.BizException;
import com.whfc.fuum.dto.SysRoleDTO;
import com.whfc.fuum.dto.SysRoleDetailDTO;
import com.whfc.fuum.dto.SysRuleDTO;
import com.whfc.fuum.dto.SysUserDTO;
import com.whfc.fuum.param.SysRoleAdd;
import com.whfc.fuum.param.SysRoleEdit;
import com.whfc.fuum.param.SysRoleRuleEdit;

import java.util.List;

/**
 * 后台操作角色的服务接口
 *
 * <AUTHOR>
 * @Description:
 * @date 2019年7月22日
 */
public interface SysRoleService {

    /**
     * 获取角色列表
     *
     * @param userId  用户id
     * @param deptId  组织机构ID
     * @param keyword 搜索关键字
     * @return 角色列表
     * @throws BizException 业务异常
     */
    List<SysRoleDTO> list(Integer userId, Integer deptId, String keyword) throws BizException;

    /**
     * 增加角色
     *
     * @param request 请求参数
     * @throws BizException 业务异常
     */
    void add(SysRoleAdd request) throws BizException;

    /**
     * 删除角色
     *
     * @param roleId 角色ID
     * @throws BizException 业务异常
     */
    void del(Integer roleId) throws BizException;

    /**
     * 编辑角色
     *
     * @param request 请求参数
     * @throws BizException 业务异常
     */
    void edit(SysRoleEdit request) throws BizException;

    /**
     * 获取角色的详细信息
     *
     * @param roleId   角色ID
     * @param platform 平台
     * @return 角色详情
     * @throws BizException 业务异常
     */
    SysRoleDetailDTO getRoleDetailById(Integer roleId, String language, Integer platform) throws BizException;

    /**
     * 查询角色关联用户
     *
     * @param roleId 角色ID
     * @return 用户列表
     * @throws BizException 业务异常
     */
    List<SysUserDTO> getRoleUserList(Integer roleId) throws BizException;


    /**
     * 查询角色关联权限
     *
     * @param roleId   角色ID
     * @param platform 权限平台
     * @return 权限列表
     * @throws BizException 业务异常
     */
    List<SysRuleDTO> getRoleRuleList(Integer roleId,String language, Integer platform) throws BizException;

    /**
     * 编辑角色权限
     *
     * @param roleRuleEdit 请求参数
     * @throws BizException 业务异常
     */
    void editRoleRule(SysRoleRuleEdit roleRuleEdit) throws BizException;

    /**
     * 根据角色ID和组织机构ID查找用户
     *
     * @param deptId  组织机构ID
     * @param roleIds 角色ID
     * @return 用户
     * @throws BizException 业务异常
     */
    List<SysUserDTO> selectUserByRoleIdsAndDeptId(Integer deptId, List<Integer> roleIds) throws BizException;
}
