package com.whfc.fuum.service;

import com.whfc.common.exception.BizException;
import com.whfc.fuum.dto.SysRuleDTO;
import com.whfc.fuum.dto.SysRuleI18nDTO;
import com.whfc.fuum.entity.SysUser;
import com.whfc.fuum.param.SysRuleAdd;
import com.whfc.fuum.param.SysRuleEdit;
import com.whfc.fuum.param.SysRuleI18nParam;

import java.util.List;

/**
 * @Description:
 * @author: xugcheng
 * @version: 1.0
 * @date: 2019/7/23 20:56
 */
public interface SysRuleService {

    /**
     * 查询权限列表
     *
     * @param user     用户
     * @param language 语言
     * @param useI18n  是否使用国际化
     * @param platform 平台
     * @return 权限列表
     * @throws BizException 业务异常
     */
    List<SysRuleDTO> getRuleList(SysUser user, String language, Integer useI18n, Integer platform) throws BizException;

    /**
     * 查询子孙权限列表
     *
     * @param ruleId 权限ID
     * @return 子孙权限列表
     * @throws BizException 业务异常
     */
    List<SysRuleDTO> getDescendantList(String language, Integer ruleId) throws BizException;

    /**
     * 添加权限
     *
     * @param param 请求参数
     * @throws BizException 业务异常
     */
    void addRule(SysRuleAdd param) throws BizException;

    /**
     * 修改权限
     *
     * @param param 请求参数
     * @throws BizException 业务异常
     */
    void editRule(SysRuleEdit param) throws BizException;

    /**
     * 启用权限
     *
     * @param ruleId 权限ID
     * @throws BizException 业务异常
     */
    void enableRule(Integer ruleId) throws BizException;

    /**
     * 禁用权限
     *
     * @param ruleId 权限ID
     * @throws BizException 业务异常
     */
    void disableRule(Integer ruleId) throws BizException;

    /**
     * 删除权限
     *
     * @param ruleId 权限ID
     * @throws BizException 业务异常
     */
    void delRule(Integer ruleId) throws BizException;

    /**
     * 根据权限Code查找角色ID
     *
     * @param ruleCode 权限编码
     * @return 角色ID列表
     * @throws BizException 业务异常
     */
    List<Integer> getRoleIdsByRuleCode(String ruleCode) throws BizException;

    /**
     * 更新权限国际化信息
     *
     * @param param
     */
    void updateI18n(SysRuleI18nParam param);

    /**
     * 查询权限国际化信息
     *
     * @param ruleId
     * @return
     */
    List<SysRuleI18nDTO> i18nDetail(Integer ruleId);
}
