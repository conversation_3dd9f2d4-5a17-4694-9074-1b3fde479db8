package com.whfc.fuum.service;


import com.whfc.common.exception.BizException;
import com.whfc.fuum.dto.WxInviteCodeDTO;
import com.whfc.fuum.dto.WxInviteDTO;
import com.whfc.fuum.param.WxInviteUserJoinParam;

/**
 * @ClasssName AppInviteService
 * @Description 用户邀请相关服务
 * <AUTHOR>
 * @Date 2020/12/25 15:23
 * @Version 1.0
 */
public interface AppInviteService {

    /**
     * 获取项目的邀请码
     *
     * @param phone
     * @param deptId
     * @return
     * @throws BizException
     */
    WxInviteCodeDTO getCode(String phone, Integer deptId) throws BizException;

    /**
     * 根据邀请码获取项目信息
     *
     * @param code
     * @param phone
     * @return
     * @throws BizException
     */
    WxInviteDTO getInviteInfo(String phone, String code) throws BizException;

    /**
     * 通过邀请码进入项目
     *
     * @param phone
     * @param nickName
     * @param request
     * @throws BizException
     */
    void inviteUserJoin(String phone, String nickName, WxInviteUserJoinParam request) throws BizException;
}
