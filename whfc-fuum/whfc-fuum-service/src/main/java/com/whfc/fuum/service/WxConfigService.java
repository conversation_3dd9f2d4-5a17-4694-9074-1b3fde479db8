package com.whfc.fuum.service;

import com.whfc.common.exception.BizException;
import com.whfc.fuum.dto.WxConfigDTO;

/**
 * @Description: 微信配置相关
 * @author: xugcheng
 * @version: 1.0
 * @date: 2020-12-15 10:50
 */
public interface WxConfigService {

    /**
     * 获取微信配置
     *
     * @param appType 应用名称
     * @param refresh 是否刷新
     * @return
     */
    WxConfigDTO getWxConfig(String appType, boolean refresh) throws BizException;

    /**
     * 刷新微信token
     *
     * @param appType 应用名称
     */
    void refreshAccessToken(String appType) throws BizException;

    /**
     * 获取微信token
     *
     * @param appType 应用名称
     * @return
     */
    String getAccessToken(String appType) throws BizException;
}
