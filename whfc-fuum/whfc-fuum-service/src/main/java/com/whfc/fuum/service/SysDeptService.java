package com.whfc.fuum.service;


import com.whfc.common.enums.SysRulePlatform;
import com.whfc.common.exception.BizException;
import com.whfc.common.result.PageData;
import com.whfc.fuum.dto.SysDepartmentDTO;
import com.whfc.fuum.dto.SysDeptDTO;
import com.whfc.fuum.dto.SysDeptExtraDTO;
import com.whfc.fuum.dto.SysUserDTO;
import com.whfc.fuum.entity.SysDeptUser;
import com.whfc.fuum.entity.SysUser;
import com.whfc.fuum.param.*;

import java.util.List;
import java.util.Map;

/**
 * @Description: 组织机构服务
 * @author: xugcheng
 * @version: 1.0
 * @date: 2019/10/15 11:58
 */
public interface SysDeptService {

    /**
     * 获取机构信息
     *
     * @param deptId 组织机构ID
     * @return 机构信息
     * @throws BizException 业务异常
     */
    SysDeptDTO getSysDeptDTO(Integer deptId) throws BizException;

    /**
     * 获取机构信息
     *
     * @param deptId 组织机构ID
     * @return 组织机构信息
     * @throws BizException 业务异常
     */
    SysDeptDTO getDeptDTOById(Integer deptId) throws BizException;

    /**
     * 获取机构名称
     *
     * @param deptId 组织机构ID
     * @return 机构名称
     * @throws BizException 业务异常
     */
    String getDeptName(Integer deptId) throws BizException;

    /**
     * 批量查找组织机构名称
     *
     * @param deptIds
     * @return
     * @throws BizException
     */
    Map<Integer, String> getDeptNameMap(List<Integer> deptIds) throws BizException;

    /**
     * 由deptId查询deptName
     *
     * @param deptId 组织机构ID
     * @return 组织机构名称
     * @throws BizException 业务异常
     */
    String getDeptNameById(Integer deptId) throws BizException;

    /**
     * 获取所有项目
     *
     * @return 所有项目
     * @throws BizException 业务异常
     */
    List<SysDeptDTO> getAllProjectList() throws BizException;

    /**
     * 查询用户可以管理的组织机构树
     *
     * @param sysUser 用户
     * @param deptId  组织机构ID
     * @param keyword 搜索关键字
     * @return
     * @throws BizException 业务异常
     */
    List<SysDeptDTO> getDeptTree(SysUser sysUser, Integer deptId, String keyword) throws BizException;

    /**
     * 根据组织机构获取用户可以查看的组织机构列表
     *
     * @param sysUser  用户
     * @param deptId   组织机构ID
     * @param ruleCode 权限编码
     * @param platform 权限平台
     * @return 组织机构列表
     * @throws BizException 业务异常
     */
    List<SysDeptDTO> getDeptListByRuleCode(SysUser sysUser, Integer deptId, String ruleCode, SysRulePlatform platform) throws BizException;

    /**
     * 组织结构列表页
     *
     * @param request 查询组织机构请求参数对象
     * @return 组织结构列表
     * @throws BizException 业务异常
     */
    PageData<SysDeptDTO> list(AppDeptListParam request) throws BizException;

    /**
     * 添加组织机构
     *
     * @param deptAdd 添加组织机构请求参数对象
     * @return
     * @throws BizException 业务异常
     */
    SysDeptDTO addDept(SysDeptAdd deptAdd) throws BizException;

    /**
     * 编辑组织机构
     *
     * @param deptEdit 编辑组织机构请求参数对象
     * @throws BizException 业务异常
     */
    void editDept(SysDeptEdit deptEdit) throws BizException;

    /**
     * 删除机构(逻辑删除)
     *
     * @param deptId 组织机构ID
     * @throws BizException 业务异常
     */
    void delDept(Integer deptId) throws BizException;

    /**
     * 组织机构位置移动
     *
     * @param request 移动组织机构位置参数
     * @throws BizException 业务异常
     */
    void indexMove(SysDeptMoveIndex request);

    /**
     * 获取父节点
     *
     * @param deptId
     * @return
     * @throws BizException
     */
    List<Integer> getParentIdList(Integer deptId) throws BizException;

    /**
     * 返回所有子孙节点
     *
     * @param deptId 组织机构ID
     * @return 所有子孙节点
     * @throws BizException 业务异常
     */
    List<Integer> getDescendantDeptIdList(Integer deptId) throws BizException;

    /**
     * 查询所有子孙节点
     *
     * @param deptId  组织机构ID
     * @param keyword 搜索关键字
     * @return
     * @throws BizException 业务异常
     */
    List<SysDeptDTO> getDescendantDeptListById(Integer deptId, String keyword) throws BizException;

    /**
     * 查询所有子孙节点
     *
     * @param deptId 组织机构ID
     * @return 所有当前组织机构子节点
     * @throws BizException 业务异常
     */
    List<SysDeptDTO> getDescendantDeptListById(Integer deptId) throws BizException;

    /**
     * 查询用户关联的项目
     *
     * @param userId 用户ID
     * @return 项目列表
     * @throws BizException 业务异常
     */
    List<SysDeptDTO> getProjectListByUserId(Integer userId) throws BizException;

    /**
     * 查询用户绑定组织机构
     *
     * @param userId 用户ID
     * @param deptId 组织机构ID
     * @return 用户绑定的组织机构
     * @throws BizException 业务异常
     */
    SysDeptUser getDeptUserByUserId(Integer userId, Integer deptId) throws BizException;

    /**
     * 导入已有用户
     *
     * @param request 组织机构导入用户请求参数
     * @throws BizException 业务异常
     */
    void importUser(SysDeptUserImport request) throws BizException;

    /**
     * 移除用户
     *
     * @param deptId 组织机构ID
     * @param userId 用户ID
     * @throws BizException 业务异常
     */
    void removeUser(Integer deptId, Integer userId) throws BizException;

    /**
     * 获取可以导入的人员列表
     *
     * @param sysUser  用户
     * @param deptId   组织机构ID
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @param keyword  搜索关键字
     * @return 人员列表
     * @throws BizException 业务异常
     */
    PageData<SysUserDTO> getImportUserList(SysUser sysUser, Integer deptId, Integer pageNum, Integer pageSize, String keyword) throws BizException;

    /**
     * 根据父节点ID和GUID 查找组织机构
     *
     * @param pid  父节点ID
     * @param guid guid
     * @return 组织机构
     * @throws BizException 业务异常
     */
    SysDeptDTO getDeptByPidAndGuid(Integer pid, String guid) throws BizException;

    /**
     * 根据guid查找组织机构
     *
     * @param guid guid
     * @return 组织机构
     * @throws BizException 业务异常
     */
    SysDeptDTO getDeptByGuid(String guid) throws BizException;

    /**
     * 根据GUID和用户 查找组织机构ID
     *
     * @param guid     guid
     * @param userId   用户ID
     * @return 组织机构ID
     * @throws BizException 业务异常
     */
    Integer getDeptByGuidAndUser(String guid, Integer userId) throws BizException;

    /**
     * 根据用户查找用户的组织机构ID列表
     *
     * @param userId   用户ID
     * @return 组织机构ID列表
     * @throws BizException 业务异常
     */
    List<Integer> getDeptIdListByUser(Integer userId) throws BizException;

    /**
     * 获取组织机构扩展信息
     *
     * @param deptId 组织机构ID
     * @return 组织机构扩展信息
     * @throws BizException 业务异常
     */
    SysDeptExtraDTO getDeptExtra(Integer deptId) throws BizException;

    /**
     * 设置组织机构扩展信息
     *
     * @param param 参数
     * @throws BizException 业务异常
     */
    void setDeptExtra(SysDeptExtraParam param) throws BizException;

    /**************************部门**************************/

    /**
     * 部门列表
     *
     * @param deptId
     * @return
     * @throws BizException
     */
    List<SysDepartmentDTO> departmentList(Integer deptId) throws BizException;

    /**
     * 部门列表
     *
     * @param deptId
     * @param keyword
     * @param pageNum
     * @param pageSize
     * @return
     * @throws BizException
     */
    PageData<SysDepartmentDTO> departmentList(Integer deptId, String keyword, Integer pageNum, Integer pageSize) throws BizException;

    /**
     * 新增部门
     *
     * @param param
     * @throws BizException
     */
    void addDepartment(SysDepartmentAdd param) throws BizException;

    /**
     * 编辑部门
     *
     * @param param
     * @throws BizException
     */
    void editDepartment(SysDepartmentEdit param) throws BizException;

    /**
     * 删除部门
     *
     * @param departmentId
     * @throws BizException
     */
    void delDepartment(Integer departmentId) throws BizException;
}
