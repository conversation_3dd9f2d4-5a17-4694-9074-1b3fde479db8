package com.whfc.fuum.service;

import com.whfc.common.exception.BizException;
import com.whfc.common.result.PageData;
import com.whfc.fuum.dto.SysAlgorithmTypeDTO;
import com.whfc.fuum.param.SysAlgorithmTypeAddParam;
import com.whfc.fuum.param.SysAlgorithmTypeEditParam;

import java.util.List;

/**
 * @Description: 系统配置算法服务
 * @author: likang
 * @version: 1.0
 * @date: 2020/9/22 11:37
 */
public interface SysAlgorithmService {


    /**
     * 新增算法基础字典列表
     *
     * @param param 清除参数
     * @throws BizException 业务异常
     */
    void addAlgorithmType(SysAlgorithmTypeAddParam param) throws BizException;

    /**
     * 编辑算法基础字典
     *
     * @param param 请求参数
     * @throws BizException 业务异常
     */
    void editAlgorithmType(SysAlgorithmTypeEditParam param) throws BizException;


    /**
     * 启用/禁用算法基础字典
     *
     * @param id    算法类型ID
     * @param state 启用/禁用参数
     * @throws BizException 业务异常
     */
    void modifyAlgorithmState(Integer id, Integer state) throws BizException;

    /**
     * 逻辑删除算法基础字典
     *
     * @param id 算法类型ID
     * @throws BizException 业务异常
     */
    void deleteAlgorithmType(Integer id) throws BizException;


    /**
     * 获取算法基础字典列表
     *
     * @param keyword  搜索关键字
     * @param state    启用/禁用参数
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @return 算法类型列表
     * @throws BizException 业务异常
     */
    PageData<SysAlgorithmTypeDTO> getAlgorithmTypeList(String keyword, Integer state, Integer pageNum, Integer pageSize) throws BizException;

    /**
     * 获取算法基础字典列表
     *
     * @param keyword 搜索关键字
     * @param state   启用/禁用参数
     * @return 算法类型列表
     * @throws BizException 业务异常
     */
    List<SysAlgorithmTypeDTO> getAlgorithmTypeList(String keyword, Integer state) throws BizException;


    /**
     * 获取已配置的算法类型
     *
     * @param types 算法类型
     * @return 算法类型
     * @throws BizException 业务异常
     */
    List<SysAlgorithmTypeDTO> getAlgorithmInTypes(List<Integer> types) throws BizException;

    /**
     * 获取已配置的算法类型
     *
     * @param type 算法类型
     * @return 算法类型
     * @throws BizException 业务异常
     */
    SysAlgorithmTypeDTO getAlgorithmInType(Integer type) throws BizException;

    /**
     * 获取未配置的算法类型
     *
     * @param types 算法类型
     * @return 算法类型
     * @throws BizException 业务异常
     */
    List<SysAlgorithmTypeDTO> getAlgorithmNotInTypes(List<Integer> types) throws BizException;


}
