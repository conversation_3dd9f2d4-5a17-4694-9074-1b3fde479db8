package com.whfc.fuum.service;


import com.whfc.common.exception.BizException;
import com.whfc.common.result.PageData;
import com.whfc.entity.dto.msg.AppMsgToUserDTO;
import com.whfc.fuum.dto.SysDeptRoleDTO;
import com.whfc.fuum.dto.SysRuleDTO;
import com.whfc.fuum.dto.SysUserDTO;
import com.whfc.fuum.entity.SysUser;
import com.whfc.fuum.entity.WxUser;
import com.whfc.fuum.param.*;

import java.util.List;
import java.util.Map;

/**
 * @Description: 用户服务
 * @author: xugcheng
 * @version: 1.0
 * @date: 2019/10/14 17:52
 */
public interface SysUserService {

    /**
     * 根据条件获取用户列表
     *
     * @param loginUser    系统用户
     * @param sysUserParam 请求参数
     * @return 用户列表
     * @throws BizException 业务异常
     */
    PageData<SysUserDTO> list(SysUser loginUser, SysUserListParam sysUserParam) throws BizException;

    /**
     * 根据用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户信息
     * @throws BizException 业务异常
     */
    SysUserDTO queryUserById(Integer userId) throws BizException;

    /**
     * 组织机构用户信息
     *
     * @param userId
     * @param deptId
     * @return
     * @throws BizException
     */
    SysUserDTO deptUserDetail(Integer userId, Integer deptId) throws BizException;

    /**
     * 组织机构添加用户
     *
     * @param deptUserAdd 请求参数
     * @throws BizException 业务异常
     */
    void addDeptUser(SysDeptUserAdd deptUserAdd) throws BizException;

    /**
     * 修改组织机构用户
     *
     * @param deptUserEdit 请求参数
     * @throws BizException 业务异常
     */
    void editDeptUser(SysDeptUserEdit deptUserEdit) throws BizException;

    /**
     * 删除用户
     *
     * @param sysUser 系统用户
     * @param userId  用户ID
     * @throws BizException 业务异常
     */
    void del(SysUser sysUser, Integer userId) throws BizException;

    /**
     * 编辑用户
     *
     * @param sysUserEdit 请求参数
     * @throws BizException 业务异常
     */
    void edit(SysUserEdit sysUserEdit) throws BizException;

    /**
     * 禁用或启动用户
     *
     * @param id     用户ID
     * @param status 状态
     * @throws BizException 业务异常
     */
    void enable(Integer id, Integer status, Integer userId) throws BizException;

    /**
     * 修改密码
     *
     * @param sysUser 系统用户
     * @param request 请求参数
     * @throws BizException 业务异常
     */
    void updatePassword(SysUser sysUser, UpdatePasswordParam request) throws BizException;

    /**
     * 重置密码
     *
     * @param request 请求参数
     * @throws BizException 业务异常
     */
    void resetPassword(SysResetPasswordParam request) throws BizException;

    /**
     * 修改签名
     *
     * @param userId
     * @param sign
     * @throws BizException
     */
    void updateSign(Integer userId, String sign) throws BizException;

    /**
     * 修改签名
     *
     * @param guid
     * @param sign
     * @throws BizException
     */
    void updateSign(String guid, String sign) throws BizException;

    /**
     * 查询当前登录用户可以管理的所有权限
     *
     * @param loginUser 系统用户
     * @param platform  平台
     * @return
     * @throws BizException 业务异常
     */
    List<SysRuleDTO> getUserRuleList(SysUser loginUser, String language, Integer platform) throws BizException;

    /**
     * 根据组织机构获取用户权限
     *
     * @param loginUser 系统用户
     * @param platform  平台
     * @param deptId    组织机构ID
     * @return 用户权限
     * @throws BizException 业务异常
     */
    List<SysRuleDTO> getUserRuleList(SysUser loginUser, String language, Integer platform, Integer deptId) throws BizException;

    /**
     * 获取小程序用户权限列表
     *
     * @param loginUser 微信用户
     * @return 小程序用户权限列表
     * @throws BizException 业务异常
     */
    List<SysRuleDTO> getMpUserRuleList(WxUser loginUser, String language) throws BizException;

    /**
     * 获取小程序用户权限列表
     *
     * @param loginUser
     * @param deptId
     * @return
     * @throws BizException
     */
    List<SysRuleDTO> getMpUserRuleList(WxUser loginUser, String language, Integer deptId) throws BizException;

    /**
     * 查询用户权限
     *
     * @param sysUserId
     * @param platform
     * @return
     * @throws BizException
     */
    List<SysRuleDTO> getUserRuleList(Integer sysUserId, String language, Integer platform) throws BizException;

    /**
     * 查询用户权限
     *
     * @param sysUserId
     * @param platform
     * @param deptId
     * @return
     * @throws BizException
     */
    List<SysRuleDTO> getUserRuleList(Integer sysUserId, String language, Integer platform, Integer deptId) throws BizException;

    /**
     * ==============用户角色==============
     **/

    /**
     * 获取用户组织机构角色
     *
     * @param userId 用户ID
     * @param deptId 组织机构ID
     * @return 用户组织机构角色
     * @throws BizException 业务异常
     */
    List<SysDeptRoleDTO> getUserDeptRoles(Integer userId, Integer deptId) throws BizException;

    /**
     * 编辑用户组织机构以及角色
     *
     * @param request 请求参数
     * @throws BizException 业务异常
     */
    void editDeptRoles(SysUserRoleEdit request) throws BizException;

    /**
     * 编辑用户组织机构以及角色
     *
     * @param userDeptRolesAdd 请求参数
     */
    void addDeptRoles(SysUserDeptRolesAdd userDeptRolesAdd) throws BizException;

    /**
     * ==============内部账号==============
     **/

    /**
     * 内部帐号列表
     *
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @param keyword  搜索关键字
     * @return 内部帐号列表
     * @throws BizException 业务异常
     */
    PageData<SysUserDTO> innerUserList(Integer pageNum, Integer pageSize, String keyword) throws BizException;

    /**
     * 添加内部帐号
     *
     * @param request 请求参数
     * @throws BizException 业务异常
     */
    void innerUserAdd(SysInnerUserAddParam request) throws BizException;

    /**
     * 编辑内部账号
     *
     * @param request 请求参数
     * @throws BizException 业务异常
     */
    void innerUserEdit(SysInnerUserEditParam request) throws BizException;

    /**
     * ==============公共查询==============
     **/

    /**
     * 查询系统用户
     *
     * @param userId 用户ID
     * @return 用户信息
     * @throws BizException 业务异常
     */
    SysUser getUserById(Integer userId) throws BizException;

    /**
     * 根据手机号获取用户信息
     *
     * @param phone 手机号
     * @return 用户信息
     * @throws BizException 业务异常
     */
    SysUser getUserByPhone(String phone) throws BizException;

    /**
     * 根据用户名查找用户
     *
     * @param username 用户名称
     * @return 用户信息
     * @throws BizException 业务异常
     */
    SysUser getUserByUsername(String username) throws BizException;

    /**
     * 根据Code获取用户信息
     *
     * @param code code
     * @return 用户信息
     * @throws
     */
    SysUser getUserByCode(String code) throws BizException;

    /**
     * 根据guid获取用户信息
     *
     * @param guid guid
     * @return 用户信息
     */
    SysUser getUserByGuid(String guid) throws BizException;

    /**
     * 小程序访客登录用户
     *
     * @return
     * @throws BizException
     */
    SysUser getMpTouristUser() throws BizException;

    /**
     * 获取消息接收人列表
     *
     * @param deptIds 组织机构ID列表
     * @return 消息接收人
     * @throws BizException 业务异常
     */
    List<AppMsgToUserDTO> getAvailableRecUsers(List<Integer> deptIds) throws BizException;

    /**
     * 使用组织机构ID查询用户信息
     *
     * @param deptId 组织机构ID
     * @return 用户信息
     * @throws BizException 业务异常
     **/
    List<SysUserDTO> getUsersByDeptId(Integer deptId) throws BizException;

    /**
     * 查询拥有权限的用户
     *
     * @param deptId
     * @param ruleCode
     * @return
     */
    List<SysUserDTO> getUsersByDeptIdAndRule(Integer deptId, String ruleCode);

    /**
     * 获取用户邮箱
     *
     * @param userIdList 用户ID列表
     * @return 用户邮箱
     */
    Map<Integer, String> getUserEmails(List<Integer> userIdList);

    /**
     * 获取用户信息
     *
     * @param userIdList
     * @return
     */
    List<SysUserDTO> getUserList(List<Integer> userIdList);

    /**
     * 判断用户是否有权限
     *
     * @param deptId
     * @param userId
     * @param ruleCode
     * @return
     */
    boolean hasRule(Integer deptId, Integer userId, String ruleCode);

    /**
     * 获取用户免密登录code
     *
     * @param userId
     * @return
     * @throws BizException
     */
    String getUserCode(Integer userId) throws BizException;


    /**
     * 获取密码最后修改时间
     *
     * @param userId 用户ID
     * @return 时间戳（毫秒）
     */
    long getPasswordLastUpdateTime(Integer userId);
}
