package com.whfc.fuum.service;

import com.whfc.common.result.PageData;
import com.whfc.fuum.dto.SysUserOperLogDTO;
import com.whfc.fuum.param.SysUserOperLogQueryParam;

/**
 * 系统用户操作日志服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/10
 */
public interface SysUserOperLogService {


    /**
     * 保存操作日志
     *
     * @param log 操作日志
     */
    void saveLog(SysUserOperLogDTO log);

    /**
     * 查询操作日志
     *
     * @param param
     * @return
     */
    PageData<SysUserOperLogDTO> list(SysUserOperLogQueryParam param);
}
