package com.whfc.fuum.service;

import com.whfc.common.exception.BizException;
import com.whfc.common.result.PageData;
import com.whfc.fuum.dto.SysBoardComponentDTO;
import com.whfc.fuum.dto.SysBoardConfigDTO;
import com.whfc.fuum.dto.SysBoardRouterDTO;
import com.whfc.fuum.dto.SysBoardTemplateDTO;
import com.whfc.fuum.param.SysBoardComponentParam;
import com.whfc.fuum.param.SysBoardConfigParam;
import com.whfc.fuum.param.SysBoardRouterParam;
import com.whfc.fuum.param.SysBoardTemplateParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/24
 */
public interface SysBoardConfigService {


    /************************************  大屏配置  ******************************************/

    /**
     * 查询大屏配置
     *
     * @param deptId
     * @param router
     * @return
     */
    List<SysBoardConfigDTO> list(Integer deptId, String router) throws BizException;

    /**
     * 分页查询大屏配置
     *
     * @param deptId
     * @param router
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageData<SysBoardConfigDTO> page(Integer deptId, String router, Integer pageNum, Integer pageSize) throws BizException;

    /**
     * 添加大屏配置
     *
     * @param param
     */
    void add(SysBoardConfigParam param) throws BizException;

    /**
     * 修改大屏配置
     *
     * @param param
     */
    void edit(SysBoardConfigParam param) throws BizException;

    /**
     * 删除大屏配置
     *
     * @param deptId
     * @param router
     */
    void delete(Integer deptId, String router) throws BizException;

    /************************************  大屏路由  ******************************************/

    /**
     * 查询大屏路由
     *
     * @return
     */
    List<SysBoardRouterDTO> routerList() throws BizException;

    /**
     * 分页查询大屏路由
     *
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageData<SysBoardRouterDTO> routerPage(Integer pageNum, Integer pageSize) throws BizException;

    /**
     * 添加大屏路由
     *
     * @param deptId
     * @param param
     */
    void routerAdd(Integer deptId, SysBoardRouterParam param) throws BizException;

    /**
     * 修改大屏路由
     *
     * @param deptId
     * @param param
     */
    void routerEdit(Integer deptId, SysBoardRouterParam param) throws BizException;

    /**
     * 删除大屏路由
     *
     * @param id
     */
    void routerDelete(Integer id) throws BizException;

    /************************************  大屏模板  ******************************************/

    /**
     * 查询大屏模板
     *
     * @param keyword
     * @return
     */
    List<SysBoardTemplateDTO> templateList(String keyword) throws BizException;

    /**
     * 分页查询大屏模板
     *
     * @param keyword
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageData<SysBoardTemplateDTO> templatePage(String keyword, Integer pageNum, Integer pageSize) throws BizException;

    /**
     * 添加大屏模板
     *
     * @param deptId
     * @param param
     */
    void templateAdd(Integer deptId, SysBoardTemplateParam param) throws BizException;

    /**
     * 修改大屏模板
     *
     * @param deptId
     * @param param
     */
    void templateEdit(Integer deptId, SysBoardTemplateParam param) throws BizException;

    /**
     * 删除大屏模板
     *
     * @param id
     */
    void templateDelete(Integer id);

    /************************************  大屏组件  ******************************************/

    /**
     * 查询大屏组件
     *
     * @param deptId
     * @param routerId
     * @param keyword
     * @return
     */
    List<SysBoardComponentDTO> componentList(Integer deptId, Integer routerId, String keyword) throws BizException;

    /**
     * 分页查询大屏组件
     *
     * @param deptId
     * @param routerId
     * @param keyword
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageData<SysBoardComponentDTO> componentPage(Integer deptId, Integer routerId, String keyword, Integer pageNum, Integer pageSize) throws BizException;

    /**
     * 添加大屏组件
     *
     * @param deptId
     * @param param
     */
    void componentAdd(Integer deptId, SysBoardComponentParam param) throws BizException;

    /**
     * 修改大屏组件
     *
     * @param deptId
     * @param param
     */
    void componentEdit(Integer deptId, SysBoardComponentParam param) throws BizException;

    /**
     * 删除大屏组件
     *
     * @param id
     */
    void componentDelete(Integer id) throws BizException;
}
