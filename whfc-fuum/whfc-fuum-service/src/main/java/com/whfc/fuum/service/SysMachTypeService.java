package com.whfc.fuum.service;

import com.whfc.common.exception.BizException;
import com.whfc.common.result.ListData;
import com.whfc.common.result.PageData;
import com.whfc.fuum.dto.SysMachListDTO;
import com.whfc.fuum.dto.SysMachTypeDTO;
import com.whfc.fuum.dto.SysMachTypeIconDTO;
import com.whfc.fuum.param.SysMachTypeAddParam;
import com.whfc.fuum.param.SysMachTypeClassifyAddParam;
import com.whfc.fuum.param.SysMachTypeClassifyEditParam;
import com.whfc.fuum.param.SysMachTypeEditParam;

import java.util.List;
import java.util.Map;

/**
 * @Description 设备类型接口类
 * <AUTHOR>
 * @Date 2020/8/24 17:30
 * @Version 1.0
 */
public interface SysMachTypeService {

    /**
     * 添加设备分类
     *
     * @param param
     * @return
     * @throws BizException 业务异常
     * <AUTHOR>
     * @date 2020/8/25 11:02
     **/
    void machTypeClassifyAdd(SysMachTypeClassifyAddParam param) throws BizException;

    /**
     * 修改设备分类
     *
     * @param param
     * @return
     * @throws BizException 业务异常
     * <AUTHOR>
     * @date 2020/8/24 18:15
     **/
    void machTypeClassifyEdit(SysMachTypeClassifyEditParam param) throws BizException;

    /**
     * 添加设备类型
     *
     * @param param
     * @return
     * @throws BizException 业务异常
     * <AUTHOR>
     * @date 2020/8/24 18:12
     **/
    void machTypeAdd(SysMachTypeAddParam param) throws BizException;

    /**
     * 修改设备类型
     *
     * @param param
     * @return
     * @throws BizException 业务异常
     * <AUTHOR>
     * @date 2020/8/24 18:15
     **/
    void machTypeEdit(SysMachTypeEditParam param) throws BizException;

    /**
     * 删除设备分类/类型
     *
     * @param machTypeId
     * @return
     * @throws BizException 业务异常
     * <AUTHOR>
     * @date 2020/8/24 20:55
     **/
    void machTypeDel(Integer machTypeId) throws BizException;

    /**
     * 启用禁用设备分类/类型
     *
     * @param machTypeId
     * @param state
     * @return
     * @throws BizException 业务异常
     * <AUTHOR>
     * @date 2020/8/24 20:58
     **/
    void enable(Integer machTypeId, Integer state) throws BizException;


    /**
     * 查询设备类型列表
     *
     * @param machTypeId
     * @param pageNum
     * @param pageSize
     * @param keyword
     * @return
     * @throws BizException 业务异常
     */
    PageData<SysMachTypeDTO> machTypeList(Integer machTypeId, Integer pageNum, Integer pageSize, String keyword) throws BizException;

    /**
     * 查询设备类型列表
     *
     * @param keyword
     * @return
     * @throws BizException 业务异常
     */
    ListData<SysMachTypeDTO> machTypeList(String keyword) throws BizException;

    /**
     * 查询设备类型树
     *
     * @return
     * @throws BizException
     */
    ListData<SysMachListDTO> machLists() throws BizException;

    /**
     * 使用在线和工作状态获取设备图标
     *
     * @param code
     * @param netState
     * @param workState
     * @return
     * @throws BizException
     */
    String getIcon(String code, Integer netState, Integer workState) throws BizException;

    /**
     * 使用在线状态获取设备图标
     *
     * @param code
     * @param netState
     * @return
     * @throws BizException
     */
    String getIcon(String code, Integer netState) throws BizException;

    /**
     * 使用在线和工作状态获取设备图标
     *
     * @param machTypeId
     * @param netState
     * @param workState
     * @return
     * @throws BizException
     */
    String getIcon(Integer machTypeId, Integer netState, Integer workState) throws BizException;

    /**
     * 使用工作状态获取设备图标
     *
     * @param machTypeId
     * @param type
     * @return
     * @throws BizException
     */
    String getIcon(Integer machTypeId, Integer type) throws BizException;

    /**
     * 获取设备状态
     *
     * @param machTypeList
     * @return
     */
    List<SysMachTypeIconDTO> getIcons(List<SysMachTypeIconDTO> machTypeList);

    /**
     * 使用主键查询设备类型
     *
     * @param machTypeId
     * @return
     * @throws BizException
     */
    SysMachTypeDTO getMachTypeById(Integer machTypeId) throws BizException;

    /**
     * 使用主键查询父节点设备内别
     *
     * @param machTypeId
     * @return
     * @throws BizException
     */
    SysMachTypeDTO getParentMachTypeById(Integer machTypeId) throws BizException;

    /**
     * 使用编码查询父节点设备名称
     *
     * @param code
     * @return
     * @throws BizException
     */
    String getParentMachTypeNameByCode(String code) throws BizException;

    /**
     * 根据设备名称模糊查找设备类型
     *
     * @param machName 设备名称
     * @return 设备类型
     * @throws BizException
     */
    SysMachTypeDTO getMachTypeByLikeMachName(String machName) throws BizException;

    /**
     * 判断设备类型:是否属于船舶
     *
     * @param machTypeId
     * @return
     * @throws BizException
     */
    boolean isShip(Integer machTypeId) throws BizException;
}
