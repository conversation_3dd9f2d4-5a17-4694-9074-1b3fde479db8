package com.whfc.fuum.service;

import com.whfc.common.exception.BizException;
import com.whfc.common.result.PageData;
import com.whfc.fuum.dto.AppCorpDTO;
import com.whfc.fuum.dto.AppStatNumDTO;
import com.whfc.fuum.param.AppCorpAddParam;
import com.whfc.fuum.param.AppCorpEditParam;
import com.whfc.fuum.param.AppCorpListQO;

import java.util.List;


/**
 * 项目合作单位
 *
 * <AUTHOR>
 * @Description:
 * @date 2019年11月20日
 */
public interface AppCorpService {

    /**
     * 获取所有的合作单位
     *
     * @param deptId 合作单位ID
     * @return 合作单位列表
     * @throws BizException 业务异常
     */
    List<AppCorpDTO> list(Integer deptId) throws BizException;

    /**
     * 主键ID查询合作单位
     *
     * @param corpIds 合作单位ID
     * @return 合作单位
     * @throws BizException 业务异常
     */
    List<AppCorpDTO> list(List<Integer> corpIds) throws BizException;

    /**
     * 合作单位列表页
     *
     * @param request 请求参数
     * @return 合作单位列表
     * @throws BizException 业务异常
     */
    PageData<AppCorpDTO> list(AppCorpListQO request) throws BizException;

    /**
     * 合作单位详情
     *
     * @param corpId 合作单位ID
     * @return
     * @throws BizException 业务异常
     */
    AppCorpDTO detail(Integer corpId) throws BizException;

    /**
     * 添加合作单位
     *
     * @param request 请求参数
     * @throws BizException 业务异常
     */
    void add(AppCorpAddParam request) throws BizException;

    /**
     * 编辑合作单位
     *
     * @param request 请求参数
     * @throws BizException 业务异常
     */
    void edit(AppCorpEditParam request) throws BizException;

    /**
     * 删除合作单位
     *
     * @param corpId 合作单位ID
     * @throws BizException 业务异常
     */
    void del(Integer corpId) throws BizException;

    /**
     * 根据组织机构和名称查找组织机构
     *
     * @param deptId
     * @param name
     * @return
     * @throws BizException
     */
    AppCorpDTO getCorpByDeptIdAndName(Integer deptId, String name) throws BizException;

    /**
     * 合作单位类型统计
     *
     * @param deptIds
     * @return
     * @throws BizException
     */
    List<AppStatNumDTO> getCorpTypeStat(List<Integer> deptIds) throws BizException;

    /**
     * 合作单位类型统计
     *
     * @param deptId
     * @return
     * @throws BizException
     */
    List<AppStatNumDTO> getCorpTypeStat(Integer deptId) throws BizException;
}
