package com.whfc.fuum.service;


import com.whfc.common.exception.BizException;
import com.whfc.common.result.PageData;
import com.whfc.common.util.Gps;
import com.whfc.fuum.dto.AppProjectDTO;
import com.whfc.fuum.dto.AppProjectDetailDTO;
import com.whfc.fuum.dto.AppProjectStat;
import com.whfc.fuum.entity.SysUser;
import com.whfc.fuum.param.AppProjectParam;

import java.util.List;

/**
 * 操作项目的服务接口
 *
 * <AUTHOR>
 * @Description:
 * @date 2019年7月22日
 */
public interface AppProjectService {

    /**
     * 获取项目列表
     *
     * @param user     用户
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @param keyword  搜索关键字
     * @return 项目列表
     * @throws BizException 业务异常
     */
    PageData<AppProjectDTO> list(SysUser user, Integer pageNum, Integer pageSize, String keyword) throws BizException;

    /**
     * 查询项目列表(不分页/简单信息)
     *
     * @param user 用户
     * @return 项目列表
     * @throws BizException 业务异常
     */
    List<AppProjectDTO> list(SysUser user) throws BizException;

    /**
     * 根据项目id获得项目的详细信息
     *
     * @param deptId 组织机构ID
     * @return 项目详情
     * @throws BizException 业务异常
     */
    AppProjectDetailDTO detail(Integer deptId) throws BizException;

    /**
     * 根据deptId查找项目
     *
     * @param deptId 组织机构ID
     * @return 项目信息
     * @throws BizException 业务异常
     */
    AppProjectDTO getByDeptId(Integer deptId) throws BizException;

    /**
     * 查询项目定位
     *
     * @param deptId
     * @return
     * @throws BizException
     */
    Gps getGps(Integer deptId) throws BizException;

    /**
     * 编辑项目的基本信息
     *
     * @param appProjectParam 请求参数
     * @throws BizException 业务异常
     */
    void edit(AppProjectParam appProjectParam) throws BizException;

    /**
     * 使用deptIds查询项目列表
     *
     * @param deptIds 组织机构ID列表
     * @return 项目列表
     * @throws BizException 业务异常
     */
    List<AppProjectDTO> getProjectList(List<Integer> deptIds) throws BizException;

    /**
     * 使用deptIds查询项目列表
     *
     * @return 项目列表
     * @throws BizException 业务异常
     */
    List<AppProjectDTO> getProjectList() throws BizException;

    /**
     * 获取项目自定义属性
     *
     * @param deptId 组织机构ID
     * @return 项目自定义参数
     * @throws BizException 业务异常
     */
    String getProjectCustomizeField(Integer deptId) throws BizException;

    /**************企业数据看板*************/

    /**
     * 获取项目统计信息
     *
     * @param deptId
     * @return
     * @throws BizException
     */
    AppProjectStat getProjectStat(Integer deptId) throws BizException;
}
