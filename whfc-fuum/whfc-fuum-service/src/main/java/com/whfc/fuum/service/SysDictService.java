package com.whfc.fuum.service;

import com.whfc.common.exception.BizException;
import com.whfc.common.result.PageData;
import com.whfc.fuum.dto.SysDictDTO;
import com.whfc.fuum.param.SysDictDataAddParam;
import com.whfc.fuum.param.SysDictDataEditParam;
import com.whfc.fuum.param.SysDictTypeAddParam;
import com.whfc.fuum.param.SysDictTypeEditParam;

/**
 * @Description: 数据字典服务
 * @author: xugcheng
 * @version: 1.0
 * @date: 2019/7/25 10:37
 */
public interface SysDictService {


    /**
     * 获取字典表类型列表
     *
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @throws BizException 业务异常
     */
    PageData<SysDictDTO> typeList(Integer pageNum, Integer pageSize) throws BizException;

    /**
     * 添加字典类型
     *
     * @param request 请求参数
     * @throws BizException 业务异常
     */
    void addType(SysDictTypeAddParam request) throws BizException;

    /**
     * 编辑字典类型
     *
     * @param request 请求参数
     * @throws BizException 业务异常
     */
    void editType(SysDictTypeEditParam request) throws BizException;

    /**
     * 删除字典表类型
     *
     * @param dictId 数据字典ID
     * @throws BizException 业务异常
     */
    void delType(Integer dictId) throws BizException;

    /**
     * 查询数据字典
     *
     * @param deptId        组装机构ID
     * @param sysDictTypeId 数据字典类型ID
     * @param pageNum       页码
     * @param pageSize      每页数量
     * @return 数据字典
     * @throws BizException 业务异常
     */
    PageData<SysDictDTO> listDictData(Integer deptId, Integer sysDictTypeId, Integer pageNum, Integer pageSize) throws BizException;

    /**
     * 添加数据字典
     *
     * @param request 请求参数
     * @throws BizException 业务异常
     */
    void addDictData(SysDictDataAddParam request) throws BizException;

    /**
     * 编辑数据字典
     *
     * @param request 请求参数
     * @throws BizException 业务异常
     */
    void editDictData(SysDictDataEditParam request) throws BizException;

    /**
     * 删除数据字典
     *
     * @param dictId 数据字典ID
     * @throws BizException 业务异常
     */
    void delDictData(Integer dictId) throws BizException;

    /**
     * 根据ID查询字典数据
     *
     * @param dictId 数据字典ID
     * @return 字典数据
     * @throws BizException 业务异常
     */
    SysDictDTO getDictById(Integer dictId) throws BizException;

    /**
     * 查询数据字典名称
     *
     * @param dictId 数据字典ID
     * @return 数据字典名称
     * @throws BizException 业务异常
     */
    String getDictName(Integer dictId) throws BizException;

    /**
     * 根据字典类型名称获取字典数据
     *
     * @param typeCode 数据字典类型code
     * @return 数据字典信息
     * @throws BizException 业务异常
     */
    SysDictDTO getDictDataByTypeCode(String typeCode) throws BizException;

    /**
     * 根据字典类型名称获取字典数据
     *
     * @param typeCode 数据字典类型code
     * @return 数据字典信息
     * @throws BizException 业务异常
     */
    SysDictDTO getDictDataByTypeCode(Integer deptId, String typeCode) throws BizException;

    /**
     * 根据code和名称获取id
     *
     * @param deptId     组织机构ID
     * @param parentCode 编码
     * @param name       名称
     * @return 字典id
     */
    Integer getOrSet(Integer deptId, String parentCode, String name) throws BizException;

    /**
     * 根据code和名称获取字典
     *
     * @param deptId     组织机构ID
     * @param parentCode 编码
     * @param name       名称
     * @return
     */
    SysDictDTO getByCodeAndName(Integer deptId,String parentCode, String name) throws BizException;
}
