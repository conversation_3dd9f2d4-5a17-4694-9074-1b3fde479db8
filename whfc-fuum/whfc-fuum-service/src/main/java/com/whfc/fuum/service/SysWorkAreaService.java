package com.whfc.fuum.service;

import com.whfc.common.exception.BizException;
import com.whfc.common.result.PageData;
import com.whfc.fuum.dto.SysWorkAreaDTO;
import com.whfc.fuum.param.SysWorkAreaAddParam;
import com.whfc.fuum.param.SysWorkAreaEditParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-09-01 14:38
 */
public interface SysWorkAreaService {

    /**
     * 获取工区列表
     *
     * @param deptId   组织机构ID
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @param keyword  搜索关键字
     * @return 工区列表
     * @throws BizException 业务异常
     */
    PageData<SysWorkAreaDTO> list(Integer deptId, Integer pageNum, Integer pageSize, String keyword) throws BizException;

    /**
     * 获取工区列表
     *
     * @param deptId  组织机构ID
     * @param keyword 搜索关键字
     * @return 工区列表
     * @throws BizException 业务异常
     */
    List<SysWorkAreaDTO> list(Integer deptId, String keyword) throws BizException;

    /**
     * 添加工区
     *
     * @param param 参数
     * @throws BizException 业务异常
     */
    void add(SysWorkAreaAddParam param) throws BizException;

    /**
     * 修改工区
     *
     * @param param 参数
     * @throws BizException 业务异常
     */
    void edit(SysWorkAreaEditParam param) throws BizException;

    /**
     * 删除工区
     *
     * @param workAreaId 工区ID
     * @throws BizException 业务异常
     */
    void del(Integer workAreaId) throws BizException;
}
