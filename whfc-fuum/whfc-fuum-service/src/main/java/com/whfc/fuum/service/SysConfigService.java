package com.whfc.fuum.service;

import com.whfc.common.exception.BizException;
import com.whfc.common.result.PageData;
import com.whfc.fuum.dto.SysExternalLinkDTO;
import com.whfc.fuum.dto.SysSkinDTO;
import com.whfc.fuum.param.SysSkinAdd;
import com.whfc.fuum.param.SysSkinEdit;

import java.util.List;

/**
 * @Description: 系统配置服务
 * @author: xugcheng
 * @version: 1.0
 * @date: 2021-12-15 17:37
 */
public interface SysConfigService {

    /**
     * 根据域名获取自定义皮肤配置
     *
     * @param host
     * @return
     * @throws BizException
     */
    SysSkinDTO getSkin(String host) throws BizException;

    /**
     * 获取所有皮肤
     *
     * @param pageNum
     * @param pageSize
     * @return
     * @throws BizException
     */
    PageData<SysSkinDTO> getSkinList(Integer pageNum, Integer pageSize) throws BizException;

    /**
     * 获取所有皮肤
     *
     * @return
     * @throws BizException
     */
    List<SysSkinDTO> getSkinList() throws BizException;

    /**
     * 新增皮肤
     *
     * @param param
     * @throws BizException
     */
    void addSkin(SysSkinAdd param) throws BizException;

    /**
     * 编辑皮肤
     *
     * @param param
     * @throws BizException
     */
    void editSkin(SysSkinEdit param) throws BizException;

    /**
     * 删除皮肤
     *
     * @param skinId
     * @throws BizException
     */
    void delSkin(Integer skinId) throws BizException;
}
